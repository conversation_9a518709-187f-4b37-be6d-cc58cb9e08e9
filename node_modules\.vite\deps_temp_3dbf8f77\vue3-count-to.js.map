{"version": 3, "sources": ["../../.pnpm/vue3-count-to@1.1.2_vue@3.5.18_typescript@5.8.3_/node_modules/vue3-count-to/node_modules/@babel/runtime/helpers/esm/defineProperty.js", "../../.pnpm/vue3-count-to@1.1.2_vue@3.5.18_typescript@5.8.3_/node_modules/vue3-count-to/src/count-to.js", "../../.pnpm/vue3-count-to@1.1.2_vue@3.5.18_typescript@5.8.3_/node_modules/vue3-count-to/src/index.js"], "sourcesContent": ["export default function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}", "import Component from 'vue-count-to/src/vue-countTo.vue';\n\nComponent.unmounted = Component.destroyed\nReflect.deleteProperty(Component, 'destroyed');\n\nexport default {\n  name: 'CountTo',\n  emits: ['callback', 'mountedCallback'],\n  ...Component\n};\n", "import CountTo from \"./count-to\";\nimport pkg from \"../package.json\";\n\nfunction install(app) {\n  app.component(\"count-to\", CountTo);\n}\n\nexport default { install, version: pkg.version };\n\nexport { CountTo };\n"], "mappings": ";;;;AAAe,SAASA,gBAAgBC,KAAKC,KAAKC,OAAAA;AAAAA,SAC5CD,OAAOD,MACTG,OAAOC,eAAeJ,KAAKC,KAAK,EAC9BC,OACAG,YAAAA,MACAC,cAAAA,MACAC,UAAAA,KAAU,CAAA,IAGZP,IAAIC,GAAAA,IAAOC,OAGNF;AAAAA;AAAAA,SAAAA,QAAAA,QAAAA,gBAAAA;AAAAA,MAAAA,OAAAA,OAAAA,KAAAA,MAAAA;AAAAA,MAAAA,OAAAA,uBAAAA;AAAAA,QAAAA,UAAAA,OAAAA,sBAAAA,MAAAA;AAAAA,uBAAAA,UAAAA,QAAAA,OAAAA,SAAAA,KAAAA;AAAAA,aAAAA,OAAAA,yBAAAA,QAAAA,GAAAA,EAAAA;IAAAA,CAAAA,IAAAA,KAAAA,KAAAA,MAAAA,MAAAA,OAAAA;EAAAA;AAAAA,SAAAA;AAAAA;ACVTQ,UAAUC,YAAYD,UAAUE,WAChCC,QAAQC,eAAeJ,WAAW,WAAA;AAElC,IAAA,UAAA,SAAA,QAAA;AAAA,WAAA,IAAA,GAAA,IAAA,UAAA,QAAA,KAAA;AAAA,QAAA,SAAA,QAAA,UAAA,CAAA,IAAA,UAAA,CAAA,IAAA,CAAA;AAAA,QAAA,IAAA,QAAA,OAAA,MAAA,GAAA,IAAA,EAAA,QAAA,SAAA,KAAA;AAAA,sBAAA,QAAA,KAAA,OAAA,GAAA,CAAA;IAAA,CAAA,IAAA,OAAA,4BAAA,OAAA,iBAAA,QAAA,OAAA,0BAAA,MAAA,CAAA,IAAA,QAAA,OAAA,MAAA,CAAA,EAAA,QAAA,SAAA,KAAA;AAAA,aAAA,eAAA,QAAA,KAAA,OAAA,yBAAA,QAAA,GAAA,CAAA;IAAA,CAAA;EAAA;AAAA,SAAA;AAAA,EAAA,EACEK,MAAM,WACNC,OAAO,CAAC,YAAY,iBAAA,EAAA,GACjBN,SAAAA;ACDL,IAAA,QAAe,EAAEO,SAJjB,SAAiBC,KAAAA;AACfA,MAAIC,UAAU,YAAYC,OAAAA;AAAAA,GAGFC,SAAAA,QAAAA;AAAAA,IAAAA,4BAAAA;", "names": ["_defineProperty", "obj", "key", "value", "Object", "defineProperty", "enumerable", "configurable", "writable", "Component", "unmounted", "destroyed", "Reflect", "deleteProperty", "name", "emits", "install", "app", "component", "<PERSON><PERSON><PERSON>", "version"]}