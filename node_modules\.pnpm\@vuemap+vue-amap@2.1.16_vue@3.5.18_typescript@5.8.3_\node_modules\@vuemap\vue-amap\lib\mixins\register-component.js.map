{"version": 3, "file": "register-component.js", "sources": ["../../../mixins/register-component.ts"], "sourcesContent": ["import {defineComponent, isProxy, toRaw, unref} from \"vue\";\r\nimport {convertEventToLowerCase, eventReg, upperCamelCase, bindInstanceEvent, removeInstanceEvent} from \"../utils\";\r\nimport type {WatchStopHandle, ComponentPublicInstance} from \"vue\";\r\n\r\nexport default defineComponent({\r\n  inject: {\r\n    parentInstance: {\r\n      default: null\r\n    }\r\n  },\r\n  inheritAttrs: false,\r\n  props: {\r\n    visible: {\r\n      type: Boolean,\r\n      default: true\r\n    }, // 是否显示，默认 true\r\n    zIndex: {\r\n      type: Number\r\n    },\r\n    reEventWhenUpdate: {\r\n      type: <PERSON><PERSON><PERSON>,\r\n      default: false\r\n    }, // 是否在组件更新时重新注册事件，主要用于数组更新时，绑定了事件但事件的对象不会更新问题\r\n    extraOptions: {\r\n      type: Object\r\n    } // 额外扩展属性\r\n  },\r\n  emits: ['init'],\r\n  data() {\r\n    return {\r\n      needInitComponents: [] as (() => void)[],\r\n      unwatchFns: [] as WatchStopHandle[],\r\n      propsRedirect: {},\r\n      converters: {},\r\n      isDestroy: false,\r\n      cacheEvents: {},\r\n      isMounted: false\r\n    };\r\n  },\r\n  created() {\r\n    this.$amapComponent = null as any;\r\n    this.$parentComponent = null as any;\r\n  },\r\n  mounted() {\r\n    if (this.parentInstance) {\r\n      if (this.parentInstance.$amapComponent) {\r\n        this.register();\r\n      } else {\r\n        this.parentInstance.addChildComponent(this.register);\r\n      }\r\n    }\r\n  },\r\n\r\n  beforeUnmount() {\r\n    if (!this.$amapComponent) return;\r\n    this.unregisterEvents();\r\n    this.unwatchFns.forEach(item => item());\r\n    this.unwatchFns = [];\r\n    this.destroyComponent();\r\n    this.isDestroy = true;\r\n  },\r\n  beforeUpdate() {\r\n    if(this.reEventWhenUpdate && this.isMounted && this.$amapComponent){\r\n      this.unregisterEvents();\r\n    }\r\n  },\r\n  updated() {\r\n    if(this.reEventWhenUpdate && this.isMounted && this.$amapComponent){\r\n      this.registerEvents();\r\n    }\r\n  },\r\n  methods: {\r\n    getHandlerFun(prop) {\r\n      if (this[`__${prop}`]) {\r\n        return this[`__${prop}`];\r\n      }\r\n      if(!this.$amapComponent){\r\n        return null;\r\n      }\r\n      return this.$amapComponent[`set${upperCamelCase(prop)}`];\r\n    },\r\n\r\n    convertProps() {\r\n      const props = {};\r\n      const {$props, propsRedirect} = this;\r\n      if(this.extraOptions){\r\n        Object.assign(props, this.extraOptions);\r\n      }\r\n      const result = Object.keys($props).reduce((res, _key) => {\r\n        let key = _key;\r\n        const propsValue = this.convertSignalProp(key, $props[key]);\r\n        if (propsValue === undefined) return res;\r\n        if (propsRedirect && propsRedirect[_key]) key = propsRedirect[key];\r\n        props[key] = propsValue;\r\n        return res;\r\n      }, props);\r\n      Object.keys(result).forEach(key => {\r\n        result[key] = this.convertProxyToRaw(result[key]);\r\n      });\r\n      return result;\r\n    },\r\n\r\n    convertProxyToRaw(value: any){\r\n      if(isProxy(value)){\r\n        return toRaw(value);\r\n      }\r\n      return unref(value);\r\n    },\r\n\r\n    convertSignalProp(key, sourceData) {\r\n      if (this.converters && this.converters[key]) {\r\n        return this.converters[key].call(this, sourceData);\r\n      }\r\n      return sourceData;\r\n    },\r\n\r\n    registerEvents() {\r\n      const $props = this.$attrs;\r\n      Object.keys($props).forEach(key => {\r\n        if(eventReg.test(key)){\r\n          const eventKey = convertEventToLowerCase(key);\r\n          bindInstanceEvent(this.$amapComponent, eventKey, $props[key]);\r\n          this.cacheEvents[eventKey] = $props[key];\r\n        }\r\n      });\r\n    },\r\n\r\n    unregisterEvents() {\r\n      Object.keys(this.cacheEvents).forEach(eventKey => {\r\n        removeInstanceEvent(this.$amapComponent, eventKey, this.cacheEvents[eventKey]);\r\n        delete this.cacheEvents[eventKey];\r\n      });\r\n    },\r\n\r\n    setPropWatchers() {\r\n      const {propsRedirect, $props} = this;\r\n\r\n      Object.keys($props).forEach(prop => {\r\n        let handleProp = prop;\r\n        if (propsRedirect && propsRedirect[prop]) handleProp = propsRedirect[prop];\r\n        const handleFun = this.getHandlerFun(handleProp);\r\n        if (!handleFun) return;\r\n        const watchOptions = {\r\n          deep: false\r\n        };\r\n        const propValueType = Object.prototype.toString.call($props[prop]);\r\n        if ( propValueType === '[object Object]' || propValueType === '[object Array]') {\r\n          watchOptions.deep = true;\r\n        }\r\n        // watch props\r\n        const unwatch = this.$watch(prop, nv => {\r\n          handleFun.call(this.$amapComponent, this.convertProxyToRaw(this.convertSignalProp(prop, nv)));\r\n        }, watchOptions);\r\n\r\n        // collect watchers for destroyed\r\n        this.unwatchFns.push(unwatch);\r\n      });\r\n    },\r\n\r\n    // some prop can not init by initial created methods\r\n    initProps() {\r\n      const props = ['editable', 'visible', 'zooms'];\r\n\r\n      props.forEach(propStr => {\r\n        if (this[propStr] !== undefined) {\r\n          const handleFun = this.getHandlerFun(propStr);\r\n          handleFun && handleFun.call(this.$amapComponent, this.convertProxyToRaw(this.convertSignalProp(propStr, this[propStr])));\r\n        }\r\n      });\r\n\r\n      // this.printReactiveProp();\r\n    },\r\n\r\n    lazyRegister(){\r\n      const $parent = this.parentInstance as (ComponentPublicInstance);\r\n      if($parent && $parent.addChildComponent){\r\n        $parent.addChildComponent(this);\r\n      }\r\n    },\r\n    addChildComponent(component : () => void){\r\n      this.needInitComponents.push(component);\r\n    },\r\n    createChildren(){\r\n      while (this.needInitComponents.length > 0){\r\n        this.needInitComponents[0]();\r\n        this.needInitComponents.splice(0, 1);\r\n      }\r\n    },\r\n    register() {\r\n      if(this.parentInstance && !this.$parentComponent){\r\n        this.$parentComponent = this.parentInstance.$amapComponent;\r\n      }\r\n      const res = this['__initComponent'] && this['__initComponent'](this.convertProps());\r\n      if (res && res.then) res.then((instance) => this.registerRest(instance)); // promise\r\n      else this.registerRest(res);\r\n    },\r\n\r\n    registerRest(instance) {\r\n      if (!this.$amapComponent && instance) this.$amapComponent = instance;\r\n      this.registerEvents();\r\n      this.initProps();\r\n      this.setPropWatchers();\r\n      this.$emit('init', this.$amapComponent, this);\r\n      this.$nextTick(() => {\r\n        this.createChildren();\r\n      });\r\n      this.isMounted = true;\r\n    },\r\n\r\n    // helper method\r\n    $$getInstance() {\r\n      return this.$amapComponent;\r\n    },\r\n    destroyComponent() {\r\n      this.$amapComponent.setMap && this.$amapComponent.setMap(null);\r\n      this.$amapComponent.close && this.$amapComponent.close();\r\n      this.$amapComponent.editor && this.$amapComponent.editor.close();\r\n    },\r\n    __visible(flag){\r\n      if(!!this.$amapComponent && !!this.$amapComponent.show && !!this.$amapComponent.hide){\r\n        flag === false ? this.$amapComponent.hide() : this.$amapComponent.show();\r\n      }\r\n    },\r\n    __zIndex(value){\r\n      if (this.$amapComponent && this.$amapComponent.setzIndex) {\r\n        this.$amapComponent.setzIndex(value);\r\n      }\r\n    }\r\n  }\r\n});\r\n"], "names": ["defineComponent", "upperCamelCase", "isProxy", "toRaw", "unref", "eventReg", "convertEventToLowerCase", "bindInstanceEvent", "removeInstanceEvent"], "mappings": ";;;;;;;;;AAIA,wBAAeA,mBAAgB,CAAA;AAAA,EAC7B,MAAQ,EAAA;AAAA,IACN,cAAgB,EAAA;AAAA,MACd,OAAS,EAAA,IAAA;AAAA,KACX;AAAA,GACF;AAAA,EACA,YAAc,EAAA,KAAA;AAAA,EACd,KAAO,EAAA;AAAA,IACL,OAAS,EAAA;AAAA,MACP,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,IAAA;AAAA,KACX;AAAA;AAAA,IACA,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,MAAA;AAAA,KACR;AAAA,IACA,iBAAmB,EAAA;AAAA,MACjB,IAAM,EAAA,OAAA;AAAA,MACN,OAAS,EAAA,KAAA;AAAA,KACX;AAAA;AAAA,IACA,YAAc,EAAA;AAAA,MACZ,IAAM,EAAA,MAAA;AAAA,KACR;AAAA;AAAA,GACF;AAAA,EACA,KAAA,EAAO,CAAC,MAAM,CAAA;AAAA,EACd,IAAO,GAAA;AACL,IAAO,OAAA;AAAA,MACL,oBAAoB,EAAC;AAAA,MACrB,YAAY,EAAC;AAAA,MACb,eAAe,EAAC;AAAA,MAChB,YAAY,EAAC;AAAA,MACb,SAAW,EAAA,KAAA;AAAA,MACX,aAAa,EAAC;AAAA,MACd,SAAW,EAAA,KAAA;AAAA,KACb,CAAA;AAAA,GACF;AAAA,EACA,OAAU,GAAA;AACR,IAAA,IAAA,CAAK,cAAiB,GAAA,IAAA,CAAA;AACtB,IAAA,IAAA,CAAK,gBAAmB,GAAA,IAAA,CAAA;AAAA,GAC1B;AAAA,EACA,OAAU,GAAA;AACR,IAAA,IAAI,KAAK,cAAgB,EAAA;AACvB,MAAI,IAAA,IAAA,CAAK,eAAe,cAAgB,EAAA;AACtC,QAAA,IAAA,CAAK,QAAS,EAAA,CAAA;AAAA,OACT,MAAA;AACL,QAAK,IAAA,CAAA,cAAA,CAAe,iBAAkB,CAAA,IAAA,CAAK,QAAQ,CAAA,CAAA;AAAA,OACrD;AAAA,KACF;AAAA,GACF;AAAA,EAEA,aAAgB,GAAA;AACd,IAAA,IAAI,CAAC,IAAK,CAAA,cAAA;AAAgB,MAAA,OAAA;AAC1B,IAAA,IAAA,CAAK,gBAAiB,EAAA,CAAA;AACtB,IAAA,IAAA,CAAK,UAAW,CAAA,OAAA,CAAQ,CAAQ,IAAA,KAAA,IAAA,EAAM,CAAA,CAAA;AACtC,IAAA,IAAA,CAAK,aAAa,EAAC,CAAA;AACnB,IAAA,IAAA,CAAK,gBAAiB,EAAA,CAAA;AACtB,IAAA,IAAA,CAAK,SAAY,GAAA,IAAA,CAAA;AAAA,GACnB;AAAA,EACA,YAAe,GAAA;AACb,IAAA,IAAG,IAAK,CAAA,iBAAA,IAAqB,IAAK,CAAA,SAAA,IAAa,KAAK,cAAe,EAAA;AACjE,MAAA,IAAA,CAAK,gBAAiB,EAAA,CAAA;AAAA,KACxB;AAAA,GACF;AAAA,EACA,OAAU,GAAA;AACR,IAAA,IAAG,IAAK,CAAA,iBAAA,IAAqB,IAAK,CAAA,SAAA,IAAa,KAAK,cAAe,EAAA;AACjE,MAAA,IAAA,CAAK,cAAe,EAAA,CAAA;AAAA,KACtB;AAAA,GACF;AAAA,EACA,OAAS,EAAA;AAAA,IACP,cAAc,IAAM,EAAA;AAClB,MAAA,IAAI,IAAK,CAAA,CAAA,EAAA,EAAK,IAAI,CAAA,CAAE,CAAG,EAAA;AACrB,QAAO,OAAA,IAAA,CAAK,CAAK,EAAA,EAAA,IAAI,CAAE,CAAA,CAAA,CAAA;AAAA,OACzB;AACA,MAAG,IAAA,CAAC,KAAK,cAAe,EAAA;AACtB,QAAO,OAAA,IAAA,CAAA;AAAA,OACT;AACA,MAAA,OAAO,KAAK,cAAe,CAAA,CAAA,GAAA,EAAMC,mBAAe,CAAA,IAAI,CAAC,CAAE,CAAA,CAAA,CAAA;AAAA,KACzD;AAAA,IAEA,YAAe,GAAA;AACb,MAAA,MAAM,QAAQ,EAAC,CAAA;AACf,MAAM,MAAA,EAAC,MAAQ,EAAA,aAAA,EAAiB,GAAA,IAAA,CAAA;AAChC,MAAA,IAAG,KAAK,YAAa,EAAA;AACnB,QAAO,MAAA,CAAA,MAAA,CAAO,KAAO,EAAA,IAAA,CAAK,YAAY,CAAA,CAAA;AAAA,OACxC;AACA,MAAM,MAAA,MAAA,GAAS,OAAO,IAAK,CAAA,MAAM,EAAE,MAAO,CAAA,CAAC,KAAK,IAAS,KAAA;AACvD,QAAA,IAAI,GAAM,GAAA,IAAA,CAAA;AACV,QAAA,MAAM,aAAa,IAAK,CAAA,iBAAA,CAAkB,GAAK,EAAA,MAAA,CAAO,GAAG,CAAC,CAAA,CAAA;AAC1D,QAAA,IAAI,UAAe,KAAA,KAAA,CAAA;AAAW,UAAO,OAAA,GAAA,CAAA;AACrC,QAAI,IAAA,aAAA,IAAiB,cAAc,IAAI,CAAA;AAAG,UAAA,GAAA,GAAM,cAAc,GAAG,CAAA,CAAA;AACjE,QAAA,KAAA,CAAM,GAAG,CAAI,GAAA,UAAA,CAAA;AACb,QAAO,OAAA,GAAA,CAAA;AAAA,SACN,KAAK,CAAA,CAAA;AACR,MAAA,MAAA,CAAO,IAAK,CAAA,MAAM,CAAE,CAAA,OAAA,CAAQ,CAAO,GAAA,KAAA;AACjC,QAAA,MAAA,CAAO,GAAG,CAAI,GAAA,IAAA,CAAK,iBAAkB,CAAA,MAAA,CAAO,GAAG,CAAC,CAAA,CAAA;AAAA,OACjD,CAAA,CAAA;AACD,MAAO,OAAA,MAAA,CAAA;AAAA,KACT;AAAA,IAEA,kBAAkB,KAAW,EAAA;AAC3B,MAAG,IAAAC,WAAA,CAAQ,KAAK,CAAE,EAAA;AAChB,QAAA,OAAOC,UAAM,KAAK,CAAA,CAAA;AAAA,OACpB;AACA,MAAA,OAAOC,UAAM,KAAK,CAAA,CAAA;AAAA,KACpB;AAAA,IAEA,iBAAA,CAAkB,KAAK,UAAY,EAAA;AACjC,MAAA,IAAI,IAAK,CAAA,UAAA,IAAc,IAAK,CAAA,UAAA,CAAW,GAAG,CAAG,EAAA;AAC3C,QAAA,OAAO,KAAK,UAAW,CAAA,GAAG,CAAE,CAAA,IAAA,CAAK,MAAM,UAAU,CAAA,CAAA;AAAA,OACnD;AACA,MAAO,OAAA,UAAA,CAAA;AAAA,KACT;AAAA,IAEA,cAAiB,GAAA;AACf,MAAA,MAAM,SAAS,IAAK,CAAA,MAAA,CAAA;AACpB,MAAA,MAAA,CAAO,IAAK,CAAA,MAAM,CAAE,CAAA,OAAA,CAAQ,CAAO,GAAA,KAAA;AACjC,QAAG,IAAAC,aAAA,CAAS,IAAK,CAAA,GAAG,CAAE,EAAA;AACpB,UAAM,MAAA,QAAA,GAAWC,6BAAwB,GAAG,CAAA,CAAA;AAC5C,UAAAC,6BAAA,CAAkB,IAAK,CAAA,cAAA,EAAgB,QAAU,EAAA,MAAA,CAAO,GAAG,CAAC,CAAA,CAAA;AAC5D,UAAA,IAAA,CAAK,WAAY,CAAA,QAAQ,CAAI,GAAA,MAAA,CAAO,GAAG,CAAA,CAAA;AAAA,SACzC;AAAA,OACD,CAAA,CAAA;AAAA,KACH;AAAA,IAEA,gBAAmB,GAAA;AACjB,MAAA,MAAA,CAAO,IAAK,CAAA,IAAA,CAAK,WAAW,CAAA,CAAE,QAAQ,CAAY,QAAA,KAAA;AAChD,QAAAC,+BAAA,CAAoB,KAAK,cAAgB,EAAA,QAAA,EAAU,IAAK,CAAA,WAAA,CAAY,QAAQ,CAAC,CAAA,CAAA;AAC7E,QAAO,OAAA,IAAA,CAAK,YAAY,QAAQ,CAAA,CAAA;AAAA,OACjC,CAAA,CAAA;AAAA,KACH;AAAA,IAEA,eAAkB,GAAA;AAChB,MAAM,MAAA,EAAC,aAAe,EAAA,MAAA,EAAU,GAAA,IAAA,CAAA;AAEhC,MAAA,MAAA,CAAO,IAAK,CAAA,MAAM,CAAE,CAAA,OAAA,CAAQ,CAAQ,IAAA,KAAA;AAClC,QAAA,IAAI,UAAa,GAAA,IAAA,CAAA;AACjB,QAAI,IAAA,aAAA,IAAiB,cAAc,IAAI,CAAA;AAAG,UAAA,UAAA,GAAa,cAAc,IAAI,CAAA,CAAA;AACzE,QAAM,MAAA,SAAA,GAAY,IAAK,CAAA,aAAA,CAAc,UAAU,CAAA,CAAA;AAC/C,QAAA,IAAI,CAAC,SAAA;AAAW,UAAA,OAAA;AAChB,QAAA,MAAM,YAAe,GAAA;AAAA,UACnB,IAAM,EAAA,KAAA;AAAA,SACR,CAAA;AACA,QAAA,MAAM,gBAAgB,MAAO,CAAA,SAAA,CAAU,SAAS,IAAK,CAAA,MAAA,CAAO,IAAI,CAAC,CAAA,CAAA;AACjE,QAAK,IAAA,aAAA,KAAkB,iBAAqB,IAAA,aAAA,KAAkB,gBAAkB,EAAA;AAC9E,UAAA,YAAA,CAAa,IAAO,GAAA,IAAA,CAAA;AAAA,SACtB;AAEA,QAAA,MAAM,OAAU,GAAA,IAAA,CAAK,MAAO,CAAA,IAAA,EAAM,CAAM,EAAA,KAAA;AACtC,UAAU,SAAA,CAAA,IAAA,CAAK,IAAK,CAAA,cAAA,EAAgB,IAAK,CAAA,iBAAA,CAAkB,KAAK,iBAAkB,CAAA,IAAA,EAAM,EAAE,CAAC,CAAC,CAAA,CAAA;AAAA,WAC3F,YAAY,CAAA,CAAA;AAGf,QAAK,IAAA,CAAA,UAAA,CAAW,KAAK,OAAO,CAAA,CAAA;AAAA,OAC7B,CAAA,CAAA;AAAA,KACH;AAAA;AAAA,IAGA,SAAY,GAAA;AACV,MAAA,MAAM,KAAQ,GAAA,CAAC,UAAY,EAAA,SAAA,EAAW,OAAO,CAAA,CAAA;AAE7C,MAAA,KAAA,CAAM,QAAQ,CAAW,OAAA,KAAA;AACvB,QAAI,IAAA,IAAA,CAAK,OAAO,CAAA,KAAM,KAAW,CAAA,EAAA;AAC/B,UAAM,MAAA,SAAA,GAAY,IAAK,CAAA,aAAA,CAAc,OAAO,CAAA,CAAA;AAC5C,UAAA,SAAA,IAAa,SAAU,CAAA,IAAA,CAAK,IAAK,CAAA,cAAA,EAAgB,IAAK,CAAA,iBAAA,CAAkB,IAAK,CAAA,iBAAA,CAAkB,OAAS,EAAA,IAAA,CAAK,OAAO,CAAC,CAAC,CAAC,CAAA,CAAA;AAAA,SACzH;AAAA,OACD,CAAA,CAAA;AAAA,KAGH;AAAA,IAEA,YAAc,GAAA;AACZ,MAAA,MAAM,UAAU,IAAK,CAAA,cAAA,CAAA;AACrB,MAAG,IAAA,OAAA,IAAW,QAAQ,iBAAkB,EAAA;AACtC,QAAA,OAAA,CAAQ,kBAAkB,IAAI,CAAA,CAAA;AAAA,OAChC;AAAA,KACF;AAAA,IACA,kBAAkB,SAAuB,EAAA;AACvC,MAAK,IAAA,CAAA,kBAAA,CAAmB,KAAK,SAAS,CAAA,CAAA;AAAA,KACxC;AAAA,IACA,cAAgB,GAAA;AACd,MAAO,OAAA,IAAA,CAAK,kBAAmB,CAAA,MAAA,GAAS,CAAE,EAAA;AACxC,QAAK,IAAA,CAAA,kBAAA,CAAmB,CAAC,CAAE,EAAA,CAAA;AAC3B,QAAK,IAAA,CAAA,kBAAA,CAAmB,MAAO,CAAA,CAAA,EAAG,CAAC,CAAA,CAAA;AAAA,OACrC;AAAA,KACF;AAAA,IACA,QAAW,GAAA;AACT,MAAA,IAAG,IAAK,CAAA,cAAA,IAAkB,CAAC,IAAA,CAAK,gBAAiB,EAAA;AAC/C,QAAK,IAAA,CAAA,gBAAA,GAAmB,KAAK,cAAe,CAAA,cAAA,CAAA;AAAA,OAC9C;AACA,MAAM,MAAA,GAAA,GAAM,KAAK,iBAAiB,CAAA,IAAK,KAAK,iBAAiB,CAAA,CAAE,IAAK,CAAA,YAAA,EAAc,CAAA,CAAA;AAClF,MAAA,IAAI,OAAO,GAAI,CAAA,IAAA;AAAM,QAAA,GAAA,CAAI,KAAK,CAAC,QAAA,KAAa,IAAK,CAAA,YAAA,CAAa,QAAQ,CAAC,CAAA,CAAA;AAAA;AAClE,QAAA,IAAA,CAAK,aAAa,GAAG,CAAA,CAAA;AAAA,KAC5B;AAAA,IAEA,aAAa,QAAU,EAAA;AACrB,MAAI,IAAA,CAAC,KAAK,cAAkB,IAAA,QAAA;AAAU,QAAA,IAAA,CAAK,cAAiB,GAAA,QAAA,CAAA;AAC5D,MAAA,IAAA,CAAK,cAAe,EAAA,CAAA;AACpB,MAAA,IAAA,CAAK,SAAU,EAAA,CAAA;AACf,MAAA,IAAA,CAAK,eAAgB,EAAA,CAAA;AACrB,MAAA,IAAA,CAAK,KAAM,CAAA,MAAA,EAAQ,IAAK,CAAA,cAAA,EAAgB,IAAI,CAAA,CAAA;AAC5C,MAAA,IAAA,CAAK,UAAU,MAAM;AACnB,QAAA,IAAA,CAAK,cAAe,EAAA,CAAA;AAAA,OACrB,CAAA,CAAA;AACD,MAAA,IAAA,CAAK,SAAY,GAAA,IAAA,CAAA;AAAA,KACnB;AAAA;AAAA,IAGA,aAAgB,GAAA;AACd,MAAA,OAAO,IAAK,CAAA,cAAA,CAAA;AAAA,KACd;AAAA,IACA,gBAAmB,GAAA;AACjB,MAAA,IAAA,CAAK,cAAe,CAAA,MAAA,IAAU,IAAK,CAAA,cAAA,CAAe,OAAO,IAAI,CAAA,CAAA;AAC7D,MAAA,IAAA,CAAK,cAAe,CAAA,KAAA,IAAS,IAAK,CAAA,cAAA,CAAe,KAAM,EAAA,CAAA;AACvD,MAAA,IAAA,CAAK,cAAe,CAAA,MAAA,IAAU,IAAK,CAAA,cAAA,CAAe,OAAO,KAAM,EAAA,CAAA;AAAA,KACjE;AAAA,IACA,UAAU,IAAK,EAAA;AACb,MAAA,IAAG,CAAC,CAAC,IAAK,CAAA,cAAA,IAAkB,CAAC,CAAC,IAAK,CAAA,cAAA,CAAe,IAAQ,IAAA,CAAC,CAAC,IAAA,CAAK,eAAe,IAAK,EAAA;AACnF,QAAA,IAAA,KAAS,QAAQ,IAAK,CAAA,cAAA,CAAe,MAAS,GAAA,IAAA,CAAK,eAAe,IAAK,EAAA,CAAA;AAAA,OACzE;AAAA,KACF;AAAA,IACA,SAAS,KAAM,EAAA;AACb,MAAA,IAAI,IAAK,CAAA,cAAA,IAAkB,IAAK,CAAA,cAAA,CAAe,SAAW,EAAA;AACxD,QAAK,IAAA,CAAA,cAAA,CAAe,UAAU,KAAK,CAAA,CAAA;AAAA,OACrC;AAAA,KACF;AAAA,GACF;AACF,CAAC,CAAA;;;;"}