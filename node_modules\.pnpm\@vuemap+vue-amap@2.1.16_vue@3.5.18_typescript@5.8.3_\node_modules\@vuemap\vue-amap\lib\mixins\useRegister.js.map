{"version": 3, "file": "useRegister.js", "sources": ["../../../mixins/useRegister.ts"], "sourcesContent": ["import {\r\n  inject,\r\n  onMounted,\r\n  onBeforeUnmount,\r\n  onBeforeUpdate,\r\n  onUpdated,\r\n  getCurrentInstance,\r\n  isProxy, toRaw, unref,\r\n  watch,\r\n  nextTick\r\n} from 'vue';\r\nimport {convertEventToLowerCase, eventReg, upperCamelCase, bindInstanceEvent, removeInstanceEvent} from \"../utils\";\r\nimport type {ComponentInternalInstance, WatchStopHandle} from 'vue';\r\n\r\nexport type TRegisterFn = () => void\r\nexport interface IProvideType{\r\n  // 父组件的地图相关实例\r\n  $amapComponent: any\r\n  // // 父组件用来缓存需要执行注册的回调\r\n  // needInitComponents: TRegisterFn[]\r\n  // 当组件初始化的时候如果父组件还未初始化成功，那么需要调用该方法将自身的初始化方法注册到父组件中\r\n  addChildComponent: (cb: TRegisterFn) => void\r\n  // 父组件是否已经销毁\r\n  isDestroy: boolean\r\n  [key : string]: any\r\n}\r\n\r\n\r\ninterface TInitComponentProps {\r\n  // 属性名重定向\r\n  propsRedirect?: Record<string, string>;\r\n  emits: (event: any, ...args: any[]) => void;\r\n  // 转化数据使用\r\n  converts?: Record<string, (val: any) => any>\r\n  // 是否是根节点，只用于map组件初始化使用\r\n  isRoot?: boolean\r\n  // 监听事件使用的方法，默认是读取地图实例的属性的set方法\r\n  watchRedirectFn?: Record<string, (source:  any) => any>\r\n  // 需要初始化的子组件\r\n  needInitComponents?: TRegisterFn[]\r\n  provideData?: IProvideType\r\n  destroyComponent?: () => void\r\n}\r\n\r\nexport const provideKey = 'parentInstance';\r\n\r\nexport const useRegister = <T, D = any>(_init: (options: any, parentComponent: D) => Promise<T>, params: TInitComponentProps) => {\r\n  let componentInstance = getCurrentInstance() as ComponentInternalInstance;\r\n  let {props, attrs} = componentInstance;\r\n  let parentInstance = inject<IProvideType | undefined>(provideKey, undefined);\r\n  const emits = params.emits;\r\n\r\n  let isMounted = false;\r\n  let $amapComponent: T;\r\n  \r\n  onMounted(() => {\r\n    if(parentInstance){\r\n      if(parentInstance.$amapComponent){\r\n        register();\r\n      }else{\r\n        parentInstance.addChildComponent(register);\r\n      }\r\n    }else if(params.isRoot){\r\n      register();\r\n    }\r\n  });\r\n  onBeforeUnmount(() => {\r\n    if(!$amapComponent){\r\n      return;\r\n    }\r\n    unregisterEvents();\r\n    stopWatchers();\r\n    if(params.destroyComponent){\r\n      params.destroyComponent();\r\n    }else{\r\n      destroyComponent();\r\n    }\r\n    if(params.provideData){\r\n      params.provideData.isDestroy = true;\r\n    }\r\n    parentInstance = undefined;\r\n    props = undefined as any;\r\n    attrs = undefined as any;\r\n    componentInstance = undefined as any;\r\n    $amapComponent = undefined as any;\r\n  });\r\n  \r\n  onBeforeUpdate(() => {\r\n    if(props.reEventWhenUpdate && isMounted && $amapComponent){\r\n      unregisterEvents();\r\n    }\r\n  });\r\n  \r\n  onUpdated(() => {\r\n    if(props.reEventWhenUpdate && isMounted && $amapComponent){\r\n      registerEvents();\r\n    }\r\n  });\r\n  \r\n  const register = () => {\r\n    const options = convertProps();\r\n    _init(options, parentInstance?.$amapComponent).then(mapInstance => {\r\n      $amapComponent = mapInstance;\r\n      registerEvents();\r\n      initProps();\r\n      setPropWatchers();\r\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\r\n      // @ts-ignore\r\n      Object.assign(componentInstance.ctx, componentInstance.exposed);\r\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\r\n      // @ts-ignore\r\n      emits('init', $amapComponent, componentInstance.ctx);\r\n      nextTick(() => {\r\n        createChildren();\r\n      }).then();\r\n      isMounted = true;\r\n    });\r\n  };\r\n  \r\n  // 初始化属性\r\n  const initProps = () => {\r\n    const propsList = ['editable', 'visible', 'zooms'];\r\n\r\n    propsList.forEach(propStr => {\r\n      if (props[propStr] !== undefined) {\r\n        const handleFun = getHandlerFun(propStr);\r\n        handleFun && handleFun.call($amapComponent, convertProxyToRaw(convertSignalProp(propStr, props[propStr])));\r\n      }\r\n    });\r\n  };\r\n  \r\n  // 开始处理props数据\r\n  const propsRedirect = params.propsRedirect || {};\r\n  const convertProps = () => {\r\n    const propsCache: Record<string, any> = {};\r\n    if(props.extraOptions){\r\n      Object.assign(propsCache, props.extraOptions);\r\n    }\r\n    Object.keys(props).forEach(_key => {\r\n      let key = _key;\r\n      const propsValue = convertSignalProp(key, props[key]);\r\n      if (propsValue !== undefined){\r\n        if (propsRedirect && propsRedirect[_key]){\r\n          key = propsRedirect[key];\r\n        }\r\n        propsCache[key] = propsValue;\r\n      }\r\n    });\r\n    return propsCache;\r\n  };\r\n  \r\n  const converters = params.converts || {};\r\n  const convertSignalProp = (key: string, sourceData: any) => {\r\n    if (converters && converters[key]) {\r\n      return converters[key].call(this, sourceData);\r\n    }\r\n    return sourceData;\r\n  };\r\n\r\n  const convertProxyToRaw = (value: any) => {\r\n    if(isProxy(value)){\r\n      return toRaw(value);\r\n    }\r\n    return unref(value);\r\n  };\r\n  \r\n  // 结束处理props数据\r\n  \r\n  // 开始监控数据变化\r\n  let unwatchFns: WatchStopHandle[] = [];\r\n  let watchRedirectFn: Record<string, (source:  any) => any> = Object.assign({\r\n    __visible: (flag: boolean) => {\r\n      if(!!$amapComponent && !!$amapComponent['show'] && !!$amapComponent['hide']){\r\n        !flag ? $amapComponent['hide']() : $amapComponent['show']();\r\n      }\r\n    },\r\n    __zIndex (value: number){\r\n      if ($amapComponent && $amapComponent['setzIndex']) {\r\n        $amapComponent['setzIndex'](value);\r\n      }\r\n    }\r\n  }, params.watchRedirectFn || {});\r\n  const setPropWatchers = () => {\r\n\r\n    Object.keys(props).forEach(prop => {\r\n      let handleProp = prop;\r\n      if (propsRedirect && propsRedirect[prop]) handleProp = propsRedirect[prop];\r\n      const handleFun = getHandlerFun(handleProp);\r\n      if (!handleFun) return;\r\n      const watchOptions = {\r\n        deep: false\r\n      };\r\n      const propValueType = Object.prototype.toString.call(props[prop]);\r\n      if ( propValueType === '[object Object]' || propValueType === '[object Array]') {\r\n        watchOptions.deep = true;\r\n      }\r\n      // watch props\r\n      const unwatch = watch(() => props[prop], nv => {\r\n        handleFun.call($amapComponent, convertProxyToRaw(convertSignalProp(prop, nv)));\r\n      }, watchOptions);\r\n\r\n      // collect watchers for destroyed\r\n      unwatchFns.push(unwatch);\r\n    });\r\n  };\r\n  \r\n  const stopWatchers = () => {\r\n    unwatchFns.forEach(fn => fn());\r\n    unwatchFns = [];\r\n    watchRedirectFn = undefined as any;\r\n  };\r\n\r\n  const getHandlerFun = (prop: string) => {\r\n    if (watchRedirectFn[`__${prop}`]) {\r\n      return watchRedirectFn[`__${prop}`];\r\n    }\r\n    if(!$amapComponent){\r\n      return null;\r\n    }\r\n    return $amapComponent[`set${upperCamelCase(prop)}`];\r\n  };\r\n  // 监控数据变化\r\n  \r\n  \r\n  // 开始为地图实例注册事件\r\n  const cacheEvents: Record<string, any> = {};\r\n  const registerEvents = () => {\r\n    Object.keys(attrs).forEach(key => {\r\n      if(eventReg.test(key)){\r\n        const eventKey = convertEventToLowerCase(key);\r\n        bindInstanceEvent($amapComponent, eventKey, attrs[key]);\r\n        cacheEvents[eventKey] = attrs[key];\r\n      }\r\n    });\r\n  };\r\n  const unregisterEvents = () => {\r\n    Object.keys(cacheEvents).forEach(eventKey => {\r\n      removeInstanceEvent($amapComponent, eventKey, cacheEvents[eventKey]);\r\n      delete cacheEvents[eventKey];\r\n    });\r\n  };\r\n  \r\n  // 处理事件\r\n  \r\n  const createChildren = () => {\r\n    const needInitComponents = params.needInitComponents || [];\r\n    while (needInitComponents.length > 0){\r\n      needInitComponents[0]();\r\n      needInitComponents.splice(0, 1);\r\n    }\r\n  };\r\n  \r\n  // 销毁组件时的回调\r\n  const destroyComponent = () => {\r\n    if(!$amapComponent){\r\n      return;\r\n    }\r\n    ($amapComponent as any).setMap && ($amapComponent as any).setMap(null);\r\n    ($amapComponent as any).close && ($amapComponent as any).close();\r\n    ($amapComponent as any).editor && ($amapComponent as any).editor.close();\r\n  };\r\n\r\n  function $$getInstance (): T{\r\n    return $amapComponent;\r\n  }\r\n  return {\r\n    $$getInstance,\r\n    parentInstance,\r\n    isMounted\r\n  };\r\n};\r\n"], "names": ["getCurrentInstance", "inject", "onMounted", "onBeforeUnmount", "onBeforeUpdate", "onUpdated", "nextTick", "isProxy", "toRaw", "unref", "watch", "upperCamelCase", "eventReg", "convertEventToLowerCase", "bindInstanceEvent", "removeInstanceEvent"], "mappings": ";;;;;;;AA4CO,MAAM,UAAa,GAAA,iBAAA;AAEb,MAAA,WAAA,GAAc,CAAa,KAAA,EAAyD,MAAgC,KAAA;AAC/H,EAAA,IAAI,oBAAoBA,sBAAmB,EAAA,CAAA;AAC3C,EAAI,IAAA,EAAC,KAAO,EAAA,KAAA,EAAS,GAAA,iBAAA,CAAA;AACrB,EAAI,IAAA,cAAA,GAAiBC,UAAiC,CAAA,UAAA,EAAY,KAAS,CAAA,CAAA,CAAA;AAC3E,EAAA,MAAM,QAAQ,MAAO,CAAA,KAAA,CAAA;AAErB,EAAA,IAAI,SAAY,GAAA,KAAA,CAAA;AAChB,EAAI,IAAA,cAAA,CAAA;AAEJ,EAAAC,aAAA,CAAU,MAAM;AACd,IAAA,IAAG,cAAe,EAAA;AAChB,MAAA,IAAG,eAAe,cAAe,EAAA;AAC/B,QAAS,QAAA,EAAA,CAAA;AAAA,OACN,MAAA;AACH,QAAA,cAAA,CAAe,kBAAkB,QAAQ,CAAA,CAAA;AAAA,OAC3C;AAAA,KACF,MAAA,IAAS,OAAO,MAAO,EAAA;AACrB,MAAS,QAAA,EAAA,CAAA;AAAA,KACX;AAAA,GACD,CAAA,CAAA;AACD,EAAAC,mBAAA,CAAgB,MAAM;AACpB,IAAA,IAAG,CAAC,cAAe,EAAA;AACjB,MAAA,OAAA;AAAA,KACF;AACA,IAAiB,gBAAA,EAAA,CAAA;AACjB,IAAa,YAAA,EAAA,CAAA;AACb,IAAA,IAAG,OAAO,gBAAiB,EAAA;AACzB,MAAA,MAAA,CAAO,gBAAiB,EAAA,CAAA;AAAA,KACrB,MAAA;AACH,MAAiB,gBAAA,EAAA,CAAA;AAAA,KACnB;AACA,IAAA,IAAG,OAAO,WAAY,EAAA;AACpB,MAAA,MAAA,CAAO,YAAY,SAAY,GAAA,IAAA,CAAA;AAAA,KACjC;AACA,IAAiB,cAAA,GAAA,KAAA,CAAA,CAAA;AACjB,IAAQ,KAAA,GAAA,KAAA,CAAA,CAAA;AACR,IAAQ,KAAA,GAAA,KAAA,CAAA,CAAA;AACR,IAAoB,iBAAA,GAAA,KAAA,CAAA,CAAA;AACpB,IAAiB,cAAA,GAAA,KAAA,CAAA,CAAA;AAAA,GAClB,CAAA,CAAA;AAED,EAAAC,kBAAA,CAAe,MAAM;AACnB,IAAG,IAAA,KAAA,CAAM,iBAAqB,IAAA,SAAA,IAAa,cAAe,EAAA;AACxD,MAAiB,gBAAA,EAAA,CAAA;AAAA,KACnB;AAAA,GACD,CAAA,CAAA;AAED,EAAAC,aAAA,CAAU,MAAM;AACd,IAAG,IAAA,KAAA,CAAM,iBAAqB,IAAA,SAAA,IAAa,cAAe,EAAA;AACxD,MAAe,cAAA,EAAA,CAAA;AAAA,KACjB;AAAA,GACD,CAAA,CAAA;AAED,EAAA,MAAM,WAAW,MAAM;AACrB,IAAA,MAAM,UAAU,YAAa,EAAA,CAAA;AAC7B,IAAA,KAAA,CAAM,OAAS,EAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAgB,cAAc,CAAA,CAAE,KAAK,CAAe,WAAA,KAAA;AACjE,MAAiB,cAAA,GAAA,WAAA,CAAA;AACjB,MAAe,cAAA,EAAA,CAAA;AACf,MAAU,SAAA,EAAA,CAAA;AACV,MAAgB,eAAA,EAAA,CAAA;AAGhB,MAAA,MAAA,CAAO,MAAO,CAAA,iBAAA,CAAkB,GAAK,EAAA,iBAAA,CAAkB,OAAO,CAAA,CAAA;AAG9D,MAAM,KAAA,CAAA,MAAA,EAAQ,cAAgB,EAAA,iBAAA,CAAkB,GAAG,CAAA,CAAA;AACnD,MAAAC,YAAA,CAAS,MAAM;AACb,QAAe,cAAA,EAAA,CAAA;AAAA,OAChB,EAAE,IAAK,EAAA,CAAA;AACR,MAAY,SAAA,GAAA,IAAA,CAAA;AAAA,KACb,CAAA,CAAA;AAAA,GACH,CAAA;AAGA,EAAA,MAAM,YAAY,MAAM;AACtB,IAAA,MAAM,SAAY,GAAA,CAAC,UAAY,EAAA,SAAA,EAAW,OAAO,CAAA,CAAA;AAEjD,IAAA,SAAA,CAAU,QAAQ,CAAW,OAAA,KAAA;AAC3B,MAAI,IAAA,KAAA,CAAM,OAAO,CAAA,KAAM,KAAW,CAAA,EAAA;AAChC,QAAM,MAAA,SAAA,GAAY,cAAc,OAAO,CAAA,CAAA;AACvC,QAAa,SAAA,IAAA,SAAA,CAAU,IAAK,CAAA,cAAA,EAAgB,iBAAkB,CAAA,iBAAA,CAAkB,SAAS,KAAM,CAAA,OAAO,CAAC,CAAC,CAAC,CAAA,CAAA;AAAA,OAC3G;AAAA,KACD,CAAA,CAAA;AAAA,GACH,CAAA;AAGA,EAAM,MAAA,aAAA,GAAgB,MAAO,CAAA,aAAA,IAAiB,EAAC,CAAA;AAC/C,EAAA,MAAM,eAAe,MAAM;AACzB,IAAA,MAAM,aAAkC,EAAC,CAAA;AACzC,IAAA,IAAG,MAAM,YAAa,EAAA;AACpB,MAAO,MAAA,CAAA,MAAA,CAAO,UAAY,EAAA,KAAA,CAAM,YAAY,CAAA,CAAA;AAAA,KAC9C;AACA,IAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAE,CAAA,OAAA,CAAQ,CAAQ,IAAA,KAAA;AACjC,MAAA,IAAI,GAAM,GAAA,IAAA,CAAA;AACV,MAAA,MAAM,UAAa,GAAA,iBAAA,CAAkB,GAAK,EAAA,KAAA,CAAM,GAAG,CAAC,CAAA,CAAA;AACpD,MAAA,IAAI,eAAe,KAAU,CAAA,EAAA;AAC3B,QAAI,IAAA,aAAA,IAAiB,aAAc,CAAA,IAAI,CAAE,EAAA;AACvC,UAAA,GAAA,GAAM,cAAc,GAAG,CAAA,CAAA;AAAA,SACzB;AACA,QAAA,UAAA,CAAW,GAAG,CAAI,GAAA,UAAA,CAAA;AAAA,OACpB;AAAA,KACD,CAAA,CAAA;AACD,IAAO,OAAA,UAAA,CAAA;AAAA,GACT,CAAA;AAEA,EAAM,MAAA,UAAA,GAAa,MAAO,CAAA,QAAA,IAAY,EAAC,CAAA;AACvC,EAAM,MAAA,iBAAA,GAAoB,CAAC,GAAA,EAAa,UAAoB,KAAA;AAC1D,IAAI,IAAA,UAAA,IAAc,UAAW,CAAA,GAAG,CAAG,EAAA;AACjC,MAAA,OAAO,UAAW,CAAA,GAAG,CAAE,CAAA,IAAA,CAAK,QAAM,UAAU,CAAA,CAAA;AAAA,KAC9C;AACA,IAAO,OAAA,UAAA,CAAA;AAAA,GACT,CAAA;AAEA,EAAM,MAAA,iBAAA,GAAoB,CAAC,KAAe,KAAA;AACxC,IAAG,IAAAC,WAAA,CAAQ,KAAK,CAAE,EAAA;AAChB,MAAA,OAAOC,UAAM,KAAK,CAAA,CAAA;AAAA,KACpB;AACA,IAAA,OAAOC,UAAM,KAAK,CAAA,CAAA;AAAA,GACpB,CAAA;AAKA,EAAA,IAAI,aAAgC,EAAC,CAAA;AACrC,EAAI,IAAA,eAAA,GAAyD,OAAO,MAAO,CAAA;AAAA,IACzE,SAAA,EAAW,CAAC,IAAkB,KAAA;AAC5B,MAAA,IAAG,CAAC,CAAC,cAAkB,IAAA,CAAC,CAAC,cAAA,CAAe,MAAM,CAAA,IAAK,CAAC,CAAC,cAAe,CAAA,MAAM,CAAE,EAAA;AAC1E,QAAA,CAAC,OAAO,cAAe,CAAA,MAAM,GAAM,GAAA,cAAA,CAAe,MAAM,CAAE,EAAA,CAAA;AAAA,OAC5D;AAAA,KACF;AAAA,IACA,SAAU,KAAc,EAAA;AACtB,MAAI,IAAA,cAAA,IAAkB,cAAe,CAAA,WAAW,CAAG,EAAA;AACjD,QAAe,cAAA,CAAA,WAAW,EAAE,KAAK,CAAA,CAAA;AAAA,OACnC;AAAA,KACF;AAAA,GACC,EAAA,MAAA,CAAO,eAAmB,IAAA,EAAE,CAAA,CAAA;AAC/B,EAAA,MAAM,kBAAkB,MAAM;AAE5B,IAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAE,CAAA,OAAA,CAAQ,CAAQ,IAAA,KAAA;AACjC,MAAA,IAAI,UAAa,GAAA,IAAA,CAAA;AACjB,MAAI,IAAA,aAAA,IAAiB,cAAc,IAAI,CAAA;AAAG,QAAA,UAAA,GAAa,cAAc,IAAI,CAAA,CAAA;AACzE,MAAM,MAAA,SAAA,GAAY,cAAc,UAAU,CAAA,CAAA;AAC1C,MAAA,IAAI,CAAC,SAAA;AAAW,QAAA,OAAA;AAChB,MAAA,MAAM,YAAe,GAAA;AAAA,QACnB,IAAM,EAAA,KAAA;AAAA,OACR,CAAA;AACA,MAAA,MAAM,gBAAgB,MAAO,CAAA,SAAA,CAAU,SAAS,IAAK,CAAA,KAAA,CAAM,IAAI,CAAC,CAAA,CAAA;AAChE,MAAK,IAAA,aAAA,KAAkB,iBAAqB,IAAA,aAAA,KAAkB,gBAAkB,EAAA;AAC9E,QAAA,YAAA,CAAa,IAAO,GAAA,IAAA,CAAA;AAAA,OACtB;AAEA,MAAA,MAAM,UAAUC,SAAM,CAAA,MAAM,KAAM,CAAA,IAAI,GAAG,CAAM,EAAA,KAAA;AAC7C,QAAA,SAAA,CAAU,KAAK,cAAgB,EAAA,iBAAA,CAAkB,kBAAkB,IAAM,EAAA,EAAE,CAAC,CAAC,CAAA,CAAA;AAAA,SAC5E,YAAY,CAAA,CAAA;AAGf,MAAA,UAAA,CAAW,KAAK,OAAO,CAAA,CAAA;AAAA,KACxB,CAAA,CAAA;AAAA,GACH,CAAA;AAEA,EAAA,MAAM,eAAe,MAAM;AACzB,IAAW,UAAA,CAAA,OAAA,CAAQ,CAAM,EAAA,KAAA,EAAA,EAAI,CAAA,CAAA;AAC7B,IAAA,UAAA,GAAa,EAAC,CAAA;AACd,IAAkB,eAAA,GAAA,KAAA,CAAA,CAAA;AAAA,GACpB,CAAA;AAEA,EAAM,MAAA,aAAA,GAAgB,CAAC,IAAiB,KAAA;AACtC,IAAA,IAAI,eAAgB,CAAA,CAAA,EAAA,EAAK,IAAI,CAAA,CAAE,CAAG,EAAA;AAChC,MAAO,OAAA,eAAA,CAAgB,CAAK,EAAA,EAAA,IAAI,CAAE,CAAA,CAAA,CAAA;AAAA,KACpC;AACA,IAAA,IAAG,CAAC,cAAe,EAAA;AACjB,MAAO,OAAA,IAAA,CAAA;AAAA,KACT;AACA,IAAA,OAAO,cAAe,CAAA,CAAA,GAAA,EAAMC,mBAAe,CAAA,IAAI,CAAC,CAAE,CAAA,CAAA,CAAA;AAAA,GACpD,CAAA;AAKA,EAAA,MAAM,cAAmC,EAAC,CAAA;AAC1C,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAE,CAAA,OAAA,CAAQ,CAAO,GAAA,KAAA;AAChC,MAAG,IAAAC,aAAA,CAAS,IAAK,CAAA,GAAG,CAAE,EAAA;AACpB,QAAM,MAAA,QAAA,GAAWC,6BAAwB,GAAG,CAAA,CAAA;AAC5C,QAAAC,6BAAA,CAAkB,cAAgB,EAAA,QAAA,EAAU,KAAM,CAAA,GAAG,CAAC,CAAA,CAAA;AACtD,QAAY,WAAA,CAAA,QAAQ,CAAI,GAAA,KAAA,CAAM,GAAG,CAAA,CAAA;AAAA,OACnC;AAAA,KACD,CAAA,CAAA;AAAA,GACH,CAAA;AACA,EAAA,MAAM,mBAAmB,MAAM;AAC7B,IAAA,MAAA,CAAO,IAAK,CAAA,WAAW,CAAE,CAAA,OAAA,CAAQ,CAAY,QAAA,KAAA;AAC3C,MAAAC,+BAAA,CAAoB,cAAgB,EAAA,QAAA,EAAU,WAAY,CAAA,QAAQ,CAAC,CAAA,CAAA;AACnE,MAAA,OAAO,YAAY,QAAQ,CAAA,CAAA;AAAA,KAC5B,CAAA,CAAA;AAAA,GACH,CAAA;AAIA,EAAA,MAAM,iBAAiB,MAAM;AAC3B,IAAM,MAAA,kBAAA,GAAqB,MAAO,CAAA,kBAAA,IAAsB,EAAC,CAAA;AACzD,IAAO,OAAA,kBAAA,CAAmB,SAAS,CAAE,EAAA;AACnC,MAAA,kBAAA,CAAmB,CAAC,CAAE,EAAA,CAAA;AACtB,MAAmB,kBAAA,CAAA,MAAA,CAAO,GAAG,CAAC,CAAA,CAAA;AAAA,KAChC;AAAA,GACF,CAAA;AAGA,EAAA,MAAM,mBAAmB,MAAM;AAC7B,IAAA,IAAG,CAAC,cAAe,EAAA;AACjB,MAAA,OAAA;AAAA,KACF;AACA,IAAC,cAAuB,CAAA,MAAA,IAAW,cAAuB,CAAA,MAAA,CAAO,IAAI,CAAA,CAAA;AACrE,IAAC,cAAA,CAAuB,KAAU,IAAA,cAAA,CAAuB,KAAM,EAAA,CAAA;AAC/D,IAAC,cAAuB,CAAA,MAAA,IAAW,cAAuB,CAAA,MAAA,CAAO,KAAM,EAAA,CAAA;AAAA,GACzE,CAAA;AAEA,EAAA,SAAS,aAAmB,GAAA;AAC1B,IAAO,OAAA,cAAA,CAAA;AAAA,GACT;AACA,EAAO,OAAA;AAAA,IACL,aAAA;AAAA,IACA,cAAA;AAAA,IACA,SAAA;AAAA,GACF,CAAA;AACF;;;;;"}