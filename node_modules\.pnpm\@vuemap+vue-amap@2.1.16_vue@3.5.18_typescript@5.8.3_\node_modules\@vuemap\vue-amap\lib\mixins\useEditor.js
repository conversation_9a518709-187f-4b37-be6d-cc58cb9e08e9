'use strict';

require('../utils/index.js');
var util = require('../utils/util.js');
var eventHelper = require('../utils/eventHelper.js');

function useEditor(editor, attrs) {
  if (!editor)
    return;
  const filters = ["addnode", "adjust", "removenode", "end", "move", "add"];
  const filterSet = {};
  Object.keys(attrs).forEach((key) => {
    if (util.eventReg.test(key)) {
      const eventKey = util.convertEventToLowerCase(key);
      if (filters.indexOf(eventKey) !== -1)
        filterSet[eventKey] = attrs[key];
    }
  });
  Object.keys(filterSet).forEach((key) => {
    eventHelper.bindInstanceEvent(editor, key, filterSet[key]);
  });
}

exports.useEditor = useEditor;
//# sourceMappingURL=useEditor.js.map
