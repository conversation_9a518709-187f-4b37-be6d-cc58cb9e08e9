export declare const propsTypes: {
    autoMove: {
        type: BooleanConstructor;
        default: boolean;
    };
    showRectangle: {
        type: BooleanConstructor;
        default: boolean;
    };
    showButton: {
        type: BooleanConstructor;
        default: boolean;
    };
    isOpen: {
        type: BooleanConstructor;
        default: boolean;
    };
    mapStyle: {
        type: StringConstructor;
    };
    layers: {
        type: ArrayConstructor;
    };
    width: {
        type: StringConstructor;
    };
    height: {
        type: StringConstructor;
    };
    offset: {
        type: ArrayConstructor;
    };
    borderStyle: {
        type: StringConstructor;
    };
    borderColor: {
        type: StringConstructor;
    };
    borderRadius: {
        type: StringConstructor;
    };
    borderWidth: {
        type: StringConstructor;
    };
    buttonSize: {
        type: StringConstructor;
    };
} & {
    visible: import("../../../utils/buildHelper").IPropOptions<boolean>;
    zIndex: import("../../../utils/buildHelper").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils/buildHelper").IPropOptions<boolean>;
    extraOptions: import("../../../utils/buildHelper").IPropOptions<any>;
};
