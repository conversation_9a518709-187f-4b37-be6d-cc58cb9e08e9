/// <reference types="@vuemap/amap-jsapi-types" />
declare const _default: import("vue").DefineComponent<{
    position: {
        type: (StringConstructor | ObjectConstructor)[];
    };
    offset: {
        type: ArrayConstructor;
    };
    showControlButton: {
        type: BooleanConstructor;
        default: boolean;
    };
} & {
    visible: import("../../../utils/buildHelper").IPropOptions<boolean>;
    zIndex: import("../../../utils/buildHelper").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils/buildHelper").IPropOptions<boolean>;
    extraOptions: import("../../../utils/buildHelper").IPropOptions<any>;
}, {
    emits: (event: "init", ...args: any[]) => void;
    $amapComponent: AMap.ControlBar;
    $$getInstance: () => AMap.ControlBar;
    parentInstance: import("../../../mixins").IProvideType | undefined;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], "init", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    position: {
        type: (StringConstructor | ObjectConstructor)[];
    };
    offset: {
        type: ArrayConstructor;
    };
    showControlButton: {
        type: BooleanConstructor;
        default: boolean;
    };
} & {
    visible: import("../../../utils/buildHelper").IPropOptions<boolean>;
    zIndex: import("../../../utils/buildHelper").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils/buildHelper").IPropOptions<boolean>;
    extraOptions: import("../../../utils/buildHelper").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
}, {
    showControlButton: boolean;
}, {}>;
export default _default;
