{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/switch/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Switch from './src/switch.vue'\n\nexport const ElSwitch = withInstall(Switch)\nexport default ElSwitch\n\nexport * from './src/switch'\n"], "names": ["withInstall", "Switch"], "mappings": ";;;;;;;;;AAEY,MAAC,QAAQ,GAAGA,mBAAW,CAACC,oBAAM;;;;;;;"}