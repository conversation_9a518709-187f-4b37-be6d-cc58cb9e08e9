/// <reference types="@vuemap/amap-loca-types" />
import AmbientLight from './AmbientLight.vue';
import type { Plugin } from "vue";
export declare const ElAmapLocaAmbientLight: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        color: {
            type: StringConstructor;
        };
        intensity: {
            type: NumberConstructor;
        };
    } & {
        visible: import("@vuemap/vue-amap").IPropOptions<boolean>;
        zIndex: import("@vuemap/vue-amap").IPropOptions<number>;
        reEventWhenUpdate: import("@vuemap/vue-amap").IPropOptions<boolean>;
        extraOptions: import("@vuemap/vue-amap").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {
        emits: (event: "init", ...args: any[]) => void;
        $amapComponent: any;
        $$getInstance: () => Loca.AmbientLight;
        parentInstance: import("@vuemap/vue-amap").IProvideType | undefined;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        color: {
            type: StringConstructor;
        };
        intensity: {
            type: NumberConstructor;
        };
    } & {
        visible: import("@vuemap/vue-amap").IPropOptions<boolean>;
        zIndex: import("@vuemap/vue-amap").IPropOptions<number>;
        reEventWhenUpdate: import("@vuemap/vue-amap").IPropOptions<boolean>;
        extraOptions: import("@vuemap/vue-amap").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {}, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        color: {
            type: StringConstructor;
        };
        intensity: {
            type: NumberConstructor;
        };
    } & {
        visible: import("@vuemap/vue-amap").IPropOptions<boolean>;
        zIndex: import("@vuemap/vue-amap").IPropOptions<number>;
        reEventWhenUpdate: import("@vuemap/vue-amap").IPropOptions<boolean>;
        extraOptions: import("@vuemap/vue-amap").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {
        emits: (event: "init", ...args: any[]) => void;
        $amapComponent: any;
        $$getInstance: () => Loca.AmbientLight;
        parentInstance: import("@vuemap/vue-amap").IProvideType | undefined;
    }, {}, {}, {}, {}>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    color: {
        type: StringConstructor;
    };
    intensity: {
        type: NumberConstructor;
    };
} & {
    visible: import("@vuemap/vue-amap").IPropOptions<boolean>;
    zIndex: import("@vuemap/vue-amap").IPropOptions<number>;
    reEventWhenUpdate: import("@vuemap/vue-amap").IPropOptions<boolean>;
    extraOptions: import("@vuemap/vue-amap").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
}, {
    emits: (event: "init", ...args: any[]) => void;
    $amapComponent: any;
    $$getInstance: () => Loca.AmbientLight;
    parentInstance: import("@vuemap/vue-amap").IProvideType | undefined;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], "init", {}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Plugin<any[]>;
export default ElAmapLocaAmbientLight;
export declare type ElAmapLocaAmbientLightInstance = InstanceType<typeof AmbientLight>;
