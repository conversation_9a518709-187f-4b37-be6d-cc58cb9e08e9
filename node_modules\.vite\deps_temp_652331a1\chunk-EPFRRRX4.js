import {
  debounce_default,
  merge_default
} from "./chunk-RHQEB4B4.js";
import {
  Teleport,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  defineComponent,
  getCurrentInstance,
  inject,
  isProxy,
  nextTick,
  onBeforeUnmount,
  onBeforeUpdate,
  onMounted,
  onUnmounted,
  onUpdated,
  openBlock,
  provide,
  ref,
  renderSlot,
  toRaw,
  unref,
  useAttrs,
  useSlots,
  vShow,
  watch,
  withDirectives
} from "./chunk-YHJVOVJ5.js";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/utils/make-installer.mjs
var makeInstaller = (components = []) => {
  const apps = [];
  const install2 = (app) => {
    if (apps.includes(app))
      return;
    apps.push(app);
    components.forEach((c) => app.use(c));
  };
  return {
    install: install2
  };
};

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/utils/guid.mjs
function guid() {
  const s = [];
  const hexDigits = "0123456789abcdef";
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.charAt(Math.floor(Math.random() * 16));
  }
  s[8] = s[13] = s[18] = s[23] = "-";
  return s.join("");
}

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/utils/util.mjs
function isMapInstance(instance) {
  if (!instance) {
    return false;
  }
  return instance instanceof AMap.Map;
}
function isOverlayGroupInstance(instance) {
  if (!instance) {
    return false;
  }
  return instance instanceof AMap.OverlayGroup;
}
function isIndoorMapInstance(instance) {
  if (!instance) {
    return false;
  }
  return instance instanceof AMap.IndoorMap;
}
function isLabelsLayerInstance(instance) {
  if (!instance) {
    return false;
  }
  return instance instanceof AMap.LabelsLayer;
}
function isVectorLayerInstance(instance) {
  if (!instance) {
    return false;
  }
  return instance instanceof AMap.VectorLayer;
}
function convertEventToLowerCase(functionName) {
  if (!functionName || functionName.length < 4) {
    return functionName;
  }
  const func = functionName.substring(3, functionName.length);
  const firstLetter = functionName[2].toLowerCase();
  return firstLetter + func;
}
var eventReg = /^on[A-Z]+/;
function loadScript(url, callback) {
  if (!url) {
    throw new Error("请传入url");
  }
  const script48 = document.createElement("script");
  script48.type = "text/javascript";
  script48.async = true;
  script48.defer = true;
  script48.src = url;
  document.body.appendChild(script48);
  if (callback) {
    script48.addEventListener("load", () => {
      callback();
    });
  }
}
function convertLnglat(lnglat) {
  if (Array.isArray(lnglat)) {
    return lnglat.map(convertLnglat);
  }
  return lnglat.toArray();
}
function upperCamelCase(prop) {
  if (!prop) {
    return prop;
  }
  return prop.charAt(0).toUpperCase() + prop.slice(1);
}

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/utils/eventHelper.mjs
function bindInstanceEvent(instance, eventName, handler) {
  if (!instance || !instance.on) {
    return;
  }
  instance.on(eventName, handler);
}
function removeInstanceEvent(instance, eventName, handler) {
  if (!instance || !instance.off) {
    return;
  }
  instance.off(eventName, handler);
}

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/utils/convert-helper.mjs
function toPixel(arr) {
  return new AMap.Pixel(arr[0], arr[1]);
}
function toSize(arr) {
  return new AMap.Size(arr[0], arr[1]);
}
function pixelTo(pixel) {
  if (Array.isArray(pixel))
    return pixel;
  return [pixel.getX(), pixel.getY()];
}
function toLngLat(arr) {
  return new AMap.LngLat(arr[0], arr[1]);
}
function lngLatTo(lngLat) {
  if (!lngLat)
    return;
  if (Array.isArray(lngLat))
    return lngLat.slice();
  return [lngLat.getLng(), lngLat.getLat()];
}
function toBounds(arrs) {
  return new AMap.Bounds(toLngLat(arrs[0]), toLngLat(arrs[1]));
}

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/utils/GPSUtil.mjs
var pi = 3.141592653589793;
var a = 6378245;
var ee = 0.006693421622965943;
var x_pi = pi * 3e3 / 180;
function lonLatToTileNumbers(lon_deg, lat_deg, zoom) {
  const lat_rad = pi / 180 * lat_deg;
  const n2 = Math.pow(2, zoom);
  const xtile = Math.floor((lon_deg + 180) / 360 * n2);
  const ytile = Math.floor((1 - Math.asinh(Math.tan(lat_rad)) / pi) / 2 * n2);
  return [xtile, ytile];
}
function tileNumbersToLonLat(xtile, ytile, zoom) {
  const n2 = Math.pow(2, zoom);
  const lon_deg = xtile / n2 * 360 - 180;
  const lat_rad = Math.atan(Math.sinh(pi * (1 - 2 * ytile / n2)));
  const lat_deg = lat_rad * 180 / pi;
  return [lon_deg, lat_deg];
}
function bd09_To_gps84(lng, lat) {
  const gcj02 = bd09_To_gcj02(lng, lat);
  const map84 = gcj02_To_gps84(gcj02.lng, gcj02.lat);
  return map84;
}
function gps84_To_bd09(lng, lat) {
  const gcj02 = gps84_To_gcj02(lng, lat);
  const bd09 = gcj02_To_bd09(gcj02.lng, gcj02.lat);
  return bd09;
}
function gps84_To_gcj02(lng, lat) {
  let dLat = transformLat(lng - 105, lat - 35);
  let dLng = transformLng(lng - 105, lat - 35);
  const radLat = lat / 180 * pi;
  let magic = Math.sin(radLat);
  magic = 1 - ee * magic * magic;
  const sqrtMagic = Math.sqrt(magic);
  dLat = dLat * 180 / (a * (1 - ee) / (magic * sqrtMagic) * pi);
  dLng = dLng * 180 / (a / sqrtMagic * Math.cos(radLat) * pi);
  const mgLat = lat + dLat;
  const mgLng = lng + dLng;
  const newCoord = {
    lng: mgLng,
    lat: mgLat
  };
  return newCoord;
}
function gcj02_To_gps84(lng, lat) {
  const coord = transform(lng, lat);
  const lontitude = lng * 2 - coord.lng;
  const latitude = lat * 2 - coord.lat;
  const newCoord = {
    lng: lontitude,
    lat: latitude
  };
  return newCoord;
}
function gcj02_To_bd09(x, y) {
  const z2 = Math.sqrt(x * x + y * y) + 2e-5 * Math.sin(y * x_pi);
  const theta = Math.atan2(y, x) + 3e-6 * Math.cos(x * x_pi);
  const bd_lng = z2 * Math.cos(theta) + 65e-4;
  const bd_lat = z2 * Math.sin(theta) + 6e-3;
  const newCoord = {
    lng: bd_lng,
    lat: bd_lat
  };
  return newCoord;
}
function bd09_To_gcj02(bd_lng, bd_lat) {
  const x = bd_lng - 65e-4;
  const y = bd_lat - 6e-3;
  const z2 = Math.sqrt(x * x + y * y) - 2e-5 * Math.sin(y * x_pi);
  const theta = Math.atan2(y, x) - 3e-6 * Math.cos(x * x_pi);
  const gg_lng = z2 * Math.cos(theta);
  const gg_lat = z2 * Math.sin(theta);
  const newCoord = {
    lng: gg_lng,
    lat: gg_lat
  };
  return newCoord;
}
function transform(lng, lat) {
  let dLat = transformLat(lng - 105, lat - 35);
  let dLng = transformLng(lng - 105, lat - 35);
  const radLat = lat / 180 * pi;
  let magic = Math.sin(radLat);
  magic = 1 - ee * magic * magic;
  const sqrtMagic = Math.sqrt(magic);
  dLat = dLat * 180 / (a * (1 - ee) / (magic * sqrtMagic) * pi);
  dLng = dLng * 180 / (a / sqrtMagic * Math.cos(radLat) * pi);
  const mgLat = lat + dLat;
  const mgLng = lng + dLng;
  const newCoord = {
    lng: mgLng,
    lat: mgLat
  };
  return newCoord;
}
function transformLat(x, y) {
  let ret = -100 + 2 * x + 3 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
  ret += (20 * Math.sin(6 * x * pi) + 20 * Math.sin(2 * x * pi)) * 2 / 3;
  ret += (20 * Math.sin(y * pi) + 40 * Math.sin(y / 3 * pi)) * 2 / 3;
  ret += (160 * Math.sin(y / 12 * pi) + 320 * Math.sin(y * pi / 30)) * 2 / 3;
  return ret;
}
function transformLng(x, y) {
  let ret = 300 + x + 2 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
  ret += (20 * Math.sin(6 * x * pi) + 20 * Math.sin(2 * x * pi)) * 2 / 3;
  ret += (20 * Math.sin(x * pi) + 40 * Math.sin(x / 3 * pi)) * 2 / 3;
  ret += (150 * Math.sin(x / 12 * pi) + 300 * Math.sin(x / 30 * pi)) * 2 / 3;
  return ret;
}

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/utils/buildHelper.mjs
var commonProps = {
  visible: {
    type: Boolean,
    default: true
  },
  zIndex: {
    type: Number
  },
  reEventWhenUpdate: {
    type: Boolean,
    default: false
  },
  extraOptions: {
    type: Object
  }
};
var buildProps = (props) => {
  return Object.assign({}, commonProps, props);
};

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/mixins/register-component.mjs
var registerComponent = defineComponent({
  inject: {
    parentInstance: {
      default: null
    }
  },
  inheritAttrs: false,
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    // 是否显示，默认 true
    zIndex: {
      type: Number
    },
    reEventWhenUpdate: {
      type: Boolean,
      default: false
    },
    // 是否在组件更新时重新注册事件，主要用于数组更新时，绑定了事件但事件的对象不会更新问题
    extraOptions: {
      type: Object
    }
    // 额外扩展属性
  },
  emits: ["init"],
  data() {
    return {
      needInitComponents: [],
      unwatchFns: [],
      propsRedirect: {},
      converters: {},
      isDestroy: false,
      cacheEvents: {},
      isMounted: false
    };
  },
  created() {
    this.$amapComponent = null;
    this.$parentComponent = null;
  },
  mounted() {
    if (this.parentInstance) {
      if (this.parentInstance.$amapComponent) {
        this.register();
      } else {
        this.parentInstance.addChildComponent(this.register);
      }
    }
  },
  beforeUnmount() {
    if (!this.$amapComponent)
      return;
    this.unregisterEvents();
    this.unwatchFns.forEach((item) => item());
    this.unwatchFns = [];
    this.destroyComponent();
    this.isDestroy = true;
  },
  beforeUpdate() {
    if (this.reEventWhenUpdate && this.isMounted && this.$amapComponent) {
      this.unregisterEvents();
    }
  },
  updated() {
    if (this.reEventWhenUpdate && this.isMounted && this.$amapComponent) {
      this.registerEvents();
    }
  },
  methods: {
    getHandlerFun(prop) {
      if (this[`__${prop}`]) {
        return this[`__${prop}`];
      }
      if (!this.$amapComponent) {
        return null;
      }
      return this.$amapComponent[`set${upperCamelCase(prop)}`];
    },
    convertProps() {
      const props = {};
      const { $props, propsRedirect } = this;
      if (this.extraOptions) {
        Object.assign(props, this.extraOptions);
      }
      const result = Object.keys($props).reduce((res, _key) => {
        let key = _key;
        const propsValue = this.convertSignalProp(key, $props[key]);
        if (propsValue === void 0)
          return res;
        if (propsRedirect && propsRedirect[_key])
          key = propsRedirect[key];
        props[key] = propsValue;
        return res;
      }, props);
      Object.keys(result).forEach((key) => {
        result[key] = this.convertProxyToRaw(result[key]);
      });
      return result;
    },
    convertProxyToRaw(value) {
      if (isProxy(value)) {
        return toRaw(value);
      }
      return unref(value);
    },
    convertSignalProp(key, sourceData) {
      if (this.converters && this.converters[key]) {
        return this.converters[key].call(this, sourceData);
      }
      return sourceData;
    },
    registerEvents() {
      const $props = this.$attrs;
      Object.keys($props).forEach((key) => {
        if (eventReg.test(key)) {
          const eventKey = convertEventToLowerCase(key);
          bindInstanceEvent(this.$amapComponent, eventKey, $props[key]);
          this.cacheEvents[eventKey] = $props[key];
        }
      });
    },
    unregisterEvents() {
      Object.keys(this.cacheEvents).forEach((eventKey) => {
        removeInstanceEvent(this.$amapComponent, eventKey, this.cacheEvents[eventKey]);
        delete this.cacheEvents[eventKey];
      });
    },
    setPropWatchers() {
      const { propsRedirect, $props } = this;
      Object.keys($props).forEach((prop) => {
        let handleProp = prop;
        if (propsRedirect && propsRedirect[prop])
          handleProp = propsRedirect[prop];
        const handleFun = this.getHandlerFun(handleProp);
        if (!handleFun)
          return;
        const watchOptions = {
          deep: false
        };
        const propValueType = Object.prototype.toString.call($props[prop]);
        if (propValueType === "[object Object]" || propValueType === "[object Array]") {
          watchOptions.deep = true;
        }
        const unwatch = this.$watch(prop, (nv) => {
          handleFun.call(this.$amapComponent, this.convertProxyToRaw(this.convertSignalProp(prop, nv)));
        }, watchOptions);
        this.unwatchFns.push(unwatch);
      });
    },
    // some prop can not init by initial created methods
    initProps() {
      const props = ["editable", "visible", "zooms"];
      props.forEach((propStr) => {
        if (this[propStr] !== void 0) {
          const handleFun = this.getHandlerFun(propStr);
          handleFun && handleFun.call(this.$amapComponent, this.convertProxyToRaw(this.convertSignalProp(propStr, this[propStr])));
        }
      });
    },
    lazyRegister() {
      const $parent = this.parentInstance;
      if ($parent && $parent.addChildComponent) {
        $parent.addChildComponent(this);
      }
    },
    addChildComponent(component) {
      this.needInitComponents.push(component);
    },
    createChildren() {
      while (this.needInitComponents.length > 0) {
        this.needInitComponents[0]();
        this.needInitComponents.splice(0, 1);
      }
    },
    register() {
      if (this.parentInstance && !this.$parentComponent) {
        this.$parentComponent = this.parentInstance.$amapComponent;
      }
      const res = this["__initComponent"] && this["__initComponent"](this.convertProps());
      if (res && res.then)
        res.then((instance) => this.registerRest(instance));
      else
        this.registerRest(res);
    },
    registerRest(instance) {
      if (!this.$amapComponent && instance)
        this.$amapComponent = instance;
      this.registerEvents();
      this.initProps();
      this.setPropWatchers();
      this.$emit("init", this.$amapComponent, this);
      this.$nextTick(() => {
        this.createChildren();
      });
      this.isMounted = true;
    },
    // helper method
    $$getInstance() {
      return this.$amapComponent;
    },
    destroyComponent() {
      this.$amapComponent.setMap && this.$amapComponent.setMap(null);
      this.$amapComponent.close && this.$amapComponent.close();
      this.$amapComponent.editor && this.$amapComponent.editor.close();
    },
    __visible(flag) {
      if (!!this.$amapComponent && !!this.$amapComponent.show && !!this.$amapComponent.hide) {
        flag === false ? this.$amapComponent.hide() : this.$amapComponent.show();
      }
    },
    __zIndex(value) {
      if (this.$amapComponent && this.$amapComponent.setzIndex) {
        this.$amapComponent.setzIndex(value);
      }
    }
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/mixins/useRegister.mjs
var provideKey = "parentInstance";
var useRegister = (_init, params) => {
  let componentInstance = getCurrentInstance();
  let { props, attrs } = componentInstance;
  let parentInstance = inject(provideKey, void 0);
  const emits = params.emits;
  let isMounted = false;
  let $amapComponent;
  onMounted(() => {
    if (parentInstance) {
      if (parentInstance.$amapComponent) {
        register();
      } else {
        parentInstance.addChildComponent(register);
      }
    } else if (params.isRoot) {
      register();
    }
  });
  onBeforeUnmount(() => {
    if (!$amapComponent) {
      return;
    }
    unregisterEvents();
    stopWatchers();
    if (params.destroyComponent) {
      params.destroyComponent();
    } else {
      destroyComponent();
    }
    if (params.provideData) {
      params.provideData.isDestroy = true;
    }
    parentInstance = void 0;
    props = void 0;
    attrs = void 0;
    componentInstance = void 0;
    $amapComponent = void 0;
  });
  onBeforeUpdate(() => {
    if (props.reEventWhenUpdate && isMounted && $amapComponent) {
      unregisterEvents();
    }
  });
  onUpdated(() => {
    if (props.reEventWhenUpdate && isMounted && $amapComponent) {
      registerEvents();
    }
  });
  const register = () => {
    const options = convertProps();
    _init(options, parentInstance == null ? void 0 : parentInstance.$amapComponent).then((mapInstance) => {
      $amapComponent = mapInstance;
      registerEvents();
      initProps();
      setPropWatchers();
      Object.assign(componentInstance.ctx, componentInstance.exposed);
      emits("init", $amapComponent, componentInstance.ctx);
      nextTick(() => {
        createChildren();
      }).then();
      isMounted = true;
    });
  };
  const initProps = () => {
    const propsList = ["editable", "visible", "zooms"];
    propsList.forEach((propStr) => {
      if (props[propStr] !== void 0) {
        const handleFun = getHandlerFun(propStr);
        handleFun && handleFun.call($amapComponent, convertProxyToRaw(convertSignalProp(propStr, props[propStr])));
      }
    });
  };
  const propsRedirect = params.propsRedirect || {};
  const convertProps = () => {
    const propsCache = {};
    if (props.extraOptions) {
      Object.assign(propsCache, props.extraOptions);
    }
    Object.keys(props).forEach((_key) => {
      let key = _key;
      const propsValue = convertSignalProp(key, props[key]);
      if (propsValue !== void 0) {
        if (propsRedirect && propsRedirect[_key]) {
          key = propsRedirect[key];
        }
        propsCache[key] = propsValue;
      }
    });
    return propsCache;
  };
  const converters = params.converts || {};
  const convertSignalProp = (key, sourceData) => {
    if (converters && converters[key]) {
      return converters[key].call(void 0, sourceData);
    }
    return sourceData;
  };
  const convertProxyToRaw = (value) => {
    if (isProxy(value)) {
      return toRaw(value);
    }
    return unref(value);
  };
  let unwatchFns = [];
  let watchRedirectFn = Object.assign({
    __visible: (flag) => {
      if (!!$amapComponent && !!$amapComponent["show"] && !!$amapComponent["hide"]) {
        !flag ? $amapComponent["hide"]() : $amapComponent["show"]();
      }
    },
    __zIndex(value) {
      if ($amapComponent && $amapComponent["setzIndex"]) {
        $amapComponent["setzIndex"](value);
      }
    }
  }, params.watchRedirectFn || {});
  const setPropWatchers = () => {
    Object.keys(props).forEach((prop) => {
      let handleProp = prop;
      if (propsRedirect && propsRedirect[prop])
        handleProp = propsRedirect[prop];
      const handleFun = getHandlerFun(handleProp);
      if (!handleFun)
        return;
      const watchOptions = {
        deep: false
      };
      const propValueType = Object.prototype.toString.call(props[prop]);
      if (propValueType === "[object Object]" || propValueType === "[object Array]") {
        watchOptions.deep = true;
      }
      const unwatch = watch(() => props[prop], (nv) => {
        handleFun.call($amapComponent, convertProxyToRaw(convertSignalProp(prop, nv)));
      }, watchOptions);
      unwatchFns.push(unwatch);
    });
  };
  const stopWatchers = () => {
    unwatchFns.forEach((fn2) => fn2());
    unwatchFns = [];
    watchRedirectFn = void 0;
  };
  const getHandlerFun = (prop) => {
    if (watchRedirectFn[`__${prop}`]) {
      return watchRedirectFn[`__${prop}`];
    }
    if (!$amapComponent) {
      return null;
    }
    return $amapComponent[`set${upperCamelCase(prop)}`];
  };
  const cacheEvents = {};
  const registerEvents = () => {
    Object.keys(attrs).forEach((key) => {
      if (eventReg.test(key)) {
        const eventKey = convertEventToLowerCase(key);
        bindInstanceEvent($amapComponent, eventKey, attrs[key]);
        cacheEvents[eventKey] = attrs[key];
      }
    });
  };
  const unregisterEvents = () => {
    Object.keys(cacheEvents).forEach((eventKey) => {
      removeInstanceEvent($amapComponent, eventKey, cacheEvents[eventKey]);
      delete cacheEvents[eventKey];
    });
  };
  const createChildren = () => {
    const needInitComponents = params.needInitComponents || [];
    while (needInitComponents.length > 0) {
      needInitComponents[0]();
      needInitComponents.splice(0, 1);
    }
  };
  const destroyComponent = () => {
    if (!$amapComponent) {
      return;
    }
    $amapComponent.setMap && $amapComponent.setMap(null);
    $amapComponent.close && $amapComponent.close();
    $amapComponent.editor && $amapComponent.editor.close();
  };
  function $$getInstance() {
    return $amapComponent;
  }
  return {
    $$getInstance,
    parentInstance,
    isMounted
  };
};

// node_modules/.pnpm/@vuemap+amap-jsapi-loader@1.0.4/node_modules/@vuemap/amap-jsapi-loader/dist/index.mjs
var d;
var e = d || (d = {});
e.notload = "notload";
e.loading = "loading";
e.loaded = "loaded";
e.failed = "failed";
var g = { key: "", AMap: { version: "1.4.15", plugins: [] }, AMapUI: { version: "1.1", plugins: [] }, Loca: { version: "1.3.2" } };
var m = { AMap: d.notload, AMapUI: d.notload, Loca: d.notload };
var n = { AMap: [], AMapUI: [], Loca: [] };
var p = [];
function q(a2) {
  "function" == typeof a2 && (m.AMap === d.loaded ? a2(window.AMap) : p.push(a2));
}
function r(a2) {
  let h = [];
  a2.AMapUI && h.push(t(a2.AMapUI));
  a2.Loca && h.push(u(a2.Loca));
  return Promise.all(h);
}
function t(a2) {
  return new Promise((h, b2) => {
    let f = [];
    if (a2.plugins)
      for (var c = 0; c < a2.plugins.length; c += 1)
        -1 == g.AMapUI.plugins.indexOf(a2.plugins[c]) && f.push(a2.plugins[c]);
    if (m.AMapUI === d.failed)
      b2("前次请求 AMapUI 失败");
    else if (m.AMapUI === d.notload) {
      m.AMapUI = d.loading;
      g.AMapUI.version = a2.version || g.AMapUI.version;
      c = g.AMapUI.version;
      let k2 = document.body || document.head, l = document.createElement("script");
      l.type = "text/javascript";
      l.src = `https://webapi.amap.com/ui/${c}/main.js`;
      l.onerror = () => {
        m.AMapUI = d.failed;
        b2("请求 AMapUI 失败");
      };
      l.onload = () => {
        m.AMapUI = d.loaded;
        if (f.length)
          window.AMapUI.loadUI(f, function() {
            for (let a3 = 0, b3 = f.length; a3 < b3; a3++) {
              let b4 = f[a3].split("/").slice(-1)[0];
              window.AMapUI[b4] = arguments[a3];
            }
            for (h(); n.AMapUI.length; )
              n.AMapUI.splice(0, 1)[0]();
          });
        else
          for (h(); n.AMapUI.length; )
            n.AMapUI.splice(0, 1)[0]();
      };
      k2.appendChild(l);
    } else
      m.AMapUI === d.loaded ? a2.version && a2.version !== g.AMapUI.version ? b2("不允许多个版本 AMapUI 混用") : f.length ? window.AMapUI.loadUI(f, function() {
        for (let a3 = 0, b3 = f.length; a3 < b3; a3++) {
          let b4 = f[a3].split("/").slice(-1)[0];
          window.AMapUI[b4] = arguments[a3];
        }
        h();
      }) : h() : a2.version && a2.version !== g.AMapUI.version ? b2("不允许多个版本 AMapUI 混用") : n.AMapUI.push((a3) => {
        a3 ? b2(a3) : f.length ? window.AMapUI.loadUI(f, function() {
          for (let a4 = 0, b3 = f.length; a4 < b3; a4++) {
            let b4 = f[a4].split("/").slice(-1)[0];
            window.AMapUI[b4] = arguments[a4];
          }
          h();
        }) : h();
      });
  });
}
function u(a2) {
  return new Promise((h, b2) => {
    if (m.Loca === d.failed)
      b2("前次请求 Loca 失败");
    else if (m.Loca === d.notload) {
      m.Loca = d.loading;
      g.Loca.version = a2.version || g.Loca.version;
      let l = g.Loca.version;
      var f = g.AMap.version.startsWith("2"), c = l.startsWith("2");
      if (f && !c || !f && c)
        b2("JSAPI 与 Loca 版本不对应！！");
      else {
        f = g.key;
        c = document.body || document.head;
        var k2 = document.createElement("script");
        k2.type = "text/javascript";
        k2.src = `https://webapi.amap.com/loca?v=${l}&key=${f}`;
        k2.onerror = () => {
          m.Loca = d.failed;
          b2("请求 AMapUI 失败");
        };
        k2.onload = () => {
          m.Loca = d.loaded;
          for (h(); n.Loca.length; )
            n.Loca.splice(0, 1)[0]();
        };
        c.appendChild(k2);
      }
    } else
      m.Loca === d.loaded ? a2.version && a2.version !== g.Loca.version ? b2("不允许多个版本 Loca 混用") : h() : a2.version && a2.version !== g.Loca.version ? b2("不允许多个版本 Loca 混用") : n.Loca.push((a3) => {
        a3 ? b2(a3) : b2();
      });
  });
}
var dist_default = { load: function(a2) {
  if ("undefined" === typeof window)
    throw Error("AMap JSAPI can only be used in Browser.");
  return new Promise((h, b2) => {
    if (m.AMap == d.failed)
      b2("");
    else if (m.AMap == d.notload) {
      let { key: l, version: k3, plugins: v2 } = a2;
      if (l) {
        window.AMap && "lbs.amap.com" !== location.host && b2("禁止多种API加载方式混用");
        g.key = l;
        g.AMap.version = k3 || g.AMap.version;
        g.AMap.plugins = v2 || g.AMap.plugins;
        m.AMap = d.loading;
        var f = document.body || document.head;
        window.___onAPILoaded = function(c2) {
          delete window.___onAPILoaded;
          if (c2)
            m.AMap = d.failed, b2(c2);
          else
            for (m.AMap = d.loaded, r(a2).then(() => {
              h(window.AMap);
            }).catch(b2); p.length; )
              p.splice(0, 1)[0]();
        };
        var c = document.createElement("script");
        c.type = "text/javascript";
        c.src = "https://webapi.amap.com/maps?callback=___onAPILoaded&v=" + g.AMap.version + "&key=" + l + "&plugin=" + g.AMap.plugins.join(",");
        c.onerror = (a3) => {
          m.AMap = d.failed;
          b2(a3);
        };
        f.appendChild(c);
      } else
        b2("请填写key");
    } else if (m.AMap == d.loaded)
      if (a2.key && a2.key !== g.key)
        b2("多个不一致的 key");
      else if (a2.version && a2.version !== g.AMap.version)
        b2("不允许多个版本 JSAPI 混用");
      else {
        f = [];
        if (a2.plugins)
          for (c = 0; c < a2.plugins.length; c += 1)
            -1 == g.AMap.plugins.indexOf(a2.plugins[c]) && f.push(a2.plugins[c]);
        f.length ? window.AMap.plugin(f, () => {
          r(a2).then(() => {
            h(window.AMap);
          }).catch(b2);
        }) : r(a2).then(() => {
          h(window.AMap);
        }).catch(b2);
      }
    else if (a2.key && a2.key !== g.key)
      b2("多个不一致的 key");
    else if (a2.version && a2.version !== g.AMap.version)
      b2("不允许多个版本 JSAPI 混用");
    else {
      var k2 = [];
      if (a2.plugins)
        for (c = 0; c < a2.plugins.length; c += 1)
          -1 == g.AMap.plugins.indexOf(a2.plugins[c]) && k2.push(a2.plugins[c]);
      q(() => {
        k2.length ? window.AMap.plugin(k2, () => {
          r(a2).then(() => {
            h(window.AMap);
          }).catch(b2);
        }) : r(a2).then(() => {
          h(window.AMap);
        }).catch(b2);
      });
    }
  });
}, reset: function() {
  delete window.AMap;
  delete window.AMapUI;
  delete window.Loca;
  g = { key: "", AMap: { version: "1.4.15", plugins: [] }, AMapUI: { version: "1.1", plugins: [] }, Loca: { version: "1.3.2" } };
  m = { AMap: d.notload, AMapUI: d.notload, Loca: d.notload };
  n = {
    AMap: [],
    AMapUI: [],
    Loca: []
  };
} };

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/services/amap-api-loader.mjs
var DEFAULT_AMP_CONFIG = {
  "key": "",
  // 申请好的Web端开发者Key，首次调用 load 时必填
  "version": "2.0",
  // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15
  "plugins": [],
  // 需要使用的的插件列表，如比例尺'AMap.Scale'等
  // 'Loca': { // 是否加载 Loca， 缺省不加载
  //   'version': '2.0.0' // Loca 版本，缺省 1.3.2
  // },
  serviceHost: "",
  securityJsCode: ""
};
function AMapAPILoader(config = {}) {
  config = merge_default({}, DEFAULT_AMP_CONFIG, config);
  if (config.serviceHost) {
    window._AMapSecurityConfig = {
      serviceHost: config.serviceHost
    };
  } else if (config.securityJsCode) {
    window._AMapSecurityConfig = {
      securityJsCode: config.securityJsCode
    };
  }
  return dist_default.load(config);
}
var resetJsApi = dist_default.reset;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/services/injected-amap-api-instance.mjs
var lazyAMapApiLoaderInstance = null;
var initAMapApiLoader = (config) => {
  if (lazyAMapApiLoaderInstance)
    return;
  if (!lazyAMapApiLoaderInstance) {
    if (config.offline) {
      lazyAMapApiLoaderInstance = new Promise((resolve) => {
        console.log("@vuemap/vue-amap离线部署");
        resolve(window.AMap);
      });
    } else {
      lazyAMapApiLoaderInstance = AMapAPILoader(config);
    }
  }
  lazyAMapApiLoaderInstance.then();
};

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/amap/props.mjs
var propsType = buildProps({
  vid: {
    type: String
  },
  // 地图ID
  center: {
    type: Array
  },
  // 初始中心经纬度
  zoom: {
    type: Number
  },
  // 地图显示的缩放级别，可以设置为浮点数；若center与level未赋值，地图初始化默认显示用户所在城市范围。
  rotation: {
    type: Number
  },
  // 地图顺时针旋转角度，取值范围 [0-360] ，默认值：0
  pitch: {
    type: Number
  },
  // 俯仰角度，默认 0，最大值根据地图当前 zoom 级别不断增大，2D地图下无效 。
  viewMode: {
    type: String
  },
  // 地图视图模式, 默认为‘2D’，可选’3D’，选择‘3D’会显示 3D 地图效果。
  features: {
    type: Array
  },
  // 设置地图上显示的元素种类, 支持'bg'（地图背景）、'point'（POI点）、'road'（道路）、'building'（建筑物），默认值：['bg','point','road','building']
  layers: {
    type: Array
  },
  // 地图图层数组，数组可以是图层 中的一个或多个，默认为普通二维地图。 当叠加多个 图层 时，普通二维地图需通过实例化一个TileLayer类实现。 如果你希望创建一个默认底图图层，使用 AMap.createDefaultLayer()
  zooms: {
    type: Array
  },
  // 图显示的缩放级别范围, 默认为 [2, 20] ，取值范围 [2 ~ 30]
  resizeEnable: {
    type: Boolean,
    default: true
  },
  // 是否监控地图容器尺寸变化，默认值为false。此属性可被 setStatus/getStatus 方法控制
  dragEnable: {
    type: Boolean,
    default: true
  },
  // 地图是否可通过鼠标拖拽平移, 默认为 true。此属性可被 setStatus/getStatus 方法控制
  zoomEnable: {
    type: Boolean,
    default: true
  },
  // 地图是否可缩放，默认值为 true。此属性可被 setStatus/getStatus 方法控制
  jogEnable: {
    type: Boolean,
    default: true
  },
  // 地图是否使用缓动效果，默认值为true。此属性可被setStatus/getStatus 方法控制
  pitchEnable: {
    type: Boolean,
    default: true
  },
  // 是否允许设置俯仰角度, 3D 视图下为 true, 2D 视图下无效。。此属性可被setStatus/getStatus 方法控制
  rotateEnable: {
    type: Boolean,
    default: true
  },
  // 地图是否可旋转, 图默认为true。此属性可被setStatus/getStatus 方法控制
  animateEnable: {
    type: Boolean,
    default: true
  },
  // 地图平移过程中是否使用动画（如调用panBy、panTo、setCenter、setZoomAndCenter等函数, 将对地图产生平移操作, 是否使用动画平移的效果）, 默认为true, 即使用动画
  keyboardEnable: {
    type: Boolean,
    default: true
  },
  // 地图是否可通过键盘控制, 默认为true, 方向键控制地图平移，"+"和"-"可以控制地图的缩放, Ctrl+“→”顺时针旋转，Ctrl+“←”逆时针旋转。此属性可被setStatus/getStatus 方法控制
  doubleClickZoom: {
    type: Boolean,
    default: true
  },
  // 地图是否可通过双击鼠标放大地图, 默认为true。此属性可被setStatus/getStatus 方法控制
  scrollWheel: {
    type: Boolean,
    default: true
  },
  // 地图是否可通过鼠标滚轮缩放浏览，默认为true。此属性可被setStatus/getStatus 方法控制
  touchZoom: {
    type: Boolean,
    default: true
  },
  // 地图在移动终端上是否可通过多点触控缩放浏览地图，默认为true。关闭手势缩放地图，请设置为false。
  touchZoomCenter: {
    type: Number
  },
  // 可缺省，当touchZoomCenter=1的时候，手机端双指缩放的以地图中心为中心，否则默认以双指中间点为中心。默认：1
  showLabel: {
    type: Boolean,
    default: true
  },
  // 是否展示地图文字和 POI 信息。默认 true
  defaultCursor: {
    type: String
  },
  // 地图默认鼠标样式。参数 defaultCursor 应符合 CSS 的 cursor 属性规范。
  isHotspot: {
    type: Boolean
  },
  // 是否开启地图热点和标注的 hover 效果。PC端默认是true, 移动端默认是 false。
  mapStyle: {
    type: String
  },
  // 设置地图的显示样式，目前支持两种地图样式： 第一种：自定义地图样式，如 "amap://styles/d6bf8c1d69cea9f5c696185ad4ac4c86" 可前往地图自定义平台定制自己的个性地图样式； 第二种：官方样式模版,如"amap://styles/grey"。 其他模版样式及自定义地图的使用说明见开发指南
  wallColor: {
    type: [String, Array]
  },
  // 地图楼块的侧面颜色
  roofColor: {
    type: [String, Array]
  },
  // 地图楼块的顶面颜色
  showBuildingBlock: {
    type: Boolean,
    default: true
  },
  // 是否展示地图 3D 楼块，默认 true
  showIndoorMap: {
    type: Boolean,
    default: false
  },
  // 是否自动展示室内地图，默认是 false
  skyColor: {
    type: [String, Array]
  },
  // 天空颜色，3D 模式下带有俯仰角时会显示
  labelRejectMask: {
    type: Boolean,
    default: false
  },
  // 文字是否拒绝掩模图层进行掩模
  mask: {
    type: Array
  },
  // 为 Map 实例指定掩模的路径，各图层将只显示路径范围内图像，3D视图下有效。 格式为一个经纬度的一维、二维或三维数组。
  WebGLParams: {
    type: Object
  },
  // 额外配置的WebGL参数 eg: preserveDrawingBuffer
  terrain: {
    type: Boolean,
    default: false
  }
  //是否开启地形，默认不开启
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/amap/amap.vue2.mjs
var _hoisted_1 = { class: "el-vue-amap-container" };
var _hoisted_2 = ["id"];
var script = defineComponent({
  ...{
    name: "ElAmap",
    inheritAttrs: false
  },
  __name: "amap",
  props: propsType,
  emits: ["init", "update:zoom", "update:center", "update:rotation", "update:pitch"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const needInitComponents = [];
    const provideData = {
      $amapComponent: void 0,
      addChildComponent(cb) {
        needInitComponents.push(cb);
      },
      isDestroy: false
    };
    provide(provideKey, provideData);
    const props = __props;
    const emits = __emit;
    const mapDomId = ref(props.vid || guid());
    let $amapComponent;
    const { $$getInstance } = useRegister((options) => {
      return new Promise((resolve, reject) => {
        if (!lazyAMapApiLoaderInstance) {
          reject(new Error("请初始化initAMapApiLoader"));
          return;
        }
        lazyAMapApiLoaderInstance.then(() => {
          nextTick(() => {
            $amapComponent = new AMap.Map(mapDomId.value, options);
            provideData.$amapComponent = $amapComponent;
            bindModelEvents();
            resolve($amapComponent);
          });
        }).catch((e2) => {
          reject(e2);
        });
      });
    }, {
      isRoot: true,
      emits,
      needInitComponents,
      provideData,
      watchRedirectFn: {
        __dragEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ dragEnable: flag });
          }
        },
        __zoomEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ zoomEnable: flag });
          }
        },
        __jogEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ jogEnable: flag });
          }
        },
        __keyboardEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ keyboardEnable: flag });
          }
        },
        __doubleClickZoom(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ doubleClickZoom: flag });
          }
        },
        __scrollWheel(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ scrollWheel: flag });
          }
        },
        __rotateEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ rotateEnable: flag });
          }
        },
        __pitchEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ pitchEnable: flag });
          }
        },
        __resizeEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ resizeEnable: flag });
          }
        },
        __showIndoorMap(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ showIndoorMap: flag });
          }
        }
      }
    });
    const bindModelEvents = () => {
      $amapComponent.on("zoomchange", () => {
        emits("update:zoom", $amapComponent.getZoom());
      });
      $amapComponent.on("rotateend", () => {
        emits("update:rotation", $amapComponent.getRotation());
      });
      $amapComponent.on("dragging", () => {
        emits("update:center", getCenter());
      });
      $amapComponent.on("dragend", () => {
        emits("update:pitch", $amapComponent.getPitch());
      });
      $amapComponent.on("touchmove", () => {
        emits("update:center", getCenter());
      });
    };
    const getCenter = () => {
      const center = $amapComponent.getCenter();
      return [center.lng, center.lat];
    };
    onBeforeUnmount(() => {
      if ($amapComponent) {
        $amapComponent.clearEvents();
        $amapComponent.destroy();
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_1, [
        createBaseVNode("div", {
          id: mapDomId.value,
          class: "el-vue-amap"
        }, null, 8, _hoisted_2),
        renderSlot(_ctx.$slots, "default")
      ]);
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/amap/amap.vue.mjs
script.__file = "src/vue-amap/packages/amap/amap.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/amap/index.mjs
script.install = (app) => {
  app.component(script.name, script);
  return app;
};
var ElAmap = script;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/ControlBar/ControlBar.vue2.mjs
var script2 = defineComponent({
  ...{
    name: "ElAmapControlControlBar",
    inheritAttrs: false
  },
  __name: "ControlBar",
  props: buildProps({
    // 控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角
    position: {
      type: [String, Object]
    },
    // 相对于地图容器左上角的偏移量，正数代表向右下偏移。默认为AMap.Pixel(10,10)
    offset: {
      type: Array
    },
    // 是否显示倾斜、旋转按钮。默认为 true
    showControlButton: {
      type: Boolean,
      default: true
    }
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.ControlBar"], () => {
          $amapComponent = new AMap.ControlBar(options);
          parentComponent.addControl($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeControl($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/ControlBar/ControlBar.vue.mjs
script2.__file = "src/vue-amap/packages/control/ControlBar/ControlBar.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/ControlBar/index.mjs
script2.install = (app) => {
  app.component(script2.name, script2);
  return app;
};
var ElAmapControlControlBar = script2;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/HawkEye/props.mjs
var propsTypes = buildProps({
  autoMove: {
    type: Boolean,
    default: true
  },
  // 是否随主图视口变化移动
  showRectangle: {
    type: Boolean,
    default: true
  },
  // 是否显示视口矩形
  showButton: {
    type: Boolean,
    default: true
  },
  // 是否显示打开关闭的按钮
  isOpen: {
    type: Boolean,
    default: true
  },
  // 默认是否展开
  mapStyle: {
    type: String
  },
  // 缩略图要显示的地图自定义样式，如'amap://styles/dark'
  layers: {
    type: Array
  },
  // 缩略图要显示的图层类型，默认为普通矢量地图
  width: {
    type: String
  },
  // 缩略图的宽度，同CSS，如'200px'
  height: {
    type: String
  },
  // 缩略图的高度，同CSS，如'200px'
  offset: {
    type: Array
  },
  // 缩略图距离地图右下角的像素距离，如 [2,2]
  borderStyle: {
    type: String
  },
  // 缩略图的边框样式，同CSS，如"double solid solid double"
  borderColor: {
    type: String
  },
  // 缩略图的边框颜色，同CSS，如'silver'
  borderRadius: {
    type: String
  },
  // 缩略图的边框角度，同CSS，如'5px'
  borderWidth: {
    type: String
  },
  // 缩略图的边框宽度，同CSS，如'2px'
  buttonSize: {
    type: String
  }
  // 箭头按钮的像素尺寸，同CSS，如'12px'
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/HawkEye/HawkEye.vue2.mjs
var script3 = defineComponent({
  ...{
    name: "ElAmapControlHawkEye",
    inheritAttrs: false
  },
  __name: "HawkEye",
  props: propsTypes,
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.HawkEye"], () => {
          $amapComponent = new AMap.HawkEye(options);
          parentComponent.addControl($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      watchRedirectFn: {
        __isOpen(flag) {
          !flag ? $amapComponent.close() : $amapComponent.open();
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeControl($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return null;
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/HawkEye/HawkEye.vue.mjs
script3.__file = "src/vue-amap/packages/control/HawkEye/HawkEye.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/HawkEye/index.mjs
script3.install = (app) => {
  app.component(script3.name, script3);
  return app;
};
var ElAmapControlHawkEye = script3;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/MapType/MapType.vue2.mjs
var script4 = defineComponent({
  ...{
    name: "ElAmapControlMapType",
    inheritAttrs: false
  },
  __name: "MapType",
  props: buildProps({
    defaultType: {
      type: Number
    },
    // 初始化默认图层类型。 取值为0：默认底图 取值为1：卫星图 默认值：0
    showTraffic: {
      type: Boolean,
      default: false
    },
    // 叠加实时交通图层 默认值：false
    showRoad: {
      type: Boolean,
      default: false
    }
    // 叠加路网图层 默认值：false
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.MapType"], () => {
          $amapComponent = new AMap.MapType(options);
          parentComponent.addControl($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeControl($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/MapType/MapType.vue.mjs
script4.__file = "src/vue-amap/packages/control/MapType/MapType.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/MapType/index.mjs
script4.install = (app) => {
  app.component(script4.name, script4);
  return app;
};
var ElAmapControlMapType = script4;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/Scale/Scale.vue2.mjs
var script5 = defineComponent({
  ...{
    name: "ElAmapControlScale",
    inheritAttrs: false
  },
  __name: "Scale",
  props: buildProps({
    position: {
      type: [String, Object]
    },
    // 控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角
    offset: {
      type: Array
    }
    // 相对于地图容器左上角的偏移量，正数代表向右下偏移。默认为AMap.Pixel(10,10)
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.Scale"], () => {
          $amapComponent = new AMap.Scale(options);
          parentComponent.addControl($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeControl($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/Scale/Scale.vue.mjs
script5.__file = "src/vue-amap/packages/control/Scale/Scale.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/Scale/index.mjs
script5.install = (app) => {
  app.component(script5.name, script5);
  return app;
};
var ElAmapControlScale = script5;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/ToolBar/ToolBar.vue2.mjs
var script6 = defineComponent({
  ...{
    name: "ElAmapControlToolBar",
    inheritAttrs: false
  },
  __name: "ToolBar",
  props: buildProps({
    position: {
      type: [String, Object]
    },
    // 控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角
    offset: {
      type: Array
    }
    // 相对于地图容器左上角的偏移量，正数代表向右下偏移。默认为AMap.Pixel(10,10)
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.ToolBar"], () => {
          $amapComponent = new AMap.ToolBar(options);
          parentComponent.addControl($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeControl($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/ToolBar/ToolBar.vue.mjs
script6.__file = "src/vue-amap/packages/control/ToolBar/ToolBar.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/ToolBar/index.mjs
script6.install = (app) => {
  app.component(script6.name, script6);
  return app;
};
var ElAmapControlToolBar = script6;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/SearchBox/props.mjs
var propsTypes2 = buildProps({
  type: {
    type: String
  },
  //输入提示时限定POI类型，多个类型用“|”分隔，目前只支持Poi类型编码如“050000” 默认值：所有类别
  city: {
    type: String
  },
  //输入提示时限定城市。可选值：城市名（中文或中文全拼）、citycode、adcode；默认值：“全国”
  datatype: {
    type: String
  },
  //返回的数据类型。可选值：all-返回所有数据类型、poi-返回POI数据类型、bus-返回公交站点数据类型、busline-返回公交线路数据类型目前暂时不支持多种类型
  citylimit: {
    type: Boolean,
    default: false
  },
  //返回的数据类型。可选值：all-返回所有数据类型、poi-返回POI数据类型、bus-返回公交站点数据类型、busline-返回公交线路数据类型目前暂时不支持多种类型
  inputId: {
    type: String
  },
  //输入框的ID
  inputCustom: {
    type: Boolean,
    default: false
  },
  //是否自定义input，自定义的时候将使用用户的inputId
  outputId: {
    type: String
  },
  //可选参数，指定一个现有的div的id或者元素，作为展示提示结果的容器，当指定了input的时候有效，缺省的时候将自动创建一个显示结果面板
  outPutDirAuto: {
    type: Boolean,
    default: true
  },
  //默认为true，表示是否在input位于页面较下方的时候自动将输入面板显示在input上方以避免被遮挡
  closeResultOnScroll: {
    type: Boolean,
    default: true
  },
  //页面滚动时关闭搜索结果列表，默认 true
  lang: {
    type: String
  },
  //设置检索语言类型，默认中文 'zh_cn'
  placeholder: {
    type: String
  },
  debounce: {
    type: Number,
    default: 100
  }
  // 手动复写增加防抖
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/SearchBox/SearchBox.vue2.mjs
var _hoisted_12 = { class: "el-vue-search-box-container" };
var _hoisted_22 = ["id", "placeholder"];
var script7 = defineComponent({
  ...{
    name: "ElAmapSearchBox",
    inheritAttrs: false
  },
  __name: "SearchBox",
  props: propsTypes2,
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const saveInputId = ref(guid());
    const saveVisible = ref(true);
    let $amapComponent;
    let preInput;
    const { $$getInstance } = useRegister((options, parentComponent) => {
      if (options.inputId) {
        saveInputId.value = options.inputId;
        delete options.inputId;
      }
      if (options.visible) {
        saveVisible.value = options.visible;
      }
      options.input = saveInputId.value;
      if (options.outputId) {
        options.output = options.outputId;
        delete options.outputId;
      }
      let _inputTimer;
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.AutoComplete"], () => {
          const debounce = props.debounce;
          preInput = AMap.Autocomplete.prototype.onInPut;
          AMap.Autocomplete.prototype.onInPut = function() {
            clearTimeout(_inputTimer);
            _inputTimer = setTimeout(() => {
              this.output && this.autoSearch();
            }, debounce);
          };
          $amapComponent = new AMap.AutoComplete(options);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      watchRedirectFn: {
        __visible(flag) {
          saveVisible.value = flag;
        },
        __citylimit(flag) {
          $amapComponent.setCityLimit(flag);
        }
      },
      destroyComponent() {
        if (preInput && AMap.Autocomplete) {
          AMap.Autocomplete.prototype.onInPut = preInput;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return withDirectives((openBlock(), createElementBlock(
        "div",
        _hoisted_12,
        [
          !_ctx.inputCustom ? (openBlock(), createElementBlock("input", {
            key: 0,
            id: saveInputId.value,
            type: "text",
            placeholder: _ctx.placeholder
          }, null, 8, _hoisted_22)) : createCommentVNode("v-if", true)
        ],
        512
        /* NEED_PATCH */
      )), [
        [vShow, _ctx.visible && !_ctx.inputCustom]
      ]);
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/SearchBox/SearchBox.vue.mjs
script7.__file = "src/vue-amap/packages/control/SearchBox/SearchBox.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/SearchBox/index.mjs
script7.install = (app) => {
  app.component(script7.name, script7);
  return app;
};
var ElAmapSearchBox = script7;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/infoWindow/InfoWindow/props.mjs
var propsTypes3 = buildProps({
  isCustom: {
    type: Boolean,
    default: false
  },
  // 是否自定义窗体。设为true时，信息窗体外框及内容完全按照content所设的值添加（默认为false，即在系统默认的信息窗体外框中显示content内容）
  autoMove: {
    type: Boolean,
    default: true
  },
  // 是否自动调整窗体到视野内（当信息窗体超出视野范围时，通过该属性设置是否自动平移地图，使信息窗体完全显示）
  avoid: {
    type: Array
  },
  // autoMove 为 true 时，自动平移到视野内后的上右下左的避让宽度。默认值： [20, 20, 20, 20]
  closeWhenClickMap: {
    type: Boolean,
    default: false
  },
  // 点标记显示位置偏移量，默认值为 [0,0] 。Marker指定position后，默认以marker左上角位置为基准点（若设置了anchor，则以anchor设置位置为基准点），对准所给定的position位置，若需使marker指定位置对准在position处，需根据marker的尺寸设置一定的偏移量。
  content: {
    type: [String, Object]
  },
  // 显示内容，可以是HTML要素字符串或者HTMLElement对象
  size: {
    type: Array
  },
  // 信息窗体尺寸（isCustom为true时，该属性无效）
  anchor: {
    type: String
  },
  // 信息窗体锚点。默认值：'bottom-center'。可选值：'top-left'|'top-center'|'top-right'|'middle-left'|'center'|'middle-right'|'bottom-left'|'bottom-center'|'bottom-right'
  offset: {
    type: Array
  },
  // 信息窗体显示位置偏移量。默认基准点为信息窗体的底部中心。默认值: [0, 0]
  position: {
    type: Object
  }
  // 信息窗体显示基点位置
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/infoWindow/InfoWindow/InfoWindow.vue2.mjs
var _hoisted_13 = { style: { "display": "none" } };
var script8 = defineComponent({
  ...{
    name: "ElAmapInfoWindow",
    inheritAttrs: false
  },
  __name: "InfoWindow",
  props: propsTypes3,
  emits: ["init", "update:visible"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const needTeleport = !props.content;
    const tempId = `info-${guid()}`;
    const divId = ref("");
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        if (!options.content) {
          options.content = `<div id="${tempId}"></div>`;
        }
        $amapComponent = new AMap.InfoWindow(options);
        $amapComponent.on("close", () => {
          emits("update:visible", false);
        });
        if (props.visible) {
          $amapComponent.open(parentComponent, props.position);
          if (needTeleport) {
            divId.value = tempId;
            nextTick(() => {
              $amapComponent.setAnchor($amapComponent.getAnchor());
            });
          }
        }
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __position(position) {
          if (props.visible) {
            $amapComponent.open(parentInstance == null ? void 0 : parentInstance.$amapComponent, position);
            if (needTeleport) {
              divId.value = tempId;
            }
          } else {
            $amapComponent.setPosition(position);
          }
        },
        __visible(flag) {
          const position = $amapComponent.getPosition();
          if (position) {
            if (!flag) {
              $amapComponent.close();
            } else {
              $amapComponent.open(parentInstance == null ? void 0 : parentInstance.$amapComponent, [position.lng, position.lat]);
              if (needTeleport) {
                divId.value = tempId;
                nextTick(() => {
                  $amapComponent.setAnchor($amapComponent.getAnchor());
                });
              }
            }
          }
        }
      },
      destroyComponent() {
        if ($amapComponent) {
          if ($amapComponent.getIsOpen()) {
            $amapComponent.close();
          }
          $amapComponent = null;
        }
      }
    });
    onUnmounted(() => {
      if ($amapComponent) {
        $amapComponent.close();
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_13, [
        !!divId.value ? (openBlock(), createBlock(Teleport, {
          key: 0,
          to: "#" + divId.value
        }, [
          renderSlot(_ctx.$slots, "default")
        ], 8, ["to"])) : createCommentVNode("v-if", true)
      ]);
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/infoWindow/InfoWindow/InfoWindow.vue.mjs
script8.__file = "src/vue-amap/packages/infoWindow/InfoWindow/InfoWindow.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/infoWindow/InfoWindow/index.mjs
script8.install = (app) => {
  app.component(script8.name, script8);
  return app;
};
var ElAmapInfoWindow = script8;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Canvas/Canvas.vue2.mjs
var script9 = defineComponent({
  ...{
    name: "ElAmapLayerCanvas",
    inheritAttrs: false
  },
  __name: "Canvas",
  props: buildProps({
    canvas: {
      required: true,
      type: Object
    },
    // Canvas DOM 对象
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    bounds: {
      type: [Array, Object]
    },
    // 图片的范围大小经纬度，如果传递数字数组类型: [minlng,minlat,maxlng,maxlat] 或 AMap.Bounds
    opacity: {
      type: Number
    }
    // 透明度，默认 1
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.CanvasLayer(options);
        parentComponent.addLayer($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeLayer($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Canvas/Canvas.vue.mjs
script9.__file = "src/vue-amap/packages/layer/data/Canvas/Canvas.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Canvas/index.mjs
script9.install = (app) => {
  app.component(script9.name, script9);
  return app;
};
var ElAmapLayerCanvas = script9;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Custom/Custom.vue2.mjs
var script10 = defineComponent({
  ...{
    name: "ElAmapLayerCustom",
    inheritAttrs: false
  },
  __name: "Custom",
  props: buildProps({
    canvas: {
      required: true,
      type: Object
    },
    // canvas 对象
    render: {
      type: Function
    },
    // 绘制函数，初始化完成时候，开发者需要给该图层设定render方法，该方法需要实现图层的绘制，API会在合适的时机自动调用该方法
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2, 20]
    opacity: {
      type: Number
    },
    // 透明度，默认 1
    alwaysRender: {
      type: Boolean,
      default: false
    }
    // 是否主动
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        const canvas = options.canvas;
        delete options.canvas;
        $amapComponent = new AMap.CustomLayer(canvas, options);
        $amapComponent.setMap(parentComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          $amapComponent.setMap(null);
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Custom/Custom.vue.mjs
script10.__file = "src/vue-amap/packages/layer/data/Custom/Custom.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Custom/index.mjs
script10.install = (app) => {
  app.component(script10.name, script10);
  return app;
};
var ElAmapLayerCustom = script10;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Flexible/Flexible.vue2.mjs
var script11 = defineComponent({
  ...{
    name: "ElAmapLayerFlexible",
    inheritAttrs: false
  },
  __name: "Flexible",
  props: buildProps({
    cacheSize: {
      type: Number
    },
    // 缓存瓦片数量
    createTile: {
      type: Function
    },
    // 由开发者实现，由API自动调用，xyz分别为切片横向纵向编号和层级，切片大小 256。假设每次创建的贴片为A(支持img或者canvas)，当创建或者获取成功时请回调success(A)，不需要显示或者失败时请回调fail()
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Number
    },
    // 热力图透明度区间数组，取值范围 [0,1] ，0表示完全透明，1表示不透明，默认： [0,1]
    tileSize: {
      type: Number
    }
    // 切片大小，取值： 256  128  64。默认值为256
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.TileLayer.Flexible(options);
        $amapComponent.setMap(parentComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          $amapComponent.destroy();
          $amapComponent.setMap(null);
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Flexible/Flexible.vue.mjs
script11.__file = "src/vue-amap/packages/layer/data/Flexible/Flexible.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Flexible/index.mjs
script11.install = (app) => {
  app.component(script11.name, script11);
  return app;
};
var ElAmapLayerFlexible = script11;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/GLCustom/GLCustom.vue2.mjs
var script12 = defineComponent({
  ...{
    name: "ElAmapLayerGlCustom",
    inheritAttrs: false
  },
  __name: "GLCustom",
  props: buildProps({
    init: {
      type: Function
    },
    // 初始化的时候，开发者可以在这个函数参数里面获取 gl 上下文，进行一些初始化的操作。
    render: {
      type: Function
    },
    // 绘制函数，初始化完成时候，开发者需要给该图层设定render方法，该方法需要实现图层的绘制，API会在合适的时机自动调用该方法
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2, 20]
    opacity: {
      type: Number
    }
    // 透明度，默认 1
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.GLCustomLayer(options);
        $amapComponent.setMap(parentComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent) {
          $amapComponent.setMap(null);
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/GLCustom/GLCustom.vue.mjs
script12.__file = "src/vue-amap/packages/layer/data/GLCustom/GLCustom.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/GLCustom/index.mjs
script12.install = (app) => {
  app.component(script12.name, script12);
  return app;
};
var ElAmapLayerGlCustom = script12;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/HeatMap/HeatMap.vue2.mjs
var script13 = defineComponent({
  ...{
    name: "ElAmapLayerHeatMap",
    inheritAttrs: false
  },
  __name: "HeatMap",
  props: buildProps({
    radius: {
      type: Number
    },
    // 热力图中单个点的半径，默认：30，单位：pixel
    gradient: {
      type: Object
    },
    // 热力图的渐变区间，热力图按照设置的颜色及间隔显示热力图，例{0.4:'rgb(0, 255, 255)',0.85:'rgb(100, 0, 255)',},其中 key 表示间隔位置，取值范围： [0,1] ，value 为颜色值。默认：heatmap.js标准配色方案
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Object
    },
    // 热力图透明度区间数组，取值范围 [0,1] ，0表示完全透明，1表示不透明，默认： [0,1]
    config: {
      type: Object
    },
    // 3D热力图属性
    dataSet: {
      type: Object
    }
    // 热力图数据集
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        AMap.plugin(["AMap.HeatMap"], () => {
          delete options.dataSet;
          $amapComponent = new AMap.HeatMap(parentComponent, options);
          if (props.dataSet) {
            $amapComponent.setDataSet(props.dataSet);
          }
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          $amapComponent.setDataSet({
            data: [],
            max: 0
          });
          $amapComponent.setMap(null);
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/HeatMap/HeatMap.vue.mjs
script13.__file = "src/vue-amap/packages/layer/data/HeatMap/HeatMap.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/HeatMap/index.mjs
script13.install = (app) => {
  app.component(script13.name, script13);
  return app;
};
var ElAmapLayerHeatMap = script13;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Image/Image.vue2.mjs
var script14 = defineComponent({
  ...{
    name: "ElAmapLayerImage",
    inheritAttrs: false
  },
  __name: "Image",
  props: buildProps({
    url: {
      type: String,
      required: true
    },
    // 图片地址链接
    zoom: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    bounds: {
      type: [Array, Object]
    },
    // 图片的范围大小经纬度，如果传递数字数组类型: [minlng,minlat,maxlng,maxlat] 或 AMap.Bounds
    opacity: {
      type: Number
    }
    // 透明度，默认 1
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.ImageLayer(options);
        parentComponent.addLayer($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __url(value) {
          $amapComponent.setImageUrl(value);
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeLayer($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Image/Image.vue.mjs
script14.__file = "src/vue-amap/packages/layer/data/Image/Image.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Image/index.mjs
script14.install = (app) => {
  app.component(script14.name, script14);
  return app;
};
var ElAmapLayerImage = script14;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Labels/Labels.vue2.mjs
var script15 = defineComponent({
  ...{
    name: "ElAmapLayerLabels",
    inheritAttrs: false
  },
  __name: "Labels",
  props: buildProps({
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Number
    },
    // 透明度，默认 1
    collision: {
      type: Boolean,
      default: true
    },
    // 标注层内的标注是否避让
    allowCollision: {
      type: Boolean,
      default: false
    }
    // 标注层内的标注是否允许其它标注层对它避让
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const needInitComponents = [];
    const provideData = {
      $amapComponent: void 0,
      addChildComponent(cb) {
        needInitComponents.push(cb);
      },
      isDestroy: false
    };
    provide(provideKey, provideData);
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.LabelsLayer(options);
        parentComponent.add($amapComponent);
        provideData.$amapComponent = $amapComponent;
        resolve($amapComponent);
      });
    }, {
      emits,
      needInitComponents,
      provideData,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeLayer($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    const $$add = (markers) => {
      $amapComponent.add(markers);
    };
    __expose({
      $$getInstance,
      $$add
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", null, [
        renderSlot(_ctx.$slots, "default")
      ]);
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Labels/Labels.vue.mjs
script15.__file = "src/vue-amap/packages/layer/data/Labels/Labels.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Labels/index.mjs
script15.install = (app) => {
  app.component(script15.name, script15);
  return app;
};
var ElAmapLayerLabels = script15;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Vector/Vector.vue2.mjs
var script16 = defineComponent({
  ...{
    name: "ElAmapLayerVector",
    inheritAttrs: false
  },
  __name: "Vector",
  props: buildProps({}),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const needInitComponents = [];
    const provideData = {
      $amapComponent: void 0,
      addChildComponent(cb) {
        needInitComponents.push(cb);
      },
      isDestroy: false
    };
    provide(provideKey, provideData);
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.VectorLayer(options);
        parentComponent.add($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      needInitComponents,
      provideData,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeLayer($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", null, [
        renderSlot(_ctx.$slots, "default")
      ]);
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Vector/Vector.vue.mjs
script16.__file = "src/vue-amap/packages/layer/data/Vector/Vector.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Vector/index.mjs
script16.install = (app) => {
  app.component(script16.name, script16);
  return app;
};
var ElAmapLayerVector = script16;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/Buildings/Buildings.vue2.mjs
var script17 = defineComponent({
  ...{
    name: "ElAmapLayerBuildings",
    inheritAttrs: false
  },
  __name: "Buildings",
  props: buildProps({
    wallColor: {
      type: [String, Array]
    },
    // 楼块侧面颜色，支持 rgba、rgb、十六进制等
    roofColor: {
      type: [String, Array]
    },
    // 楼块顶面颜色，支持 rgba、rgb、十六进制等
    heightFactor: {
      type: Number
    },
    // 楼块的高度系数因子，默认为 1，也就是正常高度
    styleOpts: {
      type: Object
    },
    // 楼块的围栏和样式设置
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Number
    }
    // 透明度，默认 1
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.Buildings(options);
        parentComponent.add($amapComponent);
        if (props.styleOpts) {
          $amapComponent.setStyle(props.styleOpts);
        }
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __styleOpts(value) {
          $amapComponent.setStyle(value);
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/Buildings/Buildings.vue.mjs
script17.__file = "src/vue-amap/packages/layer/official/Buildings/Buildings.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/Buildings/index.mjs
script17.install = (app) => {
  app.component(script17.name, script17);
  return app;
};
var ElAmapLayerBuildings = script17;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/DefaultLayer/DefaultLayer.vue2.mjs
var script18 = defineComponent({
  ...{
    name: "ElAmapLayerDefault",
    inheritAttrs: false
  },
  __name: "DefaultLayer",
  props: buildProps({
    zoom: {
      type: Array
    },
    opacity: {
      type: Number
    }
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = AMap.createDefaultLayer(options);
        parentComponent.add($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/DefaultLayer/DefaultLayer.vue.mjs
script18.__file = "src/vue-amap/packages/layer/official/DefaultLayer/DefaultLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/DefaultLayer/index.mjs
script18.install = (app) => {
  app.component(script18.name, script18);
  return app;
};
var ElAmapLayerDefault = script18;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/DistrictLayer/DistrictLayer.vue2.mjs
var script19 = defineComponent({
  ...{
    name: "ElAmapLayerDistrict",
    inheritAttrs: false
  },
  __name: "DistrictLayer",
  props: buildProps({
    type: {
      type: String,
      default: "Country",
      validator: (value) => {
        return ["World", "Country", "Province"].indexOf(value) !== -1;
      }
    },
    adcode: {
      type: String
    },
    // 行政区的编码 adcode与省市行政区对照表，下载地址：https://a.amap.com/lbs/static/file/AMap_adcode_citycode.xlsx.zip
    SOC: {
      type: String
    },
    // 设定显示的国家,对应下载地址： https://a.amap.com/jsapi_demos/static/demo-center/js/soc-list.json
    depth: {
      type: Number
    },
    // 设定数据的层级深度，depth为0的时候只显示国家面，depth为1的时候显示省级， 当国家为中国时设置depth为2的可以显示市一级
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Number
    },
    // 透明度，默认 1
    styles: {
      type: Object
    }
    // 为简易行政区图设定各面的填充颜色和描边颜色。 styles各字段的值可以是颜色值，也可以是一个返回颜色值* 的回调函数function。
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        if (props.type === "World") {
          $amapComponent = new AMap.DistrictLayer.World(options);
        } else if (props.type === "Country") {
          $amapComponent = new AMap.DistrictLayer.Country(options);
        } else if (props.type === "Province") {
          $amapComponent = new AMap.DistrictLayer.Province(options);
        }
        parentComponent.add($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __SOC(value) {
          $amapComponent.setSOC(value);
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/DistrictLayer/DistrictLayer.vue.mjs
script19.__file = "src/vue-amap/packages/layer/official/DistrictLayer/DistrictLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/DistrictLayer/index.mjs
script19.install = (app) => {
  app.component(script19.name, script19);
  return app;
};
var ElAmapLayerDistrict = script19;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/IndoorMap/IndoorMap.vue2.mjs
var script20 = defineComponent({
  ...{
    name: "ElAmapLayerIndoorMap",
    inheritAttrs: false
  },
  __name: "IndoorMap",
  props: buildProps({
    opacity: {
      type: Number
    },
    // 透明度，默认 1
    cursor: {
      type: String
    },
    // 指定鼠标悬停到店铺面时的鼠标样式
    hideFloorBar: {
      type: Boolean,
      default: false
    }
    // 是否隐藏楼层切换控件，默认值：false
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        AMap.plugin(["AMap.IndoorMap"], () => {
          $amapComponent = new AMap.IndoorMap(options);
          const layers = parentComponent.getLayers();
          layers.push($amapComponent);
          parentComponent.setLayers(layers);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      watchRedirectFn: {
        __hideFloorBar(flag) {
          !flag ? $amapComponent.hideFloorBar() : $amapComponent.showFloorBar();
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          const layers = parentInstance.$amapComponent.getLayers();
          let index = -1;
          for (let i = 0; i < layers.length; i++) {
            if (isIndoorMapInstance(layers[i])) {
              index = i;
              break;
            }
          }
          if (index > -1) {
            layers.splice(index, 1);
            parentInstance.$amapComponent.setLayers(layers);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/IndoorMap/IndoorMap.vue.mjs
script20.__file = "src/vue-amap/packages/layer/official/IndoorMap/IndoorMap.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/IndoorMap/index.mjs
script20.install = (app) => {
  app.component(script20.name, script20);
  return app;
};
var ElAmapLayerIndoorMap = script20;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/RoadNet/RoadNet.vue2.mjs
var script21 = defineComponent({
  ...{
    name: "ElAmapLayerRoadNet",
    inheritAttrs: false
  },
  __name: "RoadNet",
  props: buildProps({
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Number
    },
    // 透明度，默认 1
    tileSize: {
      type: Number
    }
    // 切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.TileLayer.RoadNet(options);
        parentComponent.add($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/RoadNet/RoadNet.vue.mjs
script21.__file = "src/vue-amap/packages/layer/official/RoadNet/RoadNet.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/RoadNet/index.mjs
script21.install = (app) => {
  app.component(script21.name, script21);
  return app;
};
var ElAmapLayerRoadNet = script21;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/Satellite/Satellite.vue2.mjs
var script22 = defineComponent({
  ...{
    name: "ElAmapLayerSatellite",
    inheritAttrs: false
  },
  __name: "Satellite",
  props: buildProps({
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Number
    },
    // 透明度，默认 1
    tileSize: {
      type: Number
    }
    // 切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.TileLayer.Satellite(options);
        parentComponent.add($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/Satellite/Satellite.vue.mjs
script22.__file = "src/vue-amap/packages/layer/official/Satellite/Satellite.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/Satellite/index.mjs
script22.install = (app) => {
  app.component(script22.name, script22);
  return app;
};
var ElAmapLayerSatellite = script22;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/TileLayer/TileLayer.vue2.mjs
var script23 = defineComponent({
  ...{
    name: "ElAmapLayerTile",
    inheritAttrs: false
  },
  __name: "TileLayer",
  props: buildProps({
    tileUrl: {
      type: String,
      required: true
    },
    // 切片取图地址 如：' https://abc{0,1,2,3}.amap.com/tile?x=[x]&y=[y]&z=[z] ' [x] 、 [y] 、 [z] 分别替代切片的xyz。
    zoom: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    dataZooms: {
      type: Array
    },
    // 数据支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Number
    },
    // 透明度，默认 1
    tileSize: {
      type: Number
    }
    // 切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.TileLayer(options);
        parentComponent.add($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/TileLayer/TileLayer.vue.mjs
script23.__file = "src/vue-amap/packages/layer/official/TileLayer/TileLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/TileLayer/index.mjs
script23.install = (app) => {
  app.component(script23.name, script23);
  return app;
};
var ElAmapLayerTile = script23;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/Traffic/Traffic.vue2.mjs
var script24 = defineComponent({
  ...{
    name: "ElAmapLayerTraffic",
    inheritAttrs: false
  },
  __name: "Traffic",
  props: buildProps({
    autoRefresh: {
      type: Boolean,
      defult: true
    },
    // 是否自动更新数据，默认开启
    interval: {
      type: Number
    },
    // 自动更新数据的间隔毫秒数，默认 180ms
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Number
    },
    // 透明度，默认 1
    tileSize: {
      type: Number
    }
    // 切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.TileLayer.Traffic(options);
        parentComponent.add($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    const stopFresh = () => {
      if ($amapComponent) {
        $amapComponent.stopFresh();
      }
    };
    __expose({
      $$getInstance,
      stopFresh
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/Traffic/Traffic.vue.mjs
script24.__file = "src/vue-amap/packages/layer/official/Traffic/Traffic.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/official/Traffic/index.mjs
script24.install = (app) => {
  app.component(script24.name, script24);
  return app;
};
var ElAmapLayerTraffic = script24;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/standard/MapboxVectorTileLayer/MapboxVectorTileLayer.vue2.mjs
var script25 = defineComponent({
  ...{
    name: "ElAmapLayerMapboxVectorTile",
    inheritAttrs: false
  },
  __name: "MapboxVectorTileLayer",
  props: buildProps({
    url: {
      type: String
    },
    // MVT 数据的链接地址
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2,22]
    dataZooms: {
      type: Array
    },
    // 瓦片数据等级范围，超过范围会使用最大/最小等级的数据，默认 [2,18]
    opacity: {
      type: Number
    },
    // 透明度，默认 1
    styles: {
      type: Object
    }
    // 样式
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        AMap.plugin(["AMap.MapboxVectorTileLayer"], () => {
          $amapComponent = new AMap.MapboxVectorTileLayer(options);
          parentComponent.addLayer($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeLayer($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/standard/MapboxVectorTileLayer/MapboxVectorTileLayer.vue.mjs
script25.__file = "src/vue-amap/packages/layer/standard/MapboxVectorTileLayer/MapboxVectorTileLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/standard/MapboxVectorTileLayer/index.mjs
script25.install = (app) => {
  app.component(script25.name, script25);
  return app;
};
var ElAmapLayerMapboxVectorTile = script25;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/standard/WMS/WMS.vue2.mjs
var script26 = defineComponent({
  ...{
    name: "ElAmapLayerWms",
    inheritAttrs: false
  },
  __name: "WMS",
  props: buildProps({
    url: {
      type: String
    },
    // wms服务的url地址，如' https://ahocevar.com/geoserver/wms '
    blend: {
      type: Boolean,
      default: false
    },
    // 地图级别切换时，不同级别的图片是否进行混合，如图层的图像内容为部分透明请设置为false
    params: {
      type: Object
    },
    // OGC标准的WMS地图服务的GetMap接口的参数，包括VERSION、LAYERS、STYLES、FORMAT、TRANSPARENT等
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Number
    }
    // 透明度，默认 1
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.TileLayer.WMS(options);
        parentComponent.add($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/standard/WMS/WMS.vue.mjs
script26.__file = "src/vue-amap/packages/layer/standard/WMS/WMS.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/standard/WMS/index.mjs
script26.install = (app) => {
  app.component(script26.name, script26);
  return app;
};
var ElAmapLayerWms = script26;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/standard/WMTS/WMTS.vue2.mjs
var script27 = defineComponent({
  ...{
    name: "ElAmapLayerWmts",
    inheritAttrs: false
  },
  __name: "WMTS",
  props: buildProps({
    url: {
      type: String
    },
    // wmts服务的url地址，如：' https://services.arcgisonline.com/arcgis/rest/services/'+ 'Demographics/USA_Population_Density/MapServer/WMTS/'
    blend: {
      type: Boolean,
      default: false
    },
    // 地图级别切换时，不同级别的图片是否进行混合，如图层的图像内容为部分透明请设置为false
    params: {
      type: Object
    },
    // OGC标准的WMS地图服务的GetMap接口的参数，包括VERSION、LAYERS、STYLES、FORMAT、TRANSPARENT等
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    opacity: {
      type: Number
    }
    // 透明度，默认 1
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.TileLayer.WMTS(options);
        parentComponent.add($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/standard/WMTS/WMTS.vue.mjs
script27.__file = "src/vue-amap/packages/layer/standard/WMTS/WMTS.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/standard/WMTS/index.mjs
script27.install = (app) => {
  app.component(script27.name, script27);
  return app;
};
var ElAmapLayerWmts = script27;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/ElasticMarker/ElasticMarker.vue2.mjs
var script28 = defineComponent({
  ...{
    name: "ElAmapElasticMarker",
    inheritAttrs: false
  },
  __name: "ElasticMarker",
  props: buildProps({
    position: {
      type: [Array, Object],
      required: true
    },
    // 点标记在地图上显示的位置
    title: {
      type: String
    },
    // 鼠标滑过点标记时的文字提示。不设置则鼠标滑过点标无文字提示。
    offset: {
      type: [Array, Object]
    },
    // 点标记显示位置偏移量，默认值为 [0,0] 。Marker指定position后，默认以marker左上角位置为基准点（若设置了anchor，则以anchor设置位置为基准点），对准所给定的position位置，若需使marker指定位置对准在position处，需根据marker的尺寸设置一定的偏移量。
    clickable: {
      type: Boolean,
      default: true
    },
    // 点标记是否可点击，默认值: true
    draggable: {
      type: Boolean,
      default: false
    },
    // 设置点标记是否可拖拽移动，默认值：false
    bubble: {
      type: Boolean,
      default: false
    },
    // 事件是否冒泡，默认为 false
    zooms: {
      type: Array
    },
    // 点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]
    cursor: {
      type: String
    },
    // 指定鼠标悬停时的鼠，默认值：'pointer'
    topWhenClick: {
      type: Boolean,
      default: false
    },
    // 鼠标点击时marker是否置顶，默认false ，不置顶
    zoomStyleMapping: {
      type: Object
    },
    // 表示地图级别与styles中样式的映射，{14:0,15:0,16:1,17:1,}表示14到15级使用styles中的第0个样式，16-17级使用第二个样式
    styles: {
      type: Array
    },
    // 多个不同样式的数组
    extData: {
      type: Object,
      default: () => null
    }
  }),
  emits: ["init", "update:position"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        AMap.plugin(["AMap.ElasticMarker"], () => {
          $amapComponent = new AMap.ElasticMarker(options);
          parentComponent.add($amapComponent);
          bindModelEvents();
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitPosition();
      });
      $amapComponent.on("touchend", () => {
        emitPosition();
      });
    };
    const emitPosition = () => {
      const position = $amapComponent.getPosition();
      emits("update:position", position == null ? void 0 : position.toArray());
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/ElasticMarker/ElasticMarker.vue.mjs
script28.__file = "src/vue-amap/packages/marker/ElasticMarker/ElasticMarker.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/ElasticMarker/index.mjs
script28.install = (app) => {
  app.component(script28.name, script28);
  return app;
};
var ElAmapElasticMarker = script28;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/LabelMarker/LabelMarker.vue2.mjs
var script29 = defineComponent({
  ...{
    name: "ElAmapLabelMarker",
    inheritAttrs: false
  },
  __name: "LabelMarker",
  props: buildProps({
    name: {
      type: String
    },
    // 标注名称，作为标注标识，并非最终在地图上显示的文字内容，显示文字内容请设置 opts.text.content
    position: {
      type: [Array, Object],
      required: true
    },
    // 标注位置
    zooms: {
      type: Array
    },
    // 点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]
    opacity: {
      type: Number
    },
    // 标注透明度，默认值: 1
    rank: {
      type: Number
    },
    // 避让优先级，获取标注的优先级，该优先级用于 labelsLayer 支持避让时，rank 值大的标注会避让掉 rank 值低的标注。默认值：1
    icon: {
      type: Object
    },
    // 标注图标设置
    text: {
      type: Object
    },
    // 标注文本设置
    extData: null,
    rotation: {
      type: Number
    }
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.LabelMarker(options);
        parentComponent.add($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/LabelMarker/LabelMarker.vue.mjs
script29.__file = "src/vue-amap/packages/marker/LabelMarker/LabelMarker.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/LabelMarker/index.mjs
script29.install = (app) => {
  app.component(script29.name, script29);
  return app;
};
var ElAmapLabelMarker = script29;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/Marker/props.mjs
var propsTypes4 = buildProps({
  position: {
    type: [Array, Object],
    required: true
  },
  // 点标记在地图上显示的位置
  icon: {
    type: [String, Object]
  },
  // 在点标记中显示的图标。可以传一个图标地址，也可以传Icon对象。有合法的content内容设置时，此属性无效。
  content: {
    type: [String, typeof HTMLElement === "undefined" ? Object : HTMLElement]
  },
  // 点标记显示内容。可以是HTML要素字符串或者HTML DOM对象。content有效时，icon属性将被覆盖。
  title: {
    type: String
  },
  // 鼠标滑过点标记时的文字提示。不设置则鼠标滑过点标无文字提示。
  offset: {
    type: [Array, Object]
  },
  // 点标记显示位置偏移量，默认值为 [0,0] 。Marker指定position后，默认以marker左上角位置为基准点（若设置了anchor，则以anchor设置位置为基准点），对准所给定的position位置，若需使marker指定位置对准在position处，需根据marker的尺寸设置一定的偏移量。
  anchor: {
    type: [String, Array]
  },
  // 设置点标记锚点，可选值：'top-left','top-center','top-right', 'middle-left', 'center', 'middle-right', 'bottom-left', 'bottom-center', 'bottom-right'
  angle: {
    type: Number
  },
  // 点标记的旋转角度，，广泛用于改变车辆行驶方向。默认值：0
  clickable: {
    type: Boolean,
    default: true
  },
  // 点标记是否可点击，默认值: true
  draggable: {
    type: Boolean,
    default: false
  },
  // 设置点标记是否可拖拽移动，默认值：false
  bubble: {
    type: Boolean,
    default: false
  },
  // 事件是否冒泡，默认为 false
  zooms: {
    type: Array
  },
  // 点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]
  cursor: {
    type: String
  },
  // 指定鼠标悬停时的鼠，默认值：'pointer'
  topWhenClick: {
    type: Boolean,
    default: false
  },
  // 鼠标点击时marker是否置顶，默认false ，不置顶
  label: {
    type: Object
  },
  // 添加文本标注
  extData: null,
  moveOptions: {
    type: Object,
    default: () => null
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/Marker/Marker.vue2.mjs
var _hoisted_14 = { style: { "display": "none" } };
var script30 = defineComponent({
  ...{
    name: "ElAmapMarker",
    inheritAttrs: false
  },
  __name: "Marker",
  props: buildProps(propsTypes4),
  emits: ["init", "update:position"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    const tempId = `marker-${guid()}`;
    const divId = ref("");
    let $amapComponent;
    let withSlot = false;
    const $slots = useSlots();
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        if ($slots.default && $slots.default().length > 0) {
          withSlot = true;
          options.content = `<div id="${tempId}"></div>`;
        }
        $amapComponent = new AMap.Marker(options);
        if (isMapInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        } else if (isOverlayGroupInstance(parentComponent)) {
          parentComponent.addOverlay($amapComponent);
        }
        if (withSlot) {
          divId.value = tempId;
          nextTick(() => {
            $amapComponent.setAnchor($amapComponent.getAnchor());
          });
        }
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __position(position) {
          if (!props.moveOptions) {
            $amapComponent.setPosition(position);
            return;
          }
          if (parentInstance == null ? void 0 : parentInstance.$amapComponent) {
            parentInstance.$amapComponent.plugin("AMap.MoveAnimation", () => {
              $amapComponent.moveTo(position, props.moveOptions);
            });
          }
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          $amapComponent.setMap(null);
          $amapComponent = null;
        }
      }
    });
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitPosition();
      });
      $amapComponent.on("touchend", () => {
        emitPosition();
      });
    };
    const emitPosition = () => {
      const position = $amapComponent.getPosition();
      emits("update:position", position.toArray());
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", _hoisted_14, [
        !!divId.value ? (openBlock(), createBlock(Teleport, {
          key: 0,
          to: "#" + divId.value
        }, [
          renderSlot(_ctx.$slots, "default")
        ], 8, ["to"])) : createCommentVNode("v-if", true)
      ]);
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/Marker/Marker.vue.mjs
script30.__file = "src/vue-amap/packages/marker/Marker/Marker.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/Marker/index.mjs
script30.install = (app) => {
  app.component(script30.name, script30);
  return app;
};
var ElAmapMarker = script30;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/MarkerCluster/MarkerCluster.vue2.mjs
var script31 = defineComponent({
  ...{
    name: "ElAmapMarkerCluster",
    inheritAttrs: false
  },
  __name: "MarkerCluster",
  props: buildProps({
    points: {
      type: Object,
      required: true
    },
    // 需要进行聚合显示的点数据
    gridSize: {
      type: Number
    },
    // 聚合计算时网格的像素大小，默认60
    maxZoom: {
      type: Number
    },
    // 最大的聚合级别，大于该级别就不进行相应的聚合。默认值为 18，即小于 18 级的级别均进行聚合，18 及以上级别不进行聚合
    averageCenter: {
      type: Boolean,
      default: true
    },
    // 聚合点的图标位置是否是所有聚合内点的中心点。默认为 true。数据中如果含有权重值，以权重高的点为中心进行聚合
    clusterByZoomChange: {
      type: Boolean,
      default: false
    },
    // 地图缩放过程中是否聚合。默认值 false。
    styles: {
      type: Array
    },
    // 指定聚合后的点标记的图标样式，可缺省，缺省时为默认样式
    renderClusterMarker: {
      type: Function
    },
    // 该方法用来实现聚合点的自定义绘制，由开发者自己实现，API 将在绘制每个聚合点的时候调用这个方法，可以实现聚合点样式的灵活设定，指定了 renderClusterMarker 后 styles 无效。
    renderMarker: {
      type: Function
    }
    // 该方法用来实现非聚合点的自定义绘制，由开发者自己实现，API 将在绘制每个非聚合点的时候调用这个方法
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        AMap.plugin(["AMap.MarkerCluster"], () => {
          const points = options.points;
          delete options.points;
          $amapComponent = new AMap.MarkerCluster(parentComponent, points, options);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      watchRedirectFn: {
        __points(value) {
          if ($amapComponent) {
            $amapComponent.setData(value);
          }
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          $amapComponent.setMap(null);
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/MarkerCluster/MarkerCluster.vue.mjs
script31.__file = "src/vue-amap/packages/marker/MarkerCluster/MarkerCluster.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/MarkerCluster/index.mjs
script31.install = (app) => {
  app.component(script31.name, script31);
  return app;
};
var ElAmapMarkerCluster = script31;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/MassMarks/MassMarks.vue2.mjs
var script32 = defineComponent({
  ...{
    name: "ElAmapMassMarks",
    inheritAttrs: false
  },
  __name: "MassMarks",
  props: buildProps({
    data: {
      type: Array,
      required: true
    },
    // 海量点数据参数
    zooms: {
      type: Array
    },
    // 点标记显示的层级范围，超过范围不显示。
    cursor: {
      type: String
    },
    // 指定鼠标悬停时的鼠，默认值：'pointer'
    styles: {
      type: [Array, Object]
    }
    // 样式
  }),
  emits: ["init", "update:center"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        const data = options.data;
        delete options.data;
        $amapComponent = new AMap.MassMarks(data, options);
        $amapComponent.setMap(parentComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      propsRedirect: {
        styles: "style"
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          $amapComponent.clear();
          $amapComponent.setMap(null);
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/MassMarks/MassMarks.vue.mjs
script32.__file = "src/vue-amap/packages/marker/MassMarks/MassMarks.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/MassMarks/index.mjs
script32.install = (app) => {
  app.component(script32.name, script32);
  return app;
};
var ElAmapMassMarks = script32;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/Text/props.mjs
var propsType2 = buildProps({
  position: {
    type: [Array, Object],
    required: true
  },
  // 点标记在地图上显示的位置
  text: {
    type: String
  },
  // 标记显示的文本内容
  title: {
    type: String
  },
  // 鼠标滑过点标记时的文字提示。不设置则鼠标滑过点标无文字提示。
  offset: {
    type: [Array, Object]
  },
  // 点标记显示位置偏移量，默认值为 [0,0] 。Marker指定position后，默认以marker左上角位置为基准点（若设置了anchor，则以anchor设置位置为基准点），对准所给定的position位置，若需使marker指定位置对准在position处，需根据marker的尺寸设置一定的偏移量。
  anchor: {
    type: [String, Array]
  },
  // 设置点标记锚点，可选值：'top-left','top-center','top-right', 'middle-left', 'center', 'middle-right', 'bottom-left', 'bottom-center', 'bottom-right'
  angle: {
    type: Number
  },
  // 点标记的旋转角度。默认值：0 。注：angle属性是使用CSS3来实现的，支持IE9及以上版本
  clickable: {
    type: Boolean,
    default: true
  },
  // 点标记是否可点击，默认值: true
  draggable: {
    type: Boolean,
    default: false
  },
  // 设置点标记是否可拖拽移动，默认值：false
  bubble: {
    type: Boolean,
    default: false
  },
  // 事件是否冒泡，默认为 false
  zooms: {
    type: Array
  },
  // 点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]
  cursor: {
    type: String
  },
  // 指定鼠标悬停时的鼠，默认值：'pointer'
  topWhenClick: {
    type: Boolean,
    default: false
  },
  // 鼠标点击时marker是否置顶，默认false ，不置顶
  textStyle: {
    type: Object
  },
  // 设置文本样式，Object同css样式表，如:{'background-color':'red'}
  extData: null
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/Text/Text.vue2.mjs
var script33 = defineComponent({
  ...{
    name: "ElAmapText",
    inheritAttrs: false
  },
  __name: "Text",
  props: propsType2,
  emits: ["init", "update:position"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.Text(options);
        parentComponent.add($amapComponent);
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      propsRedirect: {
        textStyle: "style"
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          $amapComponent.setMap(null);
          $amapComponent = null;
        }
      }
    });
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitPosition();
      });
      $amapComponent.on("touchend", () => {
        emitPosition();
      });
    };
    const emitPosition = () => {
      const position = $amapComponent.getPosition();
      emits("update:position", position.toArray());
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/Text/Text.vue.mjs
script33.__file = "src/vue-amap/packages/marker/Text/Text.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/Text/index.mjs
script33.install = (app) => {
  app.component(script33.name, script33);
  return app;
};
var ElAmapText = script33;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/mixins/useEditor.mjs
function useEditor(editor, attrs) {
  if (!editor)
    return;
  const filters = ["addnode", "adjust", "removenode", "end", "move", "add"];
  const filterSet = {};
  Object.keys(attrs).forEach((key) => {
    if (eventReg.test(key)) {
      const eventKey = convertEventToLowerCase(key);
      if (filters.indexOf(eventKey) !== -1)
        filterSet[eventKey] = attrs[key];
    }
  });
  Object.keys(filterSet).forEach((key) => {
    bindInstanceEvent(editor, key, filterSet[key]);
  });
}

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/BezierCurve/props.mjs
var propsTypes5 = buildProps({
  // 贝瑟尔曲线的路径。描述为一个二维数组规则如下：第一个元素是起点， 之后的元素同时描述控制点和途经点，之后每个元素可以有0个到2个控制点 控制点在前，途经点在最后 [ [lng,lat] ,//起点0 [lng,lat,lng,lat,lng,lat] ,//控制点、控制点、途经点2 [lng,lat,lng,lat] //控制点、途经点3 ] 或者 [ [ [lng,lat] ],//起点0 [ [lng,lat] , [lng,lat] ],//控制点、途经点1 [ [lng,lat] , [lng,lat] , [lng,lat] ],//控制点、控制点、途经点2 [ [lng,lat] , [lng,lat] ]//控制点、途经点3 ]
  path: {
    type: Array,
    required: true
  },
  // 是否将覆盖物的鼠标或touch等事件冒泡到地图上
  bubble: {
    type: Boolean,
    default: false
  },
  // 指定鼠标悬停时的鼠标样式，自定义cursor，IE仅支持cur/ani/ico格式，Opera不支持自定义cursor
  cursor: {
    type: String
  },
  // 线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC
  strokeColor: {
    type: String
  },
  // 轮廓线透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.9
  strokeOpacity: {
    type: Number
  },
  // 轮廓线宽度。默认 2
  strokeWeight: {
    type: Number
  },
  // 描边线宽度
  borderWeight: {
    type: Number
  },
  // 是否显示描边,默认false
  isOutline: {
    type: Boolean,
    default: false
  },
  // 线条描边颜色，此项仅在isOutline为true时有效，默认：#00B2D5
  outlineColor: {
    type: String
  },
  // 设置多边形是否可拖拽移动，默认为false
  draggable: {
    type: Boolean,
    default: false
  },
  extData: {
    type: Object,
    default: () => null
  },
  // 轮廓线样式，实线:solid，虚线:dashed
  strokeStyle: {
    type: String,
    validator: (value) => {
      return ["solid", "dashed"].indexOf(value) !== -1;
    }
  },
  // 勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线
  strokeDasharray: {
    type: Array
  },
  // 折线拐点的绘制样式，默认值为'miter'尖角，其他可选值：'round'圆角、'bevel'斜角
  lineJoin: {
    type: String,
    validator: (value) => {
      return ["miter", "round", "bevel"].indexOf(value) !== -1;
    }
  },
  // 折线两端线帽的绘制样式，默认值为'butt'无头，其他可选值：'round'圆头、'square'方头
  lineCap: {
    type: String,
    validator: (value) => {
      return ["butt", "round", "square"].indexOf(value) !== -1;
    }
  },
  // 是否绘制成大地线，默认false
  geodesic: {
    type: Boolean,
    default: false
  },
  // 是否延路径显示白色方向箭头,默认false。建议折线宽度大于6时使用
  showDir: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: false
  },
  editOptions: {
    type: Object
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/BezierCurve/BezierCurve.vue2.mjs
var script34 = defineComponent({
  ...{
    name: "ElAmapBezierCurve",
    inheritAttrs: false
  },
  __name: "BezierCurve",
  props: propsTypes5,
  emits: ["init", "update:path"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let destroying = false;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.BezierCurve(options);
        if (isMapInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        } else if (isOverlayGroupInstance(parentComponent)) {
          parentComponent.addOverlay($amapComponent);
        } else if (isVectorLayerInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        }
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __zIndex(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ zIndex: value });
          }
        },
        __strokeColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeColor: value });
          }
        },
        __strokeOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeOpacity: value });
          }
        },
        __strokeWeight(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeWeight: value });
          }
        },
        __borderWeight(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ borderWeight: value });
          }
        },
        __isOutline(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ isOutline: value });
          }
        },
        __outlineColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ outlineColor: value });
          }
        },
        __strokeStyle(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeStyle: value });
          }
        },
        __strokeDasharray(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeDasharray: value });
          }
        },
        __lineJoin(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ lineJoin: value });
          }
        },
        __lineCap(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ lineCap: value });
          }
        },
        __geodesic(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ geodesic: value });
          }
        },
        __showDir(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ showDir: value });
          }
        },
        __editable(flag) {
          createEditor().then(() => {
            flag ? resetEditor() : editor.close();
          });
        },
        __path(path) {
          if ($amapComponent) {
            $amapComponent.setPath(path);
            resetEditor();
          }
        }
      },
      destroyComponent() {
        destroying = true;
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (editor) {
            if (!parentInstance.isDestroy) {
              editor.close();
            }
            editor = null;
          }
          if (!parentInstance.isDestroy) {
            if (isMapInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            } else if (isOverlayGroupInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.removeOverlay($amapComponent);
            } else if (isVectorLayerInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            }
          }
          if ($amapComponent.destroy) {
            $amapComponent.destroy();
          }
          $amapComponent = null;
        }
      }
    });
    const resetEditor = debounce_default(() => {
      if (editor && props.editable) {
        editor.close();
        editor.setTarget();
        editor.setTarget($amapComponent);
        editor.open();
      }
    }, 50);
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitModel($amapComponent);
      });
      $amapComponent.on("touchend", () => {
        emitModel($amapComponent);
      });
    };
    const emitModel = debounce_default((target) => {
      if (destroying) {
        return;
      }
      const path = target.getPath();
      emits("update:path", path);
    }, 50);
    let editor;
    const attrs = useAttrs();
    const createEditor = () => {
      return new Promise((resolve) => {
        if (editor) {
          resolve();
        } else {
          AMap.plugin(["AMap.BezierCurveEditor"], () => {
            editor = new AMap.BezierCurveEditor(parentInstance == null ? void 0 : parentInstance.$amapComponent, $amapComponent, props.editOptions);
            useEditor(editor, attrs);
            bindEditorModelEvents();
            resolve();
          });
        }
      });
    };
    const bindEditorModelEvents = () => {
      editor.on("addnode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("adjust", (e2) => {
        emitModel(e2.target);
      });
      editor.on("removenode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("add", (e2) => {
        emitModel(e2.target);
      });
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/BezierCurve/BezierCurve.vue.mjs
script34.__file = "src/vue-amap/packages/vector/BezierCurve/BezierCurve.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/BezierCurve/index.mjs
script34.install = (app) => {
  app.component(script34.name, script34);
  return app;
};
var ElAmapBezierCurve = script34;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Circle/props.mjs
var propsTypes6 = buildProps({
  // 圆心位置
  center: {
    type: Array,
    required: true
  },
  // 圆半径，单位:米
  radius: {
    type: Number,
    required: true
  },
  // 是否将覆盖物的鼠标或touch等事件冒泡到地图上
  bubble: {
    type: Boolean,
    default: false
  },
  // 指定鼠标悬停时的鼠标样式，自定义cursor，IE仅支持cur/ani/ico格式，Opera不支持自定义cursor
  cursor: {
    type: String
  },
  // 线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC
  strokeColor: {
    type: String
  },
  // 轮廓线透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.9
  strokeOpacity: {
    type: Number
  },
  // 轮廓线宽度。默认 2
  strokeWeight: {
    type: Number
  },
  // 多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5
  fillColor: {
    type: String
  },
  // 多边形填充透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.5
  fillOpacity: {
    type: Number
  },
  // 设置多边形是否可拖拽移动，默认为false
  draggable: {
    type: Boolean,
    default: false
  },
  extData: {
    type: Object,
    default: () => null
  },
  // 轮廓线样式，实线:solid，虚线:dashed
  strokeStyle: {
    type: String,
    validator: (value) => {
      return ["solid", "dashed"].indexOf(value) !== -1;
    }
  },
  // 勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线
  strokeDasharray: {
    type: Array
  },
  editable: {
    type: Boolean,
    default: false
  },
  editOptions: {
    type: Object
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Circle/Circle.vue2.mjs
var script35 = defineComponent({
  ...{
    name: "ElAmapCircle",
    inheritAttrs: false
  },
  __name: "Circle",
  props: propsTypes6,
  emits: ["init", "update:center", "update:radius"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let destroying = false;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.Circle(options);
        if (isMapInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        } else if (isOverlayGroupInstance(parentComponent)) {
          parentComponent.addOverlay($amapComponent);
        } else if (isVectorLayerInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        }
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __zIndex(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ zIndex: value });
          }
        },
        __strokeColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeColor: value });
          }
        },
        __strokeOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeOpacity: value });
          }
        },
        __strokeWeight(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeWeight: value });
          }
        },
        __fillColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillColor: value });
          }
        },
        __fillOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillOpacity: value });
          }
        },
        __strokeStyle(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeStyle: value });
          }
        },
        __strokeDasharray(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeDasharray: value });
          }
        },
        __editable(flag) {
          createEditor().then(() => {
            flag ? resetEditor() : editor.close();
          });
        },
        __center(center) {
          if ($amapComponent) {
            $amapComponent.setCenter(center);
            resetEditor();
          }
        },
        __radius(radius) {
          if ($amapComponent) {
            $amapComponent.setRadius(radius);
            resetEditor();
          }
        }
      },
      destroyComponent() {
        destroying = true;
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (editor) {
            if (!parentInstance.isDestroy) {
              editor.close();
            }
            editor = null;
          }
          if (!parentInstance.isDestroy) {
            if (isMapInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            } else if (isOverlayGroupInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.removeOverlay($amapComponent);
            } else if (isVectorLayerInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            }
          }
          if ($amapComponent.destroy) {
            $amapComponent.destroy();
          }
          $amapComponent = null;
        }
      }
    });
    const resetEditor = debounce_default(() => {
      if (editor && props.editable) {
        editor.close();
        editor.setTarget();
        editor.setTarget($amapComponent);
        editor.open();
      }
    }, 50);
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitModel($amapComponent);
      });
      $amapComponent.on("touchend", () => {
        emitModel($amapComponent);
      });
    };
    const emitModel = (target) => {
      if (destroying) {
        return;
      }
      emits("update:center", target.getCenter().toArray());
      emits("update:radius", target.getRadius());
    };
    let editor;
    const attrs = useAttrs();
    const createEditor = () => {
      return new Promise((resolve) => {
        if (editor) {
          resolve();
        } else {
          AMap.plugin(["AMap.CircleEditor"], () => {
            editor = new AMap.CircleEditor(parentInstance == null ? void 0 : parentInstance.$amapComponent, $amapComponent, props.editOptions);
            useEditor(editor, attrs);
            bindEditorModelEvents();
            resolve();
          });
        }
      });
    };
    const bindEditorModelEvents = () => {
      editor.on("addnode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("adjust", (e2) => {
        emitModel(e2.target);
      });
      editor.on("removenode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("add", (e2) => {
        emitModel(e2.target);
      });
      editor.on("move", (e2) => {
        emitModel(e2.target);
      });
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Circle/Circle.vue.mjs
script35.__file = "src/vue-amap/packages/vector/Circle/Circle.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Circle/index.mjs
script35.install = (app) => {
  app.component(script35.name, script35);
  return app;
};
var ElAmapCircle = script35;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Ellipse/props.mjs
var propsTypes7 = buildProps({
  // 圆心位置
  center: {
    type: Array,
    required: true
  },
  // 椭圆的半径，用2个元素的数组表示，单位：米 如： radius: [1000, 2000] 表示横向半径是1000，纵向的半径是2000 默认值： [1000, 1000]
  radius: {
    type: Array,
    required: true
  },
  // 是否将覆盖物的鼠标或touch等事件冒泡到地图上
  bubble: {
    type: Boolean,
    default: false
  },
  // 指定鼠标悬停时的鼠标样式，自定义cursor，IE仅支持cur/ani/ico格式，Opera不支持自定义cursor
  cursor: {
    type: String
  },
  // 线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC
  strokeColor: {
    type: String
  },
  // 轮廓线透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.9
  strokeOpacity: {
    type: Number
  },
  // 轮廓线宽度。默认 2
  strokeWeight: {
    type: Number
  },
  // 多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5
  fillColor: {
    type: String
  },
  // 多边形填充透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.5
  fillOpacity: {
    type: Number
  },
  // 设置多边形是否可拖拽移动，默认为false
  draggable: {
    type: Boolean,
    default: false
  },
  extData: {
    type: Object,
    default: () => null
  },
  // 轮廓线样式，实线:solid，虚线:dashed
  strokeStyle: {
    type: String,
    validator: (value) => {
      return ["solid", "dashed"].indexOf(value) !== -1;
    }
  },
  // 勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线
  strokeDasharray: {
    type: Array
  },
  editable: {
    type: Boolean,
    default: false
  },
  editOptions: {
    type: Object
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Ellipse/Ellipse.vue2.mjs
var script36 = defineComponent({
  ...{
    name: "ElAmapEllipse",
    inheritAttrs: false
  },
  __name: "Ellipse",
  props: propsTypes7,
  emits: ["init", "update:center", "update:radius"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let destroying = false;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.Ellipse(options);
        if (isMapInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        } else if (isOverlayGroupInstance(parentComponent)) {
          parentComponent.addOverlay($amapComponent);
        } else if (isVectorLayerInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        }
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __zIndex(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ zIndex: value });
          }
        },
        __strokeColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeColor: value });
          }
        },
        __strokeOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeOpacity: value });
          }
        },
        __strokeWeight(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeWeight: value });
          }
        },
        __fillColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillColor: value });
          }
        },
        __fillOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillOpacity: value });
          }
        },
        __strokeStyle(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeStyle: value });
          }
        },
        __strokeDasharray(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeDasharray: value });
          }
        },
        __editable(flag) {
          createEditor().then(() => {
            flag ? resetEditor() : editor.close();
          });
        },
        __center(center) {
          if ($amapComponent) {
            $amapComponent.setCenter(center);
            resetEditor();
          }
        },
        __radius(radius) {
          if ($amapComponent) {
            $amapComponent.setRadius(radius);
            resetEditor();
          }
        }
      },
      destroyComponent() {
        destroying = true;
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (editor) {
            if (!parentInstance.isDestroy) {
              editor.close();
            }
            editor = null;
          }
          if (!parentInstance.isDestroy) {
            if (isMapInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            } else if (isOverlayGroupInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.removeOverlay($amapComponent);
            } else if (isVectorLayerInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            }
          }
          if ($amapComponent.destroy) {
            $amapComponent.destroy();
          }
          $amapComponent = null;
        }
      }
    });
    const resetEditor = debounce_default(() => {
      if (editor && props.editable) {
        editor.close();
        editor.setTarget();
        editor.setTarget($amapComponent);
        editor.open();
      }
    }, 50);
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitModel($amapComponent);
      });
      $amapComponent.on("touchend", () => {
        emitModel($amapComponent);
      });
    };
    const emitModel = (target) => {
      if (destroying) {
        return;
      }
      emits("update:center", target.getCenter().toArray());
      emits("update:radius", target.getRadius());
    };
    let editor;
    const attrs = useAttrs();
    const createEditor = () => {
      return new Promise((resolve) => {
        if (editor) {
          resolve();
        } else {
          AMap.plugin(["AMap.EllipseEditor"], () => {
            editor = new AMap.EllipseEditor(parentInstance == null ? void 0 : parentInstance.$amapComponent, $amapComponent, props.editOptions);
            useEditor(editor, attrs);
            bindEditorModelEvents();
            resolve();
          });
        }
      });
    };
    const bindEditorModelEvents = () => {
      editor.on("addnode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("adjust", (e2) => {
        emitModel(e2.target);
      });
      editor.on("removenode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("add", (e2) => {
        emitModel(e2.target);
      });
      editor.on("move", (e2) => {
        emitModel(e2.target);
      });
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Ellipse/Ellipse.vue.mjs
script36.__file = "src/vue-amap/packages/vector/Ellipse/Ellipse.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Ellipse/index.mjs
script36.install = (app) => {
  app.component(script36.name, script36);
  return app;
};
var ElAmapEllipse = script36;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/GeoJSON/GeoJSON.vue2.mjs
var script37 = defineComponent({
  ...{
    name: "ElAmapGeojson",
    inheritAttrs: false
  },
  __name: "GeoJSON",
  props: buildProps({
    // 要加载的标准GeoJSON对象
    geo: {
      type: Object,
      required: true
    },
    // marker的默认样式
    markerOptions: {
      type: Object
    },
    // 指定点要素的绘制方式，缺省时为Marker的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径
    getMarker: {
      type: Function
    },
    // polyline的默认样式
    polylineOptions: {
      type: Object
    },
    // 指定线要素的绘制方式，缺省时为Polyline的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径
    getPolyline: {
      type: Function
    },
    // polygon的默认样式
    polygonOptions: {
      type: Object
    },
    // 指定面要素的绘制方式，缺省时为Polygon的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径
    getPolygon: {
      type: Function
    }
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        AMap.plugin(["AMap.GeoJSON"], () => {
          if (!options.getMarker) {
            options.getMarker = createMarker;
          }
          if (!options.getPolyline) {
            options.getPolyline = createPolyline;
          }
          if (!options.getPolygon) {
            options.getPolygon = createPolygon;
          }
          $amapComponent = new AMap.GeoJSON(options);
          parentComponent.add($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      propsRedirect: {
        geo: "geoJSON"
      },
      watchRedirectFn: {
        __geoJSON(value) {
          if ($amapComponent) {
            $amapComponent.importData(value);
          }
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    const createMarker = (geojson, lnglat) => {
      let options = props.markerOptions || {};
      options = merge_default({}, options, geojson.properties);
      options.position = lnglat;
      return new AMap.Marker(options);
    };
    const createPolyline = (geojson, lnglat) => {
      let options = props.polylineOptions || {};
      options = merge_default({}, options, geojson.properties);
      options.path = lnglat;
      return new AMap.Polyline(options);
    };
    const createPolygon = (geojson, lnglat) => {
      let options = props.polygonOptions || {};
      options = merge_default({}, options, geojson.properties);
      options.path = lnglat;
      return new AMap.Polygon(options);
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/GeoJSON/GeoJSON.vue.mjs
script37.__file = "src/vue-amap/packages/vector/GeoJSON/GeoJSON.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/GeoJSON/index.mjs
script37.install = (app) => {
  app.component(script37.name, script37);
  return app;
};
var ElAmapGeojson = script37;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Polygon/props.mjs
var propsTypes8 = buildProps({
  // 多边形轮廓线的节点坐标数组。 支持 单个普通多边形({Array })，单个带孔多边形({Array<Array >})，多个带孔多边形({Array<Array<Array >>})
  path: {
    type: Array,
    required: true
  },
  // 是否将覆盖物的鼠标或touch等事件冒泡到地图上
  bubble: {
    type: Boolean,
    default: false
  },
  // 指定鼠标悬停时的鼠标样式，自定义cursor，IE仅支持cur/ani/ico格式，Opera不支持自定义cursor
  cursor: {
    type: String
  },
  // 线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC
  strokeColor: {
    type: String
  },
  // 轮廓线透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.9
  strokeOpacity: {
    type: Number
  },
  // 轮廓线宽度。默认 2
  strokeWeight: {
    type: Number
  },
  // 多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5
  fillColor: {
    type: String
  },
  // 多边形填充透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.5
  fillOpacity: {
    type: Number
  },
  // 设置多边形是否可拖拽移动，默认为false
  draggable: {
    type: Boolean,
    default: false
  },
  extData: {
    type: Object,
    default: () => null
  },
  // 轮廓线样式，实线:solid，虚线:dashed
  strokeStyle: {
    type: String,
    validator: (value) => {
      return ["solid", "dashed"].indexOf(value) !== -1;
    }
  },
  // 勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线
  strokeDasharray: {
    type: Array
  },
  editable: {
    type: Boolean,
    default: false
  },
  editOptions: {
    type: Object
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Polygon/Polygon.vue2.mjs
var script38 = defineComponent({
  ...{
    name: "ElAmapPolygon",
    inheritAttrs: false
  },
  __name: "Polygon",
  props: propsTypes8,
  emits: ["init", "update:path"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let destroying = false;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.Polygon(options);
        if (isMapInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        } else if (isOverlayGroupInstance(parentComponent)) {
          parentComponent.addOverlay($amapComponent);
        } else if (isVectorLayerInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        }
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __zIndex(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ zIndex: value });
          }
        },
        __strokeColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeColor: value });
          }
        },
        __strokeOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeOpacity: value });
          }
        },
        __strokeWeight(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeWeight: value });
          }
        },
        __fillColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillColor: value });
          }
        },
        __fillOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillOpacity: value });
          }
        },
        __strokeStyle(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeStyle: value });
          }
        },
        __strokeDasharray(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeDasharray: value });
          }
        },
        __editable(flag) {
          createEditor().then(() => {
            flag ? resetEditor() : editor.close();
          });
        },
        __path(path) {
          if ($amapComponent) {
            $amapComponent.setPath(path);
            resetEditor();
          }
        },
        __draggable(flag) {
          $amapComponent.setOptions({
            draggable: flag
          });
          if (editor) {
            if (editor.editingPolyObj && editor.editingPolyObj.origin_options) {
              editor.editingPolyObj.origin_options.draggable = flag;
            }
          }
          if (props.editable) {
            resetEditor();
          }
        }
      },
      destroyComponent() {
        destroying = true;
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (editor) {
            if (!parentInstance.isDestroy) {
              editor.close();
            }
            editor = null;
          }
          if (!parentInstance.isDestroy) {
            if (isMapInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            } else if (isOverlayGroupInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.removeOverlay($amapComponent);
            } else if (isVectorLayerInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            }
          }
          if ($amapComponent.destroy) {
            $amapComponent.destroy();
          }
          $amapComponent = null;
        }
      }
    });
    const resetEditor = debounce_default(() => {
      if (editor && props.editable) {
        editor.close();
        editor.open();
      }
    }, 50);
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitModel($amapComponent);
      });
      $amapComponent.on("touchend", () => {
        emitModel($amapComponent);
      });
    };
    const emitModel = (target) => {
      if (destroying) {
        return;
      }
      const paths = target.getPath();
      const pathArray = paths == null ? void 0 : paths.map(convertLnglat);
      emits("update:path", pathArray);
    };
    let editor;
    const attrs = useAttrs();
    const createEditor = () => {
      return new Promise((resolve) => {
        if (editor) {
          resolve();
        } else {
          AMap.plugin(["AMap.PolygonEditor"], () => {
            editor = new AMap.PolygonEditor(parentInstance == null ? void 0 : parentInstance.$amapComponent, $amapComponent, props.editOptions);
            useEditor(editor, attrs);
            bindEditorModelEvents();
            resolve();
          });
        }
      });
    };
    const bindEditorModelEvents = () => {
      editor.on("addnode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("adjust", (e2) => {
        emitModel(e2.target);
      });
      editor.on("removenode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("add", (e2) => {
        emitModel(e2.target);
      });
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Polygon/Polygon.vue.mjs
script38.__file = "src/vue-amap/packages/vector/Polygon/Polygon.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Polygon/index.mjs
script38.install = (app) => {
  app.component(script38.name, script38);
  return app;
};
var ElAmapPolygon = script38;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Polyline/props.mjs
var propsTypes9 = buildProps({
  // polyline 路径，支持 lineString 和 MultiLineString
  path: {
    type: Array,
    required: true
  },
  // 是否将覆盖物的鼠标或touch等事件冒泡到地图上
  bubble: {
    type: Boolean,
    default: false
  },
  // 指定鼠标悬停时的鼠标样式，自定义cursor，IE仅支持cur/ani/ico格式，Opera不支持自定义cursor
  cursor: {
    type: String
  },
  // 线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC
  strokeColor: {
    type: String
  },
  // 轮廓线透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.9
  strokeOpacity: {
    type: Number
  },
  // 轮廓线宽度。默认 2
  strokeWeight: {
    type: Number
  },
  // 描边线宽度
  borderWeight: {
    type: Number
  },
  // 是否显示描边,默认false
  isOutline: {
    type: Boolean,
    default: false
  },
  // 线条描边颜色，此项仅在isOutline为true时有效，默认：#00B2D5
  outlineColor: {
    type: String
  },
  // 设置多边形是否可拖拽移动，默认为false
  draggable: {
    type: Boolean,
    default: false
  },
  extData: {
    type: Object,
    default: () => null
  },
  // 轮廓线样式，实线:solid，虚线:dashed
  strokeStyle: {
    type: String,
    validator: (value) => {
      return ["solid", "dashed"].indexOf(value) !== -1;
    }
  },
  // 勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线
  strokeDasharray: {
    type: Array
  },
  // 折线拐点的绘制样式，默认值为'miter'尖角，其他可选值：'round'圆角、'bevel'斜角
  lineJoin: {
    type: String,
    validator: (value) => {
      return ["miter", "round", "bevel"].indexOf(value) !== -1;
    }
  },
  // 折线两端线帽的绘制样式，默认值为'butt'无头，其他可选值：'round'圆头、'square'方头
  lineCap: {
    type: String,
    validator: (value) => {
      return ["butt", "round", "square"].indexOf(value) !== -1;
    }
  },
  // 是否绘制成大地线，默认false
  geodesic: {
    type: Boolean,
    default: false
  },
  // 是否延路径显示白色方向箭头,默认false。建议折线宽度大于6时使用,
  showDir: {
    type: Boolean,
    default: false
  },
  editable: {
    type: Boolean,
    default: false
  },
  editOptions: {
    type: Object
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Polyline/Polyline.vue2.mjs
var script39 = defineComponent({
  ...{
    name: "ElAmapPolyline",
    inheritAttrs: false
  },
  __name: "Polyline",
  props: propsTypes9,
  emits: ["init", "update:path"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let destroying = false;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.Polyline(options);
        if (isMapInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        } else if (isOverlayGroupInstance(parentComponent)) {
          parentComponent.addOverlay($amapComponent);
        } else if (isVectorLayerInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        }
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __zIndex(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ zIndex: value });
          }
        },
        __strokeColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeColor: value });
          }
        },
        __strokeOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeOpacity: value });
          }
        },
        __strokeWeight(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeWeight: value });
          }
        },
        __borderWeight(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ borderWeight: value });
          }
        },
        __isOutline(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ isOutline: value });
          }
        },
        __outlineColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ outlineColor: value });
          }
        },
        __strokeStyle(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeStyle: value });
          }
        },
        __strokeDasharray(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeDasharray: value });
          }
        },
        __lineJoin(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ lineJoin: value });
          }
        },
        __lineCap(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ lineCap: value });
          }
        },
        __geodesic(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ geodesic: value });
          }
        },
        __showDir(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ showDir: value });
          }
        },
        __editable(flag) {
          createEditor().then(() => {
            flag ? resetEditor() : editor.close();
          });
        },
        __path(path) {
          if ($amapComponent) {
            $amapComponent.setPath(path);
            resetEditor();
          }
        }
      },
      destroyComponent() {
        destroying = true;
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (editor) {
            if (!parentInstance.isDestroy) {
              editor.close();
            }
            editor = null;
          }
          if (!parentInstance.isDestroy) {
            if (isMapInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            } else if (isOverlayGroupInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.removeOverlay($amapComponent);
            } else if (isVectorLayerInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            }
          }
          if ($amapComponent.destroy) {
            $amapComponent.destroy();
          }
          $amapComponent = null;
        }
      }
    });
    const resetEditor = debounce_default(() => {
      if (editor && props.editable) {
        editor.close();
        editor.setTarget();
        editor.setTarget($amapComponent);
        editor.open();
      }
    }, 50);
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitModel($amapComponent);
      });
      $amapComponent.on("touchend", () => {
        emitModel($amapComponent);
      });
    };
    const emitModel = (target) => {
      if (destroying) {
        return;
      }
      const paths = target.getPath();
      const pathArray = paths == null ? void 0 : paths.map(convertLnglat);
      emits("update:path", pathArray);
    };
    let editor;
    const attrs = useAttrs();
    const createEditor = () => {
      return new Promise((resolve) => {
        if (editor) {
          resolve();
        } else {
          AMap.plugin(["AMap.PolylineEditor"], () => {
            editor = new AMap.PolylineEditor(parentInstance == null ? void 0 : parentInstance.$amapComponent, $amapComponent, props.editOptions);
            useEditor(editor, attrs);
            bindEditorModelEvents();
            resolve();
          });
        }
      });
    };
    const bindEditorModelEvents = () => {
      editor.on("addnode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("adjust", (e2) => {
        emitModel(e2.target);
      });
      editor.on("removenode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("add", (e2) => {
        emitModel(e2.target);
      });
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Polyline/Polyline.vue.mjs
script39.__file = "src/vue-amap/packages/vector/Polyline/Polyline.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Polyline/index.mjs
script39.install = (app) => {
  app.component(script39.name, script39);
  return app;
};
var ElAmapPolyline = script39;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Rectangle/props.mjs
var propsTypes10 = buildProps({
  // 矩形的范围
  bounds: {
    type: Array,
    required: true
  },
  // 是否将覆盖物的鼠标或touch等事件冒泡到地图上
  bubble: {
    type: Boolean,
    default: false
  },
  // 指定鼠标悬停时的鼠标样式，自定义cursor，IE仅支持cur/ani/ico格式，Opera不支持自定义cursor
  cursor: {
    type: String
  },
  // 线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC
  strokeColor: {
    type: String
  },
  // 轮廓线透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.9
  strokeOpacity: {
    type: Number
  },
  // 轮廓线宽度。默认 2
  strokeWeight: {
    type: Number
  },
  // 多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5
  fillColor: {
    type: String
  },
  // 多边形填充透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.5
  fillOpacity: {
    type: Number
  },
  // 设置多边形是否可拖拽移动，默认为false
  draggable: {
    type: Boolean,
    default: false
  },
  extData: {
    type: Object,
    default: () => null
  },
  // 轮廓线样式，实线:solid，虚线:dashed
  strokeStyle: {
    type: String,
    validator: (value) => {
      return ["solid", "dashed"].indexOf(value) !== -1;
    }
  },
  // 勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线
  strokeDasharray: {
    type: Array
  },
  editable: {
    type: Boolean,
    default: false
  },
  editOptions: {
    type: Object
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Rectangle/Rectangle.vue2.mjs
var script40 = defineComponent({
  ...{
    name: "ElAmapRectangle",
    inheritAttrs: false
  },
  __name: "Rectangle",
  props: propsTypes10,
  emits: ["init", "update:bounds"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let destroying = false;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.Rectangle(options);
        if (isMapInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        } else if (isOverlayGroupInstance(parentComponent)) {
          parentComponent.addOverlay($amapComponent);
        } else if (isVectorLayerInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        }
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      converts: {
        bounds: (path) => {
          return new AMap.Bounds(toLngLat(path[0]), toLngLat(path[1]));
        }
      },
      watchRedirectFn: {
        __zIndex(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ zIndex: value });
          }
        },
        __strokeColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeColor: value });
          }
        },
        __strokeOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeOpacity: value });
          }
        },
        __strokeWeight(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeWeight: value });
          }
        },
        __fillColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillColor: value });
          }
        },
        __fillOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillOpacity: value });
          }
        },
        __strokeStyle(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeStyle: value });
          }
        },
        __strokeDasharray(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeDasharray: value });
          }
        },
        __editable(flag) {
          createEditor().then(() => {
            flag ? resetEditor() : editor.close();
          });
        },
        __bounds(bounds) {
          if ($amapComponent) {
            $amapComponent.setBounds(bounds);
            resetEditor();
          }
        }
      },
      destroyComponent() {
        destroying = true;
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (editor) {
            if (!parentInstance.isDestroy) {
              editor.close();
            }
            editor = null;
          }
          if (!parentInstance.isDestroy) {
            if (isMapInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            } else if (isOverlayGroupInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.removeOverlay($amapComponent);
            } else if (isVectorLayerInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            }
          }
          if ($amapComponent.destroy) {
            $amapComponent.destroy();
          }
          $amapComponent = null;
        }
      }
    });
    const resetEditor = debounce_default(() => {
      if (editor && props.editable) {
        editor.close();
        editor.setTarget();
        editor.setTarget($amapComponent);
        editor.open();
      }
    }, 50);
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitModel($amapComponent);
      });
      $amapComponent.on("touchend", () => {
        emitModel($amapComponent);
      });
    };
    const emitModel = (target) => {
      if (destroying) {
        return;
      }
      const bounds = target.getBounds();
      const southWest = bounds.getSouthWest();
      const northEast = bounds.getNorthEast();
      const paths = [southWest.toArray(), northEast.toArray()];
      emits("update:bounds", paths);
    };
    let editor;
    const attrs = useAttrs();
    const createEditor = () => {
      return new Promise((resolve) => {
        if (editor) {
          resolve();
        } else {
          AMap.plugin(["AMap.RectangleEditor"], () => {
            editor = new AMap.RectangleEditor(parentInstance == null ? void 0 : parentInstance.$amapComponent, $amapComponent, props.editOptions);
            useEditor(editor, attrs);
            bindEditorModelEvents();
            resolve();
          });
        }
      });
    };
    const bindEditorModelEvents = () => {
      editor.on("addnode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("adjust", (e2) => {
        emitModel(e2.target);
      });
      editor.on("removenode", (e2) => {
        emitModel(e2.target);
      });
      editor.on("add", (e2) => {
        emitModel(e2.target);
      });
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Rectangle/Rectangle.vue.mjs
script40.__file = "src/vue-amap/packages/vector/Rectangle/Rectangle.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/vector/Rectangle/index.mjs
script40.install = (app) => {
  app.component(script40.name, script40);
  return app;
};
var ElAmapRectangle = script40;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Tiles3D/Tiles3D.vue2.mjs
var script41 = defineComponent({
  ...{
    name: "ElAmapLayerTiles3d",
    inheritAttrs: false
  },
  __name: "Tiles3D",
  props: buildProps({
    url: {
      type: String,
      required: true
    },
    threeScriptUrl: {
      type: String,
      default: "//a.amap.com/jsapi_demos/static/data3d/lib/three.117.js"
    },
    threeGltfLoader: {
      type: String,
      default: "//a.amap.com/jsapi_demos/static/data3d/lib/GLTFLoader.117.min.js"
    },
    layerStyle: {
      type: Object
    }
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.3DTilesLayer"], () => {
          if (!window["THREE"]) {
            loadScript(options.threeScriptUrl, () => {
              loadScript(options.threeGltfLoader, () => {
                createLayer(options);
                resolve($amapComponent);
              });
            });
          } else {
            createLayer(options);
            resolve($amapComponent);
          }
        });
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent) {
          $amapComponent = null;
        }
      }
    });
    const createLayer = (options) => {
      let layerStyle = {};
      if (options.layerStyle) {
        layerStyle = JSON.parse(JSON.stringify(options.layerStyle));
      }
      $amapComponent = new AMap["3DTilesLayer"]({
        map: parentInstance == null ? void 0 : parentInstance.$amapComponent,
        url: options.url,
        // 3d Tiles 入口文件
        style: layerStyle
      });
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Tiles3D/Tiles3D.vue.mjs
script41.__file = "src/vue-amap/packages/layer/data/Tiles3D/Tiles3D.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Tiles3D/index.mjs
script41.install = (app) => {
  app.component(script41.name, script41);
  return app;
};
var ElAmapLayerTiles3d = script41;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/Geolocation/props.mjs
var propsType3 = buildProps({
  //悬停位置，默认为"RB"，即右下角.'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角
  position: {
    type: String
  },
  //缩略图距离悬停位置的像素距离，如 [2,2]
  offset: {
    type: Array
  },
  //按钮边框颜色值，同CSS，如'silver'
  borderColor: {
    type: String
  },
  borderRadius: {
    type: String
  },
  //按钮圆角边框值，同CSS，如'5px'
  buttonSize: {
    type: String
  },
  //箭头按钮的像素尺寸，同CSS，如'12px'
  convert: {
    type: Boolean,
    default: true
  },
  //是否将定位结果转换为高德坐标
  enableHighAccuracy: {
    type: Boolean,
    default: false
  },
  //进行浏览器原生定位的时候是否尝试获取较高精度，可能影响定位效率，默认为false
  timeout: {
    type: Number
  },
  //定位的超时时间，毫秒
  maximumAge: {
    type: Number
  },
  //浏览器原生定位的缓存时间，毫秒
  showButton: {
    type: Boolean,
    default: true
  },
  //是否显示定位按钮，默认为true
  showCircle: {
    type: Boolean,
    default: true
  },
  //是否显示定位精度圆，默认为true
  showMarker: {
    type: Boolean,
    default: true
  },
  //是否显示定位点，默认为true
  markerOptions: {
    type: Object
  },
  //定位点的样式
  circleOptions: {
    type: Object
  },
  //CircleOptions
  panToLocation: {
    type: Boolean,
    default: true
  },
  //定位成功后是否自动移动到响应位置
  zoomToAccuracy: {
    type: Boolean,
    default: true
  },
  //定位成功后是否自动调整级别
  GeoLocationFirst: {
    type: Boolean,
    default: true
  },
  //优先使用H5定位，默认false
  noIpLocate: {
    type: Number
  },
  //是否禁用IP精确定位，默认为0，0:都用 1:手机上不用 2:PC上不用 3:都不用
  noGeoLocation: {
    type: Number
  },
  //是否禁用浏览器原生定位，默认为0，0:都用 1:手机上不用 2:PC上不用 3:都不用
  useNative: {
    type: Boolean,
    default: false
  },
  //是否与高德定位SDK能力结合，需要同时使用安卓版高德定位sdk，否则无效
  getCityWhenFail: {
    type: Boolean,
    default: false
  },
  //定位失败之后是否返回基本城市定位信息
  needAddress: {
    type: Boolean,
    default: false
  },
  //是否需要将定位结果进行逆地理编码操作
  extensions: {
    type: String,
    validator: (value) => {
      return ["base", "all"].indexOf(value) !== -1;
    }
  }
  //是否需要详细的逆地理编码信息，默认为'base'只返回基本信息，可选'all'
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/Geolocation/Geolocation.vue2.mjs
var script42 = defineComponent({
  ...{
    name: "ElAmapControlGeolocation",
    inheritAttrs: false
  },
  __name: "Geolocation",
  props: propsType3,
  emits: ["init", "complete"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const emitComplete = (e2) => {
      emits("complete", e2);
    };
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.Geolocation"], () => {
          $amapComponent = new AMap.Geolocation(options);
          parentComponent.addControl($amapComponent);
          $amapComponent.on("complete", emitComplete);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        $amapComponent.off("complete", emitComplete);
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeControl($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return null;
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/Geolocation/Geolocation.vue.mjs
script42.__file = "src/vue-amap/packages/control/Geolocation/Geolocation.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/control/Geolocation/index.mjs
script42.install = (app) => {
  app.component(script42.name, script42);
  return app;
};
var ElAmapControlGeolocation = script42;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/CircleMarker/CircleMarker.vue2.mjs
var script43 = defineComponent({
  ...{
    name: "ElAmapCircleMarker",
    inheritAttrs: false
  },
  __name: "CircleMarker",
  props: buildProps({
    zIndex: {
      type: Number
    },
    center: {
      type: Array,
      required: true
    },
    bubble: {
      type: Boolean,
      default: false
    },
    cursor: {
      type: String
    },
    radius: {
      type: Number
    },
    strokeColor: {
      type: String
    },
    strokeOpacity: {
      type: Number
    },
    strokeWeight: {
      type: Number
    },
    fillColor: {
      type: String
    },
    fillOpacity: {
      type: Number
    },
    draggable: {
      type: Boolean,
      default: false
    },
    extData: {
      type: Object
    }
  }),
  emits: ["init", "update:center"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.CircleMarker(options);
        parentComponent.add($amapComponent);
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __strokeColor(value) {
          $amapComponent.setOptions({
            strokeColor: value
          });
        },
        __strokeOpacity(value) {
          $amapComponent.setOptions({
            strokeOpacity: value
          });
        },
        __strokeWeight(value) {
          $amapComponent.setOptions({
            strokeWeight: value
          });
        },
        __fillColor(value) {
          $amapComponent.setOptions({
            fillColor: value
          });
        },
        __fillOpacity(value) {
          $amapComponent.setOptions({
            fillOpacity: value
          });
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitPosition();
      });
      $amapComponent.on("touchend", () => {
        emitPosition();
      });
    };
    const emitPosition = () => {
      const center = $amapComponent.getCenter();
      emits("update:center", center.toArray());
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/CircleMarker/CircleMarker.vue.mjs
script43.__file = "src/vue-amap/packages/marker/CircleMarker/CircleMarker.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/marker/CircleMarker/index.mjs
script43.install = (app) => {
  app.component(script43.name, script43);
  return app;
};
var ElAmapCircleMarker = script43;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Video/VideoLayer.mjs
var __defProp = Object.defineProperty;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __publicField = (obj, key, value) => {
  __defNormalProp(obj, typeof key !== "symbol" ? key + "" : key, value);
  return value;
};
var VideoLayer = class {
  constructor(options, video) {
    __publicField(this, "canvas");
    __publicField(this, "context");
    __publicField(this, "video");
    __publicField(this, "layer");
    __publicField(this, "canPlay", false);
    __publicField(this, "frame", -1);
    const canvas = document.createElement("canvas");
    this.video = video;
    video.src = options.url;
    video.autoplay = true;
    video.muted = true;
    video.crossOrigin = "anonymous";
    video.loop = options.loop;
    const layerOptions = {
      ...options,
      canvas
    };
    this.canvas = canvas;
    this.context = canvas.getContext("2d");
    this.layer = new AMap.CanvasLayer(layerOptions);
    this.initVideoEvents();
    this.load();
  }
  getLayer() {
    return this.layer;
  }
  initVideoEvents() {
    var _a2;
    (_a2 = this.video) == null ? void 0 : _a2.addEventListener("canplaythrough", () => {
      var _a22, _b;
      if (this.canvas) {
        this.canvas.width = (_a22 = this.video) == null ? void 0 : _a22.videoWidth;
        this.canvas.height = (_b = this.video) == null ? void 0 : _b.videoHeight;
      }
      this.canPlay = true;
      this.play();
    });
  }
  load() {
    var _a2;
    (_a2 = this.video) == null ? void 0 : _a2.load();
  }
  play() {
    var _a2;
    if (this.canPlay) {
      (_a2 = this.video) == null ? void 0 : _a2.play();
      this.stopRender();
      this.render();
    }
  }
  pause() {
    var _a2;
    this.stopRender();
    (_a2 = this.video) == null ? void 0 : _a2.pause();
  }
  stopRender() {
    if (this.frame) {
      cancelAnimationFrame(this.frame);
    }
  }
  render() {
    var _a2, _b, _c, _d, _e3, _f, _g, _h;
    this.frame = window.requestAnimationFrame(() => {
      this.render();
    });
    if ((_a2 = this.video) == null ? void 0 : _a2.paused) {
      (_b = this.video) == null ? void 0 : _b.play();
    } else {
      (_e3 = this.context) == null ? void 0 : _e3.clearRect(0, 0, (_c = this.canvas) == null ? void 0 : _c.width, (_d = this.canvas) == null ? void 0 : _d.height);
      (_h = this.context) == null ? void 0 : _h.drawImage(this.video, 0, 0, (_f = this.canvas) == null ? void 0 : _f.width, (_g = this.canvas) == null ? void 0 : _g.height);
      this.layer.reFresh();
    }
  }
  destroy() {
    this.stopRender();
    if (this.video) {
      this.video.pause();
    }
    this.video = null;
  }
  setUrl(url) {
    this.stopRender();
    this.canPlay = false;
    if (this.video) {
      this.video.src = url;
      this.load();
    }
  }
  setZooms(zooms) {
    this.getLayer().setZoom(zooms);
  }
  setOpacity(opacity) {
    this.getLayer().setOpacity(opacity);
  }
  setBounds(bounds) {
    this.getLayer().setBounds(bounds);
  }
  setzIndex(zIndex) {
    this.getLayer().setzIndex(zIndex);
  }
  show() {
    this.getLayer().show();
  }
  hide() {
    this.getLayer().hide();
  }
};

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Video/Video.vue2.mjs
var script44 = defineComponent({
  ...{
    name: "ElAmapLayerVideo",
    inheritAttrs: false
  },
  __name: "Video",
  props: buildProps({
    url: {
      required: true,
      type: String
    },
    // 视频地址
    zooms: {
      type: Array
    },
    // 支持的缩放级别范围，默认范围 [2-30]
    bounds: {
      type: [Array, Object]
    },
    // 图片的范围大小经纬度，如果传递数字数组类型: [minlng,minlat,maxlng,maxlat] 或 AMap.Bounds
    opacity: {
      type: Number
    }
    // 透明度，默认 1
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    const videoRef = ref();
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new VideoLayer(options, videoRef.value);
        parentComponent.addLayer($amapComponent.getLayer());
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            $amapComponent.destroy();
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeLayer($amapComponent.getLayer());
          }
          $amapComponent = null;
        }
      }
    });
    const $$play = () => {
      $amapComponent.play();
    };
    const $$pause = () => {
      $amapComponent.pause();
    };
    __expose({
      $$getInstance,
      $$play,
      $$pause
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock(
        "video",
        {
          ref_key: "videoRef",
          ref: videoRef,
          style: { "display": "none" },
          muted: ""
        },
        null,
        512
        /* NEED_PATCH */
      );
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Video/Video.vue.mjs
script44.__file = "src/vue-amap/packages/layer/data/Video/Video.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/Video/index.mjs
script44.install = (app) => {
  app.component(script44.name, script44);
  return app;
};
var ElAmapLayerVideo = script44;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/util/MouseTool/MouseTool.vue2.mjs
var script45 = defineComponent({
  ...{
    name: "ElAmapMouseTool",
    inheritAttrs: false
  },
  __name: "MouseTool",
  props: buildProps({
    type: {
      type: String,
      required: true,
      default: "marker",
      validator: (value) => {
        return ["marker", "circle", "rectangle", "polyline", "polygon", "measureArea", "rule", "rectZoomIn", "rectZoomOut"].includes(value);
      }
    },
    // 类型
    drawOptions: {
      type: Object,
      default: () => null
    },
    // 绘制图层的属性
    autoClear: {
      type: Boolean,
      default: true
    },
    // 是否绘制结束后自动清空图层
    showTooltip: {
      type: Boolean,
      default: true
    },
    // 是否显示提示信息
    tooltipTextMap: {
      type: Object,
      default: () => null
    },
    // 提示信息的map
    textOptions: {
      type: Object,
      default: () => null
    },
    drawCursor: {
      type: String,
      default: "crosshair"
    }
  }),
  emits: ["init", "draw"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let preMapCursor = "";
    const tipTexts = {
      marker: "单击地图选择点位",
      circle: "按住鼠标左键拖拽绘制圆",
      rectangle: "按住鼠标左键拖拽绘制矩形",
      polyline: "单击地图选择拐点，双击地图完成折线绘制",
      polygon: "单击地图选择拐点，双击地图完成多边形绘制",
      measureArea: "单击地图选择拐点，双击地图完成绘制并计算面积",
      rule: "单击地图选择拐点，右击地图完成绘制并计算距离",
      rectZoomIn: "按住鼠标左键拖拽绘制矩形，松开左键放大地图",
      rectZoomOut: "按住鼠标左键拖拽绘制矩形，松开左键放大地图"
    };
    let isDrawing = true;
    const __type = () => {
      if (!isDrawing) {
        return;
      }
      const type = props.type;
      if ($amapComponent[type]) {
        const options = props.drawOptions || {};
        $amapComponent[type](options);
        setText(tipTexts[type]);
      }
    };
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        AMap.plugin(["AMap.MouseTool"], () => {
          if (props.tooltipTextMap) {
            Object.assign(tipTexts, props.tooltipTextMap);
          }
          $amapComponent = new AMap.MouseTool(parentComponent);
          preMapCursor = parentComponent.getDefaultCursor();
          createTooltip();
          __type();
          bindEvent();
          changeMapCursor();
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      watchRedirectFn: {
        __type
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            $amapComponent.close(true);
            revertMapCursor();
            if ($text) {
              parentInstance.$amapComponent.off("mousemove", getMousePosition);
              parentInstance.$amapComponent.remove($text);
              $text = null;
            }
          }
          $amapComponent = null;
        }
      }
    });
    let $text;
    const createTooltip = () => {
      if (props.showTooltip) {
        const textOptions = props.textOptions || {};
        textOptions.anchor = "top-left";
        textOptions.clickable = false;
        textOptions.bubble = true;
        textOptions.offset = [10, 10];
        $text = new AMap.Text(textOptions);
        parentInstance == null ? void 0 : parentInstance.$amapComponent.add($text);
        parentInstance == null ? void 0 : parentInstance.$amapComponent.on("mousemove", getMousePosition);
      }
    };
    const getMousePosition = (e2) => {
      const lnglat = e2.lnglat;
      $text.setPosition([lnglat.lng, lnglat.lat]);
    };
    const setText = (content) => {
      if ($text) {
        $text.setText(content);
      }
    };
    const bindEvent = () => {
      $amapComponent.on("draw", (e2) => {
        const type = props.type;
        let emitData;
        if (type === "marker") {
          emitData = e2.obj.getPosition().toArray();
        } else if (type === "circle") {
          emitData = {
            center: e2.obj.getCenter().toArray(),
            radius: e2.obj.getRadius()
          };
        } else if (type === "rectangle") {
          const bounds = e2.obj.getBounds();
          const southWest = bounds.getSouthWest();
          const northEast = bounds.getNorthEast();
          emitData = [southWest.toArray(), northEast.toArray()];
        } else if (type === "polyline") {
          const path = e2.obj.getPath();
          emitData = path.map((v2) => v2.toArray());
        } else if (type === "polygon") {
          const path = e2.obj.getPath();
          emitData = path.map((v2) => v2.toArray());
        } else if (type === "measureArea") {
          const path = e2.obj.getPath().map((v2) => v2.toArray());
          emitData = AMap.GeometryUtil.ringArea(path);
        } else if (type === "rule") {
          const path = e2.obj.getPath().map((v2) => v2.toArray());
          emitData = AMap.GeometryUtil.distanceOfLine(path);
        } else if (type === "rectZoomIn") {
          emitData = true;
        } else if (type === "rectZoomOut") {
          emitData = true;
        }
        emits("draw", emitData, e2.obj);
        if (props.autoClear) {
          $$clear();
          __type();
        }
      });
    };
    const _close = (ifClear = true) => {
      $amapComponent.close(ifClear);
    };
    const $$close = (ifClear = true) => {
      isDrawing = false;
      if ($amapComponent) {
        _close(ifClear);
        revertMapCursor();
        if ($text) {
          $text.hide();
        }
      }
    };
    const $$open = () => {
      isDrawing = true;
      changeMapCursor();
      __type();
      if ($text) {
        $text.show();
      }
    };
    const $$clear = () => {
      _close(true);
    };
    const changeMapCursor = () => {
      parentInstance == null ? void 0 : parentInstance.$amapComponent.setDefaultCursor(props.drawCursor);
    };
    const revertMapCursor = () => {
      if (preMapCursor) {
        parentInstance == null ? void 0 : parentInstance.$amapComponent.setDefaultCursor(preMapCursor);
      }
    };
    __expose({
      $$getInstance,
      $$open,
      $$close,
      $$clear
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/util/MouseTool/MouseTool.vue.mjs
script45.__file = "src/vue-amap/packages/util/MouseTool/MouseTool.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/util/MouseTool/index.mjs
script45.install = (app) => {
  app.component(script45.name, script45);
  return app;
};
var ElAmapMouseTool = script45;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/DistrictCluster/addProcess.mjs
if (typeof process !== "undefined" && typeof process.env === "undefined") {
  process.env = {};
}

// node_modules/.pnpm/@vuemap+district-cluster@0.0.12/node_modules/@vuemap/district-cluster/dist/index.mjs
var Y = class {
  constructor() {
    this._listeners = {};
  }
  on(t2, e2, r2) {
    this._listeners[t2] ? this._listeners[t2].push({ callback: e2, isOnce: r2 }) : this._listeners[t2] = [{ callback: e2, isOnce: r2 }];
  }
  off(t2, e2) {
    if (!e2)
      throw new Error("取消事件时需要传入原回调函数");
    const r2 = this._listeners[t2];
    if (r2 && r2.length > 0) {
      for (let i = 0; i < r2.length; i++)
        if (r2[i].callback === e2) {
          r2.splice(i, 1);
          break;
        }
    }
  }
  emit(t2, ...e2) {
    const r2 = this._listeners[t2];
    if (r2 && r2.length > 0)
      for (let n2 = 0; n2 < r2.length; n2++) {
        const s = r2[n2];
        s.callback.call(this, ...e2), s.isOnce && (r2.splice(n2, 1), n2--);
      }
    const i = this._listeners["*"];
    if (i && i.length > 0)
      for (let n2 = 0; n2 < i.length; n2++) {
        const s = i[n2];
        s.callback.call(this, t2, ...e2), s.isOnce && (i.splice(n2, 1), n2--);
      }
  }
  trigger(t2, ...e2) {
    this.emit(t2, e2);
  }
};
function dt(o, t2, e2) {
  e2 === void 0 && (e2 = {});
  var r2 = { type: "Feature" };
  return (e2.id === 0 || e2.id) && (r2.id = e2.id), e2.bbox && (r2.bbox = e2.bbox), r2.properties = t2 || {}, r2.geometry = o, r2;
}
function H(o, t2, e2) {
  e2 === void 0 && (e2 = {});
  for (var r2 = 0, i = o; r2 < i.length; r2++) {
    var n2 = i[r2];
    if (n2.length < 4)
      throw new Error("Each LinearRing of a Polygon must have 4 or more Positions.");
    for (var s = 0; s < n2[n2.length - 1].length; s++)
      if (n2[n2.length - 1][s] !== n2[0][s])
        throw new Error("First and last Position are not equivalent.");
  }
  var a2 = { type: "Polygon", coordinates: o };
  return dt(a2, t2, e2);
}
function Bt(o, t2, e2) {
  e2 === void 0 && (e2 = {});
  var r2 = { type: "MultiPolygon", coordinates: o };
  return dt(r2, t2, e2);
}
function ft(o) {
  return o.type === "Feature" ? o.geometry : o;
}
function $t(o, t2) {
  var e2 = { label: 0, sent: function() {
    if (n2[0] & 1)
      throw n2[1];
    return n2[1];
  }, trys: [], ops: [] }, r2, i, n2, s;
  return s = { next: a2(0), throw: a2(1), return: a2(2) }, typeof Symbol == "function" && (s[Symbol.iterator] = function() {
    return this;
  }), s;
  function a2(l) {
    return function(h) {
      return u3([l, h]);
    };
  }
  function u3(l) {
    if (r2)
      throw new TypeError("Generator is already executing.");
    for (; e2; )
      try {
        if (r2 = 1, i && (n2 = l[0] & 2 ? i.return : l[0] ? i.throw || ((n2 = i.return) && n2.call(i), 0) : i.next) && !(n2 = n2.call(i, l[1])).done)
          return n2;
        switch (i = 0, n2 && (l = [l[0] & 2, n2.value]), l[0]) {
          case 0:
          case 1:
            n2 = l;
            break;
          case 4:
            return e2.label++, { value: l[1], done: false };
          case 5:
            e2.label++, i = l[1], l = [0];
            continue;
          case 7:
            l = e2.ops.pop(), e2.trys.pop();
            continue;
          default:
            if (n2 = e2.trys, !(n2 = n2.length > 0 && n2[n2.length - 1]) && (l[0] === 6 || l[0] === 2)) {
              e2 = 0;
              continue;
            }
            if (l[0] === 3 && (!n2 || l[1] > n2[0] && l[1] < n2[3])) {
              e2.label = l[1];
              break;
            }
            if (l[0] === 6 && e2.label < n2[1]) {
              e2.label = n2[1], n2 = l;
              break;
            }
            if (n2 && e2.label < n2[2]) {
              e2.label = n2[2], e2.ops.push(l);
              break;
            }
            n2[2] && e2.ops.pop(), e2.trys.pop();
            continue;
        }
        l = t2.call(o, e2);
      } catch (h) {
        l = [6, h], i = 0;
      } finally {
        r2 = n2 = 0;
      }
    if (l[0] & 5)
      throw l[1];
    return { value: l[0] ? l[1] : void 0, done: true };
  }
}
var A = function() {
  function o(t2, e2) {
    this.next = null, this.key = t2, this.data = e2, this.left = null, this.right = null;
  }
  return o;
}();
function zt(o, t2) {
  return o > t2 ? 1 : o < t2 ? -1 : 0;
}
function P(o, t2, e2) {
  for (var r2 = new A(null, null), i = r2, n2 = r2; ; ) {
    var s = e2(o, t2.key);
    if (s < 0) {
      if (t2.left === null)
        break;
      if (e2(o, t2.left.key) < 0) {
        var a2 = t2.left;
        if (t2.left = a2.right, a2.right = t2, t2 = a2, t2.left === null)
          break;
      }
      n2.left = t2, n2 = t2, t2 = t2.left;
    } else if (s > 0) {
      if (t2.right === null)
        break;
      if (e2(o, t2.right.key) > 0) {
        var a2 = t2.right;
        if (t2.right = a2.left, a2.left = t2, t2 = a2, t2.right === null)
          break;
      }
      i.right = t2, i = t2, t2 = t2.right;
    } else
      break;
  }
  return i.right = t2.left, n2.left = t2.right, t2.left = r2.right, t2.right = r2.left, t2;
}
function X(o, t2, e2, r2) {
  var i = new A(o, t2);
  if (e2 === null)
    return i.left = i.right = null, i;
  e2 = P(o, e2, r2);
  var n2 = r2(o, e2.key);
  return n2 < 0 ? (i.left = e2.left, i.right = e2, e2.left = null) : n2 >= 0 && (i.right = e2.right, i.left = e2, e2.right = null), i;
}
function pt(o, t2, e2) {
  var r2 = null, i = null;
  if (t2) {
    t2 = P(o, t2, e2);
    var n2 = e2(t2.key, o);
    n2 === 0 ? (r2 = t2.left, i = t2.right) : n2 < 0 ? (i = t2.right, t2.right = null, r2 = t2) : (r2 = t2.left, t2.left = null, i = t2);
  }
  return { left: r2, right: i };
}
function Gt(o, t2, e2) {
  return t2 === null ? o : (o === null || (t2 = P(o.key, t2, e2), t2.left = o), t2);
}
function Q(o, t2, e2, r2, i) {
  if (o) {
    r2("" + t2 + (e2 ? "└── " : "├── ") + i(o) + `
`);
    var n2 = t2 + (e2 ? "    " : "│   ");
    o.left && Q(o.left, n2, false, r2, i), o.right && Q(o.right, n2, true, r2, i);
  }
}
var J = function() {
  function o(t2) {
    t2 === void 0 && (t2 = zt), this._root = null, this._size = 0, this._comparator = t2;
  }
  return o.prototype.insert = function(t2, e2) {
    return this._size++, this._root = X(t2, e2, this._root, this._comparator);
  }, o.prototype.add = function(t2, e2) {
    var r2 = new A(t2, e2);
    this._root === null && (r2.left = r2.right = null, this._size++, this._root = r2);
    var i = this._comparator, n2 = P(t2, this._root, i), s = i(t2, n2.key);
    return s === 0 ? this._root = n2 : (s < 0 ? (r2.left = n2.left, r2.right = n2, n2.left = null) : s > 0 && (r2.right = n2.right, r2.left = n2, n2.right = null), this._size++, this._root = r2), this._root;
  }, o.prototype.remove = function(t2) {
    this._root = this._remove(t2, this._root, this._comparator);
  }, o.prototype._remove = function(t2, e2, r2) {
    var i;
    if (e2 === null)
      return null;
    e2 = P(t2, e2, r2);
    var n2 = r2(t2, e2.key);
    return n2 === 0 ? (e2.left === null ? i = e2.right : (i = P(t2, e2.left, r2), i.right = e2.right), this._size--, i) : e2;
  }, o.prototype.pop = function() {
    var t2 = this._root;
    if (t2) {
      for (; t2.left; )
        t2 = t2.left;
      return this._root = P(t2.key, this._root, this._comparator), this._root = this._remove(t2.key, this._root, this._comparator), { key: t2.key, data: t2.data };
    }
    return null;
  }, o.prototype.findStatic = function(t2) {
    for (var e2 = this._root, r2 = this._comparator; e2; ) {
      var i = r2(t2, e2.key);
      if (i === 0)
        return e2;
      i < 0 ? e2 = e2.left : e2 = e2.right;
    }
    return null;
  }, o.prototype.find = function(t2) {
    return this._root && (this._root = P(t2, this._root, this._comparator), this._comparator(t2, this._root.key) !== 0) ? null : this._root;
  }, o.prototype.contains = function(t2) {
    for (var e2 = this._root, r2 = this._comparator; e2; ) {
      var i = r2(t2, e2.key);
      if (i === 0)
        return true;
      i < 0 ? e2 = e2.left : e2 = e2.right;
    }
    return false;
  }, o.prototype.forEach = function(t2, e2) {
    for (var r2 = this._root, i = [], n2 = false; !n2; )
      r2 !== null ? (i.push(r2), r2 = r2.left) : i.length !== 0 ? (r2 = i.pop(), t2.call(e2, r2), r2 = r2.right) : n2 = true;
    return this;
  }, o.prototype.range = function(t2, e2, r2, i) {
    for (var n2 = [], s = this._comparator, a2 = this._root, u3; n2.length !== 0 || a2; )
      if (a2)
        n2.push(a2), a2 = a2.left;
      else {
        if (a2 = n2.pop(), u3 = s(a2.key, e2), u3 > 0)
          break;
        if (s(a2.key, t2) >= 0 && r2.call(i, a2))
          return this;
        a2 = a2.right;
      }
    return this;
  }, o.prototype.keys = function() {
    var t2 = [];
    return this.forEach(function(e2) {
      var r2 = e2.key;
      return t2.push(r2);
    }), t2;
  }, o.prototype.values = function() {
    var t2 = [];
    return this.forEach(function(e2) {
      var r2 = e2.data;
      return t2.push(r2);
    }), t2;
  }, o.prototype.min = function() {
    return this._root ? this.minNode(this._root).key : null;
  }, o.prototype.max = function() {
    return this._root ? this.maxNode(this._root).key : null;
  }, o.prototype.minNode = function(t2) {
    if (t2 === void 0 && (t2 = this._root), t2)
      for (; t2.left; )
        t2 = t2.left;
    return t2;
  }, o.prototype.maxNode = function(t2) {
    if (t2 === void 0 && (t2 = this._root), t2)
      for (; t2.right; )
        t2 = t2.right;
    return t2;
  }, o.prototype.at = function(t2) {
    for (var e2 = this._root, r2 = false, i = 0, n2 = []; !r2; )
      if (e2)
        n2.push(e2), e2 = e2.left;
      else if (n2.length > 0) {
        if (e2 = n2.pop(), i === t2)
          return e2;
        i++, e2 = e2.right;
      } else
        r2 = true;
    return null;
  }, o.prototype.next = function(t2) {
    var e2 = this._root, r2 = null;
    if (t2.right) {
      for (r2 = t2.right; r2.left; )
        r2 = r2.left;
      return r2;
    }
    for (var i = this._comparator; e2; ) {
      var n2 = i(t2.key, e2.key);
      if (n2 === 0)
        break;
      n2 < 0 ? (r2 = e2, e2 = e2.left) : e2 = e2.right;
    }
    return r2;
  }, o.prototype.prev = function(t2) {
    var e2 = this._root, r2 = null;
    if (t2.left !== null) {
      for (r2 = t2.left; r2.right; )
        r2 = r2.right;
      return r2;
    }
    for (var i = this._comparator; e2; ) {
      var n2 = i(t2.key, e2.key);
      if (n2 === 0)
        break;
      n2 < 0 ? e2 = e2.left : (r2 = e2, e2 = e2.right);
    }
    return r2;
  }, o.prototype.clear = function() {
    return this._root = null, this._size = 0, this;
  }, o.prototype.toList = function() {
    return Vt(this._root);
  }, o.prototype.load = function(t2, e2, r2) {
    e2 === void 0 && (e2 = []), r2 === void 0 && (r2 = false);
    var i = t2.length, n2 = this._comparator;
    if (r2 && et(t2, e2, 0, i - 1, n2), this._root === null)
      this._root = K(t2, e2, 0, i), this._size = i;
    else {
      var s = qt(this.toList(), Zt(t2, e2), n2);
      i = this._size + i, this._root = tt({ head: s }, 0, i);
    }
    return this;
  }, o.prototype.isEmpty = function() {
    return this._root === null;
  }, Object.defineProperty(o.prototype, "size", { get: function() {
    return this._size;
  }, enumerable: true, configurable: true }), Object.defineProperty(o.prototype, "root", { get: function() {
    return this._root;
  }, enumerable: true, configurable: true }), o.prototype.toString = function(t2) {
    t2 === void 0 && (t2 = function(r2) {
      return String(r2.key);
    });
    var e2 = [];
    return Q(this._root, "", true, function(r2) {
      return e2.push(r2);
    }, t2), e2.join("");
  }, o.prototype.update = function(t2, e2, r2) {
    var i = this._comparator, n2 = pt(t2, this._root, i), s = n2.left, a2 = n2.right;
    i(t2, e2) < 0 ? a2 = X(e2, r2, a2, i) : s = X(e2, r2, s, i), this._root = Gt(s, a2, i);
  }, o.prototype.split = function(t2) {
    return pt(t2, this._root, this._comparator);
  }, o.prototype[Symbol.iterator] = function() {
    var t2;
    return $t(this, function(e2) {
      switch (e2.label) {
        case 0:
          t2 = this.minNode(), e2.label = 1;
        case 1:
          return t2 ? [4, t2] : [3, 3];
        case 2:
          return e2.sent(), t2 = this.next(t2), [3, 1];
        case 3:
          return [2];
      }
    });
  }, o;
}();
function K(o, t2, e2, r2) {
  var i = r2 - e2;
  if (i > 0) {
    var n2 = e2 + Math.floor(i / 2), s = o[n2], a2 = t2[n2], u3 = new A(s, a2);
    return u3.left = K(o, t2, e2, n2), u3.right = K(o, t2, n2 + 1, r2), u3;
  }
  return null;
}
function Zt(o, t2) {
  for (var e2 = new A(null, null), r2 = e2, i = 0; i < o.length; i++)
    r2 = r2.next = new A(o[i], t2[i]);
  return r2.next = null, e2.next;
}
function Vt(o) {
  for (var t2 = o, e2 = [], r2 = false, i = new A(null, null), n2 = i; !r2; )
    t2 ? (e2.push(t2), t2 = t2.left) : e2.length > 0 ? (t2 = n2 = n2.next = e2.pop(), t2 = t2.right) : r2 = true;
  return n2.next = null, i.next;
}
function tt(o, t2, e2) {
  var r2 = e2 - t2;
  if (r2 > 0) {
    var i = t2 + Math.floor(r2 / 2), n2 = tt(o, t2, i), s = o.head;
    return s.left = n2, o.head = o.head.next, s.right = tt(o, i + 1, e2), s;
  }
  return null;
}
function qt(o, t2, e2) {
  for (var r2 = new A(null, null), i = r2, n2 = o, s = t2; n2 !== null && s !== null; )
    e2(n2.key, s.key) < 0 ? (i.next = n2, n2 = n2.next) : (i.next = s, s = s.next), i = i.next;
  return n2 !== null ? i.next = n2 : s !== null && (i.next = s), r2.next;
}
function et(o, t2, e2, r2, i) {
  if (!(e2 >= r2)) {
    for (var n2 = o[e2 + r2 >> 1], s = e2 - 1, a2 = r2 + 1; ; ) {
      do
        s++;
      while (i(o[s], n2) < 0);
      do
        a2--;
      while (i(o[a2], n2) > 0);
      if (s >= a2)
        break;
      var u3 = o[s];
      o[s] = o[a2], o[a2] = u3, u3 = t2[s], t2[s] = t2[a2], t2[a2] = u3;
    }
    et(o, t2, e2, a2, i), et(o, t2, a2 + 1, r2, i);
  }
}
function S(o, t2) {
  if (!(o instanceof t2))
    throw new TypeError("Cannot call a class as a function");
}
function gt(o, t2) {
  for (var e2 = 0; e2 < t2.length; e2++) {
    var r2 = t2[e2];
    r2.enumerable = r2.enumerable || false, r2.configurable = true, "value" in r2 && (r2.writable = true), Object.defineProperty(o, r2.key, r2);
  }
}
function b(o, t2, e2) {
  return t2 && gt(o.prototype, t2), e2 && gt(o, e2), o;
}
var z = function(t2, e2) {
  return t2.ll.x <= e2.x && e2.x <= t2.ur.x && t2.ll.y <= e2.y && e2.y <= t2.ur.y;
};
var rt = function(t2, e2) {
  if (e2.ur.x < t2.ll.x || t2.ur.x < e2.ll.x || e2.ur.y < t2.ll.y || t2.ur.y < e2.ll.y)
    return null;
  var r2 = t2.ll.x < e2.ll.x ? e2.ll.x : t2.ll.x, i = t2.ur.x < e2.ur.x ? t2.ur.x : e2.ur.x, n2 = t2.ll.y < e2.ll.y ? e2.ll.y : t2.ll.y, s = t2.ur.y < e2.ur.y ? t2.ur.y : e2.ur.y;
  return { ll: { x: r2, y: n2 }, ur: { x: i, y: s } };
};
var N = Number.EPSILON;
N === void 0 && (N = Math.pow(2, -52));
var jt = N * N;
var it = function(t2, e2) {
  if (-N < t2 && t2 < N && -N < e2 && e2 < N)
    return 0;
  var r2 = t2 - e2;
  return r2 * r2 < jt * t2 * e2 ? 0 : t2 < e2 ? -1 : 1;
};
var Ut = function() {
  function o() {
    S(this, o), this.reset();
  }
  return b(o, [{ key: "reset", value: function() {
    this.xRounder = new yt(), this.yRounder = new yt();
  } }, { key: "round", value: function(e2, r2) {
    return { x: this.xRounder.round(e2), y: this.yRounder.round(r2) };
  } }]), o;
}();
var yt = function() {
  function o() {
    S(this, o), this.tree = new J(), this.round(0);
  }
  return b(o, [{ key: "round", value: function(e2) {
    var r2 = this.tree.add(e2), i = this.tree.prev(r2);
    if (i !== null && it(r2.key, i.key) === 0)
      return this.tree.remove(e2), i.key;
    var n2 = this.tree.next(r2);
    return n2 !== null && it(r2.key, n2.key) === 0 ? (this.tree.remove(e2), n2.key) : e2;
  } }]), o;
}();
var G = new Ut();
var Z = function(t2, e2) {
  return t2.x * e2.y - t2.y * e2.x;
};
var vt = function(t2, e2) {
  return t2.x * e2.x + t2.y * e2.y;
};
var _t = function(t2, e2, r2) {
  var i = { x: e2.x - t2.x, y: e2.y - t2.y }, n2 = { x: r2.x - t2.x, y: r2.y - t2.y }, s = Z(i, n2);
  return it(s, 0);
};
var V = function(t2) {
  return Math.sqrt(vt(t2, t2));
};
var Wt = function(t2, e2, r2) {
  var i = { x: e2.x - t2.x, y: e2.y - t2.y }, n2 = { x: r2.x - t2.x, y: r2.y - t2.y };
  return Z(n2, i) / V(n2) / V(i);
};
var Yt = function(t2, e2, r2) {
  var i = { x: e2.x - t2.x, y: e2.y - t2.y }, n2 = { x: r2.x - t2.x, y: r2.y - t2.y };
  return vt(n2, i) / V(n2) / V(i);
};
var mt = function(t2, e2, r2) {
  return e2.y === 0 ? null : { x: t2.x + e2.x / e2.y * (r2 - t2.y), y: r2 };
};
var xt = function(t2, e2, r2) {
  return e2.x === 0 ? null : { x: r2, y: t2.y + e2.y / e2.x * (r2 - t2.x) };
};
var Ht = function(t2, e2, r2, i) {
  if (e2.x === 0)
    return xt(r2, i, t2.x);
  if (i.x === 0)
    return xt(t2, e2, r2.x);
  if (e2.y === 0)
    return mt(r2, i, t2.y);
  if (i.y === 0)
    return mt(t2, e2, r2.y);
  var n2 = Z(e2, i);
  if (n2 == 0)
    return null;
  var s = { x: r2.x - t2.x, y: r2.y - t2.y }, a2 = Z(s, e2) / n2, u3 = Z(s, i) / n2, l = t2.x + u3 * e2.x, h = r2.x + a2 * i.x, c = t2.y + u3 * e2.y, d2 = r2.y + a2 * i.y, f = (l + h) / 2, g2 = (c + d2) / 2;
  return { x: f, y: g2 };
};
var I = function() {
  b(o, null, [{ key: "compare", value: function(e2, r2) {
    var i = o.comparePoints(e2.point, r2.point);
    return i !== 0 ? i : (e2.point !== r2.point && e2.link(r2), e2.isLeft !== r2.isLeft ? e2.isLeft ? 1 : -1 : q2.compare(e2.segment, r2.segment));
  } }, { key: "comparePoints", value: function(e2, r2) {
    return e2.x < r2.x ? -1 : e2.x > r2.x ? 1 : e2.y < r2.y ? -1 : e2.y > r2.y ? 1 : 0;
  } }]);
  function o(t2, e2) {
    S(this, o), t2.events === void 0 ? t2.events = [this] : t2.events.push(this), this.point = t2, this.isLeft = e2;
  }
  return b(o, [{ key: "link", value: function(e2) {
    if (e2.point === this.point)
      throw new Error("Tried to link already linked events");
    for (var r2 = e2.point.events, i = 0, n2 = r2.length; i < n2; i++) {
      var s = r2[i];
      this.point.events.push(s), s.point = this.point;
    }
    this.checkForConsuming();
  } }, { key: "checkForConsuming", value: function() {
    for (var e2 = this.point.events.length, r2 = 0; r2 < e2; r2++) {
      var i = this.point.events[r2];
      if (i.segment.consumedBy === void 0)
        for (var n2 = r2 + 1; n2 < e2; n2++) {
          var s = this.point.events[n2];
          s.consumedBy === void 0 && i.otherSE.point.events === s.otherSE.point.events && i.segment.consume(s.segment);
        }
    }
  } }, { key: "getAvailableLinkedEvents", value: function() {
    for (var e2 = [], r2 = 0, i = this.point.events.length; r2 < i; r2++) {
      var n2 = this.point.events[r2];
      n2 !== this && !n2.segment.ringOut && n2.segment.isInResult() && e2.push(n2);
    }
    return e2;
  } }, { key: "getLeftmostComparator", value: function(e2) {
    var r2 = this, i = /* @__PURE__ */ new Map(), n2 = function(a2) {
      var u3 = a2.otherSE;
      i.set(a2, { sine: Wt(r2.point, e2.point, u3.point), cosine: Yt(r2.point, e2.point, u3.point) });
    };
    return function(s, a2) {
      i.has(s) || n2(s), i.has(a2) || n2(a2);
      var u3 = i.get(s), l = u3.sine, h = u3.cosine, c = i.get(a2), d2 = c.sine, f = c.cosine;
      return l >= 0 && d2 >= 0 ? h < f ? 1 : h > f ? -1 : 0 : l < 0 && d2 < 0 ? h < f ? -1 : h > f ? 1 : 0 : d2 < l ? -1 : d2 > l ? 1 : 0;
    };
  } }]), o;
}();
var Xt = 0;
var q2 = function() {
  b(o, null, [{ key: "compare", value: function(e2, r2) {
    var i = e2.leftSE.point.x, n2 = r2.leftSE.point.x, s = e2.rightSE.point.x, a2 = r2.rightSE.point.x;
    if (a2 < i)
      return 1;
    if (s < n2)
      return -1;
    var u3 = e2.leftSE.point.y, l = r2.leftSE.point.y, h = e2.rightSE.point.y, c = r2.rightSE.point.y;
    if (i < n2) {
      if (l < u3 && l < h)
        return 1;
      if (l > u3 && l > h)
        return -1;
      var d2 = e2.comparePoint(r2.leftSE.point);
      if (d2 < 0)
        return 1;
      if (d2 > 0)
        return -1;
      var f = r2.comparePoint(e2.rightSE.point);
      return f !== 0 ? f : -1;
    }
    if (i > n2) {
      if (u3 < l && u3 < c)
        return -1;
      if (u3 > l && u3 > c)
        return 1;
      var g2 = r2.comparePoint(e2.leftSE.point);
      if (g2 !== 0)
        return g2;
      var p2 = e2.comparePoint(r2.rightSE.point);
      return p2 < 0 ? 1 : p2 > 0 ? -1 : 1;
    }
    if (u3 < l)
      return -1;
    if (u3 > l)
      return 1;
    if (s < a2) {
      var y = r2.comparePoint(e2.rightSE.point);
      if (y !== 0)
        return y;
    }
    if (s > a2) {
      var m2 = e2.comparePoint(r2.rightSE.point);
      if (m2 < 0)
        return 1;
      if (m2 > 0)
        return -1;
    }
    if (s !== a2) {
      var x = h - u3, w2 = s - i, _2 = c - l, k2 = a2 - n2;
      if (x > w2 && _2 < k2)
        return 1;
      if (x < w2 && _2 > k2)
        return -1;
    }
    return s > a2 ? 1 : s < a2 || h < c ? -1 : h > c ? 1 : e2.id < r2.id ? -1 : e2.id > r2.id ? 1 : 0;
  } }]);
  function o(t2, e2, r2, i) {
    S(this, o), this.id = ++Xt, this.leftSE = t2, t2.segment = this, t2.otherSE = e2, this.rightSE = e2, e2.segment = this, e2.otherSE = t2, this.rings = r2, this.windings = i;
  }
  return b(o, [{ key: "replaceRightSE", value: function(e2) {
    this.rightSE = e2, this.rightSE.segment = this, this.rightSE.otherSE = this.leftSE, this.leftSE.otherSE = this.rightSE;
  } }, { key: "bbox", value: function() {
    var e2 = this.leftSE.point.y, r2 = this.rightSE.point.y;
    return { ll: { x: this.leftSE.point.x, y: e2 < r2 ? e2 : r2 }, ur: { x: this.rightSE.point.x, y: e2 > r2 ? e2 : r2 } };
  } }, { key: "vector", value: function() {
    return { x: this.rightSE.point.x - this.leftSE.point.x, y: this.rightSE.point.y - this.leftSE.point.y };
  } }, { key: "isAnEndpoint", value: function(e2) {
    return e2.x === this.leftSE.point.x && e2.y === this.leftSE.point.y || e2.x === this.rightSE.point.x && e2.y === this.rightSE.point.y;
  } }, { key: "comparePoint", value: function(e2) {
    if (this.isAnEndpoint(e2))
      return 0;
    var r2 = this.leftSE.point, i = this.rightSE.point, n2 = this.vector();
    if (r2.x === i.x)
      return e2.x === r2.x ? 0 : e2.x < r2.x ? 1 : -1;
    var s = (e2.y - r2.y) / n2.y, a2 = r2.x + s * n2.x;
    if (e2.x === a2)
      return 0;
    var u3 = (e2.x - r2.x) / n2.x, l = r2.y + u3 * n2.y;
    return e2.y === l ? 0 : e2.y < l ? -1 : 1;
  } }, { key: "getIntersection", value: function(e2) {
    var r2 = this.bbox(), i = e2.bbox(), n2 = rt(r2, i);
    if (n2 === null)
      return null;
    var s = this.leftSE.point, a2 = this.rightSE.point, u3 = e2.leftSE.point, l = e2.rightSE.point, h = z(r2, u3) && this.comparePoint(u3) === 0, c = z(i, s) && e2.comparePoint(s) === 0, d2 = z(r2, l) && this.comparePoint(l) === 0, f = z(i, a2) && e2.comparePoint(a2) === 0;
    if (c && h)
      return f && !d2 ? a2 : !f && d2 ? l : null;
    if (c)
      return d2 && s.x === l.x && s.y === l.y ? null : s;
    if (h)
      return f && a2.x === u3.x && a2.y === u3.y ? null : u3;
    if (f && d2)
      return null;
    if (f)
      return a2;
    if (d2)
      return l;
    var g2 = Ht(s, this.vector(), u3, e2.vector());
    return g2 === null || !z(n2, g2) ? null : G.round(g2.x, g2.y);
  } }, { key: "split", value: function(e2) {
    var r2 = [], i = e2.events !== void 0, n2 = new I(e2, true), s = new I(e2, false), a2 = this.rightSE;
    this.replaceRightSE(s), r2.push(s), r2.push(n2);
    var u3 = new o(n2, a2, this.rings.slice(), this.windings.slice());
    return I.comparePoints(u3.leftSE.point, u3.rightSE.point) > 0 && u3.swapEvents(), I.comparePoints(this.leftSE.point, this.rightSE.point) > 0 && this.swapEvents(), i && (n2.checkForConsuming(), s.checkForConsuming()), r2;
  } }, { key: "swapEvents", value: function() {
    var e2 = this.rightSE;
    this.rightSE = this.leftSE, this.leftSE = e2, this.leftSE.isLeft = true, this.rightSE.isLeft = false;
    for (var r2 = 0, i = this.windings.length; r2 < i; r2++)
      this.windings[r2] *= -1;
  } }, { key: "consume", value: function(e2) {
    for (var r2 = this, i = e2; r2.consumedBy; )
      r2 = r2.consumedBy;
    for (; i.consumedBy; )
      i = i.consumedBy;
    var n2 = o.compare(r2, i);
    if (n2 !== 0) {
      if (n2 > 0) {
        var s = r2;
        r2 = i, i = s;
      }
      if (r2.prev === i) {
        var a2 = r2;
        r2 = i, i = a2;
      }
      for (var u3 = 0, l = i.rings.length; u3 < l; u3++) {
        var h = i.rings[u3], c = i.windings[u3], d2 = r2.rings.indexOf(h);
        d2 === -1 ? (r2.rings.push(h), r2.windings.push(c)) : r2.windings[d2] += c;
      }
      i.rings = null, i.windings = null, i.consumedBy = r2, i.leftSE.consumedBy = r2.leftSE, i.rightSE.consumedBy = r2.rightSE;
    }
  } }, { key: "prevInResult", value: function() {
    return this._prevInResult !== void 0 ? this._prevInResult : (this.prev ? this.prev.isInResult() ? this._prevInResult = this.prev : this._prevInResult = this.prev.prevInResult() : this._prevInResult = null, this._prevInResult);
  } }, { key: "beforeState", value: function() {
    if (this._beforeState !== void 0)
      return this._beforeState;
    if (!this.prev)
      this._beforeState = { rings: [], windings: [], multiPolys: [] };
    else {
      var e2 = this.prev.consumedBy || this.prev;
      this._beforeState = e2.afterState();
    }
    return this._beforeState;
  } }, { key: "afterState", value: function() {
    if (this._afterState !== void 0)
      return this._afterState;
    var e2 = this.beforeState();
    this._afterState = { rings: e2.rings.slice(0), windings: e2.windings.slice(0), multiPolys: [] };
    for (var r2 = this._afterState.rings, i = this._afterState.windings, n2 = this._afterState.multiPolys, s = 0, a2 = this.rings.length; s < a2; s++) {
      var u3 = this.rings[s], l = this.windings[s], h = r2.indexOf(u3);
      h === -1 ? (r2.push(u3), i.push(l)) : i[h] += l;
    }
    for (var c = [], d2 = [], f = 0, g2 = r2.length; f < g2; f++)
      if (i[f] !== 0) {
        var p2 = r2[f], y = p2.poly;
        if (d2.indexOf(y) === -1)
          if (p2.isExterior)
            c.push(y);
          else {
            d2.indexOf(y) === -1 && d2.push(y);
            var m2 = c.indexOf(p2.poly);
            m2 !== -1 && c.splice(m2, 1);
          }
      }
    for (var x = 0, w2 = c.length; x < w2; x++) {
      var _2 = c[x].multiPoly;
      n2.indexOf(_2) === -1 && n2.push(_2);
    }
    return this._afterState;
  } }, { key: "isInResult", value: function() {
    if (this.consumedBy)
      return false;
    if (this._isInResult !== void 0)
      return this._isInResult;
    var e2 = this.beforeState().multiPolys, r2 = this.afterState().multiPolys;
    switch (L.type) {
      case "union": {
        var i = e2.length === 0, n2 = r2.length === 0;
        this._isInResult = i !== n2;
        break;
      }
      case "intersection": {
        var s, a2;
        e2.length < r2.length ? (s = e2.length, a2 = r2.length) : (s = r2.length, a2 = e2.length), this._isInResult = a2 === L.numMultiPolys && s < a2;
        break;
      }
      case "xor": {
        var u3 = Math.abs(e2.length - r2.length);
        this._isInResult = u3 % 2 === 1;
        break;
      }
      case "difference": {
        var l = function(c) {
          return c.length === 1 && c[0].isSubject;
        };
        this._isInResult = l(e2) !== l(r2);
        break;
      }
      default:
        throw new Error("Unrecognized operation type found ".concat(L.type));
    }
    return this._isInResult;
  } }], [{ key: "fromRing", value: function(e2, r2, i) {
    var n2, s, a2, u3 = I.comparePoints(e2, r2);
    if (u3 < 0)
      n2 = e2, s = r2, a2 = 1;
    else if (u3 > 0)
      n2 = r2, s = e2, a2 = -1;
    else
      throw new Error("Tried to create degenerate segment at [".concat(e2.x, ", ").concat(e2.y, "]"));
    var l = new I(n2, true), h = new I(s, false);
    return new o(l, h, [i], [a2]);
  } }]), o;
}();
var bt = function() {
  function o(t2, e2, r2) {
    if (S(this, o), !Array.isArray(t2) || t2.length === 0)
      throw new Error("Input geometry is not a valid Polygon or MultiPolygon");
    if (this.poly = e2, this.isExterior = r2, this.segments = [], typeof t2[0][0] != "number" || typeof t2[0][1] != "number")
      throw new Error("Input geometry is not a valid Polygon or MultiPolygon");
    var i = G.round(t2[0][0], t2[0][1]);
    this.bbox = { ll: { x: i.x, y: i.y }, ur: { x: i.x, y: i.y } };
    for (var n2 = i, s = 1, a2 = t2.length; s < a2; s++) {
      if (typeof t2[s][0] != "number" || typeof t2[s][1] != "number")
        throw new Error("Input geometry is not a valid Polygon or MultiPolygon");
      var u3 = G.round(t2[s][0], t2[s][1]);
      u3.x === n2.x && u3.y === n2.y || (this.segments.push(q2.fromRing(n2, u3, this)), u3.x < this.bbox.ll.x && (this.bbox.ll.x = u3.x), u3.y < this.bbox.ll.y && (this.bbox.ll.y = u3.y), u3.x > this.bbox.ur.x && (this.bbox.ur.x = u3.x), u3.y > this.bbox.ur.y && (this.bbox.ur.y = u3.y), n2 = u3);
    }
    (i.x !== n2.x || i.y !== n2.y) && this.segments.push(q2.fromRing(n2, i, this));
  }
  return b(o, [{ key: "getSweepEvents", value: function() {
    for (var e2 = [], r2 = 0, i = this.segments.length; r2 < i; r2++) {
      var n2 = this.segments[r2];
      e2.push(n2.leftSE), e2.push(n2.rightSE);
    }
    return e2;
  } }]), o;
}();
var Qt = function() {
  function o(t2, e2) {
    if (S(this, o), !Array.isArray(t2))
      throw new Error("Input geometry is not a valid Polygon or MultiPolygon");
    this.exteriorRing = new bt(t2[0], this, true), this.bbox = { ll: { x: this.exteriorRing.bbox.ll.x, y: this.exteriorRing.bbox.ll.y }, ur: { x: this.exteriorRing.bbox.ur.x, y: this.exteriorRing.bbox.ur.y } }, this.interiorRings = [];
    for (var r2 = 1, i = t2.length; r2 < i; r2++) {
      var n2 = new bt(t2[r2], this, false);
      n2.bbox.ll.x < this.bbox.ll.x && (this.bbox.ll.x = n2.bbox.ll.x), n2.bbox.ll.y < this.bbox.ll.y && (this.bbox.ll.y = n2.bbox.ll.y), n2.bbox.ur.x > this.bbox.ur.x && (this.bbox.ur.x = n2.bbox.ur.x), n2.bbox.ur.y > this.bbox.ur.y && (this.bbox.ur.y = n2.bbox.ur.y), this.interiorRings.push(n2);
    }
    this.multiPoly = e2;
  }
  return b(o, [{ key: "getSweepEvents", value: function() {
    for (var e2 = this.exteriorRing.getSweepEvents(), r2 = 0, i = this.interiorRings.length; r2 < i; r2++)
      for (var n2 = this.interiorRings[r2].getSweepEvents(), s = 0, a2 = n2.length; s < a2; s++)
        e2.push(n2[s]);
    return e2;
  } }]), o;
}();
var wt = function() {
  function o(t2, e2) {
    if (S(this, o), !Array.isArray(t2))
      throw new Error("Input geometry is not a valid Polygon or MultiPolygon");
    try {
      typeof t2[0][0][0] == "number" && (t2 = [t2]);
    } catch (s) {
    }
    this.polys = [], this.bbox = { ll: { x: Number.POSITIVE_INFINITY, y: Number.POSITIVE_INFINITY }, ur: { x: Number.NEGATIVE_INFINITY, y: Number.NEGATIVE_INFINITY } };
    for (var r2 = 0, i = t2.length; r2 < i; r2++) {
      var n2 = new Qt(t2[r2], this);
      n2.bbox.ll.x < this.bbox.ll.x && (this.bbox.ll.x = n2.bbox.ll.x), n2.bbox.ll.y < this.bbox.ll.y && (this.bbox.ll.y = n2.bbox.ll.y), n2.bbox.ur.x > this.bbox.ur.x && (this.bbox.ur.x = n2.bbox.ur.x), n2.bbox.ur.y > this.bbox.ur.y && (this.bbox.ur.y = n2.bbox.ur.y), this.polys.push(n2);
    }
    this.isSubject = e2;
  }
  return b(o, [{ key: "getSweepEvents", value: function() {
    for (var e2 = [], r2 = 0, i = this.polys.length; r2 < i; r2++)
      for (var n2 = this.polys[r2].getSweepEvents(), s = 0, a2 = n2.length; s < a2; s++)
        e2.push(n2[s]);
    return e2;
  } }]), o;
}();
var Jt = function() {
  b(o, null, [{ key: "factory", value: function(e2) {
    for (var r2 = [], i = 0, n2 = e2.length; i < n2; i++) {
      var s = e2[i];
      if (!(!s.isInResult() || s.ringOut)) {
        for (var a2 = null, u3 = s.leftSE, l = s.rightSE, h = [u3], c = u3.point, d2 = []; a2 = u3, u3 = l, h.push(u3), u3.point !== c; )
          for (; ; ) {
            var f = u3.getAvailableLinkedEvents();
            if (f.length === 0) {
              var g2 = h[0].point, p2 = h[h.length - 1].point;
              throw new Error("Unable to complete output ring starting at [".concat(g2.x, ",") + " ".concat(g2.y, "]. Last matching segment found ends at") + " [".concat(p2.x, ", ").concat(p2.y, "]."));
            }
            if (f.length === 1) {
              l = f[0].otherSE;
              break;
            }
            for (var y = null, m2 = 0, x = d2.length; m2 < x; m2++)
              if (d2[m2].point === u3.point) {
                y = m2;
                break;
              }
            if (y !== null) {
              var w2 = d2.splice(y)[0], _2 = h.splice(w2.index);
              _2.unshift(_2[0].otherSE), r2.push(new o(_2.reverse()));
              continue;
            }
            d2.push({ index: h.length, point: u3.point });
            var k2 = u3.getLeftmostComparator(a2);
            l = f.sort(k2)[0].otherSE;
            break;
          }
        r2.push(new o(h));
      }
    }
    return r2;
  } }]);
  function o(t2) {
    S(this, o), this.events = t2;
    for (var e2 = 0, r2 = t2.length; e2 < r2; e2++)
      t2[e2].segment.ringOut = this;
    this.poly = null;
  }
  return b(o, [{ key: "getGeom", value: function() {
    for (var e2 = this.events[0].point, r2 = [e2], i = 1, n2 = this.events.length - 1; i < n2; i++) {
      var s = this.events[i].point, a2 = this.events[i + 1].point;
      _t(s, e2, a2) !== 0 && (r2.push(s), e2 = s);
    }
    if (r2.length === 1)
      return null;
    var u3 = r2[0], l = r2[1];
    _t(u3, e2, l) === 0 && r2.shift(), r2.push(r2[0]);
    for (var h = this.isExteriorRing() ? 1 : -1, c = this.isExteriorRing() ? 0 : r2.length - 1, d2 = this.isExteriorRing() ? r2.length : -1, f = [], g2 = c; g2 != d2; g2 += h)
      f.push([r2[g2].x, r2[g2].y]);
    return f;
  } }, { key: "isExteriorRing", value: function() {
    if (this._isExteriorRing === void 0) {
      var e2 = this.enclosingRing();
      this._isExteriorRing = e2 ? !e2.isExteriorRing() : true;
    }
    return this._isExteriorRing;
  } }, { key: "enclosingRing", value: function() {
    return this._enclosingRing === void 0 && (this._enclosingRing = this._calcEnclosingRing()), this._enclosingRing;
  } }, { key: "_calcEnclosingRing", value: function() {
    for (var e2 = this.events[0], r2 = 1, i = this.events.length; r2 < i; r2++) {
      var n2 = this.events[r2];
      I.compare(e2, n2) > 0 && (e2 = n2);
    }
    for (var s = e2.segment.prevInResult(), a2 = s ? s.prevInResult() : null; ; ) {
      if (!s)
        return null;
      if (!a2)
        return s.ringOut;
      if (a2.ringOut !== s.ringOut)
        return a2.ringOut.enclosingRing() !== s.ringOut ? s.ringOut : s.ringOut.enclosingRing();
      s = a2.prevInResult(), a2 = s ? s.prevInResult() : null;
    }
  } }]), o;
}();
var St = function() {
  function o(t2) {
    S(this, o), this.exteriorRing = t2, t2.poly = this, this.interiorRings = [];
  }
  return b(o, [{ key: "addInterior", value: function(e2) {
    this.interiorRings.push(e2), e2.poly = this;
  } }, { key: "getGeom", value: function() {
    var e2 = [this.exteriorRing.getGeom()];
    if (e2[0] === null)
      return null;
    for (var r2 = 0, i = this.interiorRings.length; r2 < i; r2++) {
      var n2 = this.interiorRings[r2].getGeom();
      n2 !== null && e2.push(n2);
    }
    return e2;
  } }]), o;
}();
var Kt = function() {
  function o(t2) {
    S(this, o), this.rings = t2, this.polys = this._composePolys(t2);
  }
  return b(o, [{ key: "getGeom", value: function() {
    for (var e2 = [], r2 = 0, i = this.polys.length; r2 < i; r2++) {
      var n2 = this.polys[r2].getGeom();
      n2 !== null && e2.push(n2);
    }
    return e2;
  } }, { key: "_composePolys", value: function(e2) {
    for (var r2 = [], i = 0, n2 = e2.length; i < n2; i++) {
      var s = e2[i];
      if (!s.poly)
        if (s.isExteriorRing())
          r2.push(new St(s));
        else {
          var a2 = s.enclosingRing();
          a2.poly || r2.push(new St(a2)), a2.poly.addInterior(s);
        }
    }
    return r2;
  } }]), o;
}();
var te = function() {
  function o(t2) {
    var e2 = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : q2.compare;
    S(this, o), this.queue = t2, this.tree = new J(e2), this.segments = [];
  }
  return b(o, [{ key: "process", value: function(e2) {
    var r2 = e2.segment, i = [];
    if (e2.consumedBy)
      return e2.isLeft ? this.queue.remove(e2.otherSE) : this.tree.remove(r2), i;
    var n2 = e2.isLeft ? this.tree.insert(r2) : this.tree.find(r2);
    if (!n2)
      throw new Error("Unable to find segment #".concat(r2.id, " ") + "[".concat(r2.leftSE.point.x, ", ").concat(r2.leftSE.point.y, "] -> ") + "[".concat(r2.rightSE.point.x, ", ").concat(r2.rightSE.point.y, "] ") + "in SweepLine tree. Please submit a bug report.");
    for (var s = n2, a2 = n2, u3 = void 0, l = void 0; u3 === void 0; )
      s = this.tree.prev(s), s === null ? u3 = null : s.key.consumedBy === void 0 && (u3 = s.key);
    for (; l === void 0; )
      a2 = this.tree.next(a2), a2 === null ? l = null : a2.key.consumedBy === void 0 && (l = a2.key);
    if (e2.isLeft) {
      var h = null;
      if (u3) {
        var c = u3.getIntersection(r2);
        if (c !== null && (r2.isAnEndpoint(c) || (h = c), !u3.isAnEndpoint(c)))
          for (var d2 = this._splitSafely(u3, c), f = 0, g2 = d2.length; f < g2; f++)
            i.push(d2[f]);
      }
      var p2 = null;
      if (l) {
        var y = l.getIntersection(r2);
        if (y !== null && (r2.isAnEndpoint(y) || (p2 = y), !l.isAnEndpoint(y)))
          for (var m2 = this._splitSafely(l, y), x = 0, w2 = m2.length; x < w2; x++)
            i.push(m2[x]);
      }
      if (h !== null || p2 !== null) {
        var _2 = null;
        if (h === null)
          _2 = p2;
        else if (p2 === null)
          _2 = h;
        else {
          var k2 = I.comparePoints(h, p2);
          _2 = k2 <= 0 ? h : p2;
        }
        this.queue.remove(r2.rightSE), i.push(r2.rightSE);
        for (var D2 = r2.split(_2), M = 0, R = D2.length; M < R; M++)
          i.push(D2[M]);
      }
      i.length > 0 ? (this.tree.remove(r2), i.push(e2)) : (this.segments.push(r2), r2.prev = u3);
    } else {
      if (u3 && l) {
        var E2 = u3.getIntersection(l);
        if (E2 !== null) {
          if (!u3.isAnEndpoint(E2))
            for (var T2 = this._splitSafely(u3, E2), F = 0, U = T2.length; F < U; F++)
              i.push(T2[F]);
          if (!l.isAnEndpoint(E2))
            for (var B2 = this._splitSafely(l, E2), $ = 0, W2 = B2.length; $ < W2; $++)
              i.push(B2[$]);
        }
      }
      this.tree.remove(r2);
    }
    return i;
  } }, { key: "_splitSafely", value: function(e2, r2) {
    this.tree.remove(e2);
    var i = e2.rightSE;
    this.queue.remove(i);
    var n2 = e2.split(r2);
    return n2.push(i), e2.consumedBy === void 0 && this.tree.insert(e2), n2;
  } }]), o;
}();
var Et = typeof process != "undefined" && process.env.POLYGON_CLIPPING_MAX_QUEUE_SIZE || 1e6;
var ee2 = typeof process != "undefined" && process.env.POLYGON_CLIPPING_MAX_SWEEPLINE_SEGMENTS || 1e6;
var re = function() {
  function o() {
    S(this, o);
  }
  return b(o, [{ key: "run", value: function(e2, r2, i) {
    L.type = e2, G.reset();
    for (var n2 = [new wt(r2, true)], s = 0, a2 = i.length; s < a2; s++)
      n2.push(new wt(i[s], false));
    if (L.numMultiPolys = n2.length, L.type === "difference")
      for (var u3 = n2[0], l = 1; l < n2.length; )
        rt(n2[l].bbox, u3.bbox) !== null ? l++ : n2.splice(l, 1);
    if (L.type === "intersection") {
      for (var h = 0, c = n2.length; h < c; h++)
        for (var d2 = n2[h], f = h + 1, g2 = n2.length; f < g2; f++)
          if (rt(d2.bbox, n2[f].bbox) === null)
            return [];
    }
    for (var p2 = new J(I.compare), y = 0, m2 = n2.length; y < m2; y++)
      for (var x = n2[y].getSweepEvents(), w2 = 0, _2 = x.length; w2 < _2; w2++)
        if (p2.insert(x[w2]), p2.size > Et)
          throw new Error("Infinite loop when putting segment endpoints in a priority queue (queue size too big). Please file a bug report.");
    for (var k2 = new te(p2), D2 = p2.size, M = p2.pop(); M; ) {
      var R = M.key;
      if (p2.size === D2) {
        var E2 = R.segment;
        throw new Error("Unable to pop() ".concat(R.isLeft ? "left" : "right", " SweepEvent ") + "[".concat(R.point.x, ", ").concat(R.point.y, "] from segment #").concat(E2.id, " ") + "[".concat(E2.leftSE.point.x, ", ").concat(E2.leftSE.point.y, "] -> ") + "[".concat(E2.rightSE.point.x, ", ").concat(E2.rightSE.point.y, "] from queue. ") + "Please file a bug report.");
      }
      if (p2.size > Et)
        throw new Error("Infinite loop when passing sweep line over endpoints (queue size too big). Please file a bug report.");
      if (k2.segments.length > ee2)
        throw new Error("Infinite loop when passing sweep line over endpoints (too many sweep line segments). Please file a bug report.");
      for (var T2 = k2.process(R), F = 0, U = T2.length; F < U; F++) {
        var B2 = T2[F];
        B2.consumedBy === void 0 && p2.insert(B2);
      }
      D2 = p2.size, M = p2.pop();
    }
    G.reset();
    var $ = Jt.factory(k2.segments), W2 = new Kt($);
    return W2.getGeom();
  } }]), o;
}();
var L = new re();
var ie = function(t2) {
  for (var e2 = arguments.length, r2 = new Array(e2 > 1 ? e2 - 1 : 0), i = 1; i < e2; i++)
    r2[i - 1] = arguments[i];
  return L.run("union", t2, r2);
};
var ne = function(t2) {
  for (var e2 = arguments.length, r2 = new Array(e2 > 1 ? e2 - 1 : 0), i = 1; i < e2; i++)
    r2[i - 1] = arguments[i];
  return L.run("intersection", t2, r2);
};
var oe = function(t2) {
  for (var e2 = arguments.length, r2 = new Array(e2 > 1 ? e2 - 1 : 0), i = 1; i < e2; i++)
    r2[i - 1] = arguments[i];
  return L.run("xor", t2, r2);
};
var se = function(t2) {
  for (var e2 = arguments.length, r2 = new Array(e2 > 1 ? e2 - 1 : 0), i = 1; i < e2; i++)
    r2[i - 1] = arguments[i];
  return L.run("difference", t2, r2);
};
var ae = { union: ie, intersection: ne, xor: oe, difference: se };
function ue(o, t2, e2) {
  e2 === void 0 && (e2 = {});
  var r2 = ft(o), i = ft(t2), n2 = ae.intersection(r2.coordinates, i.coordinates);
  return n2.length === 0 ? null : n2.length === 1 ? H(n2[0], e2.properties) : Bt(n2, e2.properties);
}
var v = { mergeArray(o, t2) {
  if (t2.length < 5e4)
    o.push.apply(o, t2);
  else
    for (let e2 = 0, r2 = t2.length; e2 < r2; e2 += 1)
      o.push(t2[e2]);
}, now: Date.now || function() {
  return (/* @__PURE__ */ new Date()).getTime();
}, bind(o, t2) {
  return o.bind ? o.bind(t2) : function() {
    return o.apply(t2, arguments);
  };
}, forEach(o, t2, e2) {
  if (o.forEach)
    return o.forEach(t2, e2);
  for (let r2 = 0, i = o.length; r2 < i; r2++)
    t2.call(e2, o[r2], r2);
}, map(o, t2, e2) {
  if (o.map)
    return o.map(t2, e2);
  const r2 = [];
  for (let i = 0, n2 = o.length; i < n2; i++)
    r2[i] = t2.call(e2, o[i], i);
  return r2;
}, merge(o, t2) {
  if (t2.length < 5e4)
    Array.prototype.push.apply(o, t2);
  else
    for (let e2 = 0, r2 = t2.length; e2 < r2; e2 += 1)
      o.push(t2[e2]);
}, arrayIndexOf(o, t2, e2) {
  if (o.indexOf)
    return o.indexOf(t2, e2);
  let r2, i = o, n2 = i.length >>> 0;
  if (n2 === 0)
    return -1;
  const s = 0 | e2;
  if (s >= n2)
    return -1;
  for (r2 = Math.max(s >= 0 ? s : n2 - Math.abs(s), 0); r2 < n2; ) {
    if (r2 in i && i[r2] === t2)
      return r2;
    r2++;
  }
  return -1;
}, extend(o) {
  return o || (o = {}), this.extendObjs(o, Array.prototype.slice.call(arguments, 1));
}, extendObjs(o, t2) {
  o || (o = {});
  for (let e2 = 0, r2 = t2.length; e2 < r2; e2++) {
    const i = t2[e2];
    if (i)
      for (const n2 in i)
        i.hasOwnProperty(n2) && (o[n2] = i[n2]);
  }
  return o;
}, debounce(o, t2, e2) {
  let r2, i, n2, s, a2, u3 = function() {
    const l = v.now() - s;
    l < t2 && l >= 0 ? r2 = setTimeout(u3, t2 - l) : (r2 = null, e2 || (a2 = o.apply(n2, i), r2 || (n2 = i = null)));
  };
  return function() {
    n2 = this, i = arguments, s = v.now();
    const l = e2 && !r2;
    return r2 || (r2 = setTimeout(u3, t2)), l && (a2 = o.apply(n2, i), n2 = i = null), a2;
  };
}, throttle(o, t2, e2) {
  let r2, i, n2, s = null, a2 = 0;
  e2 || (e2 = {});
  const u3 = function() {
    a2 = e2.leading === false ? 0 : v.now(), s = null, n2 = o.apply(r2, i), s || (r2 = i = null);
  };
  return function() {
    const l = v.now();
    a2 || e2.leading !== false || (a2 = l);
    const h = t2 - (l - a2);
    return r2 = this, i = arguments, h <= 0 || h > t2 ? (s && (clearTimeout(s), s = null), a2 = l, n2 = o.apply(r2, i), s || (r2 = i = null)) : s || e2.trailing === false || (s = setTimeout(u3, h)), n2;
  };
}, escapeHtml(o) {
  const t2 = { "&": "&amp;", "<": "&lt;", ">": "&gt;", '"': "&quot;", "'": "&#x27;", "`": "&#x60;" };
  return `${o}`.replace(/[&<>"']/g, function(e2) {
    return t2[e2];
  });
} };
var nt = { BBRFLAG: { I: 1, S: 2 }, ADCODES: { COUNTRY: 1e5 } };
function le(o) {
  return o;
}
function he(o) {
  if (o == null)
    return le;
  var t2, e2, r2 = o.scale[0], i = o.scale[1], n2 = o.translate[0], s = o.translate[1];
  return function(a2, u3) {
    u3 || (t2 = e2 = 0);
    var l = 2, h = a2.length, c = new Array(h);
    for (c[0] = (t2 += a2[0]) * r2 + n2, c[1] = (e2 += a2[1]) * i + s; l < h; )
      c[l] = a2[l], ++l;
    return c;
  };
}
function ce(o, t2) {
  for (var e2, r2 = o.length, i = r2 - t2; i < --r2; )
    e2 = o[i], o[i++] = o[r2], o[r2] = e2;
}
function de(o, t2) {
  return typeof t2 == "string" && (t2 = o.objects[t2]), t2.type === "GeometryCollection" ? { type: "FeatureCollection", features: t2.geometries.map(function(e2) {
    return Lt(o, e2);
  }) } : Lt(o, t2);
}
function Lt(o, t2) {
  var e2 = t2.id, r2 = t2.bbox, i = t2.properties == null ? {} : t2.properties, n2 = fe(o, t2);
  return e2 == null && r2 == null ? { type: "Feature", properties: i, geometry: n2 } : r2 == null ? { type: "Feature", id: e2, properties: i, geometry: n2 } : { type: "Feature", id: e2, bbox: r2, properties: i, geometry: n2 };
}
function fe(o, t2) {
  var e2 = he(o.transform), r2 = o.arcs;
  function i(h, c) {
    c.length && c.pop();
    for (var d2 = r2[h < 0 ? ~h : h], f = 0, g2 = d2.length; f < g2; ++f)
      c.push(e2(d2[f], f));
    h < 0 && ce(c, g2);
  }
  function n2(h) {
    return e2(h);
  }
  function s(h) {
    for (var c = [], d2 = 0, f = h.length; d2 < f; ++d2)
      i(h[d2], c);
    return c.length < 2 && c.push(c[0]), c;
  }
  function a2(h) {
    for (var c = s(h); c.length < 4; )
      c.push(c[0]);
    return c;
  }
  function u3(h) {
    return h.map(a2);
  }
  function l(h) {
    var c = h.type, d2;
    switch (c) {
      case "GeometryCollection":
        return { type: c, geometries: h.geometries.map(l) };
      case "Point":
        d2 = n2(h.coordinates);
        break;
      case "MultiPoint":
        d2 = h.coordinates.map(n2);
        break;
      case "LineString":
        d2 = s(h.arcs);
        break;
      case "MultiLineString":
        d2 = h.arcs.map(s);
        break;
      case "Polygon":
        d2 = u3(h.arcs);
        break;
      case "MultiPolygon":
        d2 = h.arcs.map(u3);
        break;
      default:
        return null;
    }
    return { type: c, coordinates: d2 };
  }
  return l(t2);
}
function pe(o, t2) {
  let e2, r2, i, n2, s = o;
  e2 = t2[t2.length - 2];
  for (let a2 = 0, u3 = t2.length - 1; a2 < u3; a2++) {
    r2 = t2[a2];
    const l = s;
    s = [], i = l[l.length - 1];
    for (let h = 0, c = l.length; h < c; h++)
      n2 = l[h], ot(n2, e2, r2) ? (ot(i, e2, r2) || s.push(kt(e2, r2, i, n2)), s.push(n2)) : ot(i, e2, r2) && s.push(kt(e2, r2, i, n2)), i = n2;
    e2 = r2;
  }
  return s.length < 3 ? [] : (s.push(s[0]), s);
}
function ge(o, t2, e2) {
  const r2 = (e2[1] - t2[1]) / (e2[0] - t2[0]) * (o[0] - t2[0]) + t2[1];
  return Math.abs(r2 - o[1]) < 1e-6 && o[0] >= t2[0] && o[0] <= e2[0];
}
function ye(o, t2) {
  for (let e2 = 0, r2 = t2.length; e2 < r2 - 1; e2++)
    if (ge(o, t2[e2], t2[e2 + 1]))
      return true;
  return false;
}
function ve(o, t2) {
  let e2 = false;
  for (let r2 = o[0], i = o[1], n2 = 0, s = t2.length, a2 = s - 1; n2 < s; a2 = n2++) {
    const u3 = t2[n2][0], l = t2[n2][1], h = t2[a2][0], c = t2[a2][1];
    l > i != c > i && r2 < (h - u3) * (i - l) / (c - l) + u3 && (e2 = !e2);
  }
  return e2;
}
function _e(o, t2, e2) {
  let r2, i = t2[0], n2 = t2[1];
  const s = e2[0] - i, a2 = e2[1] - n2, u3 = s * s + a2 * a2;
  return u3 > 0 && (r2 = ((o[0] - i) * s + (o[1] - n2) * a2) / u3, r2 > 1 ? (i = e2[0], n2 = e2[1]) : r2 > 0 && (i += s * r2, n2 += a2 * r2)), [i, n2];
}
function me(o, t2, e2) {
  const r2 = _e(o, t2, e2), i = o[0] - r2[0], n2 = o[1] - r2[1];
  return i * i + n2 * n2;
}
function xe(o, t2) {
  let e2 = Number.MAX_VALUE;
  for (let r2 = 0, i = t2.length; r2 < i - 1; r2++) {
    const n2 = me(o, t2[r2], t2[r2 + 1]);
    n2 < e2 && (e2 = n2);
  }
  return e2;
}
function ot(o, t2, e2) {
  return (e2[0] - t2[0]) * (o[1] - t2[1]) > (e2[1] - t2[1]) * (o[0] - t2[0]);
}
function kt(o, t2, e2, r2) {
  const i = [o[0] - t2[0], o[1] - t2[1]], n2 = [e2[0] - r2[0], e2[1] - r2[1]], s = o[0] * t2[1] - o[1] * t2[0], a2 = e2[0] * r2[1] - e2[1] * r2[0], u3 = 1 / (i[0] * n2[1] - i[1] * n2[0]);
  return [(s * n2[0] - a2 * i[0]) * u3, (s * n2[1] - a2 * i[1]) * u3];
}
var j = { sqClosestDistanceToPolygon: xe, pointOnPolygon: ye, pointInPolygon: ve, polygonClip: pe };
var st = nt.BBRFLAG;
var at = [];
function be(o, t2) {
  const e2 = [];
  for (let r2 = 0, i = o.length; r2 < i; r2++) {
    const n2 = o[r2].split("-");
    let s = n2[0], a2 = n2.length > 1 ? n2[1] : s;
    s = parseInt(s, t2), a2 = parseInt(a2, t2);
    for (let u3 = s; u3 <= a2; u3++)
      e2.push(u3);
  }
  return e2;
}
function It(o, t2, e2) {
  if (o[t2])
    throw new Error(`Alreay exists:  ${o[t2]}`);
  o[t2] = e2;
}
function we(o) {
  return at[o] || (at[o] = [st.I, o]), at[o];
}
function Se(o, t2, e2) {
  if (o)
    for (let r2 = o.split(":"), i = parseInt(r2[0], t2), n2 = be(r2[1].split(","), t2), s = we(i), a2 = 0, u3 = n2.length; a2 < u3; a2++)
      It(e2, n2[a2], s);
}
function Ee(o, t2, e2) {
  if (o) {
    const r2 = [];
    let i = o.split(":"), n2 = parseInt(i[0], t2);
    const s = i[1].split(";");
    for (let a2 = 0, u3 = s.length; a2 < u3; a2++) {
      i = s[a2].split(",");
      const l = [parseInt(i[0], t2), 0];
      i.length > 1 && (l[1] = parseInt(i[1], t2)), r2.push(l);
    }
    It(e2, n2, [st.S, r2]);
  }
}
function At(o, t2) {
  if (!o)
    return null;
  const e2 = o.split(","), r2 = [];
  for (let i = 0, n2 = e2.length; i < n2; i++) {
    if (parseInt(e2[i], t2) < 0)
      return null;
    r2.push(parseInt(e2[i], t2));
  }
  return r2;
}
function Le(o, t2) {
  if (!o)
    return null;
  const e2 = o.split(";"), r2 = [];
  for (let i = 0, n2 = e2.length; i < n2; i++)
    r2.push(At(e2[i], t2));
  return r2;
}
function ke(o) {
  let t2, e2;
  const r2 = o.r, i = [], n2 = o.idx.i.split("|");
  for (o.idx.i = null, t2 = 0, e2 = n2.length; t2 < e2; t2++)
    Se(n2[t2], r2, i);
  n2.length = 0;
  const s = o.idx.s.split("|");
  for (o.idx.s = null, t2 = 0, e2 = s.length; t2 < e2; t2++)
    Ee(s[t2], r2, i);
  s.length = 0, o.idx = null, o.idxList = i, o.mxr && (o.maxRect = At(o.mxr, r2), o.mxr = null), o.mxsr && (o.maxSubRect = Le(o.mxsr, r2), o.mxsr = null);
}
function Ie(o, t2, e2) {
  for (let r2 = o.geoData.sub.features, i = 0, n2 = e2.length; i < n2; i++) {
    const s = e2[i], a2 = r2[s[0]], u3 = a2.geometry.coordinates[s[1]][0], l = j.polygonClip(u3, t2);
    !l || l.length < 4 ? console.warn(`Cliped ring length werid: ${l}`) : s[2] = l;
  }
  return true;
}
function Ae(o, t2, e2) {
  const r2 = o.bbIndex, i = r2.s;
  (t2 < 0 || e2 < 0 || e2 >= r2.h || t2 >= r2.w) && console.warn("Wrong x,y", t2, e2, r2);
  const n2 = e2 * r2.w + t2, s = r2.idxList[n2];
  if (s[0] !== st.S)
    return false;
  const a2 = s[1];
  if (a2[0].length > 2)
    return false;
  const u3 = t2 * i + r2.l, l = e2 * i + r2.t;
  return Ie(o, [[u3, l], [u3 + i, l], [u3 + i, l + i], [u3, l + i], [u3, l]], a2), true;
}
var Pt = { prepareGridFeatureClip: Ae, buildIdxList: ke };
var C = class _C {
  constructor(t2, e2, r2, i) {
    this.x = t2, this.y = e2, this.width = r2, this.height = i;
  }
  static getBoundsItemToExpand() {
    return new _C(Number.MAX_VALUE, Number.MAX_VALUE, -1, -1);
  }
  static boundsIntersect(t2, e2) {
    return t2.x <= e2.x + e2.width && e2.x <= t2.x + t2.width && t2.y <= e2.y + e2.height && e2.y <= t2.y + t2.height;
  }
  isEmpty() {
    return this.width < 0;
  }
  expandByPoint(t2, e2) {
    let r2, i, n2, s;
    this.isEmpty() ? (r2 = n2 = t2, i = s = e2) : (r2 = this.x, i = this.y, n2 = this.x + this.width, s = this.y + this.height, t2 < r2 ? r2 = t2 : t2 > n2 && (n2 = t2), e2 < i ? i = e2 : e2 > s && (s = e2)), this.x = r2, this.y = i, this.width = n2 - r2, this.height = s - i;
  }
};
function Pe(o) {
  const t2 = {}, e2 = o.objects;
  for (const r2 in e2)
    t2[r2] = de(o, e2[r2]);
  return t2;
}
function Ne(o) {
  for (let t2 = o.sub ? o.sub.features : [], e2 = o.parent.properties, r2 = (e2.acroutes || []).concat([e2.adcode]), i = 0, n2 = t2.length; i < n2; i++)
    t2[i].properties.subFeatureIndex = i, t2[i].properties.acroutes = r2;
}
function Me(o) {
  if (!o._isBuiled) {
    Pt.buildIdxList(o.bbIndex), o.geoData = Pe(o.topo), o.geoData.sub && Ne(o.geoData);
    const t2 = o.topo.bbox;
    o.bounds = new C(t2[0], t2[1], t2[2] - t2[0], t2[3] - t2[1]), o.topo = null, o._isBuiled = true;
  }
  return o;
}
var Re = { buildData: Me };
var ut = {};
var O = Math.PI / 180;
var Nt = 180 / Math.PI;
var Fe = Math.PI / 4;
var Mt = 0.5 / Math.PI;
function lt(o) {
  return ut[o] || (ut[o] = 256 * Math.pow(2, o)), ut[o];
}
function Oe(o) {
  let t2 = o[1], e2 = o[0] * O, r2 = t2 * O;
  return r2 = Math.log(Math.tan(Fe + r2 / 2)), [e2, r2];
}
function Ce(o, t2) {
  t2 = t2 || 1;
  const e2 = Mt, r2 = 0.5, i = -e2, n2 = 0.5;
  return [t2 * (e2 * o[0] + r2), t2 * (i * o[1] + n2)];
}
function De(o) {
  const t2 = o[0] * Nt, e2 = (2 * Math.atan(Math.exp(o[1])) - Math.PI / 2) * Nt;
  return [parseFloat(t2.toFixed(6)), parseFloat(e2.toFixed(6))];
}
function Te(o, t2) {
  const e2 = Mt, r2 = 0.5, i = -e2, n2 = 0.5;
  return [(o[0] / t2 - r2) / e2, (o[1] / t2 - n2) / i];
}
function Rt(o, t2, e2) {
  const r2 = Ce(Oe(o), t2);
  return e2 && (r2[0] = Math.round(r2[0]), r2[1] = Math.round(r2[1])), r2;
}
function Be(o, t2, e2) {
  return Rt(o, lt(t2), e2);
}
function $e(o, t2) {
  const e2 = lt(t2), r2 = Te(o, e2);
  return De(r2);
}
function ze(o, t2) {
  const e2 = Math.cos, r2 = o[1] * O, i = o[0] * O, n2 = t2[1] * O, s = t2[0] * O, a2 = n2 - r2, u3 = s - i, l = (1 - e2(a2) + (1 - e2(u3)) * e2(r2) * e2(n2)) / 2;
  return 12756274 * Math.asin(Math.sqrt(l));
}
var ht = { haversineDistance: ze, getScale: lt, lngLatToPointByScale: Rt, pointToLngLat: $e, lngLatToPoint: Be };
var ct = class _ct {
  constructor(t2, e2, r2) {
    this.adcode = t2, this._data = e2, this._sqScaleFactor = e2.scale * e2.scale, this._opts = Object.assign({ nearTolerance: 2 }, r2), this.setNearTolerance(this._opts.nearTolerance);
  }
  static getPropsOfFeature(t2) {
    return t2 && t2.properties ? t2.properties : null;
  }
  static getAdcodeOfFeature(t2) {
    return t2 ? t2.properties.adcode : null;
  }
  static doesFeatureHasChildren(t2) {
    return !!t2 && t2.properties.childrenNum > 0;
  }
  setNearTolerance(t2) {
    this._opts.nearTolerance = t2, this._sqNearTolerance = t2 * t2;
  }
  getIdealZoom() {
    return this._data.idealZoom;
  }
  _getEmptySubFeatureGroupItem(t2) {
    return { subFeatureIndex: t2, subFeature: this.getSubFeatureByIndex(t2), pointsIndexes: [], points: [] };
  }
  groupByPosition(t2, e2) {
    let r2, i, n2 = {}, s = null;
    for (r2 = 0, i = t2.length; r2 < i; r2++) {
      const u3 = this.getLocatedSubFeatureIndex(e2.call(null, t2[r2], r2));
      n2[u3] || (n2[u3] = this._getEmptySubFeatureGroupItem(u3)), n2[u3].pointsIndexes.push(r2), n2[u3].points.push(t2[r2]), u3 < 0 && (s = n2[u3]);
    }
    const a2 = [];
    if (this._data.geoData.sub)
      for (r2 = 0, i = this._data.geoData.sub.features.length; r2 < i; r2++)
        a2.push(n2[r2] || this._getEmptySubFeatureGroupItem(r2));
    return s && a2.push(s), n2 = null, a2;
  }
  getLocatedSubFeatureIndex(t2) {
    return this._getLocatedSubFeatureIndexByPixel(this.lngLatToPixel(t2));
  }
  getSubFeatureByIndex(t2) {
    return t2 >= 0 ? this.getSubFeatures()[t2] : null;
  }
  _getLocatedSubFeatureIndexByPixel(t2) {
    if (!this._data.geoData.sub)
      return -1;
    const e2 = this._data, r2 = e2.bbIndex, i = t2[0] - r2.l, n2 = t2[1] - r2.t, s = Math.floor(n2 / r2.s), a2 = Math.floor(i / r2.s);
    if (a2 < 0 || s < 0 || s >= r2.h || a2 >= r2.w)
      return -1;
    const u3 = s * r2.w + a2, l = r2.idxList[u3];
    if (!l)
      return -1;
    const h = nt.BBRFLAG;
    switch (l[0]) {
      case h.I:
        return l[1];
      case h.S:
        return Pt.prepareGridFeatureClip(e2, a2, s), this._calcLocatedFeatureIndexOfSList(t2, l[1]);
      default:
        throw new Error(`Unknown BBRFLAG: ${l[0]}`);
    }
  }
  _calcNearestFeatureIndexOfSList(t2, e2) {
    let r2 = [];
    this._data.geoData.sub && (r2 = this._data.geoData.sub.features);
    const i = { sq: Number.MAX_VALUE, idx: -1 };
    for (let n2 = 0, s = e2.length; n2 < s; n2++) {
      const a2 = e2[n2], u3 = r2[a2[0]], l = a2[2] || u3.geometry.coordinates[a2[1]][0], h = j.sqClosestDistanceToPolygon(t2, l);
      h < i.sq && (i.sq = h, i.idx = a2[0]);
    }
    return i.sq / this._sqScaleFactor < this._sqNearTolerance ? i.idx : -1;
  }
  _calcLocatedFeatureIndexOfSList(t2, e2) {
    for (let r2 = this._data.geoData.sub.features, i = 0, n2 = e2.length; i < n2; i++) {
      const s = e2[i], a2 = r2[s[0]], u3 = s[2] || a2.geometry.coordinates[s[1]][0];
      if (j.pointInPolygon(t2, u3) || j.pointOnPolygon(t2, u3))
        return s[0];
    }
    return this._calcNearestFeatureIndexOfSList(t2, e2);
  }
  pixelToLngLat(t2, e2) {
    return ht.pointToLngLat([t2, e2], this._data.pz);
  }
  lngLatToPixel(t2) {
    t2 instanceof AMap.LngLat && (t2 = [t2.getLng(), t2.getLat()]);
    const e2 = ht.lngLatToPoint(t2, this._data.pz);
    return [Math.round(e2[0]), Math.round(e2[1])];
  }
  _convertRingCoordsToLngLats(t2) {
    const e2 = [];
    for (let r2 = 0, i = t2.length; r2 < i; r2++)
      e2[r2] = this.pixelToLngLat(t2[r2][0], t2[r2][1]);
    return e2;
  }
  _convertPolygonCoordsToLngLats(t2) {
    const e2 = [];
    for (let r2 = 0, i = t2.length; r2 < i; r2++)
      e2[r2] = this._convertRingCoordsToLngLats(t2[r2]);
    return e2;
  }
  _convertMultiPolygonCoordsToLngLats(t2) {
    const e2 = [];
    for (let r2 = 0, i = t2.length; r2 < i; r2++)
      e2[r2] = this._convertPolygonCoordsToLngLats(t2[r2]);
    return e2;
  }
  _convertCoordsToLngLats(t2, e2) {
    switch (t2) {
      case "MultiPolygon":
        return this._convertMultiPolygonCoordsToLngLats(e2);
      default:
        throw new Error(`Unknown type ${t2}`);
    }
  }
  _createLngLatFeature(t2, e2) {
    const r2 = Object.assign({}, t2);
    return e2 && Object.assign(r2.properties, e2), r2.geometry = Object.assign({}, r2.geometry), r2.geometry.coordinates = this._convertCoordsToLngLats(r2.geometry.type, r2.geometry.coordinates), r2;
  }
  getAdcode() {
    return this.getProps("adcode");
  }
  getName() {
    return this.getProps("name");
  }
  getChildrenNum() {
    return this.getProps("childrenNum");
  }
  getProps(t2) {
    const e2 = _ct.getPropsOfFeature(this._data.geoData.parent);
    return e2 ? t2 ? e2[t2] : e2 : null;
  }
  getParentFeature() {
    const t2 = this._data.geoData;
    return t2.lngLatParent || (t2.lngLatParent = this._createLngLatFeature(t2.parent)), t2.lngLatParent;
  }
  getParentFeatureInPixel() {
    return this._data.geoData.parent;
  }
  getSubFeatures() {
    const t2 = this._data.geoData;
    if (!t2.sub)
      return [];
    if (!t2.lngLatSubList) {
      const e2 = [];
      for (let r2 = t2.sub.features, i = 0, n2 = r2.length; i < n2; i++)
        e2[i] = this._createLngLatFeature(r2[i]);
      t2.lngLatSubList = e2;
    }
    return [].concat(t2.lngLatSubList);
  }
  getSubFeaturesInPixel() {
    return this._data.geoData.sub ? [].concat(this._data.geoData.sub.features) : [];
  }
  getBounds() {
    const t2 = this._data;
    if (!t2.lngLatBounds) {
      const e2 = this._data.bounds;
      t2.lngLatBounds = new AMap.Bounds(this.pixelToLngLat(e2.x, e2.y + e2.height), this.pixelToLngLat(e2.x + e2.width, e2.y));
    }
    return t2.lngLatBounds;
  }
};
var Ge = class extends Y {
  constructor(t2) {
    super(), this._opts = Object.assign({ distDataLoc: "//webapi.amap.com/ui/1.1/ui/geo/DistrictExplorer/assets/d_v2" }, t2), this._areaNodesForLocating = null, this._areaNodeCache = {}, this._opts.preload && this.loadMultiAreaNodes(this._opts.preload);
  }
  setAreaNodesForLocating(t2) {
    t2 ? Array.isArray(t2) || (t2 = [t2]) : t2 = [], this._areaNodesForLocating = t2 || [];
  }
  _loadJson(t2, e2) {
    const r2 = this;
    return fetch(t2, { headers: { Accept: "application/json" } }).then((i) => i.json()).then((i) => {
      e2 && e2.call(r2, null, i);
    }).catch((i) => {
      if (!e2)
        throw i;
      e2(i);
    });
  }
  _getAreaNodeDataFileName(t2) {
    return `an_${t2}.json`;
  }
  _getAreaNodeDataSrc(t2) {
    return `${this._opts.distDataLoc}/${this._getAreaNodeDataFileName(t2)}`;
  }
  loadAreaTree(t2) {
    this._loadJson(`${this._opts.distDataLoc}/area_tree.json`, t2);
  }
  loadCountryNode(t2) {
    this.loadAreaNode(nt.ADCODES.COUNTRY, t2);
  }
  loadMultiAreaNodes(t2, e2) {
    let r2 = [], i = false, n2;
    function s(a2) {
      return function(u3, l) {
        i || (n2--, u3 ? (e2 && e2(u3), i = true) : (r2[a2] = l, n2 === 0 && e2 && e2(null, r2)));
      };
    }
    if (t2 && t2.length) {
      const a2 = t2.length;
      for (let u3 = 0; u3 < a2; u3++)
        this.loadAreaNode(t2[u3], e2 ? s(u3) : null);
    } else
      e2 && e2(null, []);
  }
  loadAreaNode(t2, e2, r2, i) {
    if (r2 = r2 || this, this._areaNodeCache[t2]) {
      if (e2) {
        const n2 = this._areaNodeCache[t2];
        i ? e2.call(r2, null, n2, true) : setTimeout(function() {
          e2.call(r2, null, n2);
        }, 0);
      }
    } else
      this._loadJson(this._getAreaNodeDataSrc(t2), (n2, s) => {
        n2 ? e2 && e2.call(r2, n2) : (this._buildAreaNode(t2, s), e2 && e2.call(r2, null, this._areaNodeCache[t2]));
      });
  }
  getLocalAreaNode(t2) {
    return this._areaNodeCache[t2] || null;
  }
  _buildAreaNode(t2, e2) {
    if (!this._areaNodeCache[t2]) {
      if (!e2)
        throw new Error(`Empty distData: ${t2}`);
      const r2 = new ct(t2, Re.buildData(e2), this._opts);
      this._areaNodeCache[t2] = r2, this._areaNodesForLocating || (this._areaNodesForLocating = [r2]);
    }
  }
  clearAreaNodeCacheByAdcode(t2) {
    const e2 = this._areaNodeCache;
    return delete e2[t2], true;
  }
  destroy() {
    this._areaNodesForLocating = null, this._areaNodeCache = null, this._opts = null;
  }
};
var Ze = class {
  constructor(t2) {
    this.isDistReady = false, this.nodeMap = {}, this.waitFnList = [], this.singleDistExplorer = new Ge({}), this._opts = v.extend({ topAdcodes: [1e5] }, t2), this._touchMap = {}, this.singleDistExplorer.loadAreaTree((e2, r2) => {
      if (e2)
        throw e2;
      if (this.filterAreaTree(r2), this.singleCountryNode = r2, this.isDistReady = true, this.waitFnList.length) {
        for (let i = 0, n2 = this.waitFnList.length; i < n2; i++)
          this.waitFnList[i][0].call(this.waitFnList[i][1]);
        this.waitFnList.length = 0;
      }
      this.singleDistExplorer.loadMultiAreaNodes(this._opts.topAdcodes);
    });
  }
  pixelToLngLat(t2, e2, r2) {
    return ht.pointToLngLat([t2, e2], r2);
  }
  getBounds(t2) {
    const e2 = t2.bbounds;
    return new AMap.Bounds(this.pixelToLngLat(e2.x, e2.y + e2.height, 20), this.pixelToLngLat(e2.x + e2.width, e2.y, 20));
  }
  filterAreaTree(t2) {
    const e2 = [t2];
    do {
      const r2 = e2.pop();
      this.nodeMap[r2.adcode] = r2;
      const i = r2.bbox;
      if (r2.bbounds = new C(i[0], i[1], i[2], i[3]), r2.bbox = this.getBounds(r2), r2.children)
        for (let n2 = r2.children, s = 0, a2 = n2.length; s < a2; s++)
          n2[s].childIdx = s, e2.push(n2[s]);
    } while (e2.length);
  }
  isReady() {
    return this.isDistReady;
  }
  getParentAdcode(t2, e2) {
    if (!e2) {
      const r2 = this.getNodeByAdcode(t2);
      if (!r2)
        return console.warn(`Can not find node: ${t2}`), null;
      e2 = r2.acroutes;
    }
    return e2 && e2.length ? e2[e2.length - 1] : null;
  }
  getSubIdx(t2) {
    return this.getNodeByAdcode(t2).childIdx;
  }
  getChildrenNum(t2) {
    const e2 = this.getNodeByAdcode(t2);
    return this.getChildrenNumOfNode(e2);
  }
  getChildrenNumOfNode(t2) {
    return t2.children ? t2.children.length : t2.childrenNum || 0;
  }
  getNodeByAdcode(t2) {
    const e2 = this.nodeMap[t2];
    if (!e2) {
      let r2 = this.singleDistExplorer.getLocalAreaNode(`${`${t2}`.substr(0, 4)}00`);
      if (r2 || (r2 = this.singleDistExplorer.getLocalAreaNode(`${`${t2}`.substr(0, 2)}0000`)), !r2)
        return null;
      for (let i = r2.getSubFeatures(), n2 = 0, s = i.length; n2 < s; n2++)
        if (i[n2].properties.adcode === t2)
          return i[n2].properties;
    }
    return e2;
  }
  getNodeChildren(t2) {
    const e2 = this.getNodeByAdcode(t2);
    if (!e2)
      return null;
    if (e2.children)
      return e2.children;
    if (e2.childrenNum >= 0) {
      const r2 = this.singleDistExplorer.getLocalAreaNode(t2);
      if (!r2)
        return null;
      const i = [], n2 = r2.getSubFeaturesInPixel();
      for (let s = 0, a2 = n2.length; s < a2; s++)
        i.push(n2[s].properties);
      return i;
    }
    return null;
  }
  getExplorer() {
    return this.singleDistExplorer;
  }
  traverseCountry(t2, e2, r2, i, n2) {
    this.traverseNode(this.singleCountryNode, t2, e2, r2, i, n2, []);
  }
  getNodeBoundsSize(t2, e2) {
    const r2 = this.getPixelZoom(), i = Math.pow(2, r2 - e2);
    return [t2.bbounds.width / i, t2.bbounds.height / i];
  }
  doesRingRingIntersect(t2, e2) {
    const r2 = [t2.getNorthWest().toArray(), t2.getNorthEast().toArray(), t2.getSouthEast().toArray(), t2.getSouthWest().toArray(), t2.getNorthWest().toArray()], i = [e2.getNorthWest().toArray(), e2.getNorthEast().toArray(), e2.getSouthEast().toArray(), e2.getSouthWest().toArray(), e2.getNorthWest().toArray()];
    return !!ue(H([r2]), H([i]));
  }
  traverseNode(t2, e2, r2, i, n2, s, a2, u3) {
    if (!(a2 && a2.indexOf(t2.adcode) >= 0)) {
      if (this.doesRingRingIntersect(e2, t2.bbox)) {
        const l = t2.children, h = l && l.length > 0;
        if (r2 > t2.idealZoom && h)
          for (let c = 0, d2 = l.length; c < d2; c++)
            this.traverseNode(l[c], e2, r2, i, null, s, a2);
        else
          i.call(s, t2);
      }
      n2 && (u3 ? (u3.count++, u3.count >= u3.total && n2.call(s)) : n2.call(s));
    }
  }
  onReady(t2, e2, r2) {
    this.isDistReady ? r2 ? t2.call(e2) : setTimeout(function() {
      t2.call(e2);
    }, 0) : this.waitFnList.push([t2, e2]);
  }
  getPixelZoom() {
    var t2;
    return (t2 = this.singleCountryNode) === null || t2 === void 0 ? void 0 : t2.pz;
  }
  loadAreaNode(t2, e2, r2, i) {
    this.singleDistExplorer.loadAreaNode(t2, e2, r2, i);
  }
  isExcludedAdcode(t2) {
    const e2 = this._opts.excludedAdcodes;
    return e2 && e2.indexOf(t2) >= 0;
  }
  traverseTopNodes(t2, e2, r2, i, n2) {
    const s = this._opts.topAdcodes, a2 = this._opts.excludedAdcodes, u3 = { total: s.length, count: 0 };
    for (let l = 0, h = s.length; l < h; l++) {
      const c = this.getNodeByAdcode(s[l]);
      if (!c)
        throw new Error(`Can not find adcode: ${s[l]}`);
      this.traverseNode(c, t2, e2, r2, i, n2, a2, u3);
    }
  }
  tryClearCache(t2, e2) {
    if (!(e2 < 0)) {
      const r2 = [this.singleCountryNode], i = [], n2 = this._touchMap;
      do {
        const a2 = r2.pop();
        a2.children && v.mergeArray(r2, a2.children);
        const u3 = n2[a2.adcode];
        u3 && u3 !== t2 && i.push(a2.adcode);
      } while (r2.length);
      i.sort(function(a2, u3) {
        const l = n2[a2] - n2[u3];
        return l === 0 ? a2 - u3 : l;
      });
      const s = i.length - e2;
      if (!(s <= 0))
        for (let a2 = 0; a2 < s; a2++)
          this.singleDistExplorer.clearAreaNodeCacheByAdcode(i[a2]) && this.touchAdcode(i[a2], null);
    }
  }
  touchAdcode(t2, e2) {
    this._touchMap[t2] = e2;
  }
  destroy() {
    this.singleDistExplorer.destroy(), this._touchMap = {}, this.nodeMap = {}, this.singleDistExplorer = void 0, this._opts = void 0, this.waitFnList = [], this.singleCountryNode = void 0;
  }
};
function Ve(o) {
  return [o.x, o.y];
}
var qe = class {
  constructor(t2) {
    this._data = [], this._pointsMap = {}, this._opts = v.extend({ topAdcode: 1e5 }, t2), this.clearData();
  }
  clearData() {
    this._data = [], this._pointsMap = {};
  }
  setData(t2) {
    this.clearData(), this._data = t2, this._updatePointsMap(this._opts.topAdcode, "all", t2);
  }
  _updatePointsMap(t2, e2, r2) {
    let i = this._pointsMap[t2];
    i || (i = this._pointsMap[t2] = {}), i[e2] = r2, i[`${e2}_pack`] = this._buildPackItemsByAdcode(t2, r2);
  }
  getPointsByAdcode(t2, e2) {
    return this._pointsMap[t2] ? this._pointsMap[t2][e2 || "all"] : [];
  }
  getPackItemsByAdcode(t2, e2) {
    return this._pointsMap[t2] ? this._pointsMap[t2][`${e2 || "all"}_pack`] : [];
  }
  _buildPackItemsByAdcode(t2, e2) {
    const r2 = this._opts.pointPacker, i = [];
    for (let n2 = 0, s = e2.length; n2 < s; n2++)
      i[n2] = r2.call(this._opts.pointPackerThisArg, e2[n2]);
    return i;
  }
  calcDistGroup(t2, e2, r2, i) {
    const n2 = this._opts.distMgr.getNodeByAdcode(t2);
    let s = n2.acroutes || [1e5];
    e2 && n2.acroutes && (s = [].concat(s), s.push(t2)), this._calcGroupWithRoutes(s, 0, r2, i);
  }
  _calcGroupWithRoutes(t2, e2, r2, i) {
    const n2 = () => {
      e2 < t2.length - 1 ? this._calcGroupWithRoutes(t2, e2 + 1, r2, i) : r2 && r2.call(i);
    }, s = t2[e2];
    if (this.getPointsByAdcode(s, "__done"))
      n2.call(this);
    else {
      const a2 = this.getPointsByAdcode(s);
      if (!a2)
        throw new Error(`Not points found:  ${s}`);
      this._opts.distMgr.getExplorer().loadAreaNode(s, (u3, l) => {
        this._groupByAreaNode(l, a2), n2.call(this);
      }, this, true);
    }
  }
  _groupByAreaNode(t2, e2) {
    const r2 = t2.groupByPosition(e2, Ve), i = t2.getAdcode() === this._opts.topAdcode, n2 = [];
    for (let s = 0, a2 = r2.length; s < a2; s++) {
      const u3 = r2[s];
      u3.subFeature ? (this._updatePointsMap(u3.subFeature.properties.adcode, "all", u3.points), i && v.mergeArray(n2, u3.points)) : this._updatePointsMap(t2.getAdcode(), "hanging", u3.points);
    }
    i && this._updatePointsMap(t2.getAdcode(), "all", n2), this._updatePointsMap(t2.getAdcode(), "__done", true);
  }
  destroy() {
    this.clearData(), this._opts = null;
  }
};
var je = Object.defineProperty;
var Ue = Object.defineProperties;
var We = Object.getOwnPropertyDescriptors;
var Ft = Object.getOwnPropertySymbols;
var Ye = Object.prototype.hasOwnProperty;
var He = Object.prototype.propertyIsEnumerable;
var Ot = (o, t2, e2) => t2 in o ? je(o, t2, { enumerable: true, configurable: true, writable: true, value: e2 }) : o[t2] = e2;
var Ct = (o, t2) => {
  for (var e2 in t2 || (t2 = {}))
    Ye.call(t2, e2) && Ot(o, e2, t2[e2]);
  if (Ft)
    for (var e2 of Ft(t2))
      He.call(t2, e2) && Ot(o, e2, t2[e2]);
  return o;
};
var Xe = (o, t2) => Ue(o, We(t2));
var Qe = class extends Y {
  constructor(t2, e2) {
    super(), this.baseId = 1, this._currentZoom = 2, this._currentFeatures = [], this._loadLeft = 0, this._polygonCache = [], this._markerCache = [], this._opts = v.extend({ engine: "default", areaNodeCacheLimit: -1, minHeightToShowSubFeatures: 630, minSiblingAvgHeightToShowSubFeatures: 600, minSubAvgHeightToShowSubFeatures: 300, zooms: [2, 30], clusterMarkerEventSupport: true, clusterMarkerClickToShowSub: true, featureEventSupport: true, featureClickToShowSub: false, featureStyleByLevel: { country: { strokeColor: "rgb(31, 119, 180)", strokeOpacity: 0.9, strokeWeight: 2, fillColor: "rgb(49, 163, 84)", fillOpacity: 0.8 }, province: { strokeColor: "rgb(31, 119, 180)", strokeOpacity: 0.9, strokeWeight: 2, fillColor: "rgb(116, 196, 118)", fillOpacity: 0.7 }, city: { strokeColor: "rgb(31, 119, 180)", strokeOpacity: 0.9, strokeWeight: 2, fillColor: "rgb(161, 217, 155)", fillOpacity: 0.6 }, district: { strokeColor: "rgb(31, 119, 180)", strokeOpacity: 0.9, strokeWeight: 2, fillColor: "rgb(199, 233, 192)", fillOpacity: 0.5 } } }, e2), this._map = this._opts.map, this._createLayer(), this._ins = t2, this._isRendering = false, this._loadLeft = 0, this._currentFeatures = [];
  }
  _createLayer() {
    this.markerGroup = new AMap.OverlayGroup(), this._map.add(this.markerGroup), this.layer = new AMap.VectorLayer({ zIndex: this._opts.zIndex || 10, visible: this._opts.visible || true }), this._map.addLayer(this.layer);
  }
  zoomToShowSubFeatures(t2, e2) {
    const r2 = this.getMinZoomToShowSub(t2);
    if (r2 >= 3) {
      const i = this._ins.getMap();
      i && (e2 || (e2 = this._ins._distMgr.getNodeByAdcode(t2).center), i.setZoomAndCenter(r2, e2));
    }
  }
  getPixelRatio() {
    return Math.min(2, Math.round(window.devicePixelRatio || 1));
  }
  refreshViewState() {
    if (!this._ins._distMgr.isReady())
      return false;
    const t2 = this._ins;
    if (!t2.isReady())
      return false;
    const e2 = t2.getMap(), r2 = e2.getBounds(), i = e2.getSize(), n2 = e2.getZoom(3), s = this._opts.zooms[1], a2 = Math.pow(2, s - n2), u3 = r2.getNorthWest(), l = e2.lngLatToCoords([u3.getLng(), u3.getLat()]), h = new C(l[0], l[1], i.width * a2, i.height * a2);
    this._currentZoom = n2, this._currentScaleFactor = a2, this._currentViewBounds = h, this._currentViewBoundsInLngLat = r2, this._currentPixelRatio = this.getPixelRatio();
  }
  renderViewport() {
    if (this.refreshViewState(), !this._currentViewBounds)
      return false;
    this._currentRenderId = this.baseId++, this._loadLeft = 0, this._currentFeatures = [], this._renderViewDist(this._currentRenderId), this._isRendering = false;
  }
  getCurrentRenderId() {
    return this._currentRenderId;
  }
  isRenderIdStillValid(t2) {
    return t2 === this._currentRenderId;
  }
  _renderViewDist(t2) {
    const e2 = [];
    if (this._currentZoom < this._opts.zooms[0] || this._currentZoom > this._opts.zooms[1]) {
      this.isRenderIdStillValid(t2) && this._prepareFeatures(t2, e2);
      return;
    }
    this._ins.getDistMgr().traverseTopNodes(this._currentViewBoundsInLngLat, this._currentZoom, (r2) => {
      e2.push(r2.adcode);
    }, () => {
      this.isRenderIdStillValid(t2) && this._prepareFeatures(t2, e2);
    }, this);
  }
  getMinZoomToShowSub(t2) {
    const e2 = this._ins._distMgr.getNodeByAdcode(t2);
    if (!e2 || !e2.idealZoom)
      return -1;
    if (!e2._minZoomToShowSub) {
      const r2 = this._ins.getZooms();
      for (let i = r2[0]; i <= r2[1]; i++)
        if (this.shouldShowSubOnZoom(e2, i)) {
          e2._minZoomToShowSub = i;
          break;
        }
    }
    return e2._minZoomToShowSub || -1;
  }
  shouldShowSubOnZoom(t2, e2) {
    if (!t2.idealZoom)
      return false;
    if (t2._minZoomToShowSub && e2 >= t2._minZoomToShowSub)
      return true;
    let r2 = this._ins._distMgr.getNodeBoundsSize(t2, e2);
    if (t2.adcode === 1e5 && r2[1] > 400)
      return true;
    if (r2[1] < this._opts.minHeightToShowSubFeatures)
      return false;
    let i, n2, s;
    if (t2.children) {
      const u3 = t2.children;
      if (s = 0, n2 = u3.length, n2) {
        for (i = 0; i < n2; i++)
          r2 = this._ins._distMgr.getNodeBoundsSize(u3[i], e2), s += r2[1];
        if (s / n2 < this._opts.minSubAvgHeightToShowSubFeatures)
          return false;
      }
    }
    const a2 = this._ins._distMgr.getParentAdcode(t2.adcode, t2.acroutes);
    if (a2) {
      const u3 = this._ins._distMgr.getNodeByAdcode(a2), l = u3.children;
      if (l || console.error("No children bound", t2, u3), n2 = l.length, n2 > 1) {
        for (s = 0, i = 0; i < n2; i++)
          l[i].adcode !== t2.adcode && (r2 = this._ins._distMgr.getNodeBoundsSize(l[i], e2), s += r2[1]);
        if (s / (n2 - 1) < this._opts.minSiblingAvgHeightToShowSubFeatures)
          return false;
      }
    }
    return true;
  }
  _shouldShowSub(t2) {
    return !(!t2.children || !t2.children.length) && this.shouldShowSubOnZoom(t2, this._currentZoom);
  }
  _prepareFeatures(t2, e2) {
    const r2 = [], i = [];
    for (let n2 = 0, s = e2.length; n2 < s; n2++) {
      const a2 = this._ins._distMgr.getNodeByAdcode(e2[n2]);
      if (!a2)
        throw new Error(`Can not find node: ${e2[n2]}`);
      this._shouldShowSub(a2) ? i.push(e2[n2]) : r2.push(e2[n2]);
    }
    this._prepareSelfFeatures(t2, r2), this._prepareSubFeatures(t2, i), this._checkLoadFinish(t2);
  }
  _prepareSelfFeatures(t2, e2) {
    let r2;
    const i = this._currentZoom;
    for (let n2 = 0, s = e2.length; n2 < s; n2++) {
      const a2 = this._ins._distMgr.getNodeByAdcode(e2[n2]);
      if (r2 = null, a2.acroutes) {
        const u3 = this._ins._distMgr.getNodeByAdcode(a2.acroutes[a2.acroutes.length - 1]);
        (!a2.idealZoom || i < a2.idealZoom - 1 || Math.abs(i - u3.idealZoom) <= Math.abs(a2.idealZoom - i)) && (r2 = u3.adcode);
      }
      this._loadAndRenderSelf(t2, r2 || e2[n2], e2[n2]);
    }
  }
  _prepareSubFeatures(t2, e2) {
    let r2, i;
    for (r2 = 0, i = e2.length; r2 < i; r2++)
      this._loadAndRenderSub(t2, e2[r2]);
  }
  _renderSelf(t2, e2, r2) {
    let i;
    if (e2 === r2.getAdcode())
      i = r2.getParentFeature();
    else {
      const n2 = r2.getSubFeatures(), s = this._ins._distMgr.getSubIdx(e2);
      if (i = n2[s], !i) {
        console.warn("Werid, can not find sub feature", r2.getAdcode(), e2);
        return;
      }
      if (i.properties.adcode !== e2) {
        console.warn("Sub adcode not match!!", n2, s);
        return;
      }
    }
    this._ins.getDistCounter().calcDistGroup(e2, false, () => {
      this.isRenderIdStillValid(t2) && this._prepRenderFeatureInPixel(t2, i);
    }, this);
  }
  _checkLoadFinish(t2) {
    if (this._loadLeft === 0) {
      const e2 = this;
      setTimeout(function() {
        e2.isRenderIdStillValid(t2) && e2._handleRenderFinish();
      }, 0);
    }
  }
  _renderSub(t2, e2) {
    const r2 = e2.getSubFeatures();
    this._ins.getDistCounter().calcDistGroup(e2.getAdcode(), true, () => {
      if (this.isRenderIdStillValid(t2))
        for (let i = 0, n2 = r2.length; i < n2; i++)
          this._prepRenderFeatureInPixel(t2, r2[i]);
    }, this);
  }
  _handleRenderFinish() {
    this._tryFreeMemery(), this._renderAllFeature();
  }
  _renderAllFeature() {
    this._renderAllFeatureByDefault();
  }
  _renderAllFeatureByDefault() {
    var t2, e2;
    const r2 = [], i = [], n2 = [], s = [];
    for (let a2 = 0; a2 < this._polygonCache.length; a2++) {
      const u3 = this._polygonCache[a2], l = u3.getExtData()._data.adcode;
      let h = false;
      for (let c = 0; c < this._currentFeatures.length; c++) {
        const d2 = this._currentFeatures[c].feature.properties;
        if (l === d2.adcode) {
          h = true, this._currentFeatures.splice(c, 1);
          break;
        }
      }
      h || (i.push(u3), this._polygonCache.splice(a2, 1), s.push(this._markerCache[a2]), this._markerCache.splice(a2, 1), a2--);
    }
    this._currentFeatures.forEach((a2) => {
      const u3 = this._createPolygonFeature(a2.feature, a2.dataItems);
      this._opts.featureEventSupport && (u3.on("click", v.bind((h) => {
        this.emit("featureClick", h, a2.feature), this._opts.featureClickToShowSub && this._ins.zoomToShowSubFeatures(a2.feature.properties.adcode);
      }, this)), u3.on("mouseover", v.bind((h) => {
        this.emit("featureMouseover", h, a2.feature);
      }, this)), u3.on("mouseout", v.bind((h) => {
        this.emit("featureMouseout", h, a2.feature);
      }, this)));
      const l = this._createClusterMarker(a2.feature, a2.dataItems);
      this._opts.clusterMarkerEventSupport && l.on("click", v.bind((h) => {
        this.emit("clusterMarkerClick", h, Ct({ adcode: a2.feature.properties.adcode }, a2)), this._opts.clusterMarkerClickToShowSub && this._ins.zoomToShowSubFeatures(a2.feature.properties.adcode);
      }, this)), r2.push(u3), n2.push(l);
    }), this.layer.remove(i), (t2 = this.markerGroup) === null || t2 === void 0 || t2.removeOverlays(s), this.layer.add(r2), this._polygonCache.push(...r2), r2.length = 0, (e2 = this.markerGroup) === null || e2 === void 0 || e2.addOverlays(n2), this._markerCache.push(...n2), n2.length = 0;
  }
  _tryFreeMemery() {
    this._ins.getDistMgr().tryClearCache(this._currentRenderId, this._opts.areaNodeCacheLimit);
  }
  _increaseLoadLeft() {
    this._loadLeft++;
  }
  _decreaseLoadLeft(t2) {
    this._loadLeft--, this._loadLeft === 0 && this._checkLoadFinish(t2);
  }
  _loadAndRenderSelf(t2, e2, r2) {
    this._ins.getDistMgr().touchAdcode(e2, t2);
    const i = this._ins._distMgr.getExplorer(), n2 = i.getLocalAreaNode(e2);
    n2 ? this._renderSelf(t2, r2, n2) : (this._increaseLoadLeft(), i.loadAreaNode(e2, (s, a2) => {
      this.isRenderIdStillValid(t2) && (s ? console.error(s) : this._renderSelf(t2, r2, a2), this._decreaseLoadLeft(t2));
    }, this));
  }
  _loadAndRenderSub(t2, e2) {
    this._ins.getDistMgr().touchAdcode(e2, t2);
    const r2 = this._ins._distMgr.getExplorer(), i = r2.getLocalAreaNode(e2);
    i ? this._renderSub(t2, i) : (this._increaseLoadLeft(), r2.loadAreaNode(e2, (n2, s) => {
      this.isRenderIdStillValid(t2) && (n2 ? console.error(n2) : this._renderSub(t2, s), this._decreaseLoadLeft(t2));
    }, this));
  }
  _prepRenderFeatureInPixel(t2, e2) {
    if (!this._ins.getDistMgr().isExcludedAdcode(e2.properties.adcode)) {
      const r2 = this._ins.getDistCounter().getPackItemsByAdcode(e2.properties.adcode);
      this._currentFeatures.push({ feature: e2, dataItems: r2 });
    }
  }
  _createPolygonFeature(t2, e2) {
    const r2 = Object.assign({}, t2.properties);
    if (r2.dataItems = e2, this._opts.renderPolygon) {
      const n2 = this._opts.renderPolygon(t2, e2), s = n2.getExtData() || {};
      return s._data = r2, n2.setExtData(s), n2;
    }
    const i = this._getFeatureStyleOptions(t2, e2) || {};
    return new AMap.Polygon(Xe(Ct({ path: t2.geometry.coordinates }, i), { extData: { _data: r2 } }));
  }
  _createClusterMarker(t2, e2) {
    const r2 = t2.properties;
    if (r2.dataItems = e2, this._opts.renderClusterMarker) {
      const h = this._opts.renderClusterMarker(t2, e2), c = h.getExtData() || {};
      return c._data = r2, h.setExtData(c), h;
    }
    const i = { title: "amap-ui-district-cluster-marker-title", body: "amap-ui-district-cluster-marker-body", container: "amap-ui-district-cluster-marker" }, n2 = document.createElement("div"), s = document.createElement("span");
    s.className = i.title;
    const a2 = document.createElement("span");
    a2.className = i.body, n2.appendChild(s), n2.appendChild(a2);
    const u3 = [], l = [i.container, `level_${r2.level}`, `adcode_${r2.adcode}`];
    if (r2.acroutes)
      for (let h = r2.acroutes, c = 0, d2 = h.length; c < d2; c++)
        l.push(`descendant_of_${h[c]}`), c === d2 - 1 && l.push(`child_of_${h[c]}`), c > 0 && u3.push(this._ins._distMgr.getNodeByAdcode(h[c]).name);
    return n2.className = l.join(" "), u3.length > 0 ? (u3.push(r2.name), n2.setAttribute("title", u3.join(">"))) : n2.removeAttribute("title"), s.innerHTML = v.escapeHtml(r2.name), a2.innerHTML = e2.length, new AMap.Marker({ topWhenClick: true, offset: new AMap.Pixel(-20, -30), content: n2, position: r2.center, extData: { _data: r2 } });
  }
  _getFeatureStyleOptions(t2, e2) {
    const r2 = this._opts.getFeatureStyle, i = this._opts.featureStyleByLevel[t2.properties.level];
    if (!r2)
      return i;
    const n2 = r2.call(null, t2, e2);
    return n2 ? v.extend({}, this._opts.featureStyleByLevel[t2.properties.level], n2) : i;
  }
  renderLater(t2) {
    this._renderLaterId || (this._renderLaterId = setTimeout(() => {
      this.render();
    }, t2 || 100));
  }
  isRendering() {
    return this._isRendering;
  }
  render() {
    this._renderLaterId && (clearTimeout(this._renderLaterId), this._renderLaterId = null), this._isRendering = true, this._ins._distMgr.onReady(this.renderViewport, this, true);
  }
  forceRender() {
    this._renderLaterId && (clearTimeout(this._renderLaterId), this._renderLaterId = null), this._isRendering = true, this.clear(), this._ins._distMgr.onReady(this.renderViewport, this, true);
  }
  getOption(t2) {
    return this._opts[t2];
  }
  getOptions() {
    return this._opts;
  }
  show() {
    var t2;
    this.layer.show(), (t2 = this.markerGroup) === null || t2 === void 0 || t2.show();
  }
  hide() {
    var t2;
    this.layer.hide(), (t2 = this.markerGroup) === null || t2 === void 0 || t2.hide();
  }
  clear() {
    var t2;
    this.layer.clear(), (t2 = this.markerGroup) === null || t2 === void 0 || t2.clearOverlays(), this._polygonCache = [], this._markerCache = [];
  }
  setzIndex(t2) {
    this.layer.setzIndex(t2);
  }
  getZooms() {
    return this._opts.zooms;
  }
  destroy() {
    this._map.removeLayer(this.layer), this._map.remove(this.markerGroup), this._currentFeatures = [], this.clear(), this.layer = null, this._map = null, this._ins = null;
  }
};
var Je = class {
  constructor(t2, e2, r2) {
    this.x = t2, this.y = e2, this.idx = r2;
  }
};
var Ke = Object.defineProperty;
var tr = Object.defineProperties;
var er = Object.getOwnPropertyDescriptors;
var Dt = Object.getOwnPropertySymbols;
var rr = Object.prototype.hasOwnProperty;
var ir = Object.prototype.propertyIsEnumerable;
var Tt = (o, t2, e2) => t2 in o ? Ke(o, t2, { enumerable: true, configurable: true, writable: true, value: e2 }) : o[t2] = e2;
var nr = (o, t2) => {
  for (var e2 in t2 || (t2 = {}))
    rr.call(t2, e2) && Tt(o, e2, t2[e2]);
  if (Dt)
    for (var e2 of Dt(t2))
      ir.call(t2, e2) && Tt(o, e2, t2[e2]);
  return o;
};
var or = (o, t2) => tr(o, er(t2));
var sr = class extends Y {
  constructor(t2) {
    super(), this._data = { list: [], bounds: null, source: null }, this._mouseEvent = v.bind(v.debounce(() => {
      this.renderLater();
    }, 50), this), this.initCSS();
    const e2 = { autoSetFitView: true, topAdcodes: [1e5], visible: true, excludedAdcodes: null, zIndex: 10, renderOptions: {} };
    this._opts = v.extend({}, e2, t2), this.map = t2.map, this._distMgr = new Ze({ topAdcodes: this._opts.topAdcodes, excludedAdcodes: this._opts.excludedAdcodes }), this._distCounter = new qe({ distMgr: this._distMgr, pointPackerThisArg: this, pointPacker: (r2) => this._packDataItem(r2) }), this.renderEngine = new Qe(this, or(nr({}, t2.renderOptions), { zIndex: this._opts.zIndex, visible: this._opts.visible, map: t2.map })), this.renderEngine.on("*", (r2, ...i) => {
      this.emit(r2, ...i);
    }), this._opts.data && this.setData(this._opts.data), this.bindOrUnbindMapEvent();
  }
  bindOrUnbindMapEvent(t2 = true) {
    const e2 = t2 ? "on" : "off";
    this.map[e2]("moveend", this._mouseEvent), this.map[e2]("zoomend", this._mouseEvent), this.map[e2]("resize", this._mouseEvent), this.map[e2]("rotateend", this._mouseEvent), this.map[e2]("dragend", this._mouseEvent);
  }
  initCSS() {
    const t2 = "_amap_district_cluster_css";
    if (document.getElementById(t2))
      return;
    const e2 = ".amap-ui-district-cluster-container{cursor:default;-webkit-backface-visibility:hidden;-webkit-transform:translateZ(0) scale(1,1)}.amap-ui-district-cluster-container canvas{position:absolute}.amap-ui-district-cluster-container .amap-ui-hide{display:none!important}.amap-ui-district-cluster-container .overlay-title,.amap-ui-district-cluster-marker{color:#555;background-color:#fffeef;font-size:12px;white-space:nowrap;position:absolute}.amap-ui-district-cluster-container .overlay-title{padding:2px 6px;display:inline-block;z-index:99999;border:1px solid #7e7e7e;border-radius:2px}.amap-ui-district-cluster-container .overlay-title:after,.amap-ui-district-cluster-container .overlay-title:before{content:'';display:block;position:absolute;margin:auto;width:0;height:0;border:solid transparent;border-width:5px}.amap-ui-district-cluster-container .overlay-title.left{transform:translate(10px,-50%)}.amap-ui-district-cluster-container .overlay-title.left:before{top:5px}.amap-ui-district-cluster-container .overlay-title.left:after{left:-9px;top:5px;border-right-color:#fffeef}.amap-ui-district-cluster-container .overlay-title.left:before{left:-10px;border-right-color:#7e7e7e}.amap-ui-district-cluster-container .overlay-title.top{transform:translate(-50%,-130%)}.amap-ui-district-cluster-container .overlay-title.top:before{left:0;right:0}.amap-ui-district-cluster-container .overlay-title.top:after{bottom:-9px;left:0;right:0;border-top-color:#fffeef}.amap-ui-district-cluster-container .overlay-title.top:before{bottom:-10px;border-top-color:#7e7e7e}.amap-ui-district-cluster-marker{border:1px solid #8e8e8e;width:auto;height:22px;border-radius:5px 5px 5px 0;left:0;top:0}.amap-ui-district-cluster-marker:after,.amap-ui-district-cluster-marker:before{content:'';display:block;position:absolute;width:0;height:0;border:solid rgba(0,0,0,0);border-width:6px;left:13px}.amap-ui-district-cluster-marker:after{bottom:-12px;border-top-color:#fffeef}.amap-ui-district-cluster-marker:before{bottom:-13px;border-top-color:#8e8e8e}.amap-ui-district-cluster-marker span{vertical-align:middle;padding:3px 5px;display:inline-block;height:16px;line-height:16px}.amap-ui-district-cluster-marker-title{border-radius:5px 0 0 0}.amap-ui-district-cluster-marker-body{background-color:#dc3912;color:#fff;border-radius:0 5px 5px 0}.amap-ui-district-cluster-marker.level_country .amap-ui-district-cluster-marker-body{background-color:#36c}.amap-ui-district-cluster-marker.level_province .amap-ui-district-cluster-marker-body{background-color:#dc3912}.amap-ui-district-cluster-marker.level_city .amap-ui-district-cluster-marker-body{background-color:#909}.amap-ui-district-cluster-marker.level_district .amap-ui-district-cluster-marker-body{background-color:#d47}", r2 = document, i = "appendChild", n2 = "styleSheet", s = r2.createElement("style");
    s.id = t2, s.type = "text/css", r2.getElementsByTagName("head")[0][i](s), s[n2] ? s[n2].cssText = e2 : s[i](r2.createTextNode(e2));
  }
  getMinZoomToShowSub(t2) {
    return this.renderEngine.getMinZoomToShowSub(t2);
  }
  getAreaNodeProps(t2) {
    return this._distMgr.getNodeByAdcode(t2);
  }
  getDistrictExplorer() {
    return this._distMgr.getExplorer();
  }
  getRender() {
    return this.renderEngine;
  }
  zoomToShowSubFeatures(t2, e2) {
    this.renderEngine.zoomToShowSubFeatures(t2, e2);
  }
  renderLater(t2) {
    this.renderEngine.renderLater(t2);
  }
  render() {
    this.renderEngine.render();
  }
  forceRender() {
    this.renderEngine.forceRender();
  }
  getDistMgr() {
    return this._distMgr;
  }
  _clearData() {
    this.trigger("willClearData"), this._data ? this._data.list.length = 0 : this._data = { list: [], bounds: null }, this._data.source = null, this._data.bounds = null, this._data.kdTree = null, this._distCounter.clearData(), this.trigger("didClearData");
  }
  _buildDataItems(t2) {
    const e2 = this._opts, r2 = e2.getPosition, i = this._data.list, n2 = this._data.bounds;
    for (let s = 0, a2 = t2.length; s < a2; s++) {
      let u3 = t2[s], l = r2.call(this, u3, s);
      l && (l.getLng && (l = [l.getLng(), l.getLat()]), i[s] = new Je(l[0], l[1], s), n2.expandByPoint(l[0], l[1]));
    }
  }
  getDataItemsByBounds(t2) {
    const e2 = this._data.kdTree;
    if (!e2)
      return null;
    const r2 = t2.getSouthWest(), i = t2.getNorthEast(), n2 = this._data.list, s = e2.range(r2.getLng(), r2.getLat(), i.getLng(), i.getLat()), a2 = [];
    for (let u3 = 0, l = s.length; u3 < l; u3++)
      a2[u3] = this._packDataItem(n2[s[u3]]);
    return a2;
  }
  _packDataItem(t2) {
    if (!t2)
      return null;
    if (!t2._packedItem) {
      const e2 = t2.idx, r2 = [t2.x, t2.y];
      t2._packedItem = { dataIndex: e2, dataItem: this._data.source[e2], position: r2 };
    }
    return t2._packedItem;
  }
  _buildData(t2) {
    this._clearData(), this.trigger("willBuildData", t2), this._data.source = t2, this._data.bounds = C.getBoundsItemToExpand(), this._buildDataItems(t2), this._distCounter.setData(this._data.list), this.trigger("didBuildData", t2);
  }
  setData(t2) {
    t2 || (t2 = []), this._buildData(t2), this.forceRender(), t2.length && this._opts.autoSetFitView && this.setFitView();
  }
  isReady() {
    return this._distMgr.isReady() && !!this._data;
  }
  setFitView() {
    const t2 = this._data.bounds, e2 = this.getMap(), r2 = new AMap.Bounds([t2.x, t2.y], [t2.x + t2.width, t2.y + t2.height]);
    e2 && e2.setBounds(r2);
  }
  getDistCounter() {
    return this._distCounter;
  }
  getMap() {
    return this._opts.map;
  }
  getZooms() {
    return this.renderEngine.getZooms();
  }
  isHidden() {
    return !this._opts.visible;
  }
  show() {
    return this._opts.visible = true, this.getRender().show();
  }
  hide() {
    return this._opts.visible = false, this.getRender().hide();
  }
  destroy() {
    this.bindOrUnbindMapEvent(false), this.getRender().destroy(), this._distCounter.destroy(), this._distMgr.destroy(), this.renderEngine = null, this._data = { list: [], bounds: null }, this._distMgr = null, this.map = void 0, this._opts = void 0;
  }
  getzIndex() {
    return this._opts.zIndex;
  }
  setzIndex(t2) {
    this._opts.zIndex = t2, this.getRender().setzIndex(t2);
  }
};

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/DistrictCluster/DistrictCluster.vue2.mjs
var script46 = defineComponent({
  ...{
    name: "ElAmapLayerDistrictCluster",
    inheritAttrs: false
  },
  __name: "DistrictCluster",
  props: buildProps({
    data: {
      required: true,
      type: Array
    },
    // 数据源数组，每个元素即为点相关的信息
    getPosition: {
      type: Function
    },
    // 返回数据项中的经纬度信息
    autoSetFitView: {
      type: Boolean,
      default: true
    },
    // 是否在绘制后自动调整地图视野以适合全部点，默认true
    topAdcodes: {
      type: Array
    },
    // 顶层区划的adcode列表
    excludedAdcodes: {
      type: Array
    },
    renderOptions: {
      type: Object
    }
    // 绘制的引擎的参数
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        options.map = parentComponent;
        $amapComponent = new sr(options);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent) {
          $amapComponent.destroy();
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/DistrictCluster/DistrictCluster.vue.mjs
script46.__file = "src/vue-amap/packages/layer/data/DistrictCluster/DistrictCluster.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/DistrictCluster/index.mjs
script46.install = (app) => {
  app.component(script46.name, script46);
  return app;
};
var ElAmapLayerDistrictCluster = script46;

// node_modules/.pnpm/@vuemap+amap-xyz-layer@0.0.15/node_modules/@vuemap/amap-xyz-layer/dist/index.mjs
var hr = { exports: {} };
hr.exports = cr, hr.exports.default = cr;
function cr(r2, e2, t2) {
  t2 = t2 || 2;
  var n2 = e2 && e2.length, i = n2 ? e2[0] * t2 : r2.length, a2 = Zr(r2, 0, i, t2, true), s = [];
  if (!a2 || a2.next === a2.prev)
    return s;
  var h, c, l, f, v2, o, M;
  if (n2 && (a2 = ct2(r2, e2, a2, t2)), r2.length > 80 * t2) {
    h = l = r2[0], c = f = r2[1];
    for (var p2 = t2; p2 < i; p2 += t2)
      v2 = r2[p2], o = r2[p2 + 1], v2 < h && (h = v2), o < c && (c = o), v2 > l && (l = v2), o > f && (f = o);
    M = Math.max(l - h, f - c), M = M !== 0 ? 32767 / M : 0;
  }
  return tr2(a2, s, t2, h, c, M, 0), s;
}
function Zr(r2, e2, t2, n2, i) {
  var a2, s;
  if (i === wr(r2, e2, t2, n2) > 0)
    for (a2 = e2; a2 < t2; a2 += n2)
      s = Ur(a2, r2[a2], r2[a2 + 1], s);
  else
    for (a2 = t2 - n2; a2 >= e2; a2 -= n2)
      s = Ur(a2, r2[a2], r2[a2 + 1], s);
  return s && lr(s, s.next) && (ir2(s), s = s.next), s;
}
function H2(r2, e2) {
  if (!r2)
    return r2;
  e2 || (e2 = r2);
  var t2 = r2, n2;
  do
    if (n2 = false, !t2.steiner && (lr(t2, t2.next) || Y2(t2.prev, t2, t2.next) === 0)) {
      if (ir2(t2), t2 = e2 = t2.prev, t2 === t2.next)
        break;
      n2 = true;
    } else
      t2 = t2.next;
  while (n2 || t2 !== e2);
  return e2;
}
function tr2(r2, e2, t2, n2, i, a2, s) {
  if (!!r2) {
    !s && a2 && Mt2(r2, n2, i, a2);
    for (var h = r2, c, l; r2.prev !== r2.next; ) {
      if (c = r2.prev, l = r2.next, a2 ? at2(r2, n2, i, a2) : it2(r2)) {
        e2.push(c.i / t2 | 0), e2.push(r2.i / t2 | 0), e2.push(l.i / t2 | 0), ir2(r2), r2 = l.next, h = l.next;
        continue;
      }
      if (r2 = l, r2 === h) {
        s ? s === 1 ? (r2 = st2(H2(r2), e2, t2), tr2(r2, e2, t2, n2, i, a2, 2)) : s === 2 && ht2(r2, e2, t2, n2, i, a2) : tr2(H2(r2), e2, t2, n2, i, a2, 1);
        break;
      }
    }
  }
}
function it2(r2) {
  var e2 = r2.prev, t2 = r2, n2 = r2.next;
  if (Y2(e2, t2, n2) >= 0)
    return false;
  for (var i = e2.x, a2 = t2.x, s = n2.x, h = e2.y, c = t2.y, l = n2.y, f = i < a2 ? i < s ? i : s : a2 < s ? a2 : s, v2 = h < c ? h < l ? h : l : c < l ? c : l, o = i > a2 ? i > s ? i : s : a2 > s ? a2 : s, M = h > c ? h > l ? h : l : c > l ? c : l, p2 = n2.next; p2 !== e2; ) {
    if (p2.x >= f && p2.x <= o && p2.y >= v2 && p2.y <= M && j2(i, h, a2, c, s, l, p2.x, p2.y) && Y2(p2.prev, p2, p2.next) >= 0)
      return false;
    p2 = p2.next;
  }
  return true;
}
function at2(r2, e2, t2, n2) {
  var i = r2.prev, a2 = r2, s = r2.next;
  if (Y2(i, a2, s) >= 0)
    return false;
  for (var h = i.x, c = a2.x, l = s.x, f = i.y, v2 = a2.y, o = s.y, M = h < c ? h < l ? h : l : c < l ? c : l, p2 = f < v2 ? f < o ? f : o : v2 < o ? v2 : o, x = h > c ? h > l ? h : l : c > l ? c : l, y = f > v2 ? f > o ? f : o : v2 > o ? v2 : o, d2 = Pr(M, p2, e2, t2, n2), R = Pr(x, y, e2, t2, n2), m2 = r2.prevZ, g2 = r2.nextZ; m2 && m2.z >= d2 && g2 && g2.z <= R; ) {
    if (m2.x >= M && m2.x <= x && m2.y >= p2 && m2.y <= y && m2 !== i && m2 !== s && j2(h, f, c, v2, l, o, m2.x, m2.y) && Y2(m2.prev, m2, m2.next) >= 0 || (m2 = m2.prevZ, g2.x >= M && g2.x <= x && g2.y >= p2 && g2.y <= y && g2 !== i && g2 !== s && j2(h, f, c, v2, l, o, g2.x, g2.y) && Y2(g2.prev, g2, g2.next) >= 0))
      return false;
    g2 = g2.nextZ;
  }
  for (; m2 && m2.z >= d2; ) {
    if (m2.x >= M && m2.x <= x && m2.y >= p2 && m2.y <= y && m2 !== i && m2 !== s && j2(h, f, c, v2, l, o, m2.x, m2.y) && Y2(m2.prev, m2, m2.next) >= 0)
      return false;
    m2 = m2.prevZ;
  }
  for (; g2 && g2.z <= R; ) {
    if (g2.x >= M && g2.x <= x && g2.y >= p2 && g2.y <= y && g2 !== i && g2 !== s && j2(h, f, c, v2, l, o, g2.x, g2.y) && Y2(g2.prev, g2, g2.next) >= 0)
      return false;
    g2 = g2.nextZ;
  }
  return true;
}
function st2(r2, e2, t2) {
  var n2 = r2;
  do {
    var i = n2.prev, a2 = n2.next.next;
    !lr(i, a2) && Dr(i, n2, n2.next, a2) && nr2(i, a2) && nr2(a2, i) && (e2.push(i.i / t2 | 0), e2.push(n2.i / t2 | 0), e2.push(a2.i / t2 | 0), ir2(n2), ir2(n2.next), n2 = r2 = a2), n2 = n2.next;
  } while (n2 !== r2);
  return H2(n2);
}
function ht2(r2, e2, t2, n2, i, a2) {
  var s = r2;
  do {
    for (var h = s.next.next; h !== s.prev; ) {
      if (s.i !== h.i && xt2(s, h)) {
        var c = Vr(s, h);
        s = H2(s, s.next), c = H2(c, c.next), tr2(s, e2, t2, n2, i, a2, 0), tr2(c, e2, t2, n2, i, a2, 0);
        return;
      }
      h = h.next;
    }
    s = s.next;
  } while (s !== r2);
}
function ct2(r2, e2, t2, n2) {
  var i = [], a2, s, h, c, l;
  for (a2 = 0, s = e2.length; a2 < s; a2++)
    h = e2[a2] * n2, c = a2 < s - 1 ? e2[a2 + 1] * n2 : r2.length, l = Zr(r2, h, c, n2, false), l === l.next && (l.steiner = true), i.push(yt2(l));
  for (i.sort(lt2), a2 = 0; a2 < i.length; a2++)
    t2 = ft2(i[a2], t2);
  return t2;
}
function lt2(r2, e2) {
  return r2.x - e2.x;
}
function ft2(r2, e2) {
  var t2 = ot2(r2, e2);
  if (!t2)
    return e2;
  var n2 = Vr(t2, r2);
  return H2(n2, n2.next), H2(t2, t2.next);
}
function ot2(r2, e2) {
  var t2 = e2, n2 = r2.x, i = r2.y, a2 = -1 / 0, s;
  do {
    if (i <= t2.y && i >= t2.next.y && t2.next.y !== t2.y) {
      var h = t2.x + (i - t2.y) * (t2.next.x - t2.x) / (t2.next.y - t2.y);
      if (h <= n2 && h > a2 && (a2 = h, s = t2.x < t2.next.x ? t2 : t2.next, h === n2))
        return s;
    }
    t2 = t2.next;
  } while (t2 !== e2);
  if (!s)
    return null;
  var c = s, l = s.x, f = s.y, v2 = 1 / 0, o;
  t2 = s;
  do
    n2 >= t2.x && t2.x >= l && n2 !== t2.x && j2(i < f ? n2 : a2, i, l, f, i < f ? a2 : n2, i, t2.x, t2.y) && (o = Math.abs(i - t2.y) / (n2 - t2.x), nr2(t2, r2) && (o < v2 || o === v2 && (t2.x > s.x || t2.x === s.x && vt2(s, t2))) && (s = t2, v2 = o)), t2 = t2.next;
  while (t2 !== c);
  return s;
}
function vt2(r2, e2) {
  return Y2(r2.prev, r2, e2.prev) < 0 && Y2(e2.next, r2, r2.next) < 0;
}
function Mt2(r2, e2, t2, n2) {
  var i = r2;
  do
    i.z === 0 && (i.z = Pr(i.x, i.y, e2, t2, n2)), i.prevZ = i.prev, i.nextZ = i.next, i = i.next;
  while (i !== r2);
  i.prevZ.nextZ = null, i.prevZ = null, pt2(i);
}
function pt2(r2) {
  var e2, t2, n2, i, a2, s, h, c, l = 1;
  do {
    for (t2 = r2, r2 = null, a2 = null, s = 0; t2; ) {
      for (s++, n2 = t2, h = 0, e2 = 0; e2 < l && (h++, n2 = n2.nextZ, !!n2); e2++)
        ;
      for (c = l; h > 0 || c > 0 && n2; )
        h !== 0 && (c === 0 || !n2 || t2.z <= n2.z) ? (i = t2, t2 = t2.nextZ, h--) : (i = n2, n2 = n2.nextZ, c--), a2 ? a2.nextZ = i : r2 = i, i.prevZ = a2, a2 = i;
      t2 = n2;
    }
    a2.nextZ = null, l *= 2;
  } while (s > 1);
  return r2;
}
function Pr(r2, e2, t2, n2, i) {
  return r2 = (r2 - t2) * i | 0, e2 = (e2 - n2) * i | 0, r2 = (r2 | r2 << 8) & 16711935, r2 = (r2 | r2 << 4) & 252645135, r2 = (r2 | r2 << 2) & 858993459, r2 = (r2 | r2 << 1) & 1431655765, e2 = (e2 | e2 << 8) & 16711935, e2 = (e2 | e2 << 4) & 252645135, e2 = (e2 | e2 << 2) & 858993459, e2 = (e2 | e2 << 1) & 1431655765, r2 | e2 << 1;
}
function yt2(r2) {
  var e2 = r2, t2 = r2;
  do
    (e2.x < t2.x || e2.x === t2.x && e2.y < t2.y) && (t2 = e2), e2 = e2.next;
  while (e2 !== r2);
  return t2;
}
function j2(r2, e2, t2, n2, i, a2, s, h) {
  return (i - s) * (e2 - h) >= (r2 - s) * (a2 - h) && (r2 - s) * (n2 - h) >= (t2 - s) * (e2 - h) && (t2 - s) * (a2 - h) >= (i - s) * (n2 - h);
}
function xt2(r2, e2) {
  return r2.next.i !== e2.i && r2.prev.i !== e2.i && !mt2(r2, e2) && (nr2(r2, e2) && nr2(e2, r2) && dt2(r2, e2) && (Y2(r2.prev, r2, e2.prev) || Y2(r2, e2.prev, e2)) || lr(r2, e2) && Y2(r2.prev, r2, r2.next) > 0 && Y2(e2.prev, e2, e2.next) > 0);
}
function Y2(r2, e2, t2) {
  return (e2.y - r2.y) * (t2.x - e2.x) - (e2.x - r2.x) * (t2.y - e2.y);
}
function lr(r2, e2) {
  return r2.x === e2.x && r2.y === e2.y;
}
function Dr(r2, e2, t2, n2) {
  var i = or2(Y2(r2, e2, t2)), a2 = or2(Y2(r2, e2, n2)), s = or2(Y2(t2, n2, r2)), h = or2(Y2(t2, n2, e2));
  return !!(i !== a2 && s !== h || i === 0 && fr(r2, t2, e2) || a2 === 0 && fr(r2, n2, e2) || s === 0 && fr(t2, r2, n2) || h === 0 && fr(t2, e2, n2));
}
function fr(r2, e2, t2) {
  return e2.x <= Math.max(r2.x, t2.x) && e2.x >= Math.min(r2.x, t2.x) && e2.y <= Math.max(r2.y, t2.y) && e2.y >= Math.min(r2.y, t2.y);
}
function or2(r2) {
  return r2 > 0 ? 1 : r2 < 0 ? -1 : 0;
}
function mt2(r2, e2) {
  var t2 = r2;
  do {
    if (t2.i !== r2.i && t2.next.i !== r2.i && t2.i !== e2.i && t2.next.i !== e2.i && Dr(t2, t2.next, r2, e2))
      return true;
    t2 = t2.next;
  } while (t2 !== r2);
  return false;
}
function nr2(r2, e2) {
  return Y2(r2.prev, r2, r2.next) < 0 ? Y2(r2, e2, r2.next) >= 0 && Y2(r2, r2.prev, e2) >= 0 : Y2(r2, e2, r2.prev) < 0 || Y2(r2, r2.next, e2) < 0;
}
function dt2(r2, e2) {
  var t2 = r2, n2 = false, i = (r2.x + e2.x) / 2, a2 = (r2.y + e2.y) / 2;
  do
    t2.y > a2 != t2.next.y > a2 && t2.next.y !== t2.y && i < (t2.next.x - t2.x) * (a2 - t2.y) / (t2.next.y - t2.y) + t2.x && (n2 = !n2), t2 = t2.next;
  while (t2 !== r2);
  return n2;
}
function Vr(r2, e2) {
  var t2 = new Lr(r2.i, r2.x, r2.y), n2 = new Lr(e2.i, e2.x, e2.y), i = r2.next, a2 = e2.prev;
  return r2.next = e2, e2.prev = r2, t2.next = i, i.prev = t2, n2.next = t2, t2.prev = n2, a2.next = n2, n2.prev = a2, n2;
}
function Ur(r2, e2, t2, n2) {
  var i = new Lr(r2, e2, t2);
  return n2 ? (i.next = n2.next, i.prev = n2, n2.next.prev = i, n2.next = i) : (i.prev = i, i.next = i), i;
}
function ir2(r2) {
  r2.next.prev = r2.prev, r2.prev.next = r2.next, r2.prevZ && (r2.prevZ.nextZ = r2.nextZ), r2.nextZ && (r2.nextZ.prevZ = r2.prevZ);
}
function Lr(r2, e2, t2) {
  this.i = r2, this.x = e2, this.y = t2, this.prev = null, this.next = null, this.z = 0, this.prevZ = null, this.nextZ = null, this.steiner = false;
}
cr.deviation = function(r2, e2, t2, n2) {
  var i = e2 && e2.length, a2 = i ? e2[0] * t2 : r2.length, s = Math.abs(wr(r2, 0, a2, t2));
  if (i)
    for (var h = 0, c = e2.length; h < c; h++) {
      var l = e2[h] * t2, f = h < c - 1 ? e2[h + 1] * t2 : r2.length;
      s -= Math.abs(wr(r2, l, f, t2));
    }
  var v2 = 0;
  for (h = 0; h < n2.length; h += 3) {
    var o = n2[h] * t2, M = n2[h + 1] * t2, p2 = n2[h + 2] * t2;
    v2 += Math.abs((r2[o] - r2[p2]) * (r2[M + 1] - r2[o + 1]) - (r2[o] - r2[M]) * (r2[p2 + 1] - r2[o + 1]));
  }
  return s === 0 && v2 === 0 ? 0 : Math.abs((v2 - s) / s);
};
function wr(r2, e2, t2, n2) {
  for (var i = 0, a2 = e2, s = t2 - n2; a2 < t2; a2 += n2)
    i += (r2[s] - r2[a2]) * (r2[a2 + 1] + r2[s + 1]), s = a2;
  return i;
}
cr.flatten = function(r2) {
  for (var e2 = r2[0][0].length, t2 = { vertices: [], holes: [], dimensions: e2 }, n2 = 0, i = 0; i < r2.length; i++) {
    for (var a2 = 0; a2 < r2[i].length; a2++)
      for (var s = 0; s < e2; s++)
        t2.vertices.push(r2[i][a2][s]);
    i > 0 && (n2 += r2[i - 1].length, t2.holes.push(n2));
  }
  return t2;
};
var X2 = { EPSILON: 1e-12, debug: false, precision: 4, printTypes: false, printDegrees: false, printRowMajor: true };
function gt2(r2, { precision: e2 = X2.precision } = {}) {
  return r2 = ut2(r2), "".concat(parseFloat(r2.toPrecision(e2)));
}
function $r(r2) {
  return Array.isArray(r2) || ArrayBuffer.isView(r2) && !(r2 instanceof DataView);
}
function Xr(r2, e2, t2) {
  const n2 = X2.EPSILON;
  t2 && (X2.EPSILON = t2);
  try {
    if (r2 === e2)
      return true;
    if ($r(r2) && $r(e2)) {
      if (r2.length !== e2.length)
        return false;
      for (let i = 0; i < r2.length; ++i)
        if (!Xr(r2[i], e2[i]))
          return false;
      return true;
    }
    return r2 && r2.equals ? r2.equals(e2) : e2 && e2.equals ? e2.equals(r2) : typeof r2 == "number" && typeof e2 == "number" ? Math.abs(r2 - e2) <= X2.EPSILON * Math.max(1, Math.abs(r2), Math.abs(e2)) : false;
  } finally {
    X2.EPSILON = n2;
  }
}
function ut2(r2) {
  return Math.round(r2 / X2.EPSILON) * X2.EPSILON;
}
function Tt2(r2) {
  function e2() {
    var t2 = Reflect.construct(r2, Array.from(arguments));
    return Object.setPrototypeOf(t2, Object.getPrototypeOf(this)), t2;
  }
  return e2.prototype = Object.create(r2.prototype, { constructor: { value: r2, enumerable: false, writable: true, configurable: true } }), Object.setPrototypeOf ? Object.setPrototypeOf(e2, r2) : e2.__proto__ = r2, e2;
}
var Et2 = class extends Tt2(Array) {
  clone() {
    return new this.constructor().copy(this);
  }
  fromArray(e2, t2 = 0) {
    for (let n2 = 0; n2 < this.ELEMENTS; ++n2)
      this[n2] = e2[n2 + t2];
    return this.check();
  }
  toArray(e2 = [], t2 = 0) {
    for (let n2 = 0; n2 < this.ELEMENTS; ++n2)
      e2[t2 + n2] = this[n2];
    return e2;
  }
  from(e2) {
    return Array.isArray(e2) ? this.copy(e2) : this.fromObject(e2);
  }
  to(e2) {
    return e2 === this ? this : $r(e2) ? this.toArray(e2) : this.toObject(e2);
  }
  toTarget(e2) {
    return e2 ? this.to(e2) : this;
  }
  toFloat32Array() {
    return new Float32Array(this);
  }
  toString() {
    return this.formatString(X2);
  }
  formatString(e2) {
    let t2 = "";
    for (let n2 = 0; n2 < this.ELEMENTS; ++n2)
      t2 += (n2 > 0 ? ", " : "") + gt2(this[n2], e2);
    return "".concat(e2.printTypes ? this.constructor.name : "", "[").concat(t2, "]");
  }
  equals(e2) {
    if (!e2 || this.length !== e2.length)
      return false;
    for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
      if (!Xr(this[t2], e2[t2]))
        return false;
    return true;
  }
  exactEquals(e2) {
    if (!e2 || this.length !== e2.length)
      return false;
    for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
      if (this[t2] !== e2[t2])
        return false;
    return true;
  }
  negate() {
    for (let e2 = 0; e2 < this.ELEMENTS; ++e2)
      this[e2] = -this[e2];
    return this.check();
  }
  lerp(e2, t2, n2) {
    if (n2 === void 0)
      return this.lerp(this, e2, t2);
    for (let i = 0; i < this.ELEMENTS; ++i) {
      const a2 = e2[i];
      this[i] = a2 + n2 * (t2[i] - a2);
    }
    return this.check();
  }
  min(e2) {
    for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
      this[t2] = Math.min(e2[t2], this[t2]);
    return this.check();
  }
  max(e2) {
    for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
      this[t2] = Math.max(e2[t2], this[t2]);
    return this.check();
  }
  clamp(e2, t2) {
    for (let n2 = 0; n2 < this.ELEMENTS; ++n2)
      this[n2] = Math.min(Math.max(this[n2], e2[n2]), t2[n2]);
    return this.check();
  }
  add(...e2) {
    for (const t2 of e2)
      for (let n2 = 0; n2 < this.ELEMENTS; ++n2)
        this[n2] += t2[n2];
    return this.check();
  }
  subtract(...e2) {
    for (const t2 of e2)
      for (let n2 = 0; n2 < this.ELEMENTS; ++n2)
        this[n2] -= t2[n2];
    return this.check();
  }
  scale(e2) {
    if (typeof e2 == "number")
      for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
        this[t2] *= e2;
    else
      for (let t2 = 0; t2 < this.ELEMENTS && t2 < e2.length; ++t2)
        this[t2] *= e2[t2];
    return this.check();
  }
  multiplyByScalar(e2) {
    for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
      this[t2] *= e2;
    return this.check();
  }
  check() {
    if (X2.debug && !this.validate())
      throw new Error("math.gl: ".concat(this.constructor.name, " some fields set to invalid numbers'"));
    return this;
  }
  validate() {
    let e2 = this.length === this.ELEMENTS;
    for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
      e2 = e2 && Number.isFinite(this[t2]);
    return e2;
  }
  sub(e2) {
    return this.subtract(e2);
  }
  setScalar(e2) {
    for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
      this[t2] = e2;
    return this.check();
  }
  addScalar(e2) {
    for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
      this[t2] += e2;
    return this.check();
  }
  subScalar(e2) {
    return this.addScalar(-e2);
  }
  multiplyScalar(e2) {
    for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
      this[t2] *= e2;
    return this.check();
  }
  divideScalar(e2) {
    return this.multiplyByScalar(1 / e2);
  }
  clampScalar(e2, t2) {
    for (let n2 = 0; n2 < this.ELEMENTS; ++n2)
      this[n2] = Math.min(Math.max(this[n2], e2), t2);
    return this.check();
  }
  get elements() {
    return this;
  }
};
function _t2(r2, e2) {
  if (r2.length !== e2)
    return false;
  for (let t2 = 0; t2 < r2.length; ++t2)
    if (!Number.isFinite(r2[t2]))
      return false;
  return true;
}
function Rt2(r2) {
  if (!Number.isFinite(r2))
    throw new Error("Invalid number ".concat(r2));
  return r2;
}
function Sr(r2, e2, t2 = "") {
  if (X2.debug && !_t2(r2, e2))
    throw new Error("math.gl: ".concat(t2, " some fields set to invalid numbers'"));
  return r2;
}
var _ = {};
var W = {};
Object.defineProperty(W, "__esModule", { value: true }), W.setMatrixArrayType = At2, W.toRadian = Lt2, W.equals = wt2, W.RANDOM = W.ARRAY_TYPE = W.EPSILON = void 0;
var Ir = 1e-6;
W.EPSILON = Ir;
var Kr = typeof Float32Array != "undefined" ? Float32Array : Array;
W.ARRAY_TYPE = Kr;
var Ot2 = Math.random;
W.RANDOM = Ot2;
function At2(r2) {
  W.ARRAY_TYPE = Kr = r2;
}
var Pt2 = Math.PI / 180;
function Lt2(r2) {
  return r2 * Pt2;
}
function wt2(r2, e2) {
  return Math.abs(r2 - e2) <= Ir * Math.max(1, Math.abs(r2), Math.abs(e2));
}
Math.hypot || (Math.hypot = function() {
  for (var r2 = 0, e2 = arguments.length; e2--; )
    r2 += arguments[e2] * arguments[e2];
  return Math.sqrt(r2);
});
function vr(r2) {
  return typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? vr = function(t2) {
    return typeof t2;
  } : vr = function(t2) {
    return t2 && typeof Symbol == "function" && t2.constructor === Symbol && t2 !== Symbol.prototype ? "symbol" : typeof t2;
  }, vr(r2);
}
Object.defineProperty(_, "__esModule", { value: true }), _.create = Hr, _.clone = zt2, _.fromValues = Ct2, _.copy = Ft2, _.set = Nt2, _.add = bt2, _.subtract = Qr, _.multiply = Jr, _.divide = jr, _.ceil = qt2, _.floor = Yt2, _.min = kt2, _.max = Wt2, _.round = Bt2, _.scale = Zt2, _.scaleAndAdd = Dt2, _.distance = re2, _.squaredDistance = ee3, _.length = te2, _.squaredLength = ne2, _.negate = Vt2, _.inverse = Ut2, _.normalize = Xt2, _.dot = It2, _.cross = Kt2, _.lerp = Gt2, _.random = Ht2, _.transformMat2 = Qt2, _.transformMat2d = Jt2, _.transformMat3 = jt2;
var $t2 = _.transformMat4 = rn;
_.rotate = en, _.angle = tn, _.zero = nn, _.str = an, _.exactEquals = sn, _.equals = hn, _.forEach = _.sqrLen = _.sqrDist = _.dist = _.div = _.mul = _.sub = _.len = void 0;
var Q2 = St2(W);
function Gr(r2) {
  if (typeof WeakMap != "function")
    return null;
  var e2 = /* @__PURE__ */ new WeakMap(), t2 = /* @__PURE__ */ new WeakMap();
  return (Gr = function(i) {
    return i ? t2 : e2;
  })(r2);
}
function St2(r2, e2) {
  if (!e2 && r2 && r2.__esModule)
    return r2;
  if (r2 === null || vr(r2) !== "object" && typeof r2 != "function")
    return { default: r2 };
  var t2 = Gr(e2);
  if (t2 && t2.has(r2))
    return t2.get(r2);
  var n2 = {}, i = Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var a2 in r2)
    if (a2 !== "default" && Object.prototype.hasOwnProperty.call(r2, a2)) {
      var s = i ? Object.getOwnPropertyDescriptor(r2, a2) : null;
      s && (s.get || s.set) ? Object.defineProperty(n2, a2, s) : n2[a2] = r2[a2];
    }
  return n2.default = r2, t2 && t2.set(r2, n2), n2;
}
function Hr() {
  var r2 = new Q2.ARRAY_TYPE(2);
  return Q2.ARRAY_TYPE != Float32Array && (r2[0] = 0, r2[1] = 0), r2;
}
function zt2(r2) {
  var e2 = new Q2.ARRAY_TYPE(2);
  return e2[0] = r2[0], e2[1] = r2[1], e2;
}
function Ct2(r2, e2) {
  var t2 = new Q2.ARRAY_TYPE(2);
  return t2[0] = r2, t2[1] = e2, t2;
}
function Ft2(r2, e2) {
  return r2[0] = e2[0], r2[1] = e2[1], r2;
}
function Nt2(r2, e2, t2) {
  return r2[0] = e2, r2[1] = t2, r2;
}
function bt2(r2, e2, t2) {
  return r2[0] = e2[0] + t2[0], r2[1] = e2[1] + t2[1], r2;
}
function Qr(r2, e2, t2) {
  return r2[0] = e2[0] - t2[0], r2[1] = e2[1] - t2[1], r2;
}
function Jr(r2, e2, t2) {
  return r2[0] = e2[0] * t2[0], r2[1] = e2[1] * t2[1], r2;
}
function jr(r2, e2, t2) {
  return r2[0] = e2[0] / t2[0], r2[1] = e2[1] / t2[1], r2;
}
function qt2(r2, e2) {
  return r2[0] = Math.ceil(e2[0]), r2[1] = Math.ceil(e2[1]), r2;
}
function Yt2(r2, e2) {
  return r2[0] = Math.floor(e2[0]), r2[1] = Math.floor(e2[1]), r2;
}
function kt2(r2, e2, t2) {
  return r2[0] = Math.min(e2[0], t2[0]), r2[1] = Math.min(e2[1], t2[1]), r2;
}
function Wt2(r2, e2, t2) {
  return r2[0] = Math.max(e2[0], t2[0]), r2[1] = Math.max(e2[1], t2[1]), r2;
}
function Bt2(r2, e2) {
  return r2[0] = Math.round(e2[0]), r2[1] = Math.round(e2[1]), r2;
}
function Zt2(r2, e2, t2) {
  return r2[0] = e2[0] * t2, r2[1] = e2[1] * t2, r2;
}
function Dt2(r2, e2, t2, n2) {
  return r2[0] = e2[0] + t2[0] * n2, r2[1] = e2[1] + t2[1] * n2, r2;
}
function re2(r2, e2) {
  var t2 = e2[0] - r2[0], n2 = e2[1] - r2[1];
  return Math.hypot(t2, n2);
}
function ee3(r2, e2) {
  var t2 = e2[0] - r2[0], n2 = e2[1] - r2[1];
  return t2 * t2 + n2 * n2;
}
function te2(r2) {
  var e2 = r2[0], t2 = r2[1];
  return Math.hypot(e2, t2);
}
function ne2(r2) {
  var e2 = r2[0], t2 = r2[1];
  return e2 * e2 + t2 * t2;
}
function Vt2(r2, e2) {
  return r2[0] = -e2[0], r2[1] = -e2[1], r2;
}
function Ut2(r2, e2) {
  return r2[0] = 1 / e2[0], r2[1] = 1 / e2[1], r2;
}
function Xt2(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = t2 * t2 + n2 * n2;
  return i > 0 && (i = 1 / Math.sqrt(i)), r2[0] = e2[0] * i, r2[1] = e2[1] * i, r2;
}
function It2(r2, e2) {
  return r2[0] * e2[0] + r2[1] * e2[1];
}
function Kt2(r2, e2, t2) {
  var n2 = e2[0] * t2[1] - e2[1] * t2[0];
  return r2[0] = r2[1] = 0, r2[2] = n2, r2;
}
function Gt2(r2, e2, t2, n2) {
  var i = e2[0], a2 = e2[1];
  return r2[0] = i + n2 * (t2[0] - i), r2[1] = a2 + n2 * (t2[1] - a2), r2;
}
function Ht2(r2, e2) {
  e2 = e2 || 1;
  var t2 = Q2.RANDOM() * 2 * Math.PI;
  return r2[0] = Math.cos(t2) * e2, r2[1] = Math.sin(t2) * e2, r2;
}
function Qt2(r2, e2, t2) {
  var n2 = e2[0], i = e2[1];
  return r2[0] = t2[0] * n2 + t2[2] * i, r2[1] = t2[1] * n2 + t2[3] * i, r2;
}
function Jt2(r2, e2, t2) {
  var n2 = e2[0], i = e2[1];
  return r2[0] = t2[0] * n2 + t2[2] * i + t2[4], r2[1] = t2[1] * n2 + t2[3] * i + t2[5], r2;
}
function jt2(r2, e2, t2) {
  var n2 = e2[0], i = e2[1];
  return r2[0] = t2[0] * n2 + t2[3] * i + t2[6], r2[1] = t2[1] * n2 + t2[4] * i + t2[7], r2;
}
function rn(r2, e2, t2) {
  var n2 = e2[0], i = e2[1];
  return r2[0] = t2[0] * n2 + t2[4] * i + t2[12], r2[1] = t2[1] * n2 + t2[5] * i + t2[13], r2;
}
function en(r2, e2, t2, n2) {
  var i = e2[0] - t2[0], a2 = e2[1] - t2[1], s = Math.sin(n2), h = Math.cos(n2);
  return r2[0] = i * h - a2 * s + t2[0], r2[1] = i * s + a2 * h + t2[1], r2;
}
function tn(r2, e2) {
  var t2 = r2[0], n2 = r2[1], i = e2[0], a2 = e2[1], s = Math.sqrt(t2 * t2 + n2 * n2) * Math.sqrt(i * i + a2 * a2), h = s && (t2 * i + n2 * a2) / s;
  return Math.acos(Math.min(Math.max(h, -1), 1));
}
function nn(r2) {
  return r2[0] = 0, r2[1] = 0, r2;
}
function an(r2) {
  return "vec2(" + r2[0] + ", " + r2[1] + ")";
}
function sn(r2, e2) {
  return r2[0] === e2[0] && r2[1] === e2[1];
}
function hn(r2, e2) {
  var t2 = r2[0], n2 = r2[1], i = e2[0], a2 = e2[1];
  return Math.abs(t2 - i) <= Q2.EPSILON * Math.max(1, Math.abs(t2), Math.abs(i)) && Math.abs(n2 - a2) <= Q2.EPSILON * Math.max(1, Math.abs(n2), Math.abs(a2));
}
var cn = te2;
_.len = cn;
var ln = Qr;
_.sub = ln;
var fn = Jr;
_.mul = fn;
var on = jr;
_.div = on;
var vn = re2;
_.dist = vn;
var Mn = ee3;
_.sqrDist = Mn;
var pn = ne2;
_.sqrLen = pn;
var yn = function() {
  var r2 = Hr();
  return function(e2, t2, n2, i, a2, s) {
    var h, c;
    for (t2 || (t2 = 2), n2 || (n2 = 0), i ? c = Math.min(i * t2 + n2, e2.length) : c = e2.length, h = n2; h < c; h += t2)
      r2[0] = e2[h], r2[1] = e2[h + 1], a2(r2, r2, s), e2[h] = r2[0], e2[h + 1] = r2[1];
    return e2;
  };
}();
_.forEach = yn;
function xn(r2, e2, t2) {
  const n2 = e2[0], i = e2[1], a2 = t2[3] * n2 + t2[7] * i || 1;
  return r2[0] = (t2[0] * n2 + t2[4] * i) / a2, r2[1] = (t2[1] * n2 + t2[5] * i) / a2, r2;
}
function mn(r2, e2, t2) {
  const n2 = e2[0], i = e2[1], a2 = e2[2], s = t2[3] * n2 + t2[7] * i + t2[11] * a2 || 1;
  return r2[0] = (t2[0] * n2 + t2[4] * i + t2[8] * a2) / s, r2[1] = (t2[1] * n2 + t2[5] * i + t2[9] * a2) / s, r2[2] = (t2[2] * n2 + t2[6] * i + t2[10] * a2) / s, r2;
}
var T = {};
function Mr(r2) {
  return typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? Mr = function(t2) {
    return typeof t2;
  } : Mr = function(t2) {
    return t2 && typeof Symbol == "function" && t2.constructor === Symbol && t2 !== Symbol.prototype ? "symbol" : typeof t2;
  }, Mr(r2);
}
Object.defineProperty(T, "__esModule", { value: true }), T.create = ae2, T.clone = un, T.length = se2, T.fromValues = Tn, T.copy = En, T.set = _n, T.add = Rn, T.subtract = he2, T.multiply = ce2, T.divide = le2, T.ceil = On, T.floor = An, T.min = Pn, T.max = Ln, T.round = wn, T.scale = $n, T.scaleAndAdd = Sn, T.distance = fe2, T.squaredDistance = oe2, T.squaredLength = ve2, T.negate = zn, T.inverse = Cn, T.normalize = Fn, T.dot = Me2, T.cross = Nn, T.lerp = bn, T.hermite = qn, T.bezier = Yn, T.random = kn;
var dn = T.transformMat4 = Wn;
T.transformMat3 = Bn, T.transformQuat = Zn, T.rotateX = Dn, T.rotateY = Vn, T.rotateZ = Un, T.angle = Xn, T.zero = In, T.str = Kn, T.exactEquals = Gn, T.equals = Hn, T.forEach = T.sqrLen = T.len = T.sqrDist = T.dist = T.div = T.mul = T.sub = void 0;
var I2 = gn(W);
function ie2(r2) {
  if (typeof WeakMap != "function")
    return null;
  var e2 = /* @__PURE__ */ new WeakMap(), t2 = /* @__PURE__ */ new WeakMap();
  return (ie2 = function(i) {
    return i ? t2 : e2;
  })(r2);
}
function gn(r2, e2) {
  if (!e2 && r2 && r2.__esModule)
    return r2;
  if (r2 === null || Mr(r2) !== "object" && typeof r2 != "function")
    return { default: r2 };
  var t2 = ie2(e2);
  if (t2 && t2.has(r2))
    return t2.get(r2);
  var n2 = {}, i = Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var a2 in r2)
    if (a2 !== "default" && Object.prototype.hasOwnProperty.call(r2, a2)) {
      var s = i ? Object.getOwnPropertyDescriptor(r2, a2) : null;
      s && (s.get || s.set) ? Object.defineProperty(n2, a2, s) : n2[a2] = r2[a2];
    }
  return n2.default = r2, t2 && t2.set(r2, n2), n2;
}
function ae2() {
  var r2 = new I2.ARRAY_TYPE(3);
  return I2.ARRAY_TYPE != Float32Array && (r2[0] = 0, r2[1] = 0, r2[2] = 0), r2;
}
function un(r2) {
  var e2 = new I2.ARRAY_TYPE(3);
  return e2[0] = r2[0], e2[1] = r2[1], e2[2] = r2[2], e2;
}
function se2(r2) {
  var e2 = r2[0], t2 = r2[1], n2 = r2[2];
  return Math.hypot(e2, t2, n2);
}
function Tn(r2, e2, t2) {
  var n2 = new I2.ARRAY_TYPE(3);
  return n2[0] = r2, n2[1] = e2, n2[2] = t2, n2;
}
function En(r2, e2) {
  return r2[0] = e2[0], r2[1] = e2[1], r2[2] = e2[2], r2;
}
function _n(r2, e2, t2, n2) {
  return r2[0] = e2, r2[1] = t2, r2[2] = n2, r2;
}
function Rn(r2, e2, t2) {
  return r2[0] = e2[0] + t2[0], r2[1] = e2[1] + t2[1], r2[2] = e2[2] + t2[2], r2;
}
function he2(r2, e2, t2) {
  return r2[0] = e2[0] - t2[0], r2[1] = e2[1] - t2[1], r2[2] = e2[2] - t2[2], r2;
}
function ce2(r2, e2, t2) {
  return r2[0] = e2[0] * t2[0], r2[1] = e2[1] * t2[1], r2[2] = e2[2] * t2[2], r2;
}
function le2(r2, e2, t2) {
  return r2[0] = e2[0] / t2[0], r2[1] = e2[1] / t2[1], r2[2] = e2[2] / t2[2], r2;
}
function On(r2, e2) {
  return r2[0] = Math.ceil(e2[0]), r2[1] = Math.ceil(e2[1]), r2[2] = Math.ceil(e2[2]), r2;
}
function An(r2, e2) {
  return r2[0] = Math.floor(e2[0]), r2[1] = Math.floor(e2[1]), r2[2] = Math.floor(e2[2]), r2;
}
function Pn(r2, e2, t2) {
  return r2[0] = Math.min(e2[0], t2[0]), r2[1] = Math.min(e2[1], t2[1]), r2[2] = Math.min(e2[2], t2[2]), r2;
}
function Ln(r2, e2, t2) {
  return r2[0] = Math.max(e2[0], t2[0]), r2[1] = Math.max(e2[1], t2[1]), r2[2] = Math.max(e2[2], t2[2]), r2;
}
function wn(r2, e2) {
  return r2[0] = Math.round(e2[0]), r2[1] = Math.round(e2[1]), r2[2] = Math.round(e2[2]), r2;
}
function $n(r2, e2, t2) {
  return r2[0] = e2[0] * t2, r2[1] = e2[1] * t2, r2[2] = e2[2] * t2, r2;
}
function Sn(r2, e2, t2, n2) {
  return r2[0] = e2[0] + t2[0] * n2, r2[1] = e2[1] + t2[1] * n2, r2[2] = e2[2] + t2[2] * n2, r2;
}
function fe2(r2, e2) {
  var t2 = e2[0] - r2[0], n2 = e2[1] - r2[1], i = e2[2] - r2[2];
  return Math.hypot(t2, n2, i);
}
function oe2(r2, e2) {
  var t2 = e2[0] - r2[0], n2 = e2[1] - r2[1], i = e2[2] - r2[2];
  return t2 * t2 + n2 * n2 + i * i;
}
function ve2(r2) {
  var e2 = r2[0], t2 = r2[1], n2 = r2[2];
  return e2 * e2 + t2 * t2 + n2 * n2;
}
function zn(r2, e2) {
  return r2[0] = -e2[0], r2[1] = -e2[1], r2[2] = -e2[2], r2;
}
function Cn(r2, e2) {
  return r2[0] = 1 / e2[0], r2[1] = 1 / e2[1], r2[2] = 1 / e2[2], r2;
}
function Fn(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = t2 * t2 + n2 * n2 + i * i;
  return a2 > 0 && (a2 = 1 / Math.sqrt(a2)), r2[0] = e2[0] * a2, r2[1] = e2[1] * a2, r2[2] = e2[2] * a2, r2;
}
function Me2(r2, e2) {
  return r2[0] * e2[0] + r2[1] * e2[1] + r2[2] * e2[2];
}
function Nn(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = t2[0], h = t2[1], c = t2[2];
  return r2[0] = i * c - a2 * h, r2[1] = a2 * s - n2 * c, r2[2] = n2 * h - i * s, r2;
}
function bn(r2, e2, t2, n2) {
  var i = e2[0], a2 = e2[1], s = e2[2];
  return r2[0] = i + n2 * (t2[0] - i), r2[1] = a2 + n2 * (t2[1] - a2), r2[2] = s + n2 * (t2[2] - s), r2;
}
function qn(r2, e2, t2, n2, i, a2) {
  var s = a2 * a2, h = s * (2 * a2 - 3) + 1, c = s * (a2 - 2) + a2, l = s * (a2 - 1), f = s * (3 - 2 * a2);
  return r2[0] = e2[0] * h + t2[0] * c + n2[0] * l + i[0] * f, r2[1] = e2[1] * h + t2[1] * c + n2[1] * l + i[1] * f, r2[2] = e2[2] * h + t2[2] * c + n2[2] * l + i[2] * f, r2;
}
function Yn(r2, e2, t2, n2, i, a2) {
  var s = 1 - a2, h = s * s, c = a2 * a2, l = h * s, f = 3 * a2 * h, v2 = 3 * c * s, o = c * a2;
  return r2[0] = e2[0] * l + t2[0] * f + n2[0] * v2 + i[0] * o, r2[1] = e2[1] * l + t2[1] * f + n2[1] * v2 + i[1] * o, r2[2] = e2[2] * l + t2[2] * f + n2[2] * v2 + i[2] * o, r2;
}
function kn(r2, e2) {
  e2 = e2 || 1;
  var t2 = I2.RANDOM() * 2 * Math.PI, n2 = I2.RANDOM() * 2 - 1, i = Math.sqrt(1 - n2 * n2) * e2;
  return r2[0] = Math.cos(t2) * i, r2[1] = Math.sin(t2) * i, r2[2] = n2 * e2, r2;
}
function Wn(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = t2[3] * n2 + t2[7] * i + t2[11] * a2 + t2[15];
  return s = s || 1, r2[0] = (t2[0] * n2 + t2[4] * i + t2[8] * a2 + t2[12]) / s, r2[1] = (t2[1] * n2 + t2[5] * i + t2[9] * a2 + t2[13]) / s, r2[2] = (t2[2] * n2 + t2[6] * i + t2[10] * a2 + t2[14]) / s, r2;
}
function Bn(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2];
  return r2[0] = n2 * t2[0] + i * t2[3] + a2 * t2[6], r2[1] = n2 * t2[1] + i * t2[4] + a2 * t2[7], r2[2] = n2 * t2[2] + i * t2[5] + a2 * t2[8], r2;
}
function Zn(r2, e2, t2) {
  var n2 = t2[0], i = t2[1], a2 = t2[2], s = t2[3], h = e2[0], c = e2[1], l = e2[2], f = i * l - a2 * c, v2 = a2 * h - n2 * l, o = n2 * c - i * h, M = i * o - a2 * v2, p2 = a2 * f - n2 * o, x = n2 * v2 - i * f, y = s * 2;
  return f *= y, v2 *= y, o *= y, M *= 2, p2 *= 2, x *= 2, r2[0] = h + f + M, r2[1] = c + v2 + p2, r2[2] = l + o + x, r2;
}
function Dn(r2, e2, t2, n2) {
  var i = [], a2 = [];
  return i[0] = e2[0] - t2[0], i[1] = e2[1] - t2[1], i[2] = e2[2] - t2[2], a2[0] = i[0], a2[1] = i[1] * Math.cos(n2) - i[2] * Math.sin(n2), a2[2] = i[1] * Math.sin(n2) + i[2] * Math.cos(n2), r2[0] = a2[0] + t2[0], r2[1] = a2[1] + t2[1], r2[2] = a2[2] + t2[2], r2;
}
function Vn(r2, e2, t2, n2) {
  var i = [], a2 = [];
  return i[0] = e2[0] - t2[0], i[1] = e2[1] - t2[1], i[2] = e2[2] - t2[2], a2[0] = i[2] * Math.sin(n2) + i[0] * Math.cos(n2), a2[1] = i[1], a2[2] = i[2] * Math.cos(n2) - i[0] * Math.sin(n2), r2[0] = a2[0] + t2[0], r2[1] = a2[1] + t2[1], r2[2] = a2[2] + t2[2], r2;
}
function Un(r2, e2, t2, n2) {
  var i = [], a2 = [];
  return i[0] = e2[0] - t2[0], i[1] = e2[1] - t2[1], i[2] = e2[2] - t2[2], a2[0] = i[0] * Math.cos(n2) - i[1] * Math.sin(n2), a2[1] = i[0] * Math.sin(n2) + i[1] * Math.cos(n2), a2[2] = i[2], r2[0] = a2[0] + t2[0], r2[1] = a2[1] + t2[1], r2[2] = a2[2] + t2[2], r2;
}
function Xn(r2, e2) {
  var t2 = r2[0], n2 = r2[1], i = r2[2], a2 = e2[0], s = e2[1], h = e2[2], c = Math.sqrt(t2 * t2 + n2 * n2 + i * i), l = Math.sqrt(a2 * a2 + s * s + h * h), f = c * l, v2 = f && Me2(r2, e2) / f;
  return Math.acos(Math.min(Math.max(v2, -1), 1));
}
function In(r2) {
  return r2[0] = 0, r2[1] = 0, r2[2] = 0, r2;
}
function Kn(r2) {
  return "vec3(" + r2[0] + ", " + r2[1] + ", " + r2[2] + ")";
}
function Gn(r2, e2) {
  return r2[0] === e2[0] && r2[1] === e2[1] && r2[2] === e2[2];
}
function Hn(r2, e2) {
  var t2 = r2[0], n2 = r2[1], i = r2[2], a2 = e2[0], s = e2[1], h = e2[2];
  return Math.abs(t2 - a2) <= I2.EPSILON * Math.max(1, Math.abs(t2), Math.abs(a2)) && Math.abs(n2 - s) <= I2.EPSILON * Math.max(1, Math.abs(n2), Math.abs(s)) && Math.abs(i - h) <= I2.EPSILON * Math.max(1, Math.abs(i), Math.abs(h));
}
var Qn = he2;
T.sub = Qn;
var Jn = ce2;
T.mul = Jn;
var jn = le2;
T.div = jn;
var ri = fe2;
T.dist = ri;
var ei = oe2;
T.sqrDist = ei;
var ti = se2;
T.len = ti;
var ni = ve2;
T.sqrLen = ni;
var ii = function() {
  var r2 = ae2();
  return function(e2, t2, n2, i, a2, s) {
    var h, c;
    for (t2 || (t2 = 3), n2 || (n2 = 0), i ? c = Math.min(i * t2 + n2, e2.length) : c = e2.length, h = n2; h < c; h += t2)
      r2[0] = e2[h], r2[1] = e2[h + 1], r2[2] = e2[h + 2], a2(r2, r2, s), e2[h] = r2[0], e2[h + 1] = r2[1], e2[h + 2] = r2[2];
    return e2;
  };
}();
T.forEach = ii;
var ai = class extends Et2 {
  toString() {
    let e2 = "[";
    if (X2.printRowMajor) {
      e2 += "row-major:";
      for (let t2 = 0; t2 < this.RANK; ++t2)
        for (let n2 = 0; n2 < this.RANK; ++n2)
          e2 += " ".concat(this[n2 * this.RANK + t2]);
    } else {
      e2 += "column-major:";
      for (let t2 = 0; t2 < this.ELEMENTS; ++t2)
        e2 += " ".concat(this[t2]);
    }
    return e2 += "]", e2;
  }
  getElementIndex(e2, t2) {
    return t2 * this.RANK + e2;
  }
  getElement(e2, t2) {
    return this[t2 * this.RANK + e2];
  }
  setElement(e2, t2, n2) {
    return this[t2 * this.RANK + e2] = Rt2(n2), this;
  }
  getColumn(e2, t2 = new Array(this.RANK).fill(-0)) {
    const n2 = e2 * this.RANK;
    for (let i = 0; i < this.RANK; ++i)
      t2[i] = this[n2 + i];
    return t2;
  }
  setColumn(e2, t2) {
    const n2 = e2 * this.RANK;
    for (let i = 0; i < this.RANK; ++i)
      this[n2 + i] = t2[i];
    return this;
  }
};
var w = {};
function pr(r2) {
  return typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? pr = function(t2) {
    return typeof t2;
  } : pr = function(t2) {
    return t2 && typeof Symbol == "function" && t2.constructor === Symbol && t2 !== Symbol.prototype ? "symbol" : typeof t2;
  }, pr(r2);
}
Object.defineProperty(w, "__esModule", { value: true }), w.create = hi, w.fromMat4 = ci, w.clone = li, w.copy = fi, w.fromValues = oi, w.set = vi, w.identity = Mi, w.transpose = pi2, w.invert = yi, w.adjoint = xi, w.determinant = mi, w.multiply = ye2, w.translate = di, w.rotate = gi, w.scale = ui, w.fromTranslation = Ti, w.fromRotation = Ei, w.fromScaling = _i, w.fromMat2d = Ri, w.fromQuat = Oi, w.normalFromMat4 = Ai, w.projection = Pi, w.str = Li, w.frob = wi, w.add = $i, w.subtract = xe2, w.multiplyScalar = Si, w.multiplyScalarAndAdd = zi, w.exactEquals = Ci, w.equals = Fi, w.sub = w.mul = void 0;
var B = si(W);
function pe2(r2) {
  if (typeof WeakMap != "function")
    return null;
  var e2 = /* @__PURE__ */ new WeakMap(), t2 = /* @__PURE__ */ new WeakMap();
  return (pe2 = function(i) {
    return i ? t2 : e2;
  })(r2);
}
function si(r2, e2) {
  if (!e2 && r2 && r2.__esModule)
    return r2;
  if (r2 === null || pr(r2) !== "object" && typeof r2 != "function")
    return { default: r2 };
  var t2 = pe2(e2);
  if (t2 && t2.has(r2))
    return t2.get(r2);
  var n2 = {}, i = Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var a2 in r2)
    if (a2 !== "default" && Object.prototype.hasOwnProperty.call(r2, a2)) {
      var s = i ? Object.getOwnPropertyDescriptor(r2, a2) : null;
      s && (s.get || s.set) ? Object.defineProperty(n2, a2, s) : n2[a2] = r2[a2];
    }
  return n2.default = r2, t2 && t2.set(r2, n2), n2;
}
function hi() {
  var r2 = new B.ARRAY_TYPE(9);
  return B.ARRAY_TYPE != Float32Array && (r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[5] = 0, r2[6] = 0, r2[7] = 0), r2[0] = 1, r2[4] = 1, r2[8] = 1, r2;
}
function ci(r2, e2) {
  return r2[0] = e2[0], r2[1] = e2[1], r2[2] = e2[2], r2[3] = e2[4], r2[4] = e2[5], r2[5] = e2[6], r2[6] = e2[8], r2[7] = e2[9], r2[8] = e2[10], r2;
}
function li(r2) {
  var e2 = new B.ARRAY_TYPE(9);
  return e2[0] = r2[0], e2[1] = r2[1], e2[2] = r2[2], e2[3] = r2[3], e2[4] = r2[4], e2[5] = r2[5], e2[6] = r2[6], e2[7] = r2[7], e2[8] = r2[8], e2;
}
function fi(r2, e2) {
  return r2[0] = e2[0], r2[1] = e2[1], r2[2] = e2[2], r2[3] = e2[3], r2[4] = e2[4], r2[5] = e2[5], r2[6] = e2[6], r2[7] = e2[7], r2[8] = e2[8], r2;
}
function oi(r2, e2, t2, n2, i, a2, s, h, c) {
  var l = new B.ARRAY_TYPE(9);
  return l[0] = r2, l[1] = e2, l[2] = t2, l[3] = n2, l[4] = i, l[5] = a2, l[6] = s, l[7] = h, l[8] = c, l;
}
function vi(r2, e2, t2, n2, i, a2, s, h, c, l) {
  return r2[0] = e2, r2[1] = t2, r2[2] = n2, r2[3] = i, r2[4] = a2, r2[5] = s, r2[6] = h, r2[7] = c, r2[8] = l, r2;
}
function Mi(r2) {
  return r2[0] = 1, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 1, r2[5] = 0, r2[6] = 0, r2[7] = 0, r2[8] = 1, r2;
}
function pi2(r2, e2) {
  if (r2 === e2) {
    var t2 = e2[1], n2 = e2[2], i = e2[5];
    r2[1] = e2[3], r2[2] = e2[6], r2[3] = t2, r2[5] = e2[7], r2[6] = n2, r2[7] = i;
  } else
    r2[0] = e2[0], r2[1] = e2[3], r2[2] = e2[6], r2[3] = e2[1], r2[4] = e2[4], r2[5] = e2[7], r2[6] = e2[2], r2[7] = e2[5], r2[8] = e2[8];
  return r2;
}
function yi(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = e2[4], h = e2[5], c = e2[6], l = e2[7], f = e2[8], v2 = f * s - h * l, o = -f * a2 + h * c, M = l * a2 - s * c, p2 = t2 * v2 + n2 * o + i * M;
  return p2 ? (p2 = 1 / p2, r2[0] = v2 * p2, r2[1] = (-f * n2 + i * l) * p2, r2[2] = (h * n2 - i * s) * p2, r2[3] = o * p2, r2[4] = (f * t2 - i * c) * p2, r2[5] = (-h * t2 + i * a2) * p2, r2[6] = M * p2, r2[7] = (-l * t2 + n2 * c) * p2, r2[8] = (s * t2 - n2 * a2) * p2, r2) : null;
}
function xi(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = e2[4], h = e2[5], c = e2[6], l = e2[7], f = e2[8];
  return r2[0] = s * f - h * l, r2[1] = i * l - n2 * f, r2[2] = n2 * h - i * s, r2[3] = h * c - a2 * f, r2[4] = t2 * f - i * c, r2[5] = i * a2 - t2 * h, r2[6] = a2 * l - s * c, r2[7] = n2 * c - t2 * l, r2[8] = t2 * s - n2 * a2, r2;
}
function mi(r2) {
  var e2 = r2[0], t2 = r2[1], n2 = r2[2], i = r2[3], a2 = r2[4], s = r2[5], h = r2[6], c = r2[7], l = r2[8];
  return e2 * (l * a2 - s * c) + t2 * (-l * i + s * h) + n2 * (c * i - a2 * h);
}
function ye2(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = e2[3], h = e2[4], c = e2[5], l = e2[6], f = e2[7], v2 = e2[8], o = t2[0], M = t2[1], p2 = t2[2], x = t2[3], y = t2[4], d2 = t2[5], R = t2[6], m2 = t2[7], g2 = t2[8];
  return r2[0] = o * n2 + M * s + p2 * l, r2[1] = o * i + M * h + p2 * f, r2[2] = o * a2 + M * c + p2 * v2, r2[3] = x * n2 + y * s + d2 * l, r2[4] = x * i + y * h + d2 * f, r2[5] = x * a2 + y * c + d2 * v2, r2[6] = R * n2 + m2 * s + g2 * l, r2[7] = R * i + m2 * h + g2 * f, r2[8] = R * a2 + m2 * c + g2 * v2, r2;
}
function di(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = e2[3], h = e2[4], c = e2[5], l = e2[6], f = e2[7], v2 = e2[8], o = t2[0], M = t2[1];
  return r2[0] = n2, r2[1] = i, r2[2] = a2, r2[3] = s, r2[4] = h, r2[5] = c, r2[6] = o * n2 + M * s + l, r2[7] = o * i + M * h + f, r2[8] = o * a2 + M * c + v2, r2;
}
function gi(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = e2[3], h = e2[4], c = e2[5], l = e2[6], f = e2[7], v2 = e2[8], o = Math.sin(t2), M = Math.cos(t2);
  return r2[0] = M * n2 + o * s, r2[1] = M * i + o * h, r2[2] = M * a2 + o * c, r2[3] = M * s - o * n2, r2[4] = M * h - o * i, r2[5] = M * c - o * a2, r2[6] = l, r2[7] = f, r2[8] = v2, r2;
}
function ui(r2, e2, t2) {
  var n2 = t2[0], i = t2[1];
  return r2[0] = n2 * e2[0], r2[1] = n2 * e2[1], r2[2] = n2 * e2[2], r2[3] = i * e2[3], r2[4] = i * e2[4], r2[5] = i * e2[5], r2[6] = e2[6], r2[7] = e2[7], r2[8] = e2[8], r2;
}
function Ti(r2, e2) {
  return r2[0] = 1, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 1, r2[5] = 0, r2[6] = e2[0], r2[7] = e2[1], r2[8] = 1, r2;
}
function Ei(r2, e2) {
  var t2 = Math.sin(e2), n2 = Math.cos(e2);
  return r2[0] = n2, r2[1] = t2, r2[2] = 0, r2[3] = -t2, r2[4] = n2, r2[5] = 0, r2[6] = 0, r2[7] = 0, r2[8] = 1, r2;
}
function _i(r2, e2) {
  return r2[0] = e2[0], r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = e2[1], r2[5] = 0, r2[6] = 0, r2[7] = 0, r2[8] = 1, r2;
}
function Ri(r2, e2) {
  return r2[0] = e2[0], r2[1] = e2[1], r2[2] = 0, r2[3] = e2[2], r2[4] = e2[3], r2[5] = 0, r2[6] = e2[4], r2[7] = e2[5], r2[8] = 1, r2;
}
function Oi(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = t2 + t2, h = n2 + n2, c = i + i, l = t2 * s, f = n2 * s, v2 = n2 * h, o = i * s, M = i * h, p2 = i * c, x = a2 * s, y = a2 * h, d2 = a2 * c;
  return r2[0] = 1 - v2 - p2, r2[3] = f - d2, r2[6] = o + y, r2[1] = f + d2, r2[4] = 1 - l - p2, r2[7] = M - x, r2[2] = o - y, r2[5] = M + x, r2[8] = 1 - l - v2, r2;
}
function Ai(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = e2[4], h = e2[5], c = e2[6], l = e2[7], f = e2[8], v2 = e2[9], o = e2[10], M = e2[11], p2 = e2[12], x = e2[13], y = e2[14], d2 = e2[15], R = t2 * h - n2 * s, m2 = t2 * c - i * s, g2 = t2 * l - a2 * s, P2 = n2 * c - i * h, A2 = n2 * l - a2 * h, N2 = i * l - a2 * c, z2 = f * x - v2 * p2, C2 = f * y - o * p2, $ = f * d2 - M * p2, b2 = v2 * y - o * x, F = v2 * d2 - M * x, q3 = o * d2 - M * y, L2 = R * q3 - m2 * F + g2 * b2 + P2 * $ - A2 * C2 + N2 * z2;
  return L2 ? (L2 = 1 / L2, r2[0] = (h * q3 - c * F + l * b2) * L2, r2[1] = (c * $ - s * q3 - l * C2) * L2, r2[2] = (s * F - h * $ + l * z2) * L2, r2[3] = (i * F - n2 * q3 - a2 * b2) * L2, r2[4] = (t2 * q3 - i * $ + a2 * C2) * L2, r2[5] = (n2 * $ - t2 * F - a2 * z2) * L2, r2[6] = (x * N2 - y * A2 + d2 * P2) * L2, r2[7] = (y * g2 - p2 * N2 - d2 * m2) * L2, r2[8] = (p2 * A2 - x * g2 + d2 * R) * L2, r2) : null;
}
function Pi(r2, e2, t2) {
  return r2[0] = 2 / e2, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = -2 / t2, r2[5] = 0, r2[6] = -1, r2[7] = 1, r2[8] = 1, r2;
}
function Li(r2) {
  return "mat3(" + r2[0] + ", " + r2[1] + ", " + r2[2] + ", " + r2[3] + ", " + r2[4] + ", " + r2[5] + ", " + r2[6] + ", " + r2[7] + ", " + r2[8] + ")";
}
function wi(r2) {
  return Math.hypot(r2[0], r2[1], r2[2], r2[3], r2[4], r2[5], r2[6], r2[7], r2[8]);
}
function $i(r2, e2, t2) {
  return r2[0] = e2[0] + t2[0], r2[1] = e2[1] + t2[1], r2[2] = e2[2] + t2[2], r2[3] = e2[3] + t2[3], r2[4] = e2[4] + t2[4], r2[5] = e2[5] + t2[5], r2[6] = e2[6] + t2[6], r2[7] = e2[7] + t2[7], r2[8] = e2[8] + t2[8], r2;
}
function xe2(r2, e2, t2) {
  return r2[0] = e2[0] - t2[0], r2[1] = e2[1] - t2[1], r2[2] = e2[2] - t2[2], r2[3] = e2[3] - t2[3], r2[4] = e2[4] - t2[4], r2[5] = e2[5] - t2[5], r2[6] = e2[6] - t2[6], r2[7] = e2[7] - t2[7], r2[8] = e2[8] - t2[8], r2;
}
function Si(r2, e2, t2) {
  return r2[0] = e2[0] * t2, r2[1] = e2[1] * t2, r2[2] = e2[2] * t2, r2[3] = e2[3] * t2, r2[4] = e2[4] * t2, r2[5] = e2[5] * t2, r2[6] = e2[6] * t2, r2[7] = e2[7] * t2, r2[8] = e2[8] * t2, r2;
}
function zi(r2, e2, t2, n2) {
  return r2[0] = e2[0] + t2[0] * n2, r2[1] = e2[1] + t2[1] * n2, r2[2] = e2[2] + t2[2] * n2, r2[3] = e2[3] + t2[3] * n2, r2[4] = e2[4] + t2[4] * n2, r2[5] = e2[5] + t2[5] * n2, r2[6] = e2[6] + t2[6] * n2, r2[7] = e2[7] + t2[7] * n2, r2[8] = e2[8] + t2[8] * n2, r2;
}
function Ci(r2, e2) {
  return r2[0] === e2[0] && r2[1] === e2[1] && r2[2] === e2[2] && r2[3] === e2[3] && r2[4] === e2[4] && r2[5] === e2[5] && r2[6] === e2[6] && r2[7] === e2[7] && r2[8] === e2[8];
}
function Fi(r2, e2) {
  var t2 = r2[0], n2 = r2[1], i = r2[2], a2 = r2[3], s = r2[4], h = r2[5], c = r2[6], l = r2[7], f = r2[8], v2 = e2[0], o = e2[1], M = e2[2], p2 = e2[3], x = e2[4], y = e2[5], d2 = e2[6], R = e2[7], m2 = e2[8];
  return Math.abs(t2 - v2) <= B.EPSILON * Math.max(1, Math.abs(t2), Math.abs(v2)) && Math.abs(n2 - o) <= B.EPSILON * Math.max(1, Math.abs(n2), Math.abs(o)) && Math.abs(i - M) <= B.EPSILON * Math.max(1, Math.abs(i), Math.abs(M)) && Math.abs(a2 - p2) <= B.EPSILON * Math.max(1, Math.abs(a2), Math.abs(p2)) && Math.abs(s - x) <= B.EPSILON * Math.max(1, Math.abs(s), Math.abs(x)) && Math.abs(h - y) <= B.EPSILON * Math.max(1, Math.abs(h), Math.abs(y)) && Math.abs(c - d2) <= B.EPSILON * Math.max(1, Math.abs(c), Math.abs(d2)) && Math.abs(l - R) <= B.EPSILON * Math.max(1, Math.abs(l), Math.abs(R)) && Math.abs(f - m2) <= B.EPSILON * Math.max(1, Math.abs(f), Math.abs(m2));
}
var Ni = ye2;
w.mul = Ni;
var bi = xe2;
w.sub = bi;
var me2;
(function(r2) {
  r2[r2.COL0ROW0 = 0] = "COL0ROW0", r2[r2.COL0ROW1 = 1] = "COL0ROW1", r2[r2.COL0ROW2 = 2] = "COL0ROW2", r2[r2.COL1ROW0 = 3] = "COL1ROW0", r2[r2.COL1ROW1 = 4] = "COL1ROW1", r2[r2.COL1ROW2 = 5] = "COL1ROW2", r2[r2.COL2ROW0 = 6] = "COL2ROW0", r2[r2.COL2ROW1 = 7] = "COL2ROW1", r2[r2.COL2ROW2 = 8] = "COL2ROW2";
})(me2 || (me2 = {})), Object.freeze([1, 0, 0, 0, 1, 0, 0, 0, 1]);
var E = {};
function yr(r2) {
  return typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? yr = function(t2) {
    return typeof t2;
  } : yr = function(t2) {
    return t2 && typeof Symbol == "function" && t2.constructor === Symbol && t2 !== Symbol.prototype ? "symbol" : typeof t2;
  }, yr(r2);
}
Object.defineProperty(E, "__esModule", { value: true }), E.create = Hi, E.clone = Qi, E.copy = Ji, E.fromValues = ji, E.set = r1, E.identity = ue2;
var qi = E.transpose = e1;
var Yi = E.invert = t1;
E.adjoint = n1;
var ki = E.determinant = i1;
var de2 = E.multiply = Te2;
var Wi = E.translate = a1;
var Bi = E.scale = s1;
var Zi = E.rotate = h1;
var Di = E.rotateX = c1;
var Vi = E.rotateY = l1;
var Ui = E.rotateZ = f1;
E.fromTranslation = o1, E.fromScaling = v1, E.fromRotation = M1, E.fromXRotation = p1, E.fromYRotation = y1, E.fromZRotation = x1, E.fromRotationTranslation = Ee2, E.fromQuat2 = m1, E.getTranslation = d1, E.getScaling = _e2, E.getRotation = g1, E.fromRotationTranslationScale = u1, E.fromRotationTranslationScaleOrigin = T1;
var Xi = E.fromQuat = E1;
var Ii = E.frustum = _1;
E.perspectiveNO = Re2, E.perspectiveZO = O1, E.perspectiveFromFieldOfView = A1, E.orthoNO = Ae2, E.orthoZO = L1;
var Ki = E.lookAt = w1;
E.targetTo = $1, E.str = S1, E.frob = z1, E.add = C1, E.subtract = Le2, E.multiplyScalar = F1, E.multiplyScalarAndAdd = N1, E.exactEquals = b1, E.equals = q1, E.sub = E.mul = Pe2 = E.ortho = Oe2 = E.perspective = void 0;
var S2 = Gi(W);
function ge2(r2) {
  if (typeof WeakMap != "function")
    return null;
  var e2 = /* @__PURE__ */ new WeakMap(), t2 = /* @__PURE__ */ new WeakMap();
  return (ge2 = function(i) {
    return i ? t2 : e2;
  })(r2);
}
function Gi(r2, e2) {
  if (!e2 && r2 && r2.__esModule)
    return r2;
  if (r2 === null || yr(r2) !== "object" && typeof r2 != "function")
    return { default: r2 };
  var t2 = ge2(e2);
  if (t2 && t2.has(r2))
    return t2.get(r2);
  var n2 = {}, i = Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var a2 in r2)
    if (a2 !== "default" && Object.prototype.hasOwnProperty.call(r2, a2)) {
      var s = i ? Object.getOwnPropertyDescriptor(r2, a2) : null;
      s && (s.get || s.set) ? Object.defineProperty(n2, a2, s) : n2[a2] = r2[a2];
    }
  return n2.default = r2, t2 && t2.set(r2, n2), n2;
}
function Hi() {
  var r2 = new S2.ARRAY_TYPE(16);
  return S2.ARRAY_TYPE != Float32Array && (r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[6] = 0, r2[7] = 0, r2[8] = 0, r2[9] = 0, r2[11] = 0, r2[12] = 0, r2[13] = 0, r2[14] = 0), r2[0] = 1, r2[5] = 1, r2[10] = 1, r2[15] = 1, r2;
}
function Qi(r2) {
  var e2 = new S2.ARRAY_TYPE(16);
  return e2[0] = r2[0], e2[1] = r2[1], e2[2] = r2[2], e2[3] = r2[3], e2[4] = r2[4], e2[5] = r2[5], e2[6] = r2[6], e2[7] = r2[7], e2[8] = r2[8], e2[9] = r2[9], e2[10] = r2[10], e2[11] = r2[11], e2[12] = r2[12], e2[13] = r2[13], e2[14] = r2[14], e2[15] = r2[15], e2;
}
function Ji(r2, e2) {
  return r2[0] = e2[0], r2[1] = e2[1], r2[2] = e2[2], r2[3] = e2[3], r2[4] = e2[4], r2[5] = e2[5], r2[6] = e2[6], r2[7] = e2[7], r2[8] = e2[8], r2[9] = e2[9], r2[10] = e2[10], r2[11] = e2[11], r2[12] = e2[12], r2[13] = e2[13], r2[14] = e2[14], r2[15] = e2[15], r2;
}
function ji(r2, e2, t2, n2, i, a2, s, h, c, l, f, v2, o, M, p2, x) {
  var y = new S2.ARRAY_TYPE(16);
  return y[0] = r2, y[1] = e2, y[2] = t2, y[3] = n2, y[4] = i, y[5] = a2, y[6] = s, y[7] = h, y[8] = c, y[9] = l, y[10] = f, y[11] = v2, y[12] = o, y[13] = M, y[14] = p2, y[15] = x, y;
}
function r1(r2, e2, t2, n2, i, a2, s, h, c, l, f, v2, o, M, p2, x, y) {
  return r2[0] = e2, r2[1] = t2, r2[2] = n2, r2[3] = i, r2[4] = a2, r2[5] = s, r2[6] = h, r2[7] = c, r2[8] = l, r2[9] = f, r2[10] = v2, r2[11] = o, r2[12] = M, r2[13] = p2, r2[14] = x, r2[15] = y, r2;
}
function ue2(r2) {
  return r2[0] = 1, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = 1, r2[6] = 0, r2[7] = 0, r2[8] = 0, r2[9] = 0, r2[10] = 1, r2[11] = 0, r2[12] = 0, r2[13] = 0, r2[14] = 0, r2[15] = 1, r2;
}
function e1(r2, e2) {
  if (r2 === e2) {
    var t2 = e2[1], n2 = e2[2], i = e2[3], a2 = e2[6], s = e2[7], h = e2[11];
    r2[1] = e2[4], r2[2] = e2[8], r2[3] = e2[12], r2[4] = t2, r2[6] = e2[9], r2[7] = e2[13], r2[8] = n2, r2[9] = a2, r2[11] = e2[14], r2[12] = i, r2[13] = s, r2[14] = h;
  } else
    r2[0] = e2[0], r2[1] = e2[4], r2[2] = e2[8], r2[3] = e2[12], r2[4] = e2[1], r2[5] = e2[5], r2[6] = e2[9], r2[7] = e2[13], r2[8] = e2[2], r2[9] = e2[6], r2[10] = e2[10], r2[11] = e2[14], r2[12] = e2[3], r2[13] = e2[7], r2[14] = e2[11], r2[15] = e2[15];
  return r2;
}
function t1(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = e2[4], h = e2[5], c = e2[6], l = e2[7], f = e2[8], v2 = e2[9], o = e2[10], M = e2[11], p2 = e2[12], x = e2[13], y = e2[14], d2 = e2[15], R = t2 * h - n2 * s, m2 = t2 * c - i * s, g2 = t2 * l - a2 * s, P2 = n2 * c - i * h, A2 = n2 * l - a2 * h, N2 = i * l - a2 * c, z2 = f * x - v2 * p2, C2 = f * y - o * p2, $ = f * d2 - M * p2, b2 = v2 * y - o * x, F = v2 * d2 - M * x, q3 = o * d2 - M * y, L2 = R * q3 - m2 * F + g2 * b2 + P2 * $ - A2 * C2 + N2 * z2;
  return L2 ? (L2 = 1 / L2, r2[0] = (h * q3 - c * F + l * b2) * L2, r2[1] = (i * F - n2 * q3 - a2 * b2) * L2, r2[2] = (x * N2 - y * A2 + d2 * P2) * L2, r2[3] = (o * A2 - v2 * N2 - M * P2) * L2, r2[4] = (c * $ - s * q3 - l * C2) * L2, r2[5] = (t2 * q3 - i * $ + a2 * C2) * L2, r2[6] = (y * g2 - p2 * N2 - d2 * m2) * L2, r2[7] = (f * N2 - o * g2 + M * m2) * L2, r2[8] = (s * F - h * $ + l * z2) * L2, r2[9] = (n2 * $ - t2 * F - a2 * z2) * L2, r2[10] = (p2 * A2 - x * g2 + d2 * R) * L2, r2[11] = (v2 * g2 - f * A2 - M * R) * L2, r2[12] = (h * C2 - s * b2 - c * z2) * L2, r2[13] = (t2 * b2 - n2 * C2 + i * z2) * L2, r2[14] = (x * m2 - p2 * P2 - y * R) * L2, r2[15] = (f * P2 - v2 * m2 + o * R) * L2, r2) : null;
}
function n1(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = e2[4], h = e2[5], c = e2[6], l = e2[7], f = e2[8], v2 = e2[9], o = e2[10], M = e2[11], p2 = e2[12], x = e2[13], y = e2[14], d2 = e2[15];
  return r2[0] = h * (o * d2 - M * y) - v2 * (c * d2 - l * y) + x * (c * M - l * o), r2[1] = -(n2 * (o * d2 - M * y) - v2 * (i * d2 - a2 * y) + x * (i * M - a2 * o)), r2[2] = n2 * (c * d2 - l * y) - h * (i * d2 - a2 * y) + x * (i * l - a2 * c), r2[3] = -(n2 * (c * M - l * o) - h * (i * M - a2 * o) + v2 * (i * l - a2 * c)), r2[4] = -(s * (o * d2 - M * y) - f * (c * d2 - l * y) + p2 * (c * M - l * o)), r2[5] = t2 * (o * d2 - M * y) - f * (i * d2 - a2 * y) + p2 * (i * M - a2 * o), r2[6] = -(t2 * (c * d2 - l * y) - s * (i * d2 - a2 * y) + p2 * (i * l - a2 * c)), r2[7] = t2 * (c * M - l * o) - s * (i * M - a2 * o) + f * (i * l - a2 * c), r2[8] = s * (v2 * d2 - M * x) - f * (h * d2 - l * x) + p2 * (h * M - l * v2), r2[9] = -(t2 * (v2 * d2 - M * x) - f * (n2 * d2 - a2 * x) + p2 * (n2 * M - a2 * v2)), r2[10] = t2 * (h * d2 - l * x) - s * (n2 * d2 - a2 * x) + p2 * (n2 * l - a2 * h), r2[11] = -(t2 * (h * M - l * v2) - s * (n2 * M - a2 * v2) + f * (n2 * l - a2 * h)), r2[12] = -(s * (v2 * y - o * x) - f * (h * y - c * x) + p2 * (h * o - c * v2)), r2[13] = t2 * (v2 * y - o * x) - f * (n2 * y - i * x) + p2 * (n2 * o - i * v2), r2[14] = -(t2 * (h * y - c * x) - s * (n2 * y - i * x) + p2 * (n2 * c - i * h)), r2[15] = t2 * (h * o - c * v2) - s * (n2 * o - i * v2) + f * (n2 * c - i * h), r2;
}
function i1(r2) {
  var e2 = r2[0], t2 = r2[1], n2 = r2[2], i = r2[3], a2 = r2[4], s = r2[5], h = r2[6], c = r2[7], l = r2[8], f = r2[9], v2 = r2[10], o = r2[11], M = r2[12], p2 = r2[13], x = r2[14], y = r2[15], d2 = e2 * s - t2 * a2, R = e2 * h - n2 * a2, m2 = e2 * c - i * a2, g2 = t2 * h - n2 * s, P2 = t2 * c - i * s, A2 = n2 * c - i * h, N2 = l * p2 - f * M, z2 = l * x - v2 * M, C2 = l * y - o * M, $ = f * x - v2 * p2, b2 = f * y - o * p2, F = v2 * y - o * x;
  return d2 * F - R * b2 + m2 * $ + g2 * C2 - P2 * z2 + A2 * N2;
}
function Te2(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = e2[3], h = e2[4], c = e2[5], l = e2[6], f = e2[7], v2 = e2[8], o = e2[9], M = e2[10], p2 = e2[11], x = e2[12], y = e2[13], d2 = e2[14], R = e2[15], m2 = t2[0], g2 = t2[1], P2 = t2[2], A2 = t2[3];
  return r2[0] = m2 * n2 + g2 * h + P2 * v2 + A2 * x, r2[1] = m2 * i + g2 * c + P2 * o + A2 * y, r2[2] = m2 * a2 + g2 * l + P2 * M + A2 * d2, r2[3] = m2 * s + g2 * f + P2 * p2 + A2 * R, m2 = t2[4], g2 = t2[5], P2 = t2[6], A2 = t2[7], r2[4] = m2 * n2 + g2 * h + P2 * v2 + A2 * x, r2[5] = m2 * i + g2 * c + P2 * o + A2 * y, r2[6] = m2 * a2 + g2 * l + P2 * M + A2 * d2, r2[7] = m2 * s + g2 * f + P2 * p2 + A2 * R, m2 = t2[8], g2 = t2[9], P2 = t2[10], A2 = t2[11], r2[8] = m2 * n2 + g2 * h + P2 * v2 + A2 * x, r2[9] = m2 * i + g2 * c + P2 * o + A2 * y, r2[10] = m2 * a2 + g2 * l + P2 * M + A2 * d2, r2[11] = m2 * s + g2 * f + P2 * p2 + A2 * R, m2 = t2[12], g2 = t2[13], P2 = t2[14], A2 = t2[15], r2[12] = m2 * n2 + g2 * h + P2 * v2 + A2 * x, r2[13] = m2 * i + g2 * c + P2 * o + A2 * y, r2[14] = m2 * a2 + g2 * l + P2 * M + A2 * d2, r2[15] = m2 * s + g2 * f + P2 * p2 + A2 * R, r2;
}
function a1(r2, e2, t2) {
  var n2 = t2[0], i = t2[1], a2 = t2[2], s, h, c, l, f, v2, o, M, p2, x, y, d2;
  return e2 === r2 ? (r2[12] = e2[0] * n2 + e2[4] * i + e2[8] * a2 + e2[12], r2[13] = e2[1] * n2 + e2[5] * i + e2[9] * a2 + e2[13], r2[14] = e2[2] * n2 + e2[6] * i + e2[10] * a2 + e2[14], r2[15] = e2[3] * n2 + e2[7] * i + e2[11] * a2 + e2[15]) : (s = e2[0], h = e2[1], c = e2[2], l = e2[3], f = e2[4], v2 = e2[5], o = e2[6], M = e2[7], p2 = e2[8], x = e2[9], y = e2[10], d2 = e2[11], r2[0] = s, r2[1] = h, r2[2] = c, r2[3] = l, r2[4] = f, r2[5] = v2, r2[6] = o, r2[7] = M, r2[8] = p2, r2[9] = x, r2[10] = y, r2[11] = d2, r2[12] = s * n2 + f * i + p2 * a2 + e2[12], r2[13] = h * n2 + v2 * i + x * a2 + e2[13], r2[14] = c * n2 + o * i + y * a2 + e2[14], r2[15] = l * n2 + M * i + d2 * a2 + e2[15]), r2;
}
function s1(r2, e2, t2) {
  var n2 = t2[0], i = t2[1], a2 = t2[2];
  return r2[0] = e2[0] * n2, r2[1] = e2[1] * n2, r2[2] = e2[2] * n2, r2[3] = e2[3] * n2, r2[4] = e2[4] * i, r2[5] = e2[5] * i, r2[6] = e2[6] * i, r2[7] = e2[7] * i, r2[8] = e2[8] * a2, r2[9] = e2[9] * a2, r2[10] = e2[10] * a2, r2[11] = e2[11] * a2, r2[12] = e2[12], r2[13] = e2[13], r2[14] = e2[14], r2[15] = e2[15], r2;
}
function h1(r2, e2, t2, n2) {
  var i = n2[0], a2 = n2[1], s = n2[2], h = Math.hypot(i, a2, s), c, l, f, v2, o, M, p2, x, y, d2, R, m2, g2, P2, A2, N2, z2, C2, $, b2, F, q3, L2, U;
  return h < S2.EPSILON ? null : (h = 1 / h, i *= h, a2 *= h, s *= h, c = Math.sin(t2), l = Math.cos(t2), f = 1 - l, v2 = e2[0], o = e2[1], M = e2[2], p2 = e2[3], x = e2[4], y = e2[5], d2 = e2[6], R = e2[7], m2 = e2[8], g2 = e2[9], P2 = e2[10], A2 = e2[11], N2 = i * i * f + l, z2 = a2 * i * f + s * c, C2 = s * i * f - a2 * c, $ = i * a2 * f - s * c, b2 = a2 * a2 * f + l, F = s * a2 * f + i * c, q3 = i * s * f + a2 * c, L2 = a2 * s * f - i * c, U = s * s * f + l, r2[0] = v2 * N2 + x * z2 + m2 * C2, r2[1] = o * N2 + y * z2 + g2 * C2, r2[2] = M * N2 + d2 * z2 + P2 * C2, r2[3] = p2 * N2 + R * z2 + A2 * C2, r2[4] = v2 * $ + x * b2 + m2 * F, r2[5] = o * $ + y * b2 + g2 * F, r2[6] = M * $ + d2 * b2 + P2 * F, r2[7] = p2 * $ + R * b2 + A2 * F, r2[8] = v2 * q3 + x * L2 + m2 * U, r2[9] = o * q3 + y * L2 + g2 * U, r2[10] = M * q3 + d2 * L2 + P2 * U, r2[11] = p2 * q3 + R * L2 + A2 * U, e2 !== r2 && (r2[12] = e2[12], r2[13] = e2[13], r2[14] = e2[14], r2[15] = e2[15]), r2);
}
function c1(r2, e2, t2) {
  var n2 = Math.sin(t2), i = Math.cos(t2), a2 = e2[4], s = e2[5], h = e2[6], c = e2[7], l = e2[8], f = e2[9], v2 = e2[10], o = e2[11];
  return e2 !== r2 && (r2[0] = e2[0], r2[1] = e2[1], r2[2] = e2[2], r2[3] = e2[3], r2[12] = e2[12], r2[13] = e2[13], r2[14] = e2[14], r2[15] = e2[15]), r2[4] = a2 * i + l * n2, r2[5] = s * i + f * n2, r2[6] = h * i + v2 * n2, r2[7] = c * i + o * n2, r2[8] = l * i - a2 * n2, r2[9] = f * i - s * n2, r2[10] = v2 * i - h * n2, r2[11] = o * i - c * n2, r2;
}
function l1(r2, e2, t2) {
  var n2 = Math.sin(t2), i = Math.cos(t2), a2 = e2[0], s = e2[1], h = e2[2], c = e2[3], l = e2[8], f = e2[9], v2 = e2[10], o = e2[11];
  return e2 !== r2 && (r2[4] = e2[4], r2[5] = e2[5], r2[6] = e2[6], r2[7] = e2[7], r2[12] = e2[12], r2[13] = e2[13], r2[14] = e2[14], r2[15] = e2[15]), r2[0] = a2 * i - l * n2, r2[1] = s * i - f * n2, r2[2] = h * i - v2 * n2, r2[3] = c * i - o * n2, r2[8] = a2 * n2 + l * i, r2[9] = s * n2 + f * i, r2[10] = h * n2 + v2 * i, r2[11] = c * n2 + o * i, r2;
}
function f1(r2, e2, t2) {
  var n2 = Math.sin(t2), i = Math.cos(t2), a2 = e2[0], s = e2[1], h = e2[2], c = e2[3], l = e2[4], f = e2[5], v2 = e2[6], o = e2[7];
  return e2 !== r2 && (r2[8] = e2[8], r2[9] = e2[9], r2[10] = e2[10], r2[11] = e2[11], r2[12] = e2[12], r2[13] = e2[13], r2[14] = e2[14], r2[15] = e2[15]), r2[0] = a2 * i + l * n2, r2[1] = s * i + f * n2, r2[2] = h * i + v2 * n2, r2[3] = c * i + o * n2, r2[4] = l * i - a2 * n2, r2[5] = f * i - s * n2, r2[6] = v2 * i - h * n2, r2[7] = o * i - c * n2, r2;
}
function o1(r2, e2) {
  return r2[0] = 1, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = 1, r2[6] = 0, r2[7] = 0, r2[8] = 0, r2[9] = 0, r2[10] = 1, r2[11] = 0, r2[12] = e2[0], r2[13] = e2[1], r2[14] = e2[2], r2[15] = 1, r2;
}
function v1(r2, e2) {
  return r2[0] = e2[0], r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = e2[1], r2[6] = 0, r2[7] = 0, r2[8] = 0, r2[9] = 0, r2[10] = e2[2], r2[11] = 0, r2[12] = 0, r2[13] = 0, r2[14] = 0, r2[15] = 1, r2;
}
function M1(r2, e2, t2) {
  var n2 = t2[0], i = t2[1], a2 = t2[2], s = Math.hypot(n2, i, a2), h, c, l;
  return s < S2.EPSILON ? null : (s = 1 / s, n2 *= s, i *= s, a2 *= s, h = Math.sin(e2), c = Math.cos(e2), l = 1 - c, r2[0] = n2 * n2 * l + c, r2[1] = i * n2 * l + a2 * h, r2[2] = a2 * n2 * l - i * h, r2[3] = 0, r2[4] = n2 * i * l - a2 * h, r2[5] = i * i * l + c, r2[6] = a2 * i * l + n2 * h, r2[7] = 0, r2[8] = n2 * a2 * l + i * h, r2[9] = i * a2 * l - n2 * h, r2[10] = a2 * a2 * l + c, r2[11] = 0, r2[12] = 0, r2[13] = 0, r2[14] = 0, r2[15] = 1, r2);
}
function p1(r2, e2) {
  var t2 = Math.sin(e2), n2 = Math.cos(e2);
  return r2[0] = 1, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = n2, r2[6] = t2, r2[7] = 0, r2[8] = 0, r2[9] = -t2, r2[10] = n2, r2[11] = 0, r2[12] = 0, r2[13] = 0, r2[14] = 0, r2[15] = 1, r2;
}
function y1(r2, e2) {
  var t2 = Math.sin(e2), n2 = Math.cos(e2);
  return r2[0] = n2, r2[1] = 0, r2[2] = -t2, r2[3] = 0, r2[4] = 0, r2[5] = 1, r2[6] = 0, r2[7] = 0, r2[8] = t2, r2[9] = 0, r2[10] = n2, r2[11] = 0, r2[12] = 0, r2[13] = 0, r2[14] = 0, r2[15] = 1, r2;
}
function x1(r2, e2) {
  var t2 = Math.sin(e2), n2 = Math.cos(e2);
  return r2[0] = n2, r2[1] = t2, r2[2] = 0, r2[3] = 0, r2[4] = -t2, r2[5] = n2, r2[6] = 0, r2[7] = 0, r2[8] = 0, r2[9] = 0, r2[10] = 1, r2[11] = 0, r2[12] = 0, r2[13] = 0, r2[14] = 0, r2[15] = 1, r2;
}
function Ee2(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = e2[3], h = n2 + n2, c = i + i, l = a2 + a2, f = n2 * h, v2 = n2 * c, o = n2 * l, M = i * c, p2 = i * l, x = a2 * l, y = s * h, d2 = s * c, R = s * l;
  return r2[0] = 1 - (M + x), r2[1] = v2 + R, r2[2] = o - d2, r2[3] = 0, r2[4] = v2 - R, r2[5] = 1 - (f + x), r2[6] = p2 + y, r2[7] = 0, r2[8] = o + d2, r2[9] = p2 - y, r2[10] = 1 - (f + M), r2[11] = 0, r2[12] = t2[0], r2[13] = t2[1], r2[14] = t2[2], r2[15] = 1, r2;
}
function m1(r2, e2) {
  var t2 = new S2.ARRAY_TYPE(3), n2 = -e2[0], i = -e2[1], a2 = -e2[2], s = e2[3], h = e2[4], c = e2[5], l = e2[6], f = e2[7], v2 = n2 * n2 + i * i + a2 * a2 + s * s;
  return v2 > 0 ? (t2[0] = (h * s + f * n2 + c * a2 - l * i) * 2 / v2, t2[1] = (c * s + f * i + l * n2 - h * a2) * 2 / v2, t2[2] = (l * s + f * a2 + h * i - c * n2) * 2 / v2) : (t2[0] = (h * s + f * n2 + c * a2 - l * i) * 2, t2[1] = (c * s + f * i + l * n2 - h * a2) * 2, t2[2] = (l * s + f * a2 + h * i - c * n2) * 2), Ee2(r2, e2, t2), r2;
}
function d1(r2, e2) {
  return r2[0] = e2[12], r2[1] = e2[13], r2[2] = e2[14], r2;
}
function _e2(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[4], s = e2[5], h = e2[6], c = e2[8], l = e2[9], f = e2[10];
  return r2[0] = Math.hypot(t2, n2, i), r2[1] = Math.hypot(a2, s, h), r2[2] = Math.hypot(c, l, f), r2;
}
function g1(r2, e2) {
  var t2 = new S2.ARRAY_TYPE(3);
  _e2(t2, e2);
  var n2 = 1 / t2[0], i = 1 / t2[1], a2 = 1 / t2[2], s = e2[0] * n2, h = e2[1] * i, c = e2[2] * a2, l = e2[4] * n2, f = e2[5] * i, v2 = e2[6] * a2, o = e2[8] * n2, M = e2[9] * i, p2 = e2[10] * a2, x = s + f + p2, y = 0;
  return x > 0 ? (y = Math.sqrt(x + 1) * 2, r2[3] = 0.25 * y, r2[0] = (v2 - M) / y, r2[1] = (o - c) / y, r2[2] = (h - l) / y) : s > f && s > p2 ? (y = Math.sqrt(1 + s - f - p2) * 2, r2[3] = (v2 - M) / y, r2[0] = 0.25 * y, r2[1] = (h + l) / y, r2[2] = (o + c) / y) : f > p2 ? (y = Math.sqrt(1 + f - s - p2) * 2, r2[3] = (o - c) / y, r2[0] = (h + l) / y, r2[1] = 0.25 * y, r2[2] = (v2 + M) / y) : (y = Math.sqrt(1 + p2 - s - f) * 2, r2[3] = (h - l) / y, r2[0] = (o + c) / y, r2[1] = (v2 + M) / y, r2[2] = 0.25 * y), r2;
}
function u1(r2, e2, t2, n2) {
  var i = e2[0], a2 = e2[1], s = e2[2], h = e2[3], c = i + i, l = a2 + a2, f = s + s, v2 = i * c, o = i * l, M = i * f, p2 = a2 * l, x = a2 * f, y = s * f, d2 = h * c, R = h * l, m2 = h * f, g2 = n2[0], P2 = n2[1], A2 = n2[2];
  return r2[0] = (1 - (p2 + y)) * g2, r2[1] = (o + m2) * g2, r2[2] = (M - R) * g2, r2[3] = 0, r2[4] = (o - m2) * P2, r2[5] = (1 - (v2 + y)) * P2, r2[6] = (x + d2) * P2, r2[7] = 0, r2[8] = (M + R) * A2, r2[9] = (x - d2) * A2, r2[10] = (1 - (v2 + p2)) * A2, r2[11] = 0, r2[12] = t2[0], r2[13] = t2[1], r2[14] = t2[2], r2[15] = 1, r2;
}
function T1(r2, e2, t2, n2, i) {
  var a2 = e2[0], s = e2[1], h = e2[2], c = e2[3], l = a2 + a2, f = s + s, v2 = h + h, o = a2 * l, M = a2 * f, p2 = a2 * v2, x = s * f, y = s * v2, d2 = h * v2, R = c * l, m2 = c * f, g2 = c * v2, P2 = n2[0], A2 = n2[1], N2 = n2[2], z2 = i[0], C2 = i[1], $ = i[2], b2 = (1 - (x + d2)) * P2, F = (M + g2) * P2, q3 = (p2 - m2) * P2, L2 = (M - g2) * A2, U = (1 - (o + d2)) * A2, rr2 = (y + R) * A2, er2 = (p2 + m2) * N2, Wr = (y - R) * N2, Br = (1 - (o + x)) * N2;
  return r2[0] = b2, r2[1] = F, r2[2] = q3, r2[3] = 0, r2[4] = L2, r2[5] = U, r2[6] = rr2, r2[7] = 0, r2[8] = er2, r2[9] = Wr, r2[10] = Br, r2[11] = 0, r2[12] = t2[0] + z2 - (b2 * z2 + L2 * C2 + er2 * $), r2[13] = t2[1] + C2 - (F * z2 + U * C2 + Wr * $), r2[14] = t2[2] + $ - (q3 * z2 + rr2 * C2 + Br * $), r2[15] = 1, r2;
}
function E1(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = t2 + t2, h = n2 + n2, c = i + i, l = t2 * s, f = n2 * s, v2 = n2 * h, o = i * s, M = i * h, p2 = i * c, x = a2 * s, y = a2 * h, d2 = a2 * c;
  return r2[0] = 1 - v2 - p2, r2[1] = f + d2, r2[2] = o - y, r2[3] = 0, r2[4] = f - d2, r2[5] = 1 - l - p2, r2[6] = M + x, r2[7] = 0, r2[8] = o + y, r2[9] = M - x, r2[10] = 1 - l - v2, r2[11] = 0, r2[12] = 0, r2[13] = 0, r2[14] = 0, r2[15] = 1, r2;
}
function _1(r2, e2, t2, n2, i, a2, s) {
  var h = 1 / (t2 - e2), c = 1 / (i - n2), l = 1 / (a2 - s);
  return r2[0] = a2 * 2 * h, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = a2 * 2 * c, r2[6] = 0, r2[7] = 0, r2[8] = (t2 + e2) * h, r2[9] = (i + n2) * c, r2[10] = (s + a2) * l, r2[11] = -1, r2[12] = 0, r2[13] = 0, r2[14] = s * a2 * 2 * l, r2[15] = 0, r2;
}
function Re2(r2, e2, t2, n2, i) {
  var a2 = 1 / Math.tan(e2 / 2), s;
  return r2[0] = a2 / t2, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = a2, r2[6] = 0, r2[7] = 0, r2[8] = 0, r2[9] = 0, r2[11] = -1, r2[12] = 0, r2[13] = 0, r2[15] = 0, i != null && i !== 1 / 0 ? (s = 1 / (n2 - i), r2[10] = (i + n2) * s, r2[14] = 2 * i * n2 * s) : (r2[10] = -1, r2[14] = -2 * n2), r2;
}
var R1 = Re2;
var Oe2 = E.perspective = R1;
function O1(r2, e2, t2, n2, i) {
  var a2 = 1 / Math.tan(e2 / 2), s;
  return r2[0] = a2 / t2, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = a2, r2[6] = 0, r2[7] = 0, r2[8] = 0, r2[9] = 0, r2[11] = -1, r2[12] = 0, r2[13] = 0, r2[15] = 0, i != null && i !== 1 / 0 ? (s = 1 / (n2 - i), r2[10] = i * s, r2[14] = i * n2 * s) : (r2[10] = -1, r2[14] = -n2), r2;
}
function A1(r2, e2, t2, n2) {
  var i = Math.tan(e2.upDegrees * Math.PI / 180), a2 = Math.tan(e2.downDegrees * Math.PI / 180), s = Math.tan(e2.leftDegrees * Math.PI / 180), h = Math.tan(e2.rightDegrees * Math.PI / 180), c = 2 / (s + h), l = 2 / (i + a2);
  return r2[0] = c, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = l, r2[6] = 0, r2[7] = 0, r2[8] = -((s - h) * c * 0.5), r2[9] = (i - a2) * l * 0.5, r2[10] = n2 / (t2 - n2), r2[11] = -1, r2[12] = 0, r2[13] = 0, r2[14] = n2 * t2 / (t2 - n2), r2[15] = 0, r2;
}
function Ae2(r2, e2, t2, n2, i, a2, s) {
  var h = 1 / (e2 - t2), c = 1 / (n2 - i), l = 1 / (a2 - s);
  return r2[0] = -2 * h, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = -2 * c, r2[6] = 0, r2[7] = 0, r2[8] = 0, r2[9] = 0, r2[10] = 2 * l, r2[11] = 0, r2[12] = (e2 + t2) * h, r2[13] = (i + n2) * c, r2[14] = (s + a2) * l, r2[15] = 1, r2;
}
var P1 = Ae2;
var Pe2 = E.ortho = P1;
function L1(r2, e2, t2, n2, i, a2, s) {
  var h = 1 / (e2 - t2), c = 1 / (n2 - i), l = 1 / (a2 - s);
  return r2[0] = -2 * h, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = -2 * c, r2[6] = 0, r2[7] = 0, r2[8] = 0, r2[9] = 0, r2[10] = l, r2[11] = 0, r2[12] = (e2 + t2) * h, r2[13] = (i + n2) * c, r2[14] = a2 * l, r2[15] = 1, r2;
}
function w1(r2, e2, t2, n2) {
  var i, a2, s, h, c, l, f, v2, o, M, p2 = e2[0], x = e2[1], y = e2[2], d2 = n2[0], R = n2[1], m2 = n2[2], g2 = t2[0], P2 = t2[1], A2 = t2[2];
  return Math.abs(p2 - g2) < S2.EPSILON && Math.abs(x - P2) < S2.EPSILON && Math.abs(y - A2) < S2.EPSILON ? ue2(r2) : (f = p2 - g2, v2 = x - P2, o = y - A2, M = 1 / Math.hypot(f, v2, o), f *= M, v2 *= M, o *= M, i = R * o - m2 * v2, a2 = m2 * f - d2 * o, s = d2 * v2 - R * f, M = Math.hypot(i, a2, s), M ? (M = 1 / M, i *= M, a2 *= M, s *= M) : (i = 0, a2 = 0, s = 0), h = v2 * s - o * a2, c = o * i - f * s, l = f * a2 - v2 * i, M = Math.hypot(h, c, l), M ? (M = 1 / M, h *= M, c *= M, l *= M) : (h = 0, c = 0, l = 0), r2[0] = i, r2[1] = h, r2[2] = f, r2[3] = 0, r2[4] = a2, r2[5] = c, r2[6] = v2, r2[7] = 0, r2[8] = s, r2[9] = l, r2[10] = o, r2[11] = 0, r2[12] = -(i * p2 + a2 * x + s * y), r2[13] = -(h * p2 + c * x + l * y), r2[14] = -(f * p2 + v2 * x + o * y), r2[15] = 1, r2);
}
function $1(r2, e2, t2, n2) {
  var i = e2[0], a2 = e2[1], s = e2[2], h = n2[0], c = n2[1], l = n2[2], f = i - t2[0], v2 = a2 - t2[1], o = s - t2[2], M = f * f + v2 * v2 + o * o;
  M > 0 && (M = 1 / Math.sqrt(M), f *= M, v2 *= M, o *= M);
  var p2 = c * o - l * v2, x = l * f - h * o, y = h * v2 - c * f;
  return M = p2 * p2 + x * x + y * y, M > 0 && (M = 1 / Math.sqrt(M), p2 *= M, x *= M, y *= M), r2[0] = p2, r2[1] = x, r2[2] = y, r2[3] = 0, r2[4] = v2 * y - o * x, r2[5] = o * p2 - f * y, r2[6] = f * x - v2 * p2, r2[7] = 0, r2[8] = f, r2[9] = v2, r2[10] = o, r2[11] = 0, r2[12] = i, r2[13] = a2, r2[14] = s, r2[15] = 1, r2;
}
function S1(r2) {
  return "mat4(" + r2[0] + ", " + r2[1] + ", " + r2[2] + ", " + r2[3] + ", " + r2[4] + ", " + r2[5] + ", " + r2[6] + ", " + r2[7] + ", " + r2[8] + ", " + r2[9] + ", " + r2[10] + ", " + r2[11] + ", " + r2[12] + ", " + r2[13] + ", " + r2[14] + ", " + r2[15] + ")";
}
function z1(r2) {
  return Math.hypot(r2[0], r2[1], r2[2], r2[3], r2[4], r2[5], r2[6], r2[7], r2[8], r2[9], r2[10], r2[11], r2[12], r2[13], r2[14], r2[15]);
}
function C1(r2, e2, t2) {
  return r2[0] = e2[0] + t2[0], r2[1] = e2[1] + t2[1], r2[2] = e2[2] + t2[2], r2[3] = e2[3] + t2[3], r2[4] = e2[4] + t2[4], r2[5] = e2[5] + t2[5], r2[6] = e2[6] + t2[6], r2[7] = e2[7] + t2[7], r2[8] = e2[8] + t2[8], r2[9] = e2[9] + t2[9], r2[10] = e2[10] + t2[10], r2[11] = e2[11] + t2[11], r2[12] = e2[12] + t2[12], r2[13] = e2[13] + t2[13], r2[14] = e2[14] + t2[14], r2[15] = e2[15] + t2[15], r2;
}
function Le2(r2, e2, t2) {
  return r2[0] = e2[0] - t2[0], r2[1] = e2[1] - t2[1], r2[2] = e2[2] - t2[2], r2[3] = e2[3] - t2[3], r2[4] = e2[4] - t2[4], r2[5] = e2[5] - t2[5], r2[6] = e2[6] - t2[6], r2[7] = e2[7] - t2[7], r2[8] = e2[8] - t2[8], r2[9] = e2[9] - t2[9], r2[10] = e2[10] - t2[10], r2[11] = e2[11] - t2[11], r2[12] = e2[12] - t2[12], r2[13] = e2[13] - t2[13], r2[14] = e2[14] - t2[14], r2[15] = e2[15] - t2[15], r2;
}
function F1(r2, e2, t2) {
  return r2[0] = e2[0] * t2, r2[1] = e2[1] * t2, r2[2] = e2[2] * t2, r2[3] = e2[3] * t2, r2[4] = e2[4] * t2, r2[5] = e2[5] * t2, r2[6] = e2[6] * t2, r2[7] = e2[7] * t2, r2[8] = e2[8] * t2, r2[9] = e2[9] * t2, r2[10] = e2[10] * t2, r2[11] = e2[11] * t2, r2[12] = e2[12] * t2, r2[13] = e2[13] * t2, r2[14] = e2[14] * t2, r2[15] = e2[15] * t2, r2;
}
function N1(r2, e2, t2, n2) {
  return r2[0] = e2[0] + t2[0] * n2, r2[1] = e2[1] + t2[1] * n2, r2[2] = e2[2] + t2[2] * n2, r2[3] = e2[3] + t2[3] * n2, r2[4] = e2[4] + t2[4] * n2, r2[5] = e2[5] + t2[5] * n2, r2[6] = e2[6] + t2[6] * n2, r2[7] = e2[7] + t2[7] * n2, r2[8] = e2[8] + t2[8] * n2, r2[9] = e2[9] + t2[9] * n2, r2[10] = e2[10] + t2[10] * n2, r2[11] = e2[11] + t2[11] * n2, r2[12] = e2[12] + t2[12] * n2, r2[13] = e2[13] + t2[13] * n2, r2[14] = e2[14] + t2[14] * n2, r2[15] = e2[15] + t2[15] * n2, r2;
}
function b1(r2, e2) {
  return r2[0] === e2[0] && r2[1] === e2[1] && r2[2] === e2[2] && r2[3] === e2[3] && r2[4] === e2[4] && r2[5] === e2[5] && r2[6] === e2[6] && r2[7] === e2[7] && r2[8] === e2[8] && r2[9] === e2[9] && r2[10] === e2[10] && r2[11] === e2[11] && r2[12] === e2[12] && r2[13] === e2[13] && r2[14] === e2[14] && r2[15] === e2[15];
}
function q1(r2, e2) {
  var t2 = r2[0], n2 = r2[1], i = r2[2], a2 = r2[3], s = r2[4], h = r2[5], c = r2[6], l = r2[7], f = r2[8], v2 = r2[9], o = r2[10], M = r2[11], p2 = r2[12], x = r2[13], y = r2[14], d2 = r2[15], R = e2[0], m2 = e2[1], g2 = e2[2], P2 = e2[3], A2 = e2[4], N2 = e2[5], z2 = e2[6], C2 = e2[7], $ = e2[8], b2 = e2[9], F = e2[10], q3 = e2[11], L2 = e2[12], U = e2[13], rr2 = e2[14], er2 = e2[15];
  return Math.abs(t2 - R) <= S2.EPSILON * Math.max(1, Math.abs(t2), Math.abs(R)) && Math.abs(n2 - m2) <= S2.EPSILON * Math.max(1, Math.abs(n2), Math.abs(m2)) && Math.abs(i - g2) <= S2.EPSILON * Math.max(1, Math.abs(i), Math.abs(g2)) && Math.abs(a2 - P2) <= S2.EPSILON * Math.max(1, Math.abs(a2), Math.abs(P2)) && Math.abs(s - A2) <= S2.EPSILON * Math.max(1, Math.abs(s), Math.abs(A2)) && Math.abs(h - N2) <= S2.EPSILON * Math.max(1, Math.abs(h), Math.abs(N2)) && Math.abs(c - z2) <= S2.EPSILON * Math.max(1, Math.abs(c), Math.abs(z2)) && Math.abs(l - C2) <= S2.EPSILON * Math.max(1, Math.abs(l), Math.abs(C2)) && Math.abs(f - $) <= S2.EPSILON * Math.max(1, Math.abs(f), Math.abs($)) && Math.abs(v2 - b2) <= S2.EPSILON * Math.max(1, Math.abs(v2), Math.abs(b2)) && Math.abs(o - F) <= S2.EPSILON * Math.max(1, Math.abs(o), Math.abs(F)) && Math.abs(M - q3) <= S2.EPSILON * Math.max(1, Math.abs(M), Math.abs(q3)) && Math.abs(p2 - L2) <= S2.EPSILON * Math.max(1, Math.abs(p2), Math.abs(L2)) && Math.abs(x - U) <= S2.EPSILON * Math.max(1, Math.abs(x), Math.abs(U)) && Math.abs(y - rr2) <= S2.EPSILON * Math.max(1, Math.abs(y), Math.abs(rr2)) && Math.abs(d2 - er2) <= S2.EPSILON * Math.max(1, Math.abs(d2), Math.abs(er2));
}
var Y1 = Te2;
E.mul = Y1;
var k1 = Le2;
E.sub = k1;
var O2 = {};
function xr(r2) {
  return typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? xr = function(t2) {
    return typeof t2;
  } : xr = function(t2) {
    return t2 && typeof Symbol == "function" && t2.constructor === Symbol && t2 !== Symbol.prototype ? "symbol" : typeof t2;
  }, xr(r2);
}
Object.defineProperty(O2, "__esModule", { value: true }), O2.create = $e2, O2.clone = Z1, O2.fromValues = D1, O2.copy = V1, O2.set = U1, O2.add = X1, O2.subtract = Se2, O2.multiply = ze2, O2.divide = Ce2, O2.ceil = I1, O2.floor = K1, O2.min = G1, O2.max = H1, O2.round = Q1, O2.scale = J1, O2.scaleAndAdd = j1, O2.distance = Fe2, O2.squaredDistance = Ne2, O2.length = be2, O2.squaredLength = qe2, O2.negate = ra, O2.inverse = ea, O2.normalize = ta, O2.dot = na, O2.cross = ia, O2.lerp = sa, O2.random = ha;
var W1 = O2.transformMat4 = ca;
O2.transformQuat = la, O2.zero = fa, O2.str = oa, O2.exactEquals = va, O2.equals = Ma, O2.forEach = O2.sqrLen = O2.len = O2.sqrDist = O2.dist = O2.div = O2.mul = O2.sub = void 0;
var D = B1(W);
function we2(r2) {
  if (typeof WeakMap != "function")
    return null;
  var e2 = /* @__PURE__ */ new WeakMap(), t2 = /* @__PURE__ */ new WeakMap();
  return (we2 = function(i) {
    return i ? t2 : e2;
  })(r2);
}
function B1(r2, e2) {
  if (!e2 && r2 && r2.__esModule)
    return r2;
  if (r2 === null || xr(r2) !== "object" && typeof r2 != "function")
    return { default: r2 };
  var t2 = we2(e2);
  if (t2 && t2.has(r2))
    return t2.get(r2);
  var n2 = {}, i = Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var a2 in r2)
    if (a2 !== "default" && Object.prototype.hasOwnProperty.call(r2, a2)) {
      var s = i ? Object.getOwnPropertyDescriptor(r2, a2) : null;
      s && (s.get || s.set) ? Object.defineProperty(n2, a2, s) : n2[a2] = r2[a2];
    }
  return n2.default = r2, t2 && t2.set(r2, n2), n2;
}
function $e2() {
  var r2 = new D.ARRAY_TYPE(4);
  return D.ARRAY_TYPE != Float32Array && (r2[0] = 0, r2[1] = 0, r2[2] = 0, r2[3] = 0), r2;
}
function Z1(r2) {
  var e2 = new D.ARRAY_TYPE(4);
  return e2[0] = r2[0], e2[1] = r2[1], e2[2] = r2[2], e2[3] = r2[3], e2;
}
function D1(r2, e2, t2, n2) {
  var i = new D.ARRAY_TYPE(4);
  return i[0] = r2, i[1] = e2, i[2] = t2, i[3] = n2, i;
}
function V1(r2, e2) {
  return r2[0] = e2[0], r2[1] = e2[1], r2[2] = e2[2], r2[3] = e2[3], r2;
}
function U1(r2, e2, t2, n2, i) {
  return r2[0] = e2, r2[1] = t2, r2[2] = n2, r2[3] = i, r2;
}
function X1(r2, e2, t2) {
  return r2[0] = e2[0] + t2[0], r2[1] = e2[1] + t2[1], r2[2] = e2[2] + t2[2], r2[3] = e2[3] + t2[3], r2;
}
function Se2(r2, e2, t2) {
  return r2[0] = e2[0] - t2[0], r2[1] = e2[1] - t2[1], r2[2] = e2[2] - t2[2], r2[3] = e2[3] - t2[3], r2;
}
function ze2(r2, e2, t2) {
  return r2[0] = e2[0] * t2[0], r2[1] = e2[1] * t2[1], r2[2] = e2[2] * t2[2], r2[3] = e2[3] * t2[3], r2;
}
function Ce2(r2, e2, t2) {
  return r2[0] = e2[0] / t2[0], r2[1] = e2[1] / t2[1], r2[2] = e2[2] / t2[2], r2[3] = e2[3] / t2[3], r2;
}
function I1(r2, e2) {
  return r2[0] = Math.ceil(e2[0]), r2[1] = Math.ceil(e2[1]), r2[2] = Math.ceil(e2[2]), r2[3] = Math.ceil(e2[3]), r2;
}
function K1(r2, e2) {
  return r2[0] = Math.floor(e2[0]), r2[1] = Math.floor(e2[1]), r2[2] = Math.floor(e2[2]), r2[3] = Math.floor(e2[3]), r2;
}
function G1(r2, e2, t2) {
  return r2[0] = Math.min(e2[0], t2[0]), r2[1] = Math.min(e2[1], t2[1]), r2[2] = Math.min(e2[2], t2[2]), r2[3] = Math.min(e2[3], t2[3]), r2;
}
function H1(r2, e2, t2) {
  return r2[0] = Math.max(e2[0], t2[0]), r2[1] = Math.max(e2[1], t2[1]), r2[2] = Math.max(e2[2], t2[2]), r2[3] = Math.max(e2[3], t2[3]), r2;
}
function Q1(r2, e2) {
  return r2[0] = Math.round(e2[0]), r2[1] = Math.round(e2[1]), r2[2] = Math.round(e2[2]), r2[3] = Math.round(e2[3]), r2;
}
function J1(r2, e2, t2) {
  return r2[0] = e2[0] * t2, r2[1] = e2[1] * t2, r2[2] = e2[2] * t2, r2[3] = e2[3] * t2, r2;
}
function j1(r2, e2, t2, n2) {
  return r2[0] = e2[0] + t2[0] * n2, r2[1] = e2[1] + t2[1] * n2, r2[2] = e2[2] + t2[2] * n2, r2[3] = e2[3] + t2[3] * n2, r2;
}
function Fe2(r2, e2) {
  var t2 = e2[0] - r2[0], n2 = e2[1] - r2[1], i = e2[2] - r2[2], a2 = e2[3] - r2[3];
  return Math.hypot(t2, n2, i, a2);
}
function Ne2(r2, e2) {
  var t2 = e2[0] - r2[0], n2 = e2[1] - r2[1], i = e2[2] - r2[2], a2 = e2[3] - r2[3];
  return t2 * t2 + n2 * n2 + i * i + a2 * a2;
}
function be2(r2) {
  var e2 = r2[0], t2 = r2[1], n2 = r2[2], i = r2[3];
  return Math.hypot(e2, t2, n2, i);
}
function qe2(r2) {
  var e2 = r2[0], t2 = r2[1], n2 = r2[2], i = r2[3];
  return e2 * e2 + t2 * t2 + n2 * n2 + i * i;
}
function ra(r2, e2) {
  return r2[0] = -e2[0], r2[1] = -e2[1], r2[2] = -e2[2], r2[3] = -e2[3], r2;
}
function ea(r2, e2) {
  return r2[0] = 1 / e2[0], r2[1] = 1 / e2[1], r2[2] = 1 / e2[2], r2[3] = 1 / e2[3], r2;
}
function ta(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = t2 * t2 + n2 * n2 + i * i + a2 * a2;
  return s > 0 && (s = 1 / Math.sqrt(s)), r2[0] = t2 * s, r2[1] = n2 * s, r2[2] = i * s, r2[3] = a2 * s, r2;
}
function na(r2, e2) {
  return r2[0] * e2[0] + r2[1] * e2[1] + r2[2] * e2[2] + r2[3] * e2[3];
}
function ia(r2, e2, t2, n2) {
  var i = t2[0] * n2[1] - t2[1] * n2[0], a2 = t2[0] * n2[2] - t2[2] * n2[0], s = t2[0] * n2[3] - t2[3] * n2[0], h = t2[1] * n2[2] - t2[2] * n2[1], c = t2[1] * n2[3] - t2[3] * n2[1], l = t2[2] * n2[3] - t2[3] * n2[2], f = e2[0], v2 = e2[1], o = e2[2], M = e2[3];
  return r2[0] = v2 * l - o * c + M * h, r2[1] = -(f * l) + o * s - M * a2, r2[2] = f * c - v2 * s + M * i, r2[3] = -(f * h) + v2 * a2 - o * i, r2;
}
function sa(r2, e2, t2, n2) {
  var i = e2[0], a2 = e2[1], s = e2[2], h = e2[3];
  return r2[0] = i + n2 * (t2[0] - i), r2[1] = a2 + n2 * (t2[1] - a2), r2[2] = s + n2 * (t2[2] - s), r2[3] = h + n2 * (t2[3] - h), r2;
}
function ha(r2, e2) {
  e2 = e2 || 1;
  var t2, n2, i, a2, s, h;
  do
    t2 = D.RANDOM() * 2 - 1, n2 = D.RANDOM() * 2 - 1, s = t2 * t2 + n2 * n2;
  while (s >= 1);
  do
    i = D.RANDOM() * 2 - 1, a2 = D.RANDOM() * 2 - 1, h = i * i + a2 * a2;
  while (h >= 1);
  var c = Math.sqrt((1 - s) / h);
  return r2[0] = e2 * t2, r2[1] = e2 * n2, r2[2] = e2 * i * c, r2[3] = e2 * a2 * c, r2;
}
function ca(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = e2[3];
  return r2[0] = t2[0] * n2 + t2[4] * i + t2[8] * a2 + t2[12] * s, r2[1] = t2[1] * n2 + t2[5] * i + t2[9] * a2 + t2[13] * s, r2[2] = t2[2] * n2 + t2[6] * i + t2[10] * a2 + t2[14] * s, r2[3] = t2[3] * n2 + t2[7] * i + t2[11] * a2 + t2[15] * s, r2;
}
function la(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = t2[0], h = t2[1], c = t2[2], l = t2[3], f = l * n2 + h * a2 - c * i, v2 = l * i + c * n2 - s * a2, o = l * a2 + s * i - h * n2, M = -s * n2 - h * i - c * a2;
  return r2[0] = f * l + M * -s + v2 * -c - o * -h, r2[1] = v2 * l + M * -h + o * -s - f * -c, r2[2] = o * l + M * -c + f * -h - v2 * -s, r2[3] = e2[3], r2;
}
function fa(r2) {
  return r2[0] = 0, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2;
}
function oa(r2) {
  return "vec4(" + r2[0] + ", " + r2[1] + ", " + r2[2] + ", " + r2[3] + ")";
}
function va(r2, e2) {
  return r2[0] === e2[0] && r2[1] === e2[1] && r2[2] === e2[2] && r2[3] === e2[3];
}
function Ma(r2, e2) {
  var t2 = r2[0], n2 = r2[1], i = r2[2], a2 = r2[3], s = e2[0], h = e2[1], c = e2[2], l = e2[3];
  return Math.abs(t2 - s) <= D.EPSILON * Math.max(1, Math.abs(t2), Math.abs(s)) && Math.abs(n2 - h) <= D.EPSILON * Math.max(1, Math.abs(n2), Math.abs(h)) && Math.abs(i - c) <= D.EPSILON * Math.max(1, Math.abs(i), Math.abs(c)) && Math.abs(a2 - l) <= D.EPSILON * Math.max(1, Math.abs(a2), Math.abs(l));
}
var pa = Se2;
O2.sub = pa;
var ya = ze2;
O2.mul = ya;
var xa = Ce2;
O2.div = xa;
var ma = Fe2;
O2.dist = ma;
var da = Ne2;
O2.sqrDist = da;
var ga = be2;
O2.len = ga;
var ua = qe2;
O2.sqrLen = ua;
var Ta = function() {
  var r2 = $e2();
  return function(e2, t2, n2, i, a2, s) {
    var h, c;
    for (t2 || (t2 = 4), n2 || (n2 = 0), i ? c = Math.min(i * t2 + n2, e2.length) : c = e2.length, h = n2; h < c; h += t2)
      r2[0] = e2[h], r2[1] = e2[h + 1], r2[2] = e2[h + 2], r2[3] = e2[h + 3], a2(r2, r2, s), e2[h] = r2[0], e2[h + 1] = r2[1], e2[h + 2] = r2[2], e2[h + 3] = r2[3];
    return e2;
  };
}();
O2.forEach = Ta;
var zr;
(function(r2) {
  r2[r2.COL0ROW0 = 0] = "COL0ROW0", r2[r2.COL0ROW1 = 1] = "COL0ROW1", r2[r2.COL0ROW2 = 2] = "COL0ROW2", r2[r2.COL0ROW3 = 3] = "COL0ROW3", r2[r2.COL1ROW0 = 4] = "COL1ROW0", r2[r2.COL1ROW1 = 5] = "COL1ROW1", r2[r2.COL1ROW2 = 6] = "COL1ROW2", r2[r2.COL1ROW3 = 7] = "COL1ROW3", r2[r2.COL2ROW0 = 8] = "COL2ROW0", r2[r2.COL2ROW1 = 9] = "COL2ROW1", r2[r2.COL2ROW2 = 10] = "COL2ROW2", r2[r2.COL2ROW3 = 11] = "COL2ROW3", r2[r2.COL3ROW0 = 12] = "COL3ROW0", r2[r2.COL3ROW1 = 13] = "COL3ROW1", r2[r2.COL3ROW2 = 14] = "COL3ROW2", r2[r2.COL3ROW3 = 15] = "COL3ROW3";
})(zr || (zr = {}));
var Ea = 45 * Math.PI / 180;
var _a = 1;
var Cr = 0.1;
var Fr = 500;
var Ra = Object.freeze([1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1]);
var ar = class extends ai {
  static get IDENTITY() {
    return Aa();
  }
  static get ZERO() {
    return Oa();
  }
  get ELEMENTS() {
    return 16;
  }
  get RANK() {
    return 4;
  }
  get INDICES() {
    return zr;
  }
  constructor(e2) {
    super(-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0), arguments.length === 1 && Array.isArray(e2) ? this.copy(e2) : this.identity();
  }
  copy(e2) {
    return this[0] = e2[0], this[1] = e2[1], this[2] = e2[2], this[3] = e2[3], this[4] = e2[4], this[5] = e2[5], this[6] = e2[6], this[7] = e2[7], this[8] = e2[8], this[9] = e2[9], this[10] = e2[10], this[11] = e2[11], this[12] = e2[12], this[13] = e2[13], this[14] = e2[14], this[15] = e2[15], this.check();
  }
  set(e2, t2, n2, i, a2, s, h, c, l, f, v2, o, M, p2, x, y) {
    return this[0] = e2, this[1] = t2, this[2] = n2, this[3] = i, this[4] = a2, this[5] = s, this[6] = h, this[7] = c, this[8] = l, this[9] = f, this[10] = v2, this[11] = o, this[12] = M, this[13] = p2, this[14] = x, this[15] = y, this.check();
  }
  setRowMajor(e2, t2, n2, i, a2, s, h, c, l, f, v2, o, M, p2, x, y) {
    return this[0] = e2, this[1] = a2, this[2] = l, this[3] = M, this[4] = t2, this[5] = s, this[6] = f, this[7] = p2, this[8] = n2, this[9] = h, this[10] = v2, this[11] = x, this[12] = i, this[13] = c, this[14] = o, this[15] = y, this.check();
  }
  toRowMajor(e2) {
    return e2[0] = this[0], e2[1] = this[4], e2[2] = this[8], e2[3] = this[12], e2[4] = this[1], e2[5] = this[5], e2[6] = this[9], e2[7] = this[13], e2[8] = this[2], e2[9] = this[6], e2[10] = this[10], e2[11] = this[14], e2[12] = this[3], e2[13] = this[7], e2[14] = this[11], e2[15] = this[15], e2;
  }
  identity() {
    return this.copy(Ra);
  }
  fromObject(e2) {
    return this.check();
  }
  fromQuaternion(e2) {
    return Xi(this, e2), this.check();
  }
  frustum(e2) {
    const { left: t2, right: n2, bottom: i, top: a2, near: s = Cr, far: h = Fr } = e2;
    return h === 1 / 0 ? Pa(this, t2, n2, i, a2, s) : Ii(this, t2, n2, i, a2, s, h), this.check();
  }
  lookAt(e2) {
    const { eye: t2, center: n2 = [0, 0, 0], up: i = [0, 1, 0] } = e2;
    return Ki(this, t2, n2, i), this.check();
  }
  ortho(e2) {
    const { left: t2, right: n2, bottom: i, top: a2, near: s = Cr, far: h = Fr } = e2;
    return Pe2(this, t2, n2, i, a2, s, h), this.check();
  }
  orthographic(e2) {
    const { fovy: t2 = Ea, aspect: n2 = _a, focalDistance: i = 1, near: a2 = Cr, far: s = Fr } = e2;
    Ye2(t2);
    const h = t2 / 2, c = i * Math.tan(h), l = c * n2;
    return this.ortho({ left: -l, right: l, bottom: -c, top: c, near: a2, far: s });
  }
  perspective(e2) {
    const { fovy: t2 = 45 * Math.PI / 180, aspect: n2 = 1, near: i = 0.1, far: a2 = 500 } = e2;
    return Ye2(t2), Oe2(this, t2, n2, i, a2), this.check();
  }
  determinant() {
    return ki(this);
  }
  getScale(e2 = [-0, -0, -0]) {
    return e2[0] = Math.sqrt(this[0] * this[0] + this[1] * this[1] + this[2] * this[2]), e2[1] = Math.sqrt(this[4] * this[4] + this[5] * this[5] + this[6] * this[6]), e2[2] = Math.sqrt(this[8] * this[8] + this[9] * this[9] + this[10] * this[10]), e2;
  }
  getTranslation(e2 = [-0, -0, -0]) {
    return e2[0] = this[12], e2[1] = this[13], e2[2] = this[14], e2;
  }
  getRotation(e2, t2) {
    e2 = e2 || [-0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0, -0], t2 = t2 || [-0, -0, -0];
    const n2 = this.getScale(t2), i = 1 / n2[0], a2 = 1 / n2[1], s = 1 / n2[2];
    return e2[0] = this[0] * i, e2[1] = this[1] * a2, e2[2] = this[2] * s, e2[3] = 0, e2[4] = this[4] * i, e2[5] = this[5] * a2, e2[6] = this[6] * s, e2[7] = 0, e2[8] = this[8] * i, e2[9] = this[9] * a2, e2[10] = this[10] * s, e2[11] = 0, e2[12] = 0, e2[13] = 0, e2[14] = 0, e2[15] = 1, e2;
  }
  getRotationMatrix3(e2, t2) {
    e2 = e2 || [-0, -0, -0, -0, -0, -0, -0, -0, -0], t2 = t2 || [-0, -0, -0];
    const n2 = this.getScale(t2), i = 1 / n2[0], a2 = 1 / n2[1], s = 1 / n2[2];
    return e2[0] = this[0] * i, e2[1] = this[1] * a2, e2[2] = this[2] * s, e2[3] = this[4] * i, e2[4] = this[5] * a2, e2[5] = this[6] * s, e2[6] = this[8] * i, e2[7] = this[9] * a2, e2[8] = this[10] * s, e2;
  }
  transpose() {
    return qi(this, this), this.check();
  }
  invert() {
    return Yi(this, this), this.check();
  }
  multiplyLeft(e2) {
    return de2(this, e2, this), this.check();
  }
  multiplyRight(e2) {
    return de2(this, this, e2), this.check();
  }
  rotateX(e2) {
    return Di(this, this, e2), this.check();
  }
  rotateY(e2) {
    return Vi(this, this, e2), this.check();
  }
  rotateZ(e2) {
    return Ui(this, this, e2), this.check();
  }
  rotateXYZ(e2) {
    return this.rotateX(e2[0]).rotateY(e2[1]).rotateZ(e2[2]);
  }
  rotateAxis(e2, t2) {
    return Zi(this, this, e2, t2), this.check();
  }
  scale(e2) {
    return Bi(this, this, Array.isArray(e2) ? e2 : [e2, e2, e2]), this.check();
  }
  translate(e2) {
    return Wi(this, this, e2), this.check();
  }
  transform(e2, t2) {
    return e2.length === 4 ? (t2 = W1(t2 || [-0, -0, -0, -0], e2, this), Sr(t2, 4), t2) : this.transformAsPoint(e2, t2);
  }
  transformAsPoint(e2, t2) {
    const { length: n2 } = e2;
    let i;
    switch (n2) {
      case 2:
        i = $t2(t2 || [-0, -0], e2, this);
        break;
      case 3:
        i = dn(t2 || [-0, -0, -0], e2, this);
        break;
      default:
        throw new Error("Illegal vector");
    }
    return Sr(i, e2.length), i;
  }
  transformAsVector(e2, t2) {
    let n2;
    switch (e2.length) {
      case 2:
        n2 = xn(t2 || [-0, -0], e2, this);
        break;
      case 3:
        n2 = mn(t2 || [-0, -0, -0], e2, this);
        break;
      default:
        throw new Error("Illegal vector");
    }
    return Sr(n2, e2.length), n2;
  }
  transformPoint(e2, t2) {
    return this.transformAsPoint(e2, t2);
  }
  transformVector(e2, t2) {
    return this.transformAsPoint(e2, t2);
  }
  transformDirection(e2, t2) {
    return this.transformAsVector(e2, t2);
  }
  makeRotationX(e2) {
    return this.identity().rotateX(e2);
  }
  makeTranslation(e2, t2, n2) {
    return this.identity().translate([e2, t2, n2]);
  }
};
var mr;
var dr;
function Oa() {
  return mr || (mr = new ar([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]), Object.freeze(mr)), mr;
}
function Aa() {
  return dr || (dr = new ar(), Object.freeze(dr)), dr;
}
function Ye2(r2) {
  if (r2 > Math.PI * 2)
    throw Error("expected radians");
}
function Pa(r2, e2, t2, n2, i, a2) {
  const s = 2 * a2 / (t2 - e2), h = 2 * a2 / (i - n2), c = (t2 + e2) / (t2 - e2), l = (i + n2) / (i - n2), f = -1, v2 = -1, o = -2 * a2;
  return r2[0] = s, r2[1] = 0, r2[2] = 0, r2[3] = 0, r2[4] = 0, r2[5] = h, r2[6] = 0, r2[7] = 0, r2[8] = c, r2[9] = l, r2[10] = f, r2[11] = v2, r2[12] = 0, r2[13] = 0, r2[14] = o, r2[15] = 0, r2;
}
var u2 = {};
function gr(r2) {
  return typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? gr = function(t2) {
    return typeof t2;
  } : gr = function(t2) {
    return t2 && typeof Symbol == "function" && t2.constructor === Symbol && t2 !== Symbol.prototype ? "symbol" : typeof t2;
  }, gr(r2);
}
Object.defineProperty(u2, "__esModule", { value: true }), u2.create = Nr, u2.identity = wa, u2.setAxisAngle = We2, u2.getAxisAngle = $a, u2.getAngle = Sa, u2.multiply = Be2, u2.rotateX = za, u2.rotateY = Ca, u2.rotateZ = Fa, u2.calculateW = Na, u2.exp = Ze2, u2.ln = De2, u2.pow = ba, u2.slerp = Tr, u2.random = qa, u2.invert = Ya, u2.conjugate = ka, u2.fromMat3 = Ve2, u2.fromEuler = Wa, u2.str = Ba, u2.setAxes = u2.sqlerp = u2.rotationTo = u2.equals = u2.exactEquals = u2.normalize = u2.sqrLen = u2.squaredLength = u2.len = u2.length = u2.lerp = u2.dot = u2.scale = u2.mul = u2.add = u2.set = u2.copy = u2.fromValues = u2.clone = void 0;
var J2 = ur(W);
var La = ur(w);
var K2 = ur(T);
var Z2 = ur(O2);
function ke2(r2) {
  if (typeof WeakMap != "function")
    return null;
  var e2 = /* @__PURE__ */ new WeakMap(), t2 = /* @__PURE__ */ new WeakMap();
  return (ke2 = function(i) {
    return i ? t2 : e2;
  })(r2);
}
function ur(r2, e2) {
  if (!e2 && r2 && r2.__esModule)
    return r2;
  if (r2 === null || gr(r2) !== "object" && typeof r2 != "function")
    return { default: r2 };
  var t2 = ke2(e2);
  if (t2 && t2.has(r2))
    return t2.get(r2);
  var n2 = {}, i = Object.defineProperty && Object.getOwnPropertyDescriptor;
  for (var a2 in r2)
    if (a2 !== "default" && Object.prototype.hasOwnProperty.call(r2, a2)) {
      var s = i ? Object.getOwnPropertyDescriptor(r2, a2) : null;
      s && (s.get || s.set) ? Object.defineProperty(n2, a2, s) : n2[a2] = r2[a2];
    }
  return n2.default = r2, t2 && t2.set(r2, n2), n2;
}
function Nr() {
  var r2 = new J2.ARRAY_TYPE(4);
  return J2.ARRAY_TYPE != Float32Array && (r2[0] = 0, r2[1] = 0, r2[2] = 0), r2[3] = 1, r2;
}
function wa(r2) {
  return r2[0] = 0, r2[1] = 0, r2[2] = 0, r2[3] = 1, r2;
}
function We2(r2, e2, t2) {
  t2 = t2 * 0.5;
  var n2 = Math.sin(t2);
  return r2[0] = n2 * e2[0], r2[1] = n2 * e2[1], r2[2] = n2 * e2[2], r2[3] = Math.cos(t2), r2;
}
function $a(r2, e2) {
  var t2 = Math.acos(e2[3]) * 2, n2 = Math.sin(t2 / 2);
  return n2 > J2.EPSILON ? (r2[0] = e2[0] / n2, r2[1] = e2[1] / n2, r2[2] = e2[2] / n2) : (r2[0] = 1, r2[1] = 0, r2[2] = 0), t2;
}
function Sa(r2, e2) {
  var t2 = Xe2(r2, e2);
  return Math.acos(2 * t2 * t2 - 1);
}
function Be2(r2, e2, t2) {
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = e2[3], h = t2[0], c = t2[1], l = t2[2], f = t2[3];
  return r2[0] = n2 * f + s * h + i * l - a2 * c, r2[1] = i * f + s * c + a2 * h - n2 * l, r2[2] = a2 * f + s * l + n2 * c - i * h, r2[3] = s * f - n2 * h - i * c - a2 * l, r2;
}
function za(r2, e2, t2) {
  t2 *= 0.5;
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = e2[3], h = Math.sin(t2), c = Math.cos(t2);
  return r2[0] = n2 * c + s * h, r2[1] = i * c + a2 * h, r2[2] = a2 * c - i * h, r2[3] = s * c - n2 * h, r2;
}
function Ca(r2, e2, t2) {
  t2 *= 0.5;
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = e2[3], h = Math.sin(t2), c = Math.cos(t2);
  return r2[0] = n2 * c - a2 * h, r2[1] = i * c + s * h, r2[2] = a2 * c + n2 * h, r2[3] = s * c - i * h, r2;
}
function Fa(r2, e2, t2) {
  t2 *= 0.5;
  var n2 = e2[0], i = e2[1], a2 = e2[2], s = e2[3], h = Math.sin(t2), c = Math.cos(t2);
  return r2[0] = n2 * c + i * h, r2[1] = i * c - n2 * h, r2[2] = a2 * c + s * h, r2[3] = s * c - a2 * h, r2;
}
function Na(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2];
  return r2[0] = t2, r2[1] = n2, r2[2] = i, r2[3] = Math.sqrt(Math.abs(1 - t2 * t2 - n2 * n2 - i * i)), r2;
}
function Ze2(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = Math.sqrt(t2 * t2 + n2 * n2 + i * i), h = Math.exp(a2), c = s > 0 ? h * Math.sin(s) / s : 0;
  return r2[0] = t2 * c, r2[1] = n2 * c, r2[2] = i * c, r2[3] = h * Math.cos(s), r2;
}
function De2(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = Math.sqrt(t2 * t2 + n2 * n2 + i * i), h = s > 0 ? Math.atan2(s, a2) / s : 0;
  return r2[0] = t2 * h, r2[1] = n2 * h, r2[2] = i * h, r2[3] = 0.5 * Math.log(t2 * t2 + n2 * n2 + i * i + a2 * a2), r2;
}
function ba(r2, e2, t2) {
  return De2(r2, e2), Ue2(r2, r2, t2), Ze2(r2, r2), r2;
}
function Tr(r2, e2, t2, n2) {
  var i = e2[0], a2 = e2[1], s = e2[2], h = e2[3], c = t2[0], l = t2[1], f = t2[2], v2 = t2[3], o, M, p2, x, y;
  return M = i * c + a2 * l + s * f + h * v2, M < 0 && (M = -M, c = -c, l = -l, f = -f, v2 = -v2), 1 - M > J2.EPSILON ? (o = Math.acos(M), p2 = Math.sin(o), x = Math.sin((1 - n2) * o) / p2, y = Math.sin(n2 * o) / p2) : (x = 1 - n2, y = n2), r2[0] = x * i + y * c, r2[1] = x * a2 + y * l, r2[2] = x * s + y * f, r2[3] = x * h + y * v2, r2;
}
function qa(r2) {
  var e2 = J2.RANDOM(), t2 = J2.RANDOM(), n2 = J2.RANDOM(), i = Math.sqrt(1 - e2), a2 = Math.sqrt(e2);
  return r2[0] = i * Math.sin(2 * Math.PI * t2), r2[1] = i * Math.cos(2 * Math.PI * t2), r2[2] = a2 * Math.sin(2 * Math.PI * n2), r2[3] = a2 * Math.cos(2 * Math.PI * n2), r2;
}
function Ya(r2, e2) {
  var t2 = e2[0], n2 = e2[1], i = e2[2], a2 = e2[3], s = t2 * t2 + n2 * n2 + i * i + a2 * a2, h = s ? 1 / s : 0;
  return r2[0] = -t2 * h, r2[1] = -n2 * h, r2[2] = -i * h, r2[3] = a2 * h, r2;
}
function ka(r2, e2) {
  return r2[0] = -e2[0], r2[1] = -e2[1], r2[2] = -e2[2], r2[3] = e2[3], r2;
}
function Ve2(r2, e2) {
  var t2 = e2[0] + e2[4] + e2[8], n2;
  if (t2 > 0)
    n2 = Math.sqrt(t2 + 1), r2[3] = 0.5 * n2, n2 = 0.5 / n2, r2[0] = (e2[5] - e2[7]) * n2, r2[1] = (e2[6] - e2[2]) * n2, r2[2] = (e2[1] - e2[3]) * n2;
  else {
    var i = 0;
    e2[4] > e2[0] && (i = 1), e2[8] > e2[i * 3 + i] && (i = 2);
    var a2 = (i + 1) % 3, s = (i + 2) % 3;
    n2 = Math.sqrt(e2[i * 3 + i] - e2[a2 * 3 + a2] - e2[s * 3 + s] + 1), r2[i] = 0.5 * n2, n2 = 0.5 / n2, r2[3] = (e2[a2 * 3 + s] - e2[s * 3 + a2]) * n2, r2[a2] = (e2[a2 * 3 + i] + e2[i * 3 + a2]) * n2, r2[s] = (e2[s * 3 + i] + e2[i * 3 + s]) * n2;
  }
  return r2;
}
function Wa(r2, e2, t2, n2) {
  var i = 0.5 * Math.PI / 180;
  e2 *= i, t2 *= i, n2 *= i;
  var a2 = Math.sin(e2), s = Math.cos(e2), h = Math.sin(t2), c = Math.cos(t2), l = Math.sin(n2), f = Math.cos(n2);
  return r2[0] = a2 * c * f - s * h * l, r2[1] = s * h * f + a2 * c * l, r2[2] = s * c * l - a2 * h * f, r2[3] = s * c * f + a2 * h * l, r2;
}
function Ba(r2) {
  return "quat(" + r2[0] + ", " + r2[1] + ", " + r2[2] + ", " + r2[3] + ")";
}
var Za = Z2.clone;
u2.clone = Za;
var Da = Z2.fromValues;
u2.fromValues = Da;
var Va = Z2.copy;
u2.copy = Va;
var Ua = Z2.set;
u2.set = Ua;
var Xa = Z2.add;
u2.add = Xa;
var Ia = Be2;
u2.mul = Ia;
var Ue2 = Z2.scale;
u2.scale = Ue2;
var Xe2 = Z2.dot;
u2.dot = Xe2;
var Ka = Z2.lerp;
u2.lerp = Ka;
var Ie2 = Z2.length;
u2.length = Ie2;
var Ga = Ie2;
u2.len = Ga;
var Ke2 = Z2.squaredLength;
u2.squaredLength = Ke2;
var Ha = Ke2;
u2.sqrLen = Ha;
var br = Z2.normalize;
u2.normalize = br;
var Qa = Z2.exactEquals;
u2.exactEquals = Qa;
var Ja = Z2.equals;
u2.equals = Ja;
var ja = function() {
  var r2 = K2.create(), e2 = K2.fromValues(1, 0, 0), t2 = K2.fromValues(0, 1, 0);
  return function(n2, i, a2) {
    var s = K2.dot(i, a2);
    return s < -0.999999 ? (K2.cross(r2, e2, i), K2.len(r2) < 1e-6 && K2.cross(r2, t2, i), K2.normalize(r2, r2), We2(n2, r2, Math.PI), n2) : s > 0.999999 ? (n2[0] = 0, n2[1] = 0, n2[2] = 0, n2[3] = 1, n2) : (K2.cross(r2, i, a2), n2[0] = r2[0], n2[1] = r2[1], n2[2] = r2[2], n2[3] = 1 + s, br(n2, n2));
  };
}();
u2.rotationTo = ja;
var r0 = function() {
  var r2 = Nr(), e2 = Nr();
  return function(t2, n2, i, a2, s, h) {
    return Tr(r2, n2, s, h), Tr(e2, i, a2, h), Tr(t2, r2, e2, 2 * h * (1 - h)), t2;
  };
}();
u2.sqlerp = r0;
var e0 = function() {
  var r2 = La.create();
  return function(e2, t2, n2, i) {
    return r2[0] = n2[0], r2[3] = n2[1], r2[6] = n2[2], r2[1] = i[0], r2[4] = i[1], r2[7] = i[2], r2[2] = -t2[0], r2[5] = -t2[1], r2[8] = -t2[2], br(e2, Ve2(e2, r2));
  };
}();
u2.setAxes = e0;
var Ge2;
(function(r2) {
  r2[r2.ZYX = 0] = "ZYX", r2[r2.YXZ = 1] = "YXZ", r2[r2.XZY = 2] = "XZY", r2[r2.ZXY = 3] = "ZXY", r2[r2.YZX = 4] = "YZX", r2[r2.XYZ = 5] = "XYZ";
})(Ge2 || (Ge2 = {}));
var k = 3.141592653589793;
var Er = 6378245;
var _r = 0.006693421622965943;
var Rr = k * 3e3 / 180;
function sr2(r2, e2, t2) {
  const n2 = k / 180 * e2, i = Math.pow(2, t2), a2 = Math.floor((r2 + 180) / 360 * i), s = Math.floor((1 - Math.asinh(Math.tan(n2)) / k) / 2 * i);
  return [a2, s];
}
function He2(r2, e2, t2) {
  const n2 = Math.pow(2, t2), i = r2 / n2 * 360 - 180, a2 = Math.atan(Math.sinh(k * (1 - 2 * e2 / n2))) * 180 / k;
  return [i, a2];
}
function t0(r2, e2) {
  let t2 = je2(r2 - 105, e2 - 35), n2 = rt2(r2 - 105, e2 - 35);
  const i = e2 / 180 * k;
  let a2 = Math.sin(i);
  a2 = 1 - _r * a2 * a2;
  const s = Math.sqrt(a2);
  t2 = t2 * 180 / (Er * (1 - _r) / (a2 * s) * k), n2 = n2 * 180 / (Er / s * Math.cos(i) * k);
  const h = e2 + t2;
  return { lng: r2 + n2, lat: h };
}
function Qe2(r2, e2) {
  const t2 = i0(r2, e2), n2 = r2 * 2 - t2.lng, i = e2 * 2 - t2.lat;
  return { lng: n2, lat: i };
}
function Je2(r2, e2) {
  const t2 = Math.sqrt(r2 * r2 + e2 * e2) + 2e-5 * Math.sin(e2 * Rr), n2 = Math.atan2(e2, r2) + 3e-6 * Math.cos(r2 * Rr), i = t2 * Math.cos(n2) + 65e-4, a2 = t2 * Math.sin(n2) + 6e-3;
  return { lng: i, lat: a2 };
}
function n0(r2, e2) {
  const t2 = r2 - 65e-4, n2 = e2 - 6e-3, i = Math.sqrt(t2 * t2 + n2 * n2) - 2e-5 * Math.sin(n2 * Rr), a2 = Math.atan2(n2, t2) - 3e-6 * Math.cos(t2 * Rr), s = i * Math.cos(a2), h = i * Math.sin(a2);
  return { lng: s, lat: h };
}
function i0(r2, e2) {
  let t2 = je2(r2 - 105, e2 - 35), n2 = rt2(r2 - 105, e2 - 35);
  const i = e2 / 180 * k;
  let a2 = Math.sin(i);
  a2 = 1 - _r * a2 * a2;
  const s = Math.sqrt(a2);
  t2 = t2 * 180 / (Er * (1 - _r) / (a2 * s) * k), n2 = n2 * 180 / (Er / s * Math.cos(i) * k);
  const h = e2 + t2;
  return { lng: r2 + n2, lat: h };
}
function je2(r2, e2) {
  let t2 = -100 + 2 * r2 + 3 * e2 + 0.2 * e2 * e2 + 0.1 * r2 * e2 + 0.2 * Math.sqrt(Math.abs(r2));
  return t2 += (20 * Math.sin(6 * r2 * k) + 20 * Math.sin(2 * r2 * k)) * 2 / 3, t2 += (20 * Math.sin(e2 * k) + 40 * Math.sin(e2 / 3 * k)) * 2 / 3, t2 += (160 * Math.sin(e2 / 12 * k) + 320 * Math.sin(e2 * k / 30)) * 2 / 3, t2;
}
function rt2(r2, e2) {
  let t2 = 300 + r2 + 2 * e2 + 0.1 * r2 * r2 + 0.1 * r2 * e2 + 0.1 * Math.sqrt(Math.abs(r2));
  return t2 += (20 * Math.sin(6 * r2 * k) + 20 * Math.sin(2 * r2 * k)) * 2 / 3, t2 += (20 * Math.sin(r2 * k) + 40 * Math.sin(r2 / 3 * k)) * 2 / 3, t2 += (150 * Math.sin(r2 / 12 * k) + 300 * Math.sin(r2 / 30 * k)) * 2 / 3, t2;
}
function et2(r2, e2) {
  for (var t2 in e2)
    e2.hasOwnProperty(t2) && (r2[t2] = e2[t2]);
  return r2;
}
function a0(r2, e2) {
  for (var t2 in e2)
    r2[t2] = e2[t2];
}
function tt2(r2) {
  return typeof r2 == "string";
}
var nt2 = void 0;
var Or = null;
function V2(r2, e2) {
  isNaN(r2) && (r2 = Ib(r2), r2 = isNaN(r2) ? 0 : r2), tt2(r2) && (r2 = parseFloat(r2)), isNaN(e2) && (e2 = Ib(e2), e2 = isNaN(e2) ? 0 : e2), tt2(e2) && (e2 = parseFloat(e2)), this.lng = r2, this.lat = e2;
}
V2.TL = function(r2) {
  return r2 && 180 >= r2.lng && -180 <= r2.lng && 74 >= r2.lat && -74 <= r2.lat;
}, V2.prototype.lb = function(r2) {
  return r2 && this.lat == r2.lat && this.lng == r2.lng;
};
function Ar(r2, e2) {
  this.x = r2 || 0, this.y = e2 || 0, this.x = this.x, this.y = this.y;
}
Ar.prototype.lb = function(r2) {
  return r2 && r2.x == this.x && r2.y == this.y;
};
function qr() {
}
qr.prototype.nh = function() {
  aa("lngLatToPoint方法未实现");
}, qr.prototype.wi = function() {
  aa("pointToLngLat方法未实现");
};
function G2() {
}
G2.prototype = new qr(), et2(G2, { $O: 637099681e-2, lG: [1289059486e-2, 836237787e-2, 5591021, 348198983e-2, 167804312e-2, 0], Au: [75, 60, 45, 30, 15, 0], fP: [[1410526172116255e-23, 898305509648872e-20, -1.9939833816331, 200.9824383106796, -187.2403703815547, 91.6087516669843, -23.38765649603339, 2.57121317296198, -0.03801003308653, 173379812e-1], [-7435856389565537e-24, 8983055097726239e-21, -0.78625201886289, 96.32687599759846, -1.85204757529826, -59.36935905485877, 47.40033549296737, -16.50741931063887, 2.28786674699375, 1026014486e-2], [-3030883460898826e-23, 898305509983578e-20, 0.30071316287616, 59.74293618442277, 7.357984074871, -25.38371002664745, 13.45380521110908, -3.29883767235584, 0.32710905363475, 685681737e-2], [-1981981304930552e-23, 8983055099779535e-21, 0.03278182852591, 40.31678527705744, 0.65659298677277, -4.44255534477492, 0.85341911805263, 0.12923347998204, -0.04625736007561, 448277706e-2], [309191371068437e-23, 8983055096812155e-21, 6995724062e-14, 23.10934304144901, -23663490511e-14, -0.6321817810242, -0.00663494467273, 0.03430082397953, -0.00466043876332, 25551644e-1], [2890871144776878e-24, 8983055095805407e-21, -3068298e-14, 7.47137025468032, -353937994e-14, -0.02145144861037, -1234426596e-14, 10322952773e-14, -323890364e-14, 826088.5]], iG: [[-0.0015702102444, 111320.7020616939, 1704480524535203, -10338987376042340, 26112667856603880, -35149669176653700, 26595700718403920, -10725012454188240, 1800819912950474, 82.5], [8277824516172526e-19, 111320.7020463578, 6477955746671607e-7, -4082003173641316e-6, 1077490566351142e-5, -1517187553151559e-5, 1205306533862167e-5, -5124939663577472e-6, 9133119359512032e-7, 67.5], [0.00337398766765, 111320.7020202162, 4481351045890365e-9, -2339375119931662e-8, 7968221547186455e-8, -1159649932797253e-7, 9723671115602145e-8, -4366194633752821e-8, 8477230501135234e-9, 52.5], [0.00220636496208, 111320.7020209128, 51751.86112841131, 3796837749470245e-9, 992013.7397791013, -122195221711287e-8, 1340652697009075e-9, -620943.6990984312, 144416.9293806241, 37.5], [-3441963504368392e-19, 111320.7020576856, 278.2353980772752, 2485758690035394e-9, 6070.750963243378, 54821.18345352118, 9540.606633304236, -2710.55326746645, 1405.483844121726, 22.5], [-3218135878613132e-19, 111320.7020701615, 0.00369383431289, 823725.6402795718, 0.46104986909093, 2351.343141331292, 1.58060784298199, 8.77738589078284, 0.37238884252424, 7.45]], Z1: function(i, e2) {
  if (!i || !e2)
    return 0;
  var t2, n2, i = this.Fb(i);
  return i ? (t2 = this.Tk(i.lng), n2 = this.Tk(i.lat), e2 = this.Fb(e2), e2 ? this.Pe(t2, this.Tk(e2.lng), n2, this.Tk(e2.lat)) : 0) : 0;
}, Vo: function(r2, e2) {
  return !r2 || !e2 ? 0 : (r2.lng = this.JD(r2.lng, -180, 180), r2.lat = this.ND(r2.lat, -74, 74), e2.lng = this.JD(e2.lng, -180, 180), e2.lat = this.ND(e2.lat, -74, 74), this.Pe(this.Tk(r2.lng), this.Tk(e2.lng), this.Tk(r2.lat), this.Tk(e2.lat)));
}, Fb: function(r2) {
  if (r2 === Or || r2 === nt2)
    return new V2(0, 0);
  var e2, t2;
  e2 = new V2(Math.abs(r2.lng), Math.abs(r2.lat));
  for (var n2 = 0; n2 < this.lG.length; n2++)
    if (e2.lat >= this.lG[n2]) {
      t2 = this.fP[n2];
      break;
    }
  return r2 = this.gK(r2, t2), r2 = new V2(r2.lng.toFixed(6), r2.lat.toFixed(6));
}, Eb: function(r2) {
  if (r2 === Or || r2 === nt2 || 180 < r2.lng || -180 > r2.lng || 90 < r2.lat || -90 > r2.lat)
    return new V2(0, 0);
  var e2, t2;
  r2.lng = this.JD(r2.lng, -180, 180), r2.lat = this.ND(r2.lat, -74, 74), e2 = new V2(r2.lng, r2.lat);
  for (var n2 = 0; n2 < this.Au.length; n2++)
    if (e2.lat >= this.Au[n2]) {
      t2 = this.iG[n2];
      break;
    }
  if (!t2) {
    for (n2 = 0; n2 < this.Au.length; n2++)
      if (e2.lat <= -this.Au[n2]) {
        t2 = this.iG[n2];
        break;
      }
  }
  return r2 = this.gK(r2, t2), r2 = new V2(r2.lng.toFixed(2), r2.lat.toFixed(2));
}, gK: function(r2, e2) {
  if (r2 && e2) {
    var t2 = e2[0] + e2[1] * Math.abs(r2.lng), n2 = Math.abs(r2.lat) / e2[9], n2 = e2[2] + e2[3] * n2 + e2[4] * n2 * n2 + e2[5] * n2 * n2 * n2 + e2[6] * n2 * n2 * n2 * n2 + e2[7] * n2 * n2 * n2 * n2 * n2 + e2[8] * n2 * n2 * n2 * n2 * n2 * n2, t2 = t2 * (0 > r2.lng ? -1 : 1), n2 = n2 * (0 > r2.lat ? -1 : 1);
    return new V2(t2, n2);
  }
}, Pe: function(r2, e2, t2, n2) {
  return this.$O * Math.acos(Math.sin(t2) * Math.sin(n2) + Math.cos(t2) * Math.cos(n2) * Math.cos(e2 - r2));
}, Tk: function(r2) {
  return Math.PI * r2 / 180;
}, Z3: function(r2) {
  return 180 * r2 / Math.PI;
}, ND: function(r2, e2, t2) {
  return e2 != Or && (r2 = Math.max(r2, e2)), t2 != Or && (r2 = Math.min(r2, t2)), r2;
}, JD: function(r2, e2, t2) {
  for (; r2 > t2; )
    r2 -= t2 - e2;
  for (; r2 < e2; )
    r2 += t2 - e2;
  return r2;
} }), et2(G2.prototype, { Jm: function(r2) {
  return G2.Eb(r2);
}, nh: function(r2) {
  return r2 = G2.Eb(r2), new Ar(r2.lng, r2.lat);
}, qh: function(r2) {
  return G2.Fb(r2);
}, wi: function(r2) {
  return r2 = new V2(r2.x, r2.y), G2.Fb(r2);
}, fc: function(r2, e2, t2, n2, i) {
  if (r2)
    return r2 = this.Jm(r2, i), e2 = this.Lc(e2), new Ar(Math.round((r2.lng - t2.lng) / e2 + n2.width / 2), Math.round((t2.lat - r2.lat) / e2 + n2.height / 2));
}, zb: function(r2, e2, t2, n2, i) {
  if (r2)
    return e2 = this.Lc(e2), this.qh(new V2(t2.lng + e2 * (r2.x - n2.width / 2), t2.lat - e2 * (r2.y - n2.height / 2)), i);
}, Lc: function(r2) {
  return Math.pow(2, 18 - r2);
} });
var Yr = G2.prototype;
a0(Yr, { lngLatToPoint: Yr.nh, pointToLngLat: Yr.wi });
var kr = { Point: V2, Pixel: Ar, MercatorProjection: G2 };
var s0 = class {
  constructor(e2, t2) {
    this.projection = new kr.MercatorProjection(), this.levelMax = e2, this.levelMin = t2;
  }
  _getRetain(e2) {
    return Math.pow(2, e2 - 18);
  }
  getResolution(e2, t2) {
    return Math.pow(2, 18 - t2) * Math.cos(e2);
  }
  lnglatToPoint(e2, t2) {
    let n2 = new kr.Point(e2, t2), i = this.projection.lngLatToPoint(n2);
    return { pointX: i.x, pointY: i.y };
  }
  pointToLnglat(e2, t2) {
    let n2 = new kr.Pixel(e2, t2), i = this.projection.pointToLngLat(n2);
    return { lng: i.lng, lat: i.lat };
  }
  _lngToTileX(e2, t2) {
    let n2 = this.lnglatToPoint(e2, 0);
    return Math.floor(n2.pointX * this._getRetain(t2) / 256);
  }
  _latToTileY(e2, t2) {
    let n2 = this.lnglatToPoint(0, e2);
    return Math.floor(n2.pointY * this._getRetain(t2) / 256);
  }
  lnglatToTile(e2, t2, n2) {
    let i = this._lngToTileX(e2, n2), a2 = this._latToTileY(t2, n2);
    return [i, a2];
  }
  _lngToPixelX(e2, t2) {
    let n2 = this._lngToTileX(e2, t2), i = this.lnglatToPoint(e2, 0);
    return Math.floor(i.pointX * this._getRetain(t2) - n2 * 256);
  }
  _latToPixelY(e2, t2) {
    let n2 = this._latToTileY(e2, t2), i = this.lnglatToPoint(0, e2);
    return Math.floor(i.pointY * this._getRetain(t2) - n2 * 256);
  }
  lnglatToPixel(e2, t2, n2) {
    let i = this._lngToPixelX(e2, n2), a2 = this._latToPixelY(t2, n2);
    return { pixelX: i, pixelY: a2 };
  }
  _pixelXToLng(e2, t2, n2) {
    let i = (t2 * 256 + e2) / this._getRetain(n2);
    return this.pointToLnglat(i, 0).lng;
  }
  _pixelYToLat(e2, t2, n2) {
    let i = (t2 * 256 + e2) / this._getRetain(n2);
    return this.pointToLnglat(0, i).lat;
  }
  pixelToLnglat(e2, t2, n2, i, a2) {
    let s = (n2 * 256 + e2) / this._getRetain(a2), h = (i * 256 + t2) / this._getRetain(a2), c = this.pointToLnglat(s, h);
    return [c.lng, c.lat];
  }
};
Object.freeze = function(r2) {
  return r2;
};
var h0 = /\{ *([\w_-]+) *\}/g;
function c0(r2, e2) {
  return r2.replace(h0, function(t2, n2) {
    let i = e2[n2];
    if (i === void 0)
      throw new Error(`No value provided for variable ${t2}`);
    return typeof i == "function" && (i = i(e2)), i;
  });
}
var l0 = class {
  constructor(e2, t2) {
    if (this.program = null, this.showTiles = [], this.isReadRender = false, this.isLayerShow = false, this.tileCache = [], this.gridCache = {}, this.transformBaidu = new s0(), this.maskCache = [], this._projectionMatrix = new ar(), this._viewMatrix4 = new ar(), this._mvpMatrix4 = new ar(), this.isOrtho = false, !e2)
      throw new Error("请传入地图实例");
    this.validate(t2), this.map = e2, this.center = e2.getCenter().toArray(), this.options = Object.assign(this.getDefaultGlLayerOptions(), t2), this.customCoords = e2.customCoords, this.customCoords.lngLatsToCoords([this.center]), this.layer = new AMap.GLCustomLayer({ zooms: this.options.zooms, opacity: this.options.opacity, visible: this.options.visible, zIndex: this.options.zIndex, init: (n2) => {
      this.gl = n2;
      const i = `
                    uniform mat4 u_ProjectionMatrix;
                    uniform mat4 u_ViewMatrix4;
                    uniform mat4 u_MvpMatrix4;
                    uniform bool u_isOrtho;
                    attribute vec2 a_pos;
                    attribute vec2 a_TextCoord;
                    varying vec2 v_TextCoord;
                    void main() {
                       if(u_isOrtho){
                         gl_Position = u_MvpMatrix4 * vec4(a_pos,0.0, 1.0);
                       }else{
                         gl_Position = u_ProjectionMatrix * u_ViewMatrix4 * vec4(a_pos,0.0, 1.0);
                       }
                       v_TextCoord = a_TextCoord;
                    }`, a2 = `
                    precision mediump float;
                    uniform sampler2D u_Sampler;
                    uniform bool u_isFirst;
                    varying vec2 v_TextCoord;
                    void main() {
                       if(u_isFirst){
                         gl_FragColor = vec4(1.0, 0.0, 0.0, 0.0);
                       }else{
                         gl_FragColor = texture2D(u_Sampler, v_TextCoord);
                       }
                    }`, s = n2.createShader(n2.VERTEX_SHADER);
      n2.shaderSource(s, i), n2.compileShader(s);
      const h = n2.createShader(n2.FRAGMENT_SHADER);
      n2.shaderSource(h, a2), n2.compileShader(h), this.program = n2.createProgram(), n2.attachShader(this.program, s), n2.attachShader(this.program, h), n2.linkProgram(this.program), this.a_Pos = n2.getAttribLocation(this.program, "a_pos"), this.a_TextCoord = n2.getAttribLocation(this.program, "a_TextCoord");
      const c = "uniform mat4 u_ProjectionMatrix;uniform mat4 u_ViewMatrix4;uniform mat4 u_MvpMatrix4;uniform bool u_isOrtho;attribute vec2 a_pos;void main() {   if(u_isOrtho){     gl_Position = u_MvpMatrix4 * vec4(a_pos,0.0, 1.0);   }else{     gl_Position = u_ProjectionMatrix * u_ViewMatrix4 * vec4(a_pos,0.0, 1.0);   }}", l = "void main() {    gl_FragColor = vec4(0.0, 1.0, 0.0, 0.0);}", f = n2.createShader(n2.VERTEX_SHADER);
      n2.shaderSource(f, c), n2.compileShader(f);
      const v2 = n2.createShader(n2.FRAGMENT_SHADER);
      n2.shaderSource(v2, l), n2.compileShader(v2), this.maskProgram = n2.createProgram(), n2.attachShader(this.maskProgram, f), n2.attachShader(this.maskProgram, v2), n2.linkProgram(this.maskProgram), this.mask_Pos = n2.getAttribLocation(this.maskProgram, "a_pos"), this.options.visible && (this.isLayerShow = true), this.mapCallback = () => {
        this.isLayerShow && this.update();
      }, e2.on("dragging", this.mapCallback), e2.on("moveend", this.mapCallback), e2.on("zoomchange", this.mapCallback), e2.on("rotatechange", this.mapCallback), this._createMask(this.options.mask), this.update();
    }, render: (n2) => {
      if (!this.isReadRender)
        return;
      const i = this.options.zooms;
      if (!(this.map.getZoom() < i[0] || this.map.getZoom() > i[1])) {
        if (this.customCoords.setCenter(this.center), e2.getView().type === "3D") {
          this.isOrtho = false;
          const { near: a2, far: s, fov: h, up: c, lookAt: l, position: f } = this.customCoords.getCameraParams();
          this._viewMatrix4.lookAt({ eye: f, center: l, up: c }).translate([0, 0, this.options.altitude]), this._projectionMatrix.perspective({ fovy: h * Math.PI / 180, far: s, near: a2, aspect: n2.drawingBufferWidth / n2.drawingBufferHeight });
        } else {
          this.isOrtho = true;
          const a2 = this.customCoords.getMVPMatrix();
          this._mvpMatrix4.copy(a2);
        }
        this.reset(n2), this.maskCache.length > 0 ? (n2.clearStencil(0), n2.clear(n2.STENCIL_BUFFER_BIT), n2.enable(n2.STENCIL_TEST), n2.stencilFunc(n2.ALWAYS, 1, 255), n2.stencilOp(n2.KEEP, n2.KEEP, n2.REPLACE), this._renderMask(n2), n2.stencilFunc(n2.EQUAL, 1, 255), n2.stencilOp(n2.KEEP, n2.KEEP, n2.KEEP), n2.disable(n2.DEPTH_TEST), this._renderTile(n2), n2.enable(n2.DEPTH_TEST), n2.disable(n2.STENCIL_TEST)) : this._renderTile(n2), this.reset(n2);
      }
    } }), e2.add(this.layer);
  }
  _renderMask(e2) {
    if (this.maskCache.length) {
      this.customCoords.setCenter(this.center), e2.useProgram(this.maskProgram), this.setVertex(e2, this.maskProgram);
      for (const t2 of this.maskCache)
        e2.bindBuffer(e2.ARRAY_BUFFER, t2.vertexBuffer), e2.bindBuffer(e2.ELEMENT_ARRAY_BUFFER, t2.indexBuffer), e2.vertexAttribPointer(this.mask_Pos, 2, e2.FLOAT, false, 0, 0), e2.enableVertexAttribArray(this.mask_Pos), e2.drawElements(e2.TRIANGLES, t2.deviationLength, e2.UNSIGNED_INT, 0);
    }
  }
  _renderTile(e2) {
    var t2, n2, i, a2, s, h;
    this.customCoords.setCenter(this.center), e2.useProgram(this.program), this.setVertex(e2, this.program);
    let c = 0;
    for (const l of this.showTiles) {
      if (!l.isLoad || l.imageError)
        continue;
      e2.bindTexture(e2.TEXTURE_2D, l.texture), e2.activeTexture(e2.TEXTURE0), e2.texParameteri(e2.TEXTURE_2D, e2.TEXTURE_MIN_FILTER, e2.LINEAR), e2.texParameteri(e2.TEXTURE_2D, e2.TEXTURE_WRAP_S, e2.CLAMP_TO_EDGE), e2.texParameteri(e2.TEXTURE_2D, e2.TEXTURE_WRAP_T, e2.MIRRORED_REPEAT);
      const f = e2.getUniformLocation(this.program, "u_Sampler");
      e2.uniform1i(f, 0), e2.uniform1f(e2.getUniformLocation(this.program, "u_isFirst"), c === 0), e2.bindBuffer(e2.ARRAY_BUFFER, l.buffer), e2.vertexAttribPointer(this.a_Pos, (t2 = l.PosParam) === null || t2 === void 0 ? void 0 : t2.size, e2.FLOAT, false, (n2 = l.PosParam) === null || n2 === void 0 ? void 0 : n2.stride, (i = l.PosParam) === null || i === void 0 ? void 0 : i.offset), e2.vertexAttribPointer(this.a_TextCoord, (a2 = l.TextCoordParam) === null || a2 === void 0 ? void 0 : a2.size, e2.FLOAT, false, (s = l.TextCoordParam) === null || s === void 0 ? void 0 : s.stride, (h = l.TextCoordParam) === null || h === void 0 ? void 0 : h.offset), e2.enableVertexAttribArray(this.a_Pos), e2.enableVertexAttribArray(this.a_TextCoord), e2.enable(e2.BLEND), e2.blendFunc(e2.SRC_ALPHA, e2.ONE_MINUS_SRC_ALPHA), e2.drawArrays(e2.TRIANGLE_STRIP, 0, 4), c++;
    }
  }
  reset(e2) {
    e2.disable(e2.BLEND), e2.disable(e2.CULL_FACE), e2.disable(e2.DEPTH_TEST), e2.disable(e2.POLYGON_OFFSET_FILL), e2.disable(e2.SCISSOR_TEST), e2.disable(e2.STENCIL_TEST), e2.disable(e2.SAMPLE_ALPHA_TO_COVERAGE), e2.blendEquation(e2.FUNC_ADD), e2.blendFunc(e2.ONE, e2.ZERO), e2.blendFuncSeparate(e2.ONE, e2.ZERO, e2.ONE, e2.ZERO), e2.colorMask(true, true, true, true), e2.clearColor(0, 0, 0, 0), e2.depthMask(true), e2.depthFunc(e2.LESS), e2.clearDepth(1), e2.stencilMask(4294967295), e2.stencilFunc(e2.ALWAYS, 0, 4294967295), e2.stencilOp(e2.KEEP, e2.KEEP, e2.KEEP), e2.clearStencil(0), e2.cullFace(e2.BACK), e2.frontFace(e2.CCW), e2.polygonOffset(0, 0), e2.activeTexture(e2.TEXTURE0), e2.bindFramebuffer(e2.FRAMEBUFFER, null), e2.useProgram(null), e2.lineWidth(1), e2.scissor(0, 0, e2.canvas.width, e2.canvas.height), e2.viewport(0, 0, e2.canvas.width, e2.canvas.height);
  }
  validate(e2) {
    if (!e2.url)
      throw new Error("请传入url");
    if (e2.url.includes("{s}") && (!e2.subdomains || e2.subdomains.length === 0))
      throw new Error("请传入subdomains");
  }
  getDefaultGlLayerOptions() {
    return { url: "", zooms: [2, 18], opacity: 1, visible: true, zIndex: 120, proj: "gcj02", tileType: "xyz", debug: false, cacheSize: 512, tileMaxZoom: 18, altitude: 0 };
  }
  _createMask(e2) {
    if (!e2 || e2.length === 0) {
      this.maskCache = [];
      return;
    }
    const t2 = this.getMaskDeep(e2);
    if (t2 < 2 || t2 > 4) {
      console.warn("mask数据格式不正确");
      return;
    }
    t2 === 2 ? e2 = [[e2]] : t2 === 3 && (e2 = [e2]);
    for (let n2 of e2) {
      n2 = this._convertMaskLnglatToCoords(n2);
      const i = hr.exports.flatten(n2), a2 = hr.exports(i.vertices, i.holes, i.dimensions), s = this.gl, h = new Float32Array(i.vertices), c = s.createBuffer();
      s.bindBuffer(s.ARRAY_BUFFER, c), s.bufferData(s.ARRAY_BUFFER, h, s.STATIC_DRAW), s.bindBuffer(s.ARRAY_BUFFER, null);
      const l = s.createBuffer();
      s.bindBuffer(s.ELEMENT_ARRAY_BUFFER, l), s.bufferData(s.ELEMENT_ARRAY_BUFFER, new Uint32Array(a2), s.STATIC_DRAW), s.bindBuffer(s.ELEMENT_ARRAY_BUFFER, null), this.maskCache.push({ FSIZE: h.BYTES_PER_ELEMENT, vertexBuffer: c, indexBuffer: l, deviationLength: a2.length });
    }
  }
  getMaskDeep(e2, t2 = 1) {
    return e2.length ? typeof e2[0] == "number" ? t2 : this.getMaskDeep(e2[0], t2 + 1) : -1;
  }
  _convertMaskLnglatToCoords(e2) {
    return !e2 || e2.length === 0 ? e2 : typeof e2[0] == "number" ? this._convertLnglatToCoords([e2[0], e2[1]]) : e2.map((t2) => this._convertMaskLnglatToCoords(t2));
  }
  _convertLnglatToCoords(e2) {
    return this.customCoords.setCenter(this.center), this.customCoords.lngLatsToCoords([e2])[0];
  }
  update() {
    if (!this.gl)
      return;
    this.isReadRender = false;
    const e2 = this.gl, t2 = this.map, n2 = t2.getCenter();
    let i = Math.ceil(t2.getZoom());
    i > this.options.tileMaxZoom && (i = this.options.tileMaxZoom);
    const a2 = t2.getBounds();
    let s, h, c, l;
    if (this.options.tileType === "xyz" ? (c = a2.getNorthWest().toArray(), l = a2.getSouthEast().toArray()) : (c = a2.getSouthWest().toArray(), l = a2.getNorthEast().toArray()), this.options.proj === "wgs84") {
      const o = Qe2(...c), M = Qe2(...l);
      s = sr2(o.lng, o.lat, i), h = sr2(M.lng, M.lat, i);
    } else if (this.options.proj === "bd09") {
      const o = Je2(...c), M = Je2(...l);
      s = this.transformBaidu.lnglatToTile(o.lng, o.lat, i), h = this.transformBaidu.lnglatToTile(M.lng, M.lat, i);
    } else
      s = sr2(c[0], c[1], i), h = sr2(l[0], l[1], i);
    const f = [];
    for (let o = s[0]; o <= h[0]; o++)
      for (let M = s[1]; M <= h[1]; M++) {
        const p2 = { x: o, y: M, z: i };
        f.push(p2), this.addGridCache(p2, 0, 0), o === h[0] && this.addGridCache(p2, 1, 0), M === h[1] && this.addGridCache(p2, 0, 1), o === h[0] && M === h[1] && this.addGridCache(p2, 1, 1);
      }
    let v2;
    this.options.tileType === "xyz" ? v2 = sr2(n2.getLng(), n2.getLat(), i) : v2 = this.transformBaidu.lnglatToTile(n2.getLng(), n2.getLat(), i), f.sort((o, M) => this.tileDistance(o, v2) - this.tileDistance(M, v2)), this._cancelOutViewImage(f), this._clearShowTile();
    for (const o of f) {
      const M = this.createTileKey(o), p2 = this.getTileCache(M);
      if (p2) {
        if (p2.imageError)
          continue;
        !p2.isLoad && p2.image && p2.imageCanceled && (p2.image.src = p2.url, p2.imageCanceled = false), this.showTiles.push(p2);
      } else {
        const x = this.createTile(e2, o);
        this.showTiles.push(x), this.pushTileCache(x);
      }
    }
    this.showTiles.length > 0 && this.showTiles.unshift(this.showTiles[0]), this.isReadRender = true;
  }
  _cancelOutViewImage(e2) {
    this.tileCache.forEach((t2) => {
      e2.findIndex((n2) => this.createTileKey(n2) === t2.xyzKey) === -1 && !t2.isLoad && t2.image && (t2.image.src = "", t2.imageCanceled = true);
    });
  }
  getTileCache(e2) {
    return this.tileCache.find((t2) => t2.xyzKey === e2);
  }
  pushTileCache(e2) {
    const t2 = this.options.cacheSize;
    t2 > 0 && this.tileCache.length >= t2 && (this.showTiles.findIndex((n2) => n2.xyzKey === this.tileCache[0].xyzKey) < 0 && this._destroyTile(this.tileCache[0]), this.tileCache.splice(0, 1)), this.tileCache.push(e2);
  }
  addGridCache(e2, t2, n2) {
    const i = this.createTileKey(e2.x + t2, e2.y + n2, e2.z);
    if (!this.gridCache[i])
      if (this.options.proj === "wgs84") {
        const a2 = t0(...He2(e2.x + t2, e2.y + n2, e2.z)), s = this._convertLnglatToCoords([a2.lng, a2.lat]);
        this.gridCache[i] = { lng: s[0], lat: s[1] };
      } else if (this.options.tileType === "bd09") {
        const a2 = n0(...this.transformBaidu.pixelToLnglat(0, 0, e2.x + t2, e2.y + n2, e2.z)), s = this._convertLnglatToCoords([a2.lng, a2.lat]);
        this.gridCache[i] = { lng: s[0], lat: s[1] };
      } else {
        const a2 = this._convertLnglatToCoords(He2(e2.x + t2, e2.y + n2, e2.z));
        this.gridCache[i] = { lng: a2[0], lat: a2[1] };
      }
  }
  tileDistance(e2, t2) {
    return Math.sqrt(Math.pow(e2.x - t2[0], 2) + Math.pow(e2.y - t2[1], 2));
  }
  createTileKey(e2, t2, n2) {
    return e2 instanceof Object ? `${e2.z}/${e2.x}/${e2.y}` : `${n2}/${e2}/${t2}`;
  }
  deepFormatTileNumber(e2, t2) {
    return e2 >= 0 && e2 < t2 ? e2 : (e2 > 0 && e2 >= t2 ? e2 = e2 - t2 : e2 < 0 && (e2 = e2 + t2), this.deepFormatTileNumber(e2, t2));
  }
  createTile(e2, t2) {
    let n2 = Math.ceil(this.map.getZoom());
    n2 > this.options.tileMaxZoom && (n2 = this.options.tileMaxZoom);
    const i = Math.pow(2, n2);
    let a2 = t2.x, s = t2.y;
    a2 = this.deepFormatTileNumber(a2, i), s = this.deepFormatTileNumber(s, i);
    const h = { x: a2, y: s, z: t2.z };
    this.options.subdomains && (h.s = this.options.subdomains[Math.abs(t2.x + t2.y) % this.options.subdomains.length]);
    const c = c0(this.options.url, h), l = { xyz: t2, xyzKey: this.createTileKey(t2), isLoad: false, url: c, imageCanceled: false };
    let f, v2, o, M;
    this.options.tileType === "xyz" ? (f = this.gridCache[this.createTileKey(t2)], v2 = this.gridCache[this.createTileKey(t2.x + 1, t2.y, t2.z)], o = this.gridCache[this.createTileKey(t2.x, t2.y + 1, t2.z)], M = this.gridCache[this.createTileKey(t2.x + 1, t2.y + 1, t2.z)]) : (f = this.gridCache[this.createTileKey(t2.x, t2.y + 1, t2.z)], v2 = this.gridCache[this.createTileKey(t2.x + 1, t2.y + 1, t2.z)], o = this.gridCache[this.createTileKey(t2)], M = this.gridCache[this.createTileKey(t2.x + 1, t2.y, t2.z)]);
    const p2 = new Float32Array([f.lng, f.lat, 0, 1, o.lng, o.lat, 0, 0, v2.lng, v2.lat, 1, 1, M.lng, M.lat, 1, 0]), x = p2.BYTES_PER_ELEMENT, y = e2.createBuffer();
    e2.bindBuffer(e2.ARRAY_BUFFER, y), e2.bufferData(e2.ARRAY_BUFFER, p2, e2.STATIC_DRAW), l.buffer = y, l.PosParam = { size: 2, stride: x * 4, offset: 0 }, l.TextCoordParam = { size: 2, stride: x * 4, offset: x * 2 };
    const d2 = new Image(256, 256);
    return d2.onload = () => {
      if (l.texture = e2.createTexture(), e2.bindTexture(e2.TEXTURE_2D, l.texture), e2.pixelStorei(e2.UNPACK_FLIP_Y_WEBGL, 1), this.options.debug) {
        const R = document.createElement("canvas");
        R.width = 256, R.height = 256;
        const m2 = R.getContext("2d");
        m2.drawImage(d2, 0, 0), m2.font = "25px Verdana", m2.fillStyle = "#ff0000", m2.strokeStyle = "#FF0000", m2.strokeRect(0, 0, 256, 256), m2.fillText(`(${[t2.x, t2.y, t2.z].join(",")})`, 10, 30), e2.texImage2D(e2.TEXTURE_2D, 0, e2.RGBA, e2.RGBA, e2.UNSIGNED_BYTE, R);
      } else
        e2.texImage2D(e2.TEXTURE_2D, 0, e2.RGBA, e2.RGBA, e2.UNSIGNED_BYTE, d2);
      l.isLoad = true, this.showTiles.findIndex((R) => R === l) >= 0 && this.requestRender();
    }, d2.onerror = () => {
      l.imageCanceled || (l.imageError = true);
    }, d2.crossOrigin = "anonymous", d2.src = c, l.image = d2, l;
  }
  setVertex(e2, t2) {
    e2.uniformMatrix4fv(e2.getUniformLocation(t2, "u_ProjectionMatrix"), false, this._projectionMatrix.toArray()), e2.uniformMatrix4fv(e2.getUniformLocation(t2, "u_ViewMatrix4"), false, this._viewMatrix4.toArray()), e2.uniformMatrix4fv(e2.getUniformLocation(t2, "u_MvpMatrix4"), false, this._mvpMatrix4.toArray()), e2.uniform1f(e2.getUniformLocation(t2, "u_isOrtho"), this.isOrtho);
  }
  requestRender() {
    this.map && (this.map.getContext().setDirty(), this.map.setNeedUpdate(true));
  }
  show() {
    this.isLayerShow = true, this.update(), this.layer.show();
  }
  hide() {
    this.isLayerShow = false, this.layer.hide();
  }
  getzIndex() {
    return this.layer.getzIndex();
  }
  setzIndex(e2) {
    this.options.zIndex = e2, this.layer.setzIndex(e2);
  }
  getOpacity() {
    return this.layer.getOpacity();
  }
  setOpacity(e2) {
    this.options.opacity = e2, this.layer.setOpacity(e2);
  }
  getZooms() {
    return this.layer.getZooms();
  }
  setZooms(e2) {
    this.options.zooms = e2, this.layer.setZooms(e2);
  }
  setMask(e2) {
    this._destroyMaskCache(), this._createMask(e2), this.options.mask = e2, this.requestRender();
  }
  getMask() {
    return this.options.mask;
  }
  getMap() {
    return this.map;
  }
  _destroyMaskCache() {
    this.maskCache.forEach((e2) => {
      this.gl.deleteBuffer(e2.vertexBuffer), this.gl.deleteBuffer(e2.indexBuffer);
    }), this.maskCache = [];
  }
  _destroyTile(e2) {
    e2.buffer && this.gl.deleteBuffer(e2.buffer), e2.texture && this.gl.deleteTexture(e2.texture);
  }
  _clearAllCacheTile() {
    this.tileCache.forEach((e2) => {
      this._destroyTile(e2);
    }), this.tileCache = [];
  }
  _clearShowTile() {
    this.showTiles.forEach((e2) => {
      this.tileCache.findIndex((t2) => t2.xyzKey === e2.xyzKey) < 0 && this._destroyTile(e2);
    }), this.showTiles = [];
  }
  destroy() {
    this.isLayerShow = false, this.map.remove(this.layer), this.map.off("dragging", this.mapCallback), this.map.off("moveend", this.mapCallback), this.map.off("zoomchange", this.mapCallback), this.map.off("rotatechange", this.mapCallback), this._destroyMaskCache(), this._clearShowTile(), this._clearAllCacheTile(), this.gridCache = {}, this.transformBaidu = null, this.mapCallback = null, this.gl.deleteProgram(this.program), this.gl.deleteProgram(this.maskProgram), this.program = null, this.maskProgram = null, this.options = void 0, this.customCoords = void 0, this.center = void 0, this.layer = null, this.gl = null, this.map = null;
  }
};

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/CustomXyz/props.mjs
var propsTypes11 = buildProps({
  url: {
    required: true,
    type: String
  },
  // 瓦片地址，支持 {s} {x} {y} {z}，示例：http://webst0{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}
  subdomains: {
    type: Array
  },
  // 子域名数组，当url中设置{s}后，该属性必填
  tileType: {
    type: String,
    default: "xyz",
    validator: (value) => {
      return ["xyz", "bd09"].indexOf(value) !== -1;
    }
  },
  // 瓦片分割类型，默认是xyz，xyz代表瓦片是编号是从左上角开始，百度瓦片是由中间开始，所以需要区分普通瓦片还是百度
  proj: {
    type: String,
    default: "gcj02",
    validator: (value) => {
      return ["wgs84", "gcj02", "bd09"].indexOf(value) !== -1;
    }
  },
  // 瓦片使用的坐标系，默认是gcj02
  zooms: {
    type: Object
  },
  // 图层缩放等级范围，默认 [2, 18]
  opacity: {
    type: Number
  },
  // 图层透明度，默认为 1
  zIndex: {
    type: Number,
    default: 120
  },
  mask: {
    type: Array
  },
  cacheSize: {
    type: Number,
    default: 512
  },
  debug: {
    type: Boolean,
    default: false
  },
  tileMaxZoom: {
    type: Number
  },
  altitude: {
    type: Number
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/CustomXyz/CustomXyz.vue2.mjs
var script47 = defineComponent({
  ...{
    name: "ElAmapLayerCustomXyz",
    inheritAttrs: false
  },
  __name: "CustomXyz",
  props: propsTypes11,
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new l0(parentComponent, options);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent) {
          $amapComponent.destroy();
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/CustomXyz/CustomXyz.vue.mjs
script47.__file = "src/vue-amap/packages/layer/data/CustomXyz/CustomXyz.vue";

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/packages/layer/data/CustomXyz/index.mjs
script47.install = (app) => {
  app.component(script47.name, script47);
  return app;
};
var ElAmapLayerCustomXyz = script47;

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/component.mjs
var Components = [
  ElAmap,
  ElAmapControlControlBar,
  ElAmapControlHawkEye,
  ElAmapControlMapType,
  ElAmapControlScale,
  ElAmapControlToolBar,
  ElAmapSearchBox,
  ElAmapInfoWindow,
  ElAmapLayerCanvas,
  ElAmapLayerCustom,
  ElAmapLayerFlexible,
  ElAmapLayerGlCustom,
  ElAmapLayerHeatMap,
  ElAmapLayerImage,
  ElAmapLayerLabels,
  ElAmapLayerVector,
  ElAmapLayerBuildings,
  ElAmapLayerDefault,
  ElAmapLayerDistrict,
  ElAmapLayerIndoorMap,
  ElAmapLayerRoadNet,
  ElAmapLayerSatellite,
  ElAmapLayerTile,
  ElAmapLayerTraffic,
  ElAmapLayerMapboxVectorTile,
  ElAmapLayerWms,
  ElAmapLayerWmts,
  ElAmapElasticMarker,
  ElAmapLabelMarker,
  ElAmapMarker,
  ElAmapMarkerCluster,
  ElAmapMassMarks,
  ElAmapText,
  ElAmapBezierCurve,
  ElAmapCircle,
  ElAmapEllipse,
  ElAmapGeojson,
  ElAmapPolygon,
  ElAmapPolyline,
  ElAmapRectangle,
  ElAmapLayerTiles3d,
  ElAmapControlGeolocation,
  ElAmapCircleMarker,
  ElAmapLayerVideo,
  ElAmapMouseTool,
  ElAmapLayerDistrictCluster,
  ElAmapLayerCustomXyz
];

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/defaults.mjs
var installer = makeInstaller([...Components]);

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/hooks/useCitySearch.mjs
function useCitySearch() {
  return new Promise((resolve) => {
    AMap.plugin("AMap.CitySearch", function() {
      const citySearch = new AMap.CitySearch();
      const getLocalCity = () => {
        return new Promise((resolve2, reject) => {
          citySearch.getLocalCity(function(status, result) {
            if (status === "complete" && result.info === "OK") {
              resolve2(result);
            } else {
              reject({
                status,
                result
              });
            }
          });
        });
      };
      resolve({
        getLocalCity
      });
    });
  });
}

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/hooks/useWeather.mjs
function useWeather() {
  return new Promise((resolve) => {
    AMap.plugin("AMap.Weather", function() {
      const weather = new AMap.Weather();
      const getLive = (city) => {
        return new Promise((resolve2, reject) => {
          weather.getLive(city, function(err, result) {
            if (!err && result.info === "OK") {
              resolve2(result);
            } else {
              reject({
                result
              });
            }
          });
        });
      };
      const getForecast = (city) => {
        return new Promise((resolve2, reject) => {
          weather.getForecast(city, function(err, result) {
            if (!err && result.info === "OK") {
              resolve2(result);
            } else {
              reject({
                result
              });
            }
          });
        });
      };
      resolve({
        getLive,
        getForecast
      });
    });
  });
}

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/hooks/useGeolocation.mjs
function useGeolocation(options) {
  return new Promise((resolve) => {
    AMap.plugin("AMap.Geolocation", function() {
      const geolocation = new AMap.Geolocation(options);
      const getCurrentPosition = () => {
        return new Promise((resolve2, reject) => {
          geolocation.getCurrentPosition(function(status, result) {
            if (status === "complete" && result.info === "SUCCESS") {
              resolve2(result);
            } else {
              reject({
                status,
                result
              });
            }
          });
        });
      };
      const getCityInfo = () => {
        return new Promise((resolve2, reject) => {
          geolocation.getCityInfo(function(status, result) {
            if (status === "complete" && result.info === "SUCCESS") {
              resolve2(result);
            } else {
              reject({
                status,
                result
              });
            }
          });
        });
      };
      resolve({
        getCurrentPosition,
        getCityInfo
      });
    });
  });
}

// node_modules/.pnpm/@vuemap+vue-amap@2.1.16_vue@3.5.18_typescript@5.8.3_/node_modules/@vuemap/vue-amap/es/index.mjs
var install = installer.install;

export {
  makeInstaller,
  guid,
  isMapInstance,
  isOverlayGroupInstance,
  isIndoorMapInstance,
  isLabelsLayerInstance,
  isVectorLayerInstance,
  convertEventToLowerCase,
  eventReg,
  loadScript,
  convertLnglat,
  upperCamelCase,
  bindInstanceEvent,
  removeInstanceEvent,
  toPixel,
  toSize,
  pixelTo,
  toLngLat,
  lngLatTo,
  toBounds,
  lonLatToTileNumbers,
  tileNumbersToLonLat,
  bd09_To_gps84,
  gps84_To_bd09,
  gps84_To_gcj02,
  gcj02_To_gps84,
  gcj02_To_bd09,
  bd09_To_gcj02,
  commonProps,
  buildProps,
  registerComponent,
  provideKey,
  useRegister,
  resetJsApi,
  lazyAMapApiLoaderInstance,
  initAMapApiLoader,
  ElAmap,
  ElAmapControlControlBar,
  ElAmapControlHawkEye,
  ElAmapControlMapType,
  ElAmapControlScale,
  ElAmapControlToolBar,
  ElAmapSearchBox,
  ElAmapInfoWindow,
  ElAmapLayerCanvas,
  ElAmapLayerCustom,
  ElAmapLayerFlexible,
  ElAmapLayerGlCustom,
  ElAmapLayerHeatMap,
  ElAmapLayerImage,
  ElAmapLayerLabels,
  ElAmapLayerVector,
  ElAmapLayerBuildings,
  ElAmapLayerDefault,
  ElAmapLayerDistrict,
  ElAmapLayerIndoorMap,
  ElAmapLayerRoadNet,
  ElAmapLayerSatellite,
  ElAmapLayerTile,
  ElAmapLayerTraffic,
  ElAmapLayerMapboxVectorTile,
  ElAmapLayerWms,
  ElAmapLayerWmts,
  ElAmapElasticMarker,
  ElAmapLabelMarker,
  ElAmapMarker,
  ElAmapMarkerCluster,
  ElAmapMassMarks,
  ElAmapText,
  ElAmapBezierCurve,
  ElAmapCircle,
  ElAmapEllipse,
  ElAmapGeojson,
  ElAmapPolygon,
  ElAmapPolyline,
  ElAmapRectangle,
  ElAmapLayerTiles3d,
  ElAmapControlGeolocation,
  ElAmapCircleMarker,
  ElAmapLayerVideo,
  ElAmapMouseTool,
  ElAmapLayerDistrictCluster,
  ElAmapLayerCustomXyz,
  installer,
  useCitySearch,
  useWeather,
  useGeolocation,
  install
};
/*! Bundled license information:

@vuemap/district-cluster/dist/index.mjs:
  (**
  * splaytree v3.1.1
  * Fast Splay tree for Node and browser
  *
  * <AUTHOR> Milevski <<EMAIL>>
  * @license MIT
  * @preserve
  *)
  (*! *****************************************************************************
  Copyright (c) Microsoft Corporation. All rights reserved.
  Licensed under the Apache License, Version 2.0 (the "License"); you may not use
  this file except in compliance with the License. You may obtain a copy of the
  License at http://www.apache.org/licenses/LICENSE-2.0
  
  THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
  WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
  MERCHANTABLITY OR NON-INFRINGEMENT.
  
  See the Apache Version 2.0 License for specific language governing permissions
  and limitations under the License.
  ***************************************************************************** *)
*/
//# sourceMappingURL=chunk-EPFRRRX4.js.map
