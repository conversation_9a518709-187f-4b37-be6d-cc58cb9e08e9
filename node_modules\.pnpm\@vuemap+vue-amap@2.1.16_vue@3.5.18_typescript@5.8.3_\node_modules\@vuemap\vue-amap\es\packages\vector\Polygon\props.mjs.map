{"version": 3, "file": "props.mjs", "sources": ["../../../../../packages/vector/Polygon/props.ts"], "sourcesContent": ["import {buildProps} from \"../../../utils/buildHelper\";\r\nimport type {PropType} from \"vue\";\r\n\r\nexport const propsTypes = buildProps({\r\n  // 多边形轮廓线的节点坐标数组。 支持 单个普通多边形({Array })，单个带孔多边形({Array<Array >})，多个带孔多边形({Array<Array<Array >>})\r\n  path: {\r\n    type: Array,\r\n    required: true\r\n  }, \r\n  // 是否将覆盖物的鼠标或touch等事件冒泡到地图上\r\n  bubble: {\r\n    type: Boolean,\r\n    default: false\r\n  }, \r\n  // 指定鼠标悬停时的鼠标样式，自定义cursor，IE仅支持cur/ani/ico格式，Opera不支持自定义cursor\r\n  cursor: {\r\n    type: String\r\n  }, \r\n  // 线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC\r\n  strokeColor: {\r\n    type: String\r\n  }, \r\n  // 轮廓线透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.9\r\n  strokeOpacity: {\r\n    type: Number\r\n  }, \r\n  // 轮廓线宽度。默认 2\r\n  strokeWeight: {\r\n    type: Number\r\n  }, \r\n  // 多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5\r\n  fillColor: {\r\n    type: String\r\n  }, \r\n  // 多边形填充透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.5\r\n  fillOpacity: {\r\n    type: Number\r\n  }, \r\n  // 设置多边形是否可拖拽移动，默认为false\r\n  draggable: {\r\n    type: Boolean,\r\n    default: false\r\n  }, \r\n  extData: {\r\n    type: Object,\r\n    default: () => null\r\n  },\r\n  // 轮廓线样式，实线:solid，虚线:dashed\r\n  strokeStyle: {\r\n    type: String as PropType<'solid' | 'dashed'>,\r\n    validator: (value : string): boolean => {\r\n      // 这个值必须匹配下列字符串中的一个\r\n      return ['solid', 'dashed'].indexOf(value) !== -1;\r\n    }\r\n  }, \r\n  // 勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线\r\n  strokeDasharray: {\r\n    type: Array\r\n  },\r\n  editable: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  editOptions: {\r\n    type: Object\r\n  }\r\n});"], "names": [], "mappings": ";;AAGO,MAAM,aAAa,UAAW,CAAA;AAAA;AAAA,EAEnC,IAAM,EAAA;AAAA,IACJ,IAAM,EAAA,KAAA;AAAA,IACN,QAAU,EAAA,IAAA;AAAA,GACZ;AAAA;AAAA,EAEA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AAAA,EAEA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,aAAe,EAAA;AAAA,IACb,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,YAAc,EAAA;AAAA,IACZ,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,SAAW,EAAA;AAAA,IACT,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,SAAW,EAAA;AAAA,IACT,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA,EACA,OAAS,EAAA;AAAA,IACP,IAAM,EAAA,MAAA;AAAA,IACN,SAAS,MAAM,IAAA;AAAA,GACjB;AAAA;AAAA,EAEA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,IACN,SAAA,EAAW,CAAC,KAA4B,KAAA;AAEtC,MAAA,OAAO,CAAC,OAAS,EAAA,QAAQ,CAAE,CAAA,OAAA,CAAQ,KAAK,CAAM,KAAA,CAAA,CAAA,CAAA;AAAA,KAChD;AAAA,GACF;AAAA;AAAA,EAEA,eAAiB,EAAA;AAAA,IACf,IAAM,EAAA,KAAA;AAAA,GACR;AAAA,EACA,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA,EACA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,GACR;AACF,CAAC;;;;"}