export declare const ElCollapse: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
    readonly accordion: BooleanConstructor;
    readonly modelValue: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/collapse").CollapseModelValue & {}) | (() => import("./src/collapse").CollapseModelValue) | ((new (...args: any[]) => import("./src/collapse").CollapseModelValue & {}) | (() => import("./src/collapse").CollapseModelValue))[], unknown, unknown, () => [], boolean>;
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly accordion: BooleanConstructor;
        readonly modelValue: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/collapse").CollapseModelValue & {}) | (() => import("./src/collapse").CollapseModelValue) | ((new (...args: any[]) => import("./src/collapse").CollapseModelValue & {}) | (() => import("./src/collapse").CollapseModelValue))[], unknown, unknown, () => [], boolean>;
    }>> & {
        onChange?: ((value: import("./src/collapse").CollapseModelValue) => any) | undefined;
        "onUpdate:modelValue"?: ((value: import("./src/collapse").CollapseModelValue) => any) | undefined;
    }>>;
    emit: ((event: "update:modelValue", value: import("./src/collapse").CollapseModelValue) => void) & ((event: "change", value: import("./src/collapse").CollapseModelValue) => void);
    activeNames: import("vue").Ref<(string | number)[]>;
    setActiveNames: (_activeNames: import("./src/collapse").CollapseActiveName[]) => void;
    rootKls: import("vue").ComputedRef<string>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {
    "update:modelValue": (value: import("./src/collapse").CollapseModelValue) => "string" | "number" | "bigint" | "boolean" | "symbol" | "undefined" | "object" | "function";
    change: (value: import("./src/collapse").CollapseModelValue) => "string" | "number" | "bigint" | "boolean" | "symbol" | "undefined" | "object" | "function";
}, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly accordion: BooleanConstructor;
    readonly modelValue: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/collapse").CollapseModelValue & {}) | (() => import("./src/collapse").CollapseModelValue) | ((new (...args: any[]) => import("./src/collapse").CollapseModelValue & {}) | (() => import("./src/collapse").CollapseModelValue))[], unknown, unknown, () => [], boolean>;
}>> & {
    onChange?: ((value: import("./src/collapse").CollapseModelValue) => any) | undefined;
    "onUpdate:modelValue"?: ((value: import("./src/collapse").CollapseModelValue) => any) | undefined;
}, {
    readonly modelValue: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("./src/collapse").CollapseModelValue & {}) | (() => import("./src/collapse").CollapseModelValue) | ((new (...args: any[]) => import("./src/collapse").CollapseModelValue & {}) | (() => import("./src/collapse").CollapseModelValue))[], unknown, unknown>;
    readonly accordion: boolean;
}>> & {
    CollapseItem: import("vue").DefineComponent<{
        readonly title: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly name: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName) | ((new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName))[], unknown, unknown, () => number, boolean>;
        readonly disabled: BooleanConstructor;
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly title: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
            readonly name: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName) | ((new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName))[], unknown, unknown, () => number, boolean>;
            readonly disabled: BooleanConstructor;
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        focusing: import("vue").Ref<boolean>;
        id: import("vue").Ref<number>;
        isActive: import("vue").ComputedRef<boolean | undefined>;
        handleFocus: () => void;
        handleHeaderClick: () => void;
        handleEnterClick: () => void;
        arrowKls: import("vue").ComputedRef<string[]>;
        headKls: import("vue").ComputedRef<(string | {
            focusing: boolean | undefined;
        })[]>;
        rootKls: import("vue").ComputedRef<string[]>;
        itemWrapperKls: import("vue").ComputedRef<string>;
        itemContentKls: import("vue").ComputedRef<string>;
        scopedContentId: import("vue").ComputedRef<string>;
        scopedHeadId: import("vue").ComputedRef<string>;
        ElCollapseTransition: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{}, {
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            reset: (el: import("vue").RendererElement) => void;
            on: {
                beforeEnter(el: import("vue").RendererElement): void;
                enter(el: import("vue").RendererElement): void;
                afterEnter(el: import("vue").RendererElement): void;
                enterCancelled(el: import("vue").RendererElement): void;
                beforeLeave(el: import("vue").RendererElement): void;
                leave(el: import("vue").RendererElement): void;
                afterLeave(el: import("vue").RendererElement): void;
                leaveCancelled(el: import("vue").RendererElement): void;
            };
        }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>>;
        ElIcon: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
            readonly size: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }, {
            props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
                readonly size: {
                    readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
                readonly color: {
                    readonly type: import("vue").PropType<string>;
                    readonly required: false;
                    readonly validator: ((val: unknown) => boolean) | undefined;
                    __epPropKey: true;
                };
            }>> & {
                [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
            }>>;
            ns: {
                namespace: import("vue").ComputedRef<string>;
                b: (blockSuffix?: string) => string;
                e: (element?: string | undefined) => string;
                m: (modifier?: string | undefined) => string;
                be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
                em: (element?: string | undefined, modifier?: string | undefined) => string;
                bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
                bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
                is: {
                    (name: string, state: boolean | undefined): string;
                    (name: string): string;
                };
                cssVar: (object: Record<string, string>) => Record<string, string>;
                cssVarName: (name: string) => string;
                cssVarBlock: (object: Record<string, string>) => Record<string, string>;
                cssVarBlockName: (name: string) => string;
            };
            style: import("vue").ComputedRef<import("vue").CSSProperties>;
        }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
            readonly size: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>>, {}>> & Record<string, any>;
        ArrowRight: any;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly title: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly name: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName) | ((new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName))[], unknown, unknown, () => number, boolean>;
        readonly disabled: BooleanConstructor;
    }>>, {
        readonly title: string;
        readonly disabled: boolean;
        readonly name: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName) | ((new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName))[], unknown, unknown>;
    }>;
};
export default ElCollapse;
export declare const ElCollapseItem: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
    readonly title: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly name: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName) | ((new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName))[], unknown, unknown, () => number, boolean>;
    readonly disabled: BooleanConstructor;
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly title: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
        readonly name: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName) | ((new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName))[], unknown, unknown, () => number, boolean>;
        readonly disabled: BooleanConstructor;
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    focusing: import("vue").Ref<boolean>;
    id: import("vue").Ref<number>;
    isActive: import("vue").ComputedRef<boolean | undefined>;
    handleFocus: () => void;
    handleHeaderClick: () => void;
    handleEnterClick: () => void;
    arrowKls: import("vue").ComputedRef<string[]>;
    headKls: import("vue").ComputedRef<(string | {
        focusing: boolean | undefined;
    })[]>;
    rootKls: import("vue").ComputedRef<string[]>;
    itemWrapperKls: import("vue").ComputedRef<string>;
    itemContentKls: import("vue").ComputedRef<string>;
    scopedContentId: import("vue").ComputedRef<string>;
    scopedHeadId: import("vue").ComputedRef<string>;
    ElCollapseTransition: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{}, {
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        reset: (el: import("vue").RendererElement) => void;
        on: {
            beforeEnter(el: import("vue").RendererElement): void;
            enter(el: import("vue").RendererElement): void;
            afterEnter(el: import("vue").RendererElement): void;
            enterCancelled(el: import("vue").RendererElement): void;
            beforeLeave(el: import("vue").RendererElement): void;
            leave(el: import("vue").RendererElement): void;
            afterLeave(el: import("vue").RendererElement): void;
            leaveCancelled(el: import("vue").RendererElement): void;
        };
    }, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>>;
    ElIcon: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
        readonly size: {
            readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly color: {
            readonly type: import("vue").PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }, {
        props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
            readonly size: {
                readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
            readonly color: {
                readonly type: import("vue").PropType<string>;
                readonly required: false;
                readonly validator: ((val: unknown) => boolean) | undefined;
                __epPropKey: true;
            };
        }>> & {
            [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
        }>>;
        ns: {
            namespace: import("vue").ComputedRef<string>;
            b: (blockSuffix?: string) => string;
            e: (element?: string | undefined) => string;
            m: (modifier?: string | undefined) => string;
            be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
            em: (element?: string | undefined, modifier?: string | undefined) => string;
            bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
            bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
            is: {
                (name: string, state: boolean | undefined): string;
                (name: string): string;
            };
            cssVar: (object: Record<string, string>) => Record<string, string>;
            cssVarName: (name: string) => string;
            cssVarBlock: (object: Record<string, string>) => Record<string, string>;
            cssVarBlockName: (name: string) => string;
        };
        style: import("vue").ComputedRef<import("vue").CSSProperties>;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
        readonly size: {
            readonly type: import("vue").PropType<import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => (string | number) & {}) | (() => string | number) | ((new (...args: any[]) => (string | number) & {}) | (() => string | number))[], unknown, unknown>>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly color: {
            readonly type: import("vue").PropType<string>;
            readonly required: false;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
    }>>, {}>> & Record<string, any>;
    ArrowRight: any;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly title: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "", boolean>;
    readonly name: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName) | ((new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName))[], unknown, unknown, () => number, boolean>;
    readonly disabled: BooleanConstructor;
}>>, {
    readonly title: string;
    readonly disabled: boolean;
    readonly name: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName) | ((new (...args: any[]) => import("./src/collapse").CollapseActiveName & {}) | (() => import("./src/collapse").CollapseActiveName))[], unknown, unknown>;
}>>;
export * from './src/collapse';
export * from './src/collapse-item';
export * from './src/constants';
export type { CollapseInstance, CollapseItemInstance } from './src/instance';
