'use strict';

function useWeather() {
  return new Promise((resolve) => {
    AMap.plugin("AMap.Weather", function() {
      const weather = new AMap.Weather();
      const getLive = (city) => {
        return new Promise((resolve2, reject) => {
          weather.getLive(city, function(err, result) {
            if (!err && result.info === "OK") {
              resolve2(result);
            } else {
              reject({
                result
              });
            }
          });
        });
      };
      const getForecast = (city) => {
        return new Promise((resolve2, reject) => {
          weather.getForecast(city, function(err, result) {
            if (!err && result.info === "OK") {
              resolve2(result);
            } else {
              reject({
                result
              });
            }
          });
        });
      };
      resolve({
        getLive,
        getForecast
      });
    });
  });
}

exports.useWeather = useWeather;
//# sourceMappingURL=useWeather.js.map
