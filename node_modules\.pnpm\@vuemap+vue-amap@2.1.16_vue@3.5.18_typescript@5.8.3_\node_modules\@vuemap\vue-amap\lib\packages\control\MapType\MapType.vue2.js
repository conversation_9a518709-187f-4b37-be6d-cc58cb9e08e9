'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
require('../../../mixins/index.js');
var buildHelper = require('../../../utils/buildHelper.js');
var useRegister = require('../../../mixins/useRegister.js');

var script = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "ElAmapControlMapType",
    inheritAttrs: false
  },
  __name: "MapType",
  props: buildHelper.buildProps({
    defaultType: {
      type: Number
    },
    // 初始化默认图层类型。 取值为0：默认底图 取值为1：卫星图 默认值：0
    showTraffic: {
      type: Boolean,
      default: false
    },
    // 叠加实时交通图层 默认值：false
    showRoad: {
      type: Boolean,
      default: false
    }
    // 叠加路网图层 默认值：false
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister.useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.MapType"], () => {
          $amapComponent = new AMap.MapType(options);
          parentComponent.addControl($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeControl($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div");
    };
  }
});

exports.default = script;
//# sourceMappingURL=MapType.vue2.js.map
