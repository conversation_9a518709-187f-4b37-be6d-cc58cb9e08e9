/// <reference types="@vuemap/amap-jsapi-types" />
import Amap from './amap.vue';
import type { Plugin } from "vue";
export declare const ElAmap: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        vid: {
            type: StringConstructor;
        };
        center: {
            type: ArrayConstructor;
        };
        zoom: {
            type: NumberConstructor;
        };
        rotation: {
            type: NumberConstructor;
        };
        pitch: {
            type: NumberConstructor;
        };
        viewMode: {
            type: StringConstructor;
        };
        features: {
            type: ArrayConstructor;
        };
        layers: {
            type: ArrayConstructor;
        };
        zooms: {
            type: ArrayConstructor;
        };
        resizeEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        dragEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        zoomEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        jogEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        pitchEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        rotateEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        animateEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        keyboardEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        doubleClickZoom: {
            type: BooleanConstructor;
            default: boolean;
        };
        scrollWheel: {
            type: BooleanConstructor;
            default: boolean;
        };
        touchZoom: {
            type: BooleanConstructor;
            default: boolean;
        };
        touchZoomCenter: {
            type: NumberConstructor;
        };
        showLabel: {
            type: BooleanConstructor;
            default: boolean;
        };
        defaultCursor: {
            type: StringConstructor;
        };
        isHotspot: {
            type: BooleanConstructor;
        };
        mapStyle: {
            type: StringConstructor;
        };
        wallColor: {
            type: (StringConstructor | ArrayConstructor)[];
        };
        roofColor: {
            type: (StringConstructor | ArrayConstructor)[];
        };
        showBuildingBlock: {
            type: BooleanConstructor;
            default: boolean;
        };
        showIndoorMap: {
            type: BooleanConstructor;
            default: boolean;
        };
        skyColor: {
            type: (StringConstructor | ArrayConstructor)[];
        };
        labelRejectMask: {
            type: BooleanConstructor;
            default: boolean;
        };
        mask: {
            type: ArrayConstructor;
        };
        WebGLParams: {
            type: ObjectConstructor;
        };
        terrain: {
            type: BooleanConstructor;
            default: boolean;
        };
    } & {
        visible: import("../..").IPropOptions<boolean>;
        zIndex: import("../..").IPropOptions<number>;
        reEventWhenUpdate: import("../..").IPropOptions<boolean>;
        extraOptions: import("../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
        "onUpdate:zoom"?: ((...args: any[]) => any) | undefined;
        "onUpdate:center"?: ((...args: any[]) => any) | undefined;
        "onUpdate:rotation"?: ((...args: any[]) => any) | undefined;
        "onUpdate:pitch"?: ((...args: any[]) => any) | undefined;
    }, {
        needInitComponents: import("../..").TRegisterFn[];
        provideData: import("../..").IProvideType;
        props: import("@vue/shared").LooseRequired<{
            readonly resizeEnable: boolean;
            readonly dragEnable: boolean;
            readonly zoomEnable: boolean;
            readonly jogEnable: boolean;
            readonly pitchEnable: boolean;
            readonly rotateEnable: boolean;
            readonly animateEnable: boolean;
            readonly keyboardEnable: boolean;
            readonly doubleClickZoom: boolean;
            readonly scrollWheel: boolean;
            readonly touchZoom: boolean;
            readonly showLabel: boolean;
            readonly isHotspot: boolean;
            readonly showBuildingBlock: boolean;
            readonly showIndoorMap: boolean;
            readonly labelRejectMask: boolean;
            readonly terrain: boolean;
            readonly visible?: boolean | undefined;
            readonly zIndex?: number | undefined;
            readonly reEventWhenUpdate?: boolean | undefined;
            readonly extraOptions?: any;
            readonly vid?: string | undefined;
            readonly center?: unknown[] | undefined;
            readonly zoom?: number | undefined;
            readonly rotation?: number | undefined;
            readonly pitch?: number | undefined;
            readonly viewMode?: string | undefined;
            readonly features?: unknown[] | undefined;
            readonly layers?: unknown[] | undefined;
            readonly zooms?: unknown[] | undefined;
            readonly touchZoomCenter?: number | undefined;
            readonly defaultCursor?: string | undefined;
            readonly mapStyle?: string | undefined;
            readonly wallColor?: string | unknown[] | undefined;
            readonly roofColor?: string | unknown[] | undefined;
            readonly skyColor?: string | unknown[] | undefined;
            readonly mask?: unknown[] | undefined;
            readonly WebGLParams?: Record<string, any> | undefined;
            readonly onInit?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:zoom"?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:center"?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:rotation"?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:pitch"?: ((...args: any[]) => any) | undefined;
        } & {}>;
        emits: (event: "init" | "update:zoom" | "update:center" | "update:rotation" | "update:pitch", ...args: any[]) => void;
        mapDomId: import("vue").Ref<string>;
        $amapComponent: AMap.Map;
        $$getInstance: () => AMap.Map;
        bindModelEvents: () => void;
        getCenter: () => [number, number];
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("init" | "update:zoom" | "update:center" | "update:rotation" | "update:pitch")[], import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        vid: {
            type: StringConstructor;
        };
        center: {
            type: ArrayConstructor;
        };
        zoom: {
            type: NumberConstructor;
        };
        rotation: {
            type: NumberConstructor;
        };
        pitch: {
            type: NumberConstructor;
        };
        viewMode: {
            type: StringConstructor;
        };
        features: {
            type: ArrayConstructor;
        };
        layers: {
            type: ArrayConstructor;
        };
        zooms: {
            type: ArrayConstructor;
        };
        resizeEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        dragEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        zoomEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        jogEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        pitchEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        rotateEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        animateEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        keyboardEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        doubleClickZoom: {
            type: BooleanConstructor;
            default: boolean;
        };
        scrollWheel: {
            type: BooleanConstructor;
            default: boolean;
        };
        touchZoom: {
            type: BooleanConstructor;
            default: boolean;
        };
        touchZoomCenter: {
            type: NumberConstructor;
        };
        showLabel: {
            type: BooleanConstructor;
            default: boolean;
        };
        defaultCursor: {
            type: StringConstructor;
        };
        isHotspot: {
            type: BooleanConstructor;
        };
        mapStyle: {
            type: StringConstructor;
        };
        wallColor: {
            type: (StringConstructor | ArrayConstructor)[];
        };
        roofColor: {
            type: (StringConstructor | ArrayConstructor)[];
        };
        showBuildingBlock: {
            type: BooleanConstructor;
            default: boolean;
        };
        showIndoorMap: {
            type: BooleanConstructor;
            default: boolean;
        };
        skyColor: {
            type: (StringConstructor | ArrayConstructor)[];
        };
        labelRejectMask: {
            type: BooleanConstructor;
            default: boolean;
        };
        mask: {
            type: ArrayConstructor;
        };
        WebGLParams: {
            type: ObjectConstructor;
        };
        terrain: {
            type: BooleanConstructor;
            default: boolean;
        };
    } & {
        visible: import("../..").IPropOptions<boolean>;
        zIndex: import("../..").IPropOptions<number>;
        reEventWhenUpdate: import("../..").IPropOptions<boolean>;
        extraOptions: import("../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
        "onUpdate:zoom"?: ((...args: any[]) => any) | undefined;
        "onUpdate:center"?: ((...args: any[]) => any) | undefined;
        "onUpdate:rotation"?: ((...args: any[]) => any) | undefined;
        "onUpdate:pitch"?: ((...args: any[]) => any) | undefined;
    }, {
        resizeEnable: boolean;
        dragEnable: boolean;
        zoomEnable: boolean;
        jogEnable: boolean;
        pitchEnable: boolean;
        rotateEnable: boolean;
        animateEnable: boolean;
        keyboardEnable: boolean;
        doubleClickZoom: boolean;
        scrollWheel: boolean;
        touchZoom: boolean;
        showLabel: boolean;
        isHotspot: boolean;
        showBuildingBlock: boolean;
        showIndoorMap: boolean;
        labelRejectMask: boolean;
        terrain: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        vid: {
            type: StringConstructor;
        };
        center: {
            type: ArrayConstructor;
        };
        zoom: {
            type: NumberConstructor;
        };
        rotation: {
            type: NumberConstructor;
        };
        pitch: {
            type: NumberConstructor;
        };
        viewMode: {
            type: StringConstructor;
        };
        features: {
            type: ArrayConstructor;
        };
        layers: {
            type: ArrayConstructor;
        };
        zooms: {
            type: ArrayConstructor;
        };
        resizeEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        dragEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        zoomEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        jogEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        pitchEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        rotateEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        animateEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        keyboardEnable: {
            type: BooleanConstructor;
            default: boolean;
        };
        doubleClickZoom: {
            type: BooleanConstructor;
            default: boolean;
        };
        scrollWheel: {
            type: BooleanConstructor;
            default: boolean;
        };
        touchZoom: {
            type: BooleanConstructor;
            default: boolean;
        };
        touchZoomCenter: {
            type: NumberConstructor;
        };
        showLabel: {
            type: BooleanConstructor;
            default: boolean;
        };
        defaultCursor: {
            type: StringConstructor;
        };
        isHotspot: {
            type: BooleanConstructor;
        };
        mapStyle: {
            type: StringConstructor;
        };
        wallColor: {
            type: (StringConstructor | ArrayConstructor)[];
        };
        roofColor: {
            type: (StringConstructor | ArrayConstructor)[];
        };
        showBuildingBlock: {
            type: BooleanConstructor;
            default: boolean;
        };
        showIndoorMap: {
            type: BooleanConstructor;
            default: boolean;
        };
        skyColor: {
            type: (StringConstructor | ArrayConstructor)[];
        };
        labelRejectMask: {
            type: BooleanConstructor;
            default: boolean;
        };
        mask: {
            type: ArrayConstructor;
        };
        WebGLParams: {
            type: ObjectConstructor;
        };
        terrain: {
            type: BooleanConstructor;
            default: boolean;
        };
    } & {
        visible: import("../..").IPropOptions<boolean>;
        zIndex: import("../..").IPropOptions<number>;
        reEventWhenUpdate: import("../..").IPropOptions<boolean>;
        extraOptions: import("../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
        "onUpdate:zoom"?: ((...args: any[]) => any) | undefined;
        "onUpdate:center"?: ((...args: any[]) => any) | undefined;
        "onUpdate:rotation"?: ((...args: any[]) => any) | undefined;
        "onUpdate:pitch"?: ((...args: any[]) => any) | undefined;
    }, {
        needInitComponents: import("../..").TRegisterFn[];
        provideData: import("../..").IProvideType;
        props: import("@vue/shared").LooseRequired<{
            readonly resizeEnable: boolean;
            readonly dragEnable: boolean;
            readonly zoomEnable: boolean;
            readonly jogEnable: boolean;
            readonly pitchEnable: boolean;
            readonly rotateEnable: boolean;
            readonly animateEnable: boolean;
            readonly keyboardEnable: boolean;
            readonly doubleClickZoom: boolean;
            readonly scrollWheel: boolean;
            readonly touchZoom: boolean;
            readonly showLabel: boolean;
            readonly isHotspot: boolean;
            readonly showBuildingBlock: boolean;
            readonly showIndoorMap: boolean;
            readonly labelRejectMask: boolean;
            readonly terrain: boolean;
            readonly visible?: boolean | undefined;
            readonly zIndex?: number | undefined;
            readonly reEventWhenUpdate?: boolean | undefined;
            readonly extraOptions?: any;
            readonly vid?: string | undefined;
            readonly center?: unknown[] | undefined;
            readonly zoom?: number | undefined;
            readonly rotation?: number | undefined;
            readonly pitch?: number | undefined;
            readonly viewMode?: string | undefined;
            readonly features?: unknown[] | undefined;
            readonly layers?: unknown[] | undefined;
            readonly zooms?: unknown[] | undefined;
            readonly touchZoomCenter?: number | undefined;
            readonly defaultCursor?: string | undefined;
            readonly mapStyle?: string | undefined;
            readonly wallColor?: string | unknown[] | undefined;
            readonly roofColor?: string | unknown[] | undefined;
            readonly skyColor?: string | unknown[] | undefined;
            readonly mask?: unknown[] | undefined;
            readonly WebGLParams?: Record<string, any> | undefined;
            readonly onInit?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:zoom"?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:center"?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:rotation"?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:pitch"?: ((...args: any[]) => any) | undefined;
        } & {}>;
        emits: (event: "init" | "update:zoom" | "update:center" | "update:rotation" | "update:pitch", ...args: any[]) => void;
        mapDomId: import("vue").Ref<string>;
        $amapComponent: AMap.Map;
        $$getInstance: () => AMap.Map;
        bindModelEvents: () => void;
        getCenter: () => [number, number];
    }, {}, {}, {}, {
        resizeEnable: boolean;
        dragEnable: boolean;
        zoomEnable: boolean;
        jogEnable: boolean;
        pitchEnable: boolean;
        rotateEnable: boolean;
        animateEnable: boolean;
        keyboardEnable: boolean;
        doubleClickZoom: boolean;
        scrollWheel: boolean;
        touchZoom: boolean;
        showLabel: boolean;
        isHotspot: boolean;
        showBuildingBlock: boolean;
        showIndoorMap: boolean;
        labelRejectMask: boolean;
        terrain: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    vid: {
        type: StringConstructor;
    };
    center: {
        type: ArrayConstructor;
    };
    zoom: {
        type: NumberConstructor;
    };
    rotation: {
        type: NumberConstructor;
    };
    pitch: {
        type: NumberConstructor;
    };
    viewMode: {
        type: StringConstructor;
    };
    features: {
        type: ArrayConstructor;
    };
    layers: {
        type: ArrayConstructor;
    };
    zooms: {
        type: ArrayConstructor;
    };
    resizeEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    dragEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    zoomEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    jogEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    pitchEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    rotateEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    animateEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    keyboardEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    doubleClickZoom: {
        type: BooleanConstructor;
        default: boolean;
    };
    scrollWheel: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchZoom: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchZoomCenter: {
        type: NumberConstructor;
    };
    showLabel: {
        type: BooleanConstructor;
        default: boolean;
    };
    defaultCursor: {
        type: StringConstructor;
    };
    isHotspot: {
        type: BooleanConstructor;
    };
    mapStyle: {
        type: StringConstructor;
    };
    wallColor: {
        type: (StringConstructor | ArrayConstructor)[];
    };
    roofColor: {
        type: (StringConstructor | ArrayConstructor)[];
    };
    showBuildingBlock: {
        type: BooleanConstructor;
        default: boolean;
    };
    showIndoorMap: {
        type: BooleanConstructor;
        default: boolean;
    };
    skyColor: {
        type: (StringConstructor | ArrayConstructor)[];
    };
    labelRejectMask: {
        type: BooleanConstructor;
        default: boolean;
    };
    mask: {
        type: ArrayConstructor;
    };
    WebGLParams: {
        type: ObjectConstructor;
    };
    terrain: {
        type: BooleanConstructor;
        default: boolean;
    };
} & {
    visible: import("../..").IPropOptions<boolean>;
    zIndex: import("../..").IPropOptions<number>;
    reEventWhenUpdate: import("../..").IPropOptions<boolean>;
    extraOptions: import("../..").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
    "onUpdate:zoom"?: ((...args: any[]) => any) | undefined;
    "onUpdate:center"?: ((...args: any[]) => any) | undefined;
    "onUpdate:rotation"?: ((...args: any[]) => any) | undefined;
    "onUpdate:pitch"?: ((...args: any[]) => any) | undefined;
}, {
    needInitComponents: import("../..").TRegisterFn[];
    provideData: import("../..").IProvideType;
    props: import("@vue/shared").LooseRequired<{
        readonly resizeEnable: boolean;
        readonly dragEnable: boolean;
        readonly zoomEnable: boolean;
        readonly jogEnable: boolean;
        readonly pitchEnable: boolean;
        readonly rotateEnable: boolean;
        readonly animateEnable: boolean;
        readonly keyboardEnable: boolean;
        readonly doubleClickZoom: boolean;
        readonly scrollWheel: boolean;
        readonly touchZoom: boolean;
        readonly showLabel: boolean;
        readonly isHotspot: boolean;
        readonly showBuildingBlock: boolean;
        readonly showIndoorMap: boolean;
        readonly labelRejectMask: boolean;
        readonly terrain: boolean;
        readonly visible?: boolean | undefined;
        readonly zIndex?: number | undefined;
        readonly reEventWhenUpdate?: boolean | undefined;
        readonly extraOptions?: any;
        readonly vid?: string | undefined;
        readonly center?: unknown[] | undefined;
        readonly zoom?: number | undefined;
        readonly rotation?: number | undefined;
        readonly pitch?: number | undefined;
        readonly viewMode?: string | undefined;
        readonly features?: unknown[] | undefined;
        readonly layers?: unknown[] | undefined;
        readonly zooms?: unknown[] | undefined;
        readonly touchZoomCenter?: number | undefined;
        readonly defaultCursor?: string | undefined;
        readonly mapStyle?: string | undefined;
        readonly wallColor?: string | unknown[] | undefined;
        readonly roofColor?: string | unknown[] | undefined;
        readonly skyColor?: string | unknown[] | undefined;
        readonly mask?: unknown[] | undefined;
        readonly WebGLParams?: Record<string, any> | undefined;
        readonly onInit?: ((...args: any[]) => any) | undefined;
        readonly "onUpdate:zoom"?: ((...args: any[]) => any) | undefined;
        readonly "onUpdate:center"?: ((...args: any[]) => any) | undefined;
        readonly "onUpdate:rotation"?: ((...args: any[]) => any) | undefined;
        readonly "onUpdate:pitch"?: ((...args: any[]) => any) | undefined;
    } & {}>;
    emits: (event: "init" | "update:zoom" | "update:center" | "update:rotation" | "update:pitch", ...args: any[]) => void;
    mapDomId: import("vue").Ref<string>;
    $amapComponent: AMap.Map;
    $$getInstance: () => AMap.Map;
    bindModelEvents: () => void;
    getCenter: () => [number, number];
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("init" | "update:zoom" | "update:center" | "update:rotation" | "update:pitch")[], "init" | "update:zoom" | "update:center" | "update:rotation" | "update:pitch", {
    resizeEnable: boolean;
    dragEnable: boolean;
    zoomEnable: boolean;
    jogEnable: boolean;
    pitchEnable: boolean;
    rotateEnable: boolean;
    animateEnable: boolean;
    keyboardEnable: boolean;
    doubleClickZoom: boolean;
    scrollWheel: boolean;
    touchZoom: boolean;
    showLabel: boolean;
    isHotspot: boolean;
    showBuildingBlock: boolean;
    showIndoorMap: boolean;
    labelRejectMask: boolean;
    terrain: boolean;
}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Plugin<any[]>;
export default ElAmap;
export declare type ElAmapInstance = InstanceType<typeof Amap>;
