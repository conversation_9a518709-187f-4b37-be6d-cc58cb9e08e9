import { defineComponent, openBlock, createElementBlock } from 'vue';
import { merge } from 'lodash-es';
import '../../../mixins/index.mjs';
import '../../../utils/index.mjs';
import { buildProps } from '../../../utils/buildHelper.mjs';
import { useRegister } from '../../../mixins/useRegister.mjs';

var script = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapGeojson",
    inheritAttrs: false
  },
  __name: "GeoJSON",
  props: buildProps({
    // 要加载的标准GeoJSON对象
    geo: {
      type: Object,
      required: true
    },
    // marker的默认样式
    markerOptions: {
      type: Object
    },
    // 指定点要素的绘制方式，缺省时为Marker的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径
    getMarker: {
      type: Function
    },
    // polyline的默认样式
    polylineOptions: {
      type: Object
    },
    // 指定线要素的绘制方式，缺省时为Polyline的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径
    getPolyline: {
      type: Function
    },
    // polygon的默认样式
    polygonOptions: {
      type: Object
    },
    // 指定面要素的绘制方式，缺省时为Polygon的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径
    getPolygon: {
      type: Function
    }
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        AMap.plugin(["AMap.GeoJSON"], () => {
          if (!options.getMarker) {
            options.getMarker = createMarker;
          }
          if (!options.getPolyline) {
            options.getPolyline = createPolyline;
          }
          if (!options.getPolygon) {
            options.getPolygon = createPolygon;
          }
          $amapComponent = new AMap.GeoJSON(options);
          parentComponent.add($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      propsRedirect: {
        geo: "geoJSON"
      },
      watchRedirectFn: {
        __geoJSON(value) {
          if ($amapComponent) {
            $amapComponent.importData(value);
          }
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    const createMarker = (geojson, lnglat) => {
      let options = props.markerOptions || {};
      options = merge({}, options, geojson.properties);
      options.position = lnglat;
      return new AMap.Marker(options);
    };
    const createPolyline = (geojson, lnglat) => {
      let options = props.polylineOptions || {};
      options = merge({}, options, geojson.properties);
      options.path = lnglat;
      return new AMap.Polyline(options);
    };
    const createPolygon = (geojson, lnglat) => {
      let options = props.polygonOptions || {};
      options = merge({}, options, geojson.properties);
      options.path = lnglat;
      return new AMap.Polygon(options);
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

export { script as default };
//# sourceMappingURL=GeoJSON.vue2.mjs.map
