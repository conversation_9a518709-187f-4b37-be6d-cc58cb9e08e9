{"version": 3, "file": "collapse.mjs", "sources": ["../../../../../../packages/components/collapse/src/collapse.ts"], "sourcesContent": ["import {\n  buildProps,\n  definePropType,\n  isNumber,\n  isString,\n  mutable,\n} from '@element-plus/utils'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport type { ExtractPropTypes } from 'vue'\nimport type { Arrayable } from '@element-plus/utils'\n\nexport type CollapseActiveName = string | number\nexport type CollapseModelValue = Arrayable<CollapseActiveName>\n\nexport const emitChangeFn = (value: CollapseModelValue) =>\n  typeof isNumber(value) || isString(value) || Array.isArray(value)\n\nexport const collapseProps = buildProps({\n  accordion: Boolean,\n  modelValue: {\n    type: definePropType<CollapseModelValue>([Array, String, Number]),\n    default: () => mutable([] as const),\n  },\n} as const)\nexport type CollapseProps = ExtractPropTypes<typeof collapseProps>\n\nexport const collapseEmits = {\n  [UPDATE_MODEL_EVENT]: emitChangeFn,\n  [CHANGE_EVENT]: emitChangeFn,\n}\nexport type CollapseEmits = typeof collapseEmits\n"], "names": [], "mappings": ";;;;;;;AAOY,MAAC,YAAY,GAAG,CAAC,KAAK,KAAK,OAAO,QAAQ,CAAC,KAAK,EAAE;AAClD,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AACjD,IAAI,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC;AAC9B,GAAG;AACH,CAAC,EAAE;AACS,MAAC,aAAa,GAAG;AAC7B,EAAE,CAAC,kBAAkB,GAAG,YAAY;AACpC,EAAE,CAAC,YAAY,GAAG,YAAY;AAC9B;;;;"}