/// <reference types="@vuemap/amap-jsapi-types" />
import Rectangle from './Rectangle.vue';
import type { Plugin } from "vue";
export declare const ElAmapRectangle: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        bounds: {
            type: ArrayConstructor;
            required: true;
        };
        bubble: {
            type: BooleanConstructor;
            default: boolean;
        };
        cursor: {
            type: StringConstructor;
        };
        strokeColor: {
            type: StringConstructor;
        };
        strokeOpacity: {
            type: NumberConstructor;
        };
        strokeWeight: {
            type: NumberConstructor;
        };
        fillColor: {
            type: StringConstructor;
        };
        fillOpacity: {
            type: NumberConstructor;
        };
        draggable: {
            type: BooleanConstructor;
            default: boolean;
        };
        extData: {
            type: ObjectConstructor;
            default: () => null;
        };
        strokeStyle: {
            type: import("vue").PropType<"solid" | "dashed">;
            validator: (value: string) => boolean;
        };
        strokeDasharray: {
            type: ArrayConstructor;
        };
        editable: {
            type: BooleanConstructor;
            default: boolean;
        };
        editOptions: {
            type: ObjectConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
        "onUpdate:bounds"?: ((...args: any[]) => any) | undefined;
    }, {
        props: import("@vue/shared").LooseRequired<{
            readonly bounds: unknown[];
            readonly draggable: boolean;
            readonly bubble: boolean;
            readonly extData: Record<string, any>;
            readonly editable: boolean;
            readonly visible?: boolean | undefined;
            readonly zIndex?: number | undefined;
            readonly reEventWhenUpdate?: boolean | undefined;
            readonly extraOptions?: any;
            readonly cursor?: string | undefined;
            readonly strokeColor?: string | undefined;
            readonly strokeOpacity?: number | undefined;
            readonly strokeWeight?: number | undefined;
            readonly strokeStyle?: "solid" | "dashed" | undefined;
            readonly strokeDasharray?: unknown[] | undefined;
            readonly editOptions?: Record<string, any> | undefined;
            readonly fillColor?: string | undefined;
            readonly fillOpacity?: number | undefined;
            readonly onInit?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:bounds"?: ((...args: any[]) => any) | undefined;
        } & {}>;
        emits: (event: "init" | "update:bounds", ...args: any[]) => void;
        $amapComponent: AMap.Rectangle;
        destroying: boolean;
        $$getInstance: () => AMap.Rectangle;
        parentInstance: import("../../..").IProvideType | undefined;
        resetEditor: import("lodash").DebouncedFunc<() => void>;
        bindModelEvents: () => void;
        emitModel: (target: AMap.Rectangle) => void;
        editor: AMap.RectangleEditor;
        attrs: {
            [x: string]: unknown;
        };
        createEditor: () => Promise<void>;
        bindEditorModelEvents: () => void;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("init" | "update:bounds")[], import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        bounds: {
            type: ArrayConstructor;
            required: true;
        };
        bubble: {
            type: BooleanConstructor;
            default: boolean;
        };
        cursor: {
            type: StringConstructor;
        };
        strokeColor: {
            type: StringConstructor;
        };
        strokeOpacity: {
            type: NumberConstructor;
        };
        strokeWeight: {
            type: NumberConstructor;
        };
        fillColor: {
            type: StringConstructor;
        };
        fillOpacity: {
            type: NumberConstructor;
        };
        draggable: {
            type: BooleanConstructor;
            default: boolean;
        };
        extData: {
            type: ObjectConstructor;
            default: () => null;
        };
        strokeStyle: {
            type: import("vue").PropType<"solid" | "dashed">;
            validator: (value: string) => boolean;
        };
        strokeDasharray: {
            type: ArrayConstructor;
        };
        editable: {
            type: BooleanConstructor;
            default: boolean;
        };
        editOptions: {
            type: ObjectConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
        "onUpdate:bounds"?: ((...args: any[]) => any) | undefined;
    }, {
        draggable: boolean;
        bubble: boolean;
        extData: Record<string, any>;
        editable: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        bounds: {
            type: ArrayConstructor;
            required: true;
        };
        bubble: {
            type: BooleanConstructor;
            default: boolean;
        };
        cursor: {
            type: StringConstructor;
        };
        strokeColor: {
            type: StringConstructor;
        };
        strokeOpacity: {
            type: NumberConstructor;
        };
        strokeWeight: {
            type: NumberConstructor;
        };
        fillColor: {
            type: StringConstructor;
        };
        fillOpacity: {
            type: NumberConstructor;
        };
        draggable: {
            type: BooleanConstructor;
            default: boolean;
        };
        extData: {
            type: ObjectConstructor;
            default: () => null;
        };
        strokeStyle: {
            type: import("vue").PropType<"solid" | "dashed">;
            validator: (value: string) => boolean;
        };
        strokeDasharray: {
            type: ArrayConstructor;
        };
        editable: {
            type: BooleanConstructor;
            default: boolean;
        };
        editOptions: {
            type: ObjectConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
        "onUpdate:bounds"?: ((...args: any[]) => any) | undefined;
    }, {
        props: import("@vue/shared").LooseRequired<{
            readonly bounds: unknown[];
            readonly draggable: boolean;
            readonly bubble: boolean;
            readonly extData: Record<string, any>;
            readonly editable: boolean;
            readonly visible?: boolean | undefined;
            readonly zIndex?: number | undefined;
            readonly reEventWhenUpdate?: boolean | undefined;
            readonly extraOptions?: any;
            readonly cursor?: string | undefined;
            readonly strokeColor?: string | undefined;
            readonly strokeOpacity?: number | undefined;
            readonly strokeWeight?: number | undefined;
            readonly strokeStyle?: "solid" | "dashed" | undefined;
            readonly strokeDasharray?: unknown[] | undefined;
            readonly editOptions?: Record<string, any> | undefined;
            readonly fillColor?: string | undefined;
            readonly fillOpacity?: number | undefined;
            readonly onInit?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:bounds"?: ((...args: any[]) => any) | undefined;
        } & {}>;
        emits: (event: "init" | "update:bounds", ...args: any[]) => void;
        $amapComponent: AMap.Rectangle;
        destroying: boolean;
        $$getInstance: () => AMap.Rectangle;
        parentInstance: import("../../..").IProvideType | undefined;
        resetEditor: import("lodash").DebouncedFunc<() => void>;
        bindModelEvents: () => void;
        emitModel: (target: AMap.Rectangle) => void;
        editor: AMap.RectangleEditor;
        attrs: {
            [x: string]: unknown;
        };
        createEditor: () => Promise<void>;
        bindEditorModelEvents: () => void;
    }, {}, {}, {}, {
        draggable: boolean;
        bubble: boolean;
        extData: Record<string, any>;
        editable: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    bounds: {
        type: ArrayConstructor;
        required: true;
    };
    bubble: {
        type: BooleanConstructor;
        default: boolean;
    };
    cursor: {
        type: StringConstructor;
    };
    strokeColor: {
        type: StringConstructor;
    };
    strokeOpacity: {
        type: NumberConstructor;
    };
    strokeWeight: {
        type: NumberConstructor;
    };
    fillColor: {
        type: StringConstructor;
    };
    fillOpacity: {
        type: NumberConstructor;
    };
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    extData: {
        type: ObjectConstructor;
        default: () => null;
    };
    strokeStyle: {
        type: import("vue").PropType<"solid" | "dashed">;
        validator: (value: string) => boolean;
    };
    strokeDasharray: {
        type: ArrayConstructor;
    };
    editable: {
        type: BooleanConstructor;
        default: boolean;
    };
    editOptions: {
        type: ObjectConstructor;
    };
} & {
    visible: import("../../..").IPropOptions<boolean>;
    zIndex: import("../../..").IPropOptions<number>;
    reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
    extraOptions: import("../../..").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
    "onUpdate:bounds"?: ((...args: any[]) => any) | undefined;
}, {
    props: import("@vue/shared").LooseRequired<{
        readonly bounds: unknown[];
        readonly draggable: boolean;
        readonly bubble: boolean;
        readonly extData: Record<string, any>;
        readonly editable: boolean;
        readonly visible?: boolean | undefined;
        readonly zIndex?: number | undefined;
        readonly reEventWhenUpdate?: boolean | undefined;
        readonly extraOptions?: any;
        readonly cursor?: string | undefined;
        readonly strokeColor?: string | undefined;
        readonly strokeOpacity?: number | undefined;
        readonly strokeWeight?: number | undefined;
        readonly strokeStyle?: "solid" | "dashed" | undefined;
        readonly strokeDasharray?: unknown[] | undefined;
        readonly editOptions?: Record<string, any> | undefined;
        readonly fillColor?: string | undefined;
        readonly fillOpacity?: number | undefined;
        readonly onInit?: ((...args: any[]) => any) | undefined;
        readonly "onUpdate:bounds"?: ((...args: any[]) => any) | undefined;
    } & {}>;
    emits: (event: "init" | "update:bounds", ...args: any[]) => void;
    $amapComponent: AMap.Rectangle;
    destroying: boolean;
    $$getInstance: () => AMap.Rectangle;
    parentInstance: import("../../..").IProvideType | undefined;
    resetEditor: import("lodash").DebouncedFunc<() => void>;
    bindModelEvents: () => void;
    emitModel: (target: AMap.Rectangle) => void;
    editor: AMap.RectangleEditor;
    attrs: {
        [x: string]: unknown;
    };
    createEditor: () => Promise<void>;
    bindEditorModelEvents: () => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("init" | "update:bounds")[], "init" | "update:bounds", {
    draggable: boolean;
    bubble: boolean;
    extData: Record<string, any>;
    editable: boolean;
}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Plugin<any[]>;
export default ElAmapRectangle;
export declare type ElAmapRectangleInstance = InstanceType<typeof Rectangle>;
