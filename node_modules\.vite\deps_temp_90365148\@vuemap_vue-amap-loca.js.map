{"version": 3, "sources": ["../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/utils/buildHelper.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/mixins/useLoca.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/GridLayer/GridLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/GridLayer/GridLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/GridLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/HeatMapLayer/HeatMapLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/HeatMapLayer/HeatMapLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/HeatMapLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/HexagonLayer/HexagonLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/HexagonLayer/HexagonLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/HexagonLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/IconLayer/IconLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/IconLayer/IconLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/IconLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/LineLayer/LineLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LineLayer/LineLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/LineLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/LinkLayer/LinkLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LinkLayer/LinkLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/LinkLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/Loca/Loca.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/Loca/Loca.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/Loca/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PointLayer/PointLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PointLayer/PointLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PointLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PolygonLayer/PolygonLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PolygonLayer/PolygonLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PolygonLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PrismLayer/PrismLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PrismLayer/PrismLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PrismLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PulseLineLayer/PulseLineLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PulseLineLayer/PulseLineLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PulseLineLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PulseLinkLayer/PulseLinkLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PulseLinkLayer/PulseLinkLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PulseLinkLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/ScatterLayer/ScatterLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/ScatterLayer/ScatterLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/ScatterLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/ZMarkerLayer/ZMarkerLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/ZMarkerLayer/ZMarkerLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/ZMarkerLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/AmbientLight/AmbientLight.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/AmbientLight/AmbientLight.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/AmbientLight/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/DirectionalLight/DirectionalLight.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/DirectionalLight/DirectionalLight.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/DirectionalLight/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PointLight/PointLight.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PointLight/PointLight.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/PointLight/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/LaserLayer/LaserLayer.vue", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LaserLayer/LaserLayer.vue.mjs", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/packages/LaserLayer/index.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/component.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/defaults.ts", "../../.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/index.ts"], "sourcesContent": ["import {commonProps} from '@vuemap/vue-amap';\r\nimport type {ICommonProps, IPropOptions} from '@vuemap/vue-amap';\r\nimport type {ComponentObjectPropsOptions, PropType} from \"vue\";\r\n\r\nexport interface ILocaProps{\r\n  // \r\n  sourceUrl: IPropOptions<string>\r\n  // \r\n  sourceData: IPropOptions<object>\r\n  // \r\n  geoBufferSource: IPropOptions\r\n  // \r\n  layerStyle: IPropOptions<object>\r\n  defaultStyleValue: IPropOptions<object>\r\n  zooms: IPropOptions<object>\r\n  opacity: IPropOptions<number>\r\n  initEvents: IPropOptions<boolean>\r\n  visibleDuration: IPropOptions<number>\r\n  onClick:  IPropOptions<(e: any) => void>\r\n  onMousemove:  IPropOptions<(e: any) => void>\r\n  onRightclick:  IPropOptions<(e: any) => void>\r\n}\r\n\r\n/**\r\n * 绑定Loca的属性\r\n * @param props\r\n */\r\nexport const buildLocaProps = <Props extends ComponentObjectPropsOptions>(props: Props): Props & {\r\n  [K  in keyof ICommonProps]: ICommonProps[K]\r\n} & {\r\n  [T  in keyof ILocaProps]: ILocaProps[T]\r\n} => {\r\n  return Object.assign({}, commonProps, {\r\n    sourceUrl: {\r\n      type: String\r\n    },\r\n    sourceData: {\r\n      type: Object\r\n    },\r\n    geoBufferSource: {\r\n      type: [ArrayBuffer, String],\r\n      default () {\r\n        return null;\r\n      }\r\n    },\r\n    layerStyle: {\r\n      type: Object\r\n    },\r\n    defaultStyleValue: {\r\n      type: Object,\r\n      default () {\r\n        return {};\r\n      }\r\n    },\r\n    zooms: {\r\n      type: Array\r\n    },\r\n    opacity: {\r\n      type: Number\r\n    },\r\n    initEvents: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    visibleDuration: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    onClick: {\r\n      type: Function as PropType<(e: any) => void>,\r\n      default: null\r\n    },\r\n    onMousemove: {\r\n      type: Function as PropType<(e: any) => void>,\r\n      default: null\r\n    },\r\n    onRightclick: {\r\n      type: Function as PropType<(e: any) => void>,\r\n      default: null\r\n    },\r\n  } as ILocaProps, props);\r\n};\r\n\r\nexport const commonEmitNames = ['init', 'mousemove', 'click', 'rightclick' ];", "import {nextTick} from \"vue\";\r\nimport type {IProvideType} from '@vuemap/vue-amap';\r\n\r\n\r\ninterface IUseWatchFnType {\r\n  setSource: () => void\r\n  $amapComponent: () => any\r\n  props: any\r\n}\r\n\r\nexport interface IUseLocaTypes {\r\n  $$getInstance: () => any\r\n  parentInstance: IProvideType\r\n}\r\n\r\nexport function useWatchFn (options: IUseWatchFnType){\r\n  return {\r\n    __layerStyle (style: any) {\r\n      nextTick(() => {\r\n        if (options.$amapComponent()?.setStyle) {\r\n          options.$amapComponent().setStyle(style);\r\n        }\r\n      }).then();\r\n    },\r\n    __sourceUrl (){\r\n      nextTick(() => {\r\n        options.setSource();\r\n      }).then();\r\n    },\r\n    __sourceData (){\r\n      nextTick(() => {\r\n        options.setSource();\r\n      }).then();\r\n    },\r\n    __geoBufferSource (){\r\n      nextTick(() => {\r\n        options.setSource();\r\n      }).then();\r\n    },\r\n    __visible (flag: boolean) {\r\n      const $amapComponent = options.$amapComponent();\r\n      if ($amapComponent?.show && $amapComponent?.hide) {\r\n        !flag ? $amapComponent.hide(options.props.visibleDuration) : $amapComponent.show(options.props.visibleDuration);\r\n      }\r\n    }\r\n  };\r\n}\r\n\r\nexport function useLocaEvents (options: {\r\n  parentInstance?: IProvideType\r\n  $amapComponent: any\r\n  emits: any,\r\n  props: any\r\n  setStyle: () => void\r\n}){\r\n  let isDragging = false;\r\n  let isRotating = false;\r\n  let source: any;\r\n  const {parentInstance, $amapComponent, emits, props, setStyle}  = options;\r\n  \r\n  const setSource = () => {\r\n    if (source) {\r\n      source.destroy();\r\n      source = null;\r\n    }\r\n    if (props.geoBufferSource) {\r\n      if(typeof props.geoBufferSource === 'string'){\r\n        source = new Loca.GeoBufferSource({\r\n          url: props.geoBufferSource\r\n        });\r\n      }else{\r\n        source = new Loca.GeoBufferSource({\r\n          data: props.geoBufferSource\r\n        });\r\n      }\r\n    }else if (props.sourceUrl) {\r\n      source = new Loca.GeoJSONSource({\r\n        url: props.sourceUrl\r\n      });\r\n    } else if (props.sourceData) {\r\n      source = new Loca.GeoJSONSource({\r\n        data: props.sourceData\r\n      });\r\n    } else {\r\n      source = new Loca.GeoJSONSource({\r\n      });\r\n    }\r\n    $amapComponent.setSource(source);\r\n  };\r\n  \r\n  const initComplete = () => {\r\n    if (props.initEvents) {\r\n      bindEvents();\r\n    }\r\n  };\r\n  const bindEvents = () => {\r\n    if(parentInstance){\r\n      const map = parentInstance.getMap();\r\n      if(props.onClick !== null){\r\n        map.on('click', clickMap);\r\n      }\r\n      if(props.onMousemove !== null){\r\n        map.on('mousemove', mouseMoveMap);\r\n        map.on('dragstart', dragStart);\r\n        map.on('dragend', dragEnd);\r\n        map.on('rotatestart', rotateStart);\r\n        map.on('rotateend', rotateEnd);\r\n        map.on('mouseout', mouseoutMap);\r\n      }\r\n      if(props.onRightclick !== null){\r\n        map.on('rightclick', rightclickMap);\r\n      }\r\n    }\r\n  };\r\n  const clickMap = (e: any) => {\r\n    const feature = _getFeature(e);\r\n    emits('click', feature, e);\r\n  };\r\n  const rightclickMap = (e: any) => {\r\n    const feature = _getFeature(e);\r\n    emits('rightclick', feature, e);\r\n  };\r\n  const mouseMoveMap = (e:any) => {\r\n    if(isDragging || isRotating){\r\n      return;\r\n    }\r\n    const feature = _getFeature(e);\r\n    emits('mousemove', feature, e);\r\n  };\r\n  const _getFeature = (e:any) => {\r\n    return $amapComponent.queryFeature(e.pixel.toArray());\r\n  };\r\n  const dragStart =  () => {\r\n    isDragging = true;\r\n  };\r\n  const dragEnd  = () => {\r\n    isDragging = false;\r\n  };\r\n  const mouseoutMap =  () => {\r\n    isDragging = false;\r\n    isRotating = false;\r\n  };\r\n  const rotateStart  = () => {\r\n    isRotating = true;\r\n  };\r\n  const rotateEnd  = () => {\r\n    isRotating = false;\r\n  };\r\n  const unBindEvents =  ()  => {\r\n    if(parentInstance){\r\n      const map = parentInstance.getMap();\r\n      map.off('click', clickMap);\r\n      map.off('rightclick', rightclickMap);\r\n      map.off('mousemove', mouseMoveMap);\r\n      map.off('dragstart', dragStart);\r\n      map.off('dragend', dragEnd);\r\n      map.off('rotatestart', rotateStart);\r\n      map.off('rotateend', rotateEnd);\r\n      map.off('mouseout', mouseoutMap);\r\n    }\r\n  };\r\n  \r\n  setSource();\r\n  setStyle();\r\n  parentInstance?.$amapComponent.add($amapComponent);\r\n  initComplete();\r\n  \r\n  const _destroyComponent = () => {\r\n    unBindEvents();\r\n    if(!parentInstance?.isDestroy){\r\n      parentInstance?.$amapComponent.remove($amapComponent);\r\n      $amapComponent.destroy();\r\n    }\r\n    if (source) {\r\n      source.destroy();\r\n      source = null;\r\n    }\r\n  };\r\n  \r\n  return {\r\n    _destroyComponent,\r\n    setSource\r\n  };\r\n}", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaGrid',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。\r\n  cullface: {\r\n    type: String\r\n  },\r\n  // 面是否接受光照，光照信息在 loca 对象中配置\r\n  acceptLight: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  // 立体网格的粗糙度，值越高，说明表面越粗糙。\r\n  shininess: {\r\n    type: Number\r\n  },\r\n  // 当面有厚度的时候，有没有侧面和底面\r\n  hasSide: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  // 是否开启深度检测，开启后可能会影响zIndex\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.GridLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.GridLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          topColor: '#fff',\r\n          sideTopColor: '#fff',\r\n          sideBottomColor: '#fff',\r\n          altitude: 0,\r\n          height: 0,\r\n          radius: 1000,\r\n          gap: 0,\r\n          unit: 'meter'\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          topColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.topColor === undefined ? style.topColor : feature.properties.topColor;\r\n          },\r\n          sideTopColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideTopColor === undefined ? style.sideTopColor : feature.properties.sideTopColor;\r\n          },\r\n          sideBottomColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideBottomColor === undefined ? style.sideBottomColor : feature.properties.sideBottomColor;\r\n          },\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude;\r\n          },\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          radius: style.radius,\r\n          gap: style.gap,\r\n          unit: style.unit\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle as any);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null as any;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './GridLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/GridLayer/GridLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=GridLayer.vue.mjs.map\n", "import GridLayer from './GridLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nGridLayer.install = (app: App) => {\r\n  app.component(GridLayer.name, GridLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaGrid = GridLayer as typeof GridLayer & Plugin;\r\nexport default ElAmapLocaGrid;\r\n\r\nexport type ElAmapLocaGridInstance = InstanceType<typeof GridLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaHeatmap',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.HeatMapLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.HeatMapLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          radius: 20,\r\n          value: 10,\r\n          gradient: {0.5: 'blue', 0.65: 'rgb(117,211,248)', 0.7: 'rgb(0, 255, 0)', 0.9: '#ffea00', 1.0: 'red'},\r\n          opacity: [0, 1],\r\n          height: 100,\r\n          heightBezier: [0.4, 0.2, 0.4, 0.8],\r\n          max: null,\r\n          min: null,\r\n          unit: 'px',\r\n          difference: false\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          radius: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.radius === undefined ? style.radius : feature.properties.radius;\r\n          },\r\n          value: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.value === undefined ? style.value : feature.properties.value;\r\n          },\r\n          gradient: style.gradient,\r\n          opacity: style.opacity,\r\n          height: style.height,\r\n          heightBezier: style.heightBezier,\r\n          max: style.max,\r\n          min: style.min,\r\n          unit: style.unit,\r\n          difference: style.difference\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle as any);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null as any;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './HeatMapLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/HeatMapLayer/HeatMapLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=HeatMapLayer.vue.mjs.map\n", "import HeatMapLayer from './HeatMapLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nHeatMapLayer.install = (app: App) => {\r\n  app.component(HeatMapLayer.name, HeatMapLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaHeatmap = HeatMapLayer as typeof HeatMapLayer & Plugin;\r\nexport default ElAmapLocaHeatmap;\r\n\r\nexport type ElAmapLocaHeatmapInstance = InstanceType<typeof HeatMapLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaHexagon',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  cullface: {\r\n    type: String\r\n  }, // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。\r\n  acceptLight: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 面是否接受光照，光照信息在 loca 对象中配置\r\n  shininess: {\r\n    type: Number\r\n  }, // 立体网格的粗糙度，值越高，说明表面越粗糙。\r\n  hasSide: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 当面有厚度的时候，有没有侧面和底面\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }// 是否开启深度检测，开启后可能会影响zIndex\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.HexagonLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.HexagonLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          topColor: '#fff',\r\n          sideTopColor: '#fff',\r\n          sideBottomColor: '#fff',\r\n          altitude: 0,\r\n          height: 0,\r\n          radius: 1000,\r\n          gap: 0,\r\n          unit: 'meter'\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          topColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.topColor === undefined ? style.topColor : feature.properties.topColor;\r\n          },\r\n          sideTopColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideTopColor === undefined ? style.sideTopColor : feature.properties.sideTopColor;\r\n          },\r\n          sideBottomColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideBottomColor === undefined ? style.sideBottomColor : feature.properties.sideBottomColor;\r\n          },\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude;\r\n          },\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          radius: style.radius,\r\n          gap: style.gap,\r\n          unit: style.unit\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './HexagonLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/HexagonLayer/HexagonLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=HexagonLayer.vue.mjs.map\n", "import HexagonLayer from './HexagonLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nHexagonLayer.install = (app: App) => {\r\n  app.component(HexagonLayer.name, HexagonLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaHexagon = HexagonLayer as typeof HexagonLayer & Plugin;\r\nexport default ElAmapLocaHexagon;\r\n\r\nexport type ElAmapLocaHexagonInstance = InstanceType<typeof HexagonLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaIcon',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.IconLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.IconLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          unit: 'px',\r\n          icon: '',\r\n          iconSize: [20, 20],\r\n          rotation: 0,\r\n          opacity: 1,\r\n          offset: [0, 0]\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          unit: style.unit,\r\n          icon: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.icon === undefined ? style.icon : feature.properties.icon;\r\n          },\r\n          iconSize: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.iconSize === undefined ? style.iconSize : feature.properties.iconSize;\r\n          },\r\n          rotation: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.rotation === undefined ? style.rotation : feature.properties.rotation;\r\n          },\r\n          opacity: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.opacity === undefined ? style.opacity : feature.properties.opacity;\r\n          },\r\n          offset: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.offset === undefined ? style.offset : feature.properties.offset;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './IconLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/IconLayer/IconLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=IconLayer.vue.mjs.map\n", "import IconLayer from './IconLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nIconLayer.install = (app: App) => {\r\n  app.component(IconLayer.name, IconLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaIcon = IconLayer as typeof IconLayer & Plugin;\r\nexport default ElAmapLocaIcon;\r\n\r\nexport type ElAmapLocaIconInstance = InstanceType<typeof IconLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaLine',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.LineLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.LineLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          color: '#fff',\r\n          lineWidth: 2,\r\n          altitude: 0,\r\n          borderWidth: 0,\r\n          borderColor: '#fff',\r\n          dashArray: [10, 0, 10, 0]\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          color: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.color === undefined ? style.color : feature.properties.color;\r\n          },\r\n          lineWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.lineWidth === undefined ? style.lineWidth : feature.properties.lineWidth;\r\n          },\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude;\r\n          },\r\n          borderWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderWidth === undefined ? style.borderWidth : feature.properties.borderWidth;\r\n          },\r\n          borderColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderColor === undefined ? style.borderColor : feature.properties.borderColor;\r\n          },\r\n          dashArray: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.dashArray === undefined ? style.dashArray : feature.properties.dashArray;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './LineLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/LineLayer/LineLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=LineLayer.vue.mjs.map\n", "import LineLayer from './LineLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nLineLayer.install = (app: App) => {\r\n  app.component(LineLayer.name, LineLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaLine = LineLayer as typeof LineLayer & Plugin;\r\nexport default ElAmapLocaLine;\r\n\r\nexport type ElAmapLocaLineInstance = InstanceType<typeof LineLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaLink',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.LinkLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.LinkLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          lineColors: ['rgba(255,255,255,1)', 'rgba(255,255,255,0)'],\r\n          height: 100,\r\n          smoothSteps: 100\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          lineColors: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.lineColors === undefined ? style.lineColors : feature.properties.lineColors;\r\n          },\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          smoothSteps: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.smoothSteps === undefined ? style.smoothSteps : feature.properties.smoothSteps;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './LinkLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/LinkLayer/LinkLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=LinkLayer.vue.mjs.map\n", "import LinkLayer from './LinkLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nLinkLayer.install = (app: App) => {\r\n  app.component(LinkLayer.name, LinkLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaLink = LinkLayer as typeof LinkLayer & Plugin;\r\nexport default ElAmapLocaLink;\r\n\r\nexport type ElAmapLocaLinkInstance = InstanceType<typeof LinkLayer>\r\n", "<template>\r\n  <div><slot /></div>\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions, provide} from 'vue';\r\nimport {provideKey, useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport type { TRegisterFn, IProvideType} from \"@vuemap/vue-amap\";\r\nimport type {PropType} from 'vue';\r\nimport type {EventOptions} from \"./Type\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLoca',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst needInitComponents: TRegisterFn[] = [];\r\nconst provideData:IProvideType = {\r\n  $amapComponent: undefined,\r\n  addChildComponent (cb){\r\n    needInitComponents.push(cb);\r\n  },\r\n  isDestroy: false,\r\n  getMap: () => {\r\n    return getMap();\r\n  }\r\n};\r\n\r\nprovide(provideKey, provideData);\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  ambLight: {\r\n    type: Object\r\n  }, // 环境光\r\n  dirLight: {\r\n    type: Object\r\n  }, // 平行光\r\n  pointLight: {\r\n    type: Object\r\n  }, // 点光\r\n  onClick: {\r\n    type: Function,\r\n    default: null\r\n  },\r\n  onMousemove: {\r\n    type: Function,\r\n    default: null\r\n  },\r\n  onRightclick: {\r\n    type: Function,\r\n    default: null\r\n  },\r\n  eventOptions: {\r\n    type: Object as PropType<EventOptions>,\r\n    default: () => ({\r\n      hitFirst: true\r\n    })\r\n  } // 对于事件是否只触发第一个选中的数据\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\nlet isDragging = false;\r\nlet isRotating = false;\r\nlet hitFirst = true;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.Container, AMap.Map>((options, parentComponent) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.Container({\r\n      map: parentComponent\r\n    });\r\n    provideData.$amapComponent = $amapComponent;\r\n    if (options.ambLight) {\r\n      $amapComponent.ambLight = options.ambLight;\r\n    }\r\n    if (options.dirLight) {\r\n      $amapComponent.dirLight = options.dirLight;\r\n    }\r\n    if (options.pointLight) {\r\n      $amapComponent.pointLight = options.pointLight;\r\n    }\r\n    hitFirst = options.eventOptions.hitFirst;\r\n    bindEvents();\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  needInitComponents,\r\n  provideData,\r\n  destroyComponent () {\r\n    if ($amapComponent) {\r\n      unBindEvents();\r\n      if($amapComponent.animate && $amapComponent.animate.stop){\r\n        $amapComponent.animate.stop();\r\n      }\r\n      $amapComponent.destroy();\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\nconst getMap = () => {\r\n  return parentInstance?.$amapComponent;\r\n};\r\n\r\nconst bindEvents = () => {\r\n  if(parentInstance){\r\n    const map = getMap();\r\n    if(props.onClick !== null){\r\n      map.on('click', clickMap);\r\n    }\r\n    if(props.onMousemove !== null){\r\n      map.on('mousemove', mouseMoveMap);\r\n      map.on('dragstart', dragStart);\r\n      map.on('dragend', dragEnd);\r\n      map.on('rotatestart', rotateStart);\r\n      map.on('rotateend', rotateEnd);\r\n      map.on('mouseout', mouseoutMap);\r\n    }\r\n    if(props.onRightclick !== null){\r\n      map.on('rightclick', rightclickMap);\r\n    }\r\n  }\r\n};\r\nconst clickMap = (e) => {\r\n  const features = _getFeature(e);\r\n  emits('click', features, e);\r\n};\r\nconst rightclickMap = (e) => {\r\n  const features = _getFeature(e);\r\n  emits('rightclick', features, e);\r\n};\r\nconst mouseMoveMap = (e) => {\r\n  if(isDragging || isRotating){\r\n    return;\r\n  }\r\n  const features = _getFeature(e);\r\n  emits('mousemove', features, e);\r\n};\r\nconst _getFeature = (e) => {\r\n  const features: any[] = [];\r\n  if($amapComponent.layers){\r\n    const layers: any[] = [];\r\n    $amapComponent.layers.forEach(( v => {\r\n      layers.push(v);\r\n    }));\r\n    layers.sort((a,b) => b.zIndex - a.zIndex);\r\n    const layerLen = layers.length;\r\n    for(let i=0;i<layerLen;i++){\r\n      const temp = layers[i].queryFeature(e.pixel.toArray());\r\n      if(temp){\r\n        features.push(temp);\r\n        if(hitFirst){\r\n          break;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  return features;\r\n};\r\nconst dragStart = () => {\r\n  isDragging = true;\r\n};\r\nconst dragEnd = () => {\r\n  isDragging = false;\r\n};\r\nconst mouseoutMap = () => {\r\n  isDragging = false;\r\n  isRotating = false;\r\n};\r\nconst rotateStart = () => {\r\n  isRotating = true;\r\n};\r\nconst rotateEnd = () => {\r\n  isRotating = false;\r\n};\r\nconst unBindEvents = () => {\r\n  if(parentInstance){\r\n    const map = getMap();\r\n    map.off('click', clickMap);\r\n    map.off('rightclick', rightclickMap);\r\n    map.off('mousemove', mouseMoveMap);\r\n    map.off('dragstart', dragStart);\r\n    map.off('dragend', dragEnd);\r\n    map.off('rotatestart', rotateStart);\r\n    map.off('rotateend', rotateEnd);\r\n    map.off('mouseout', mouseoutMap);\r\n  }\r\n};\r\n\r\ndefineExpose({\r\n  $$getInstance\r\n});\r\n\r\n</script>\r\n", "import script from './Loca.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/Loca/Loca.vue\";\n\nexport { script as default };\n//# sourceMappingURL=Loca.vue.mjs.map\n", "import Loca from './Loca.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nLoca.install = (app: App) => {\r\n  app.component(Loca.name, Loca);\r\n  return app;\r\n};\r\nexport const ElAmapLoca = Loca as typeof Loca & Plugin;\r\nexport default ElAmapLoca;\r\n\r\nexport type ElAmapLocaInstance = InstanceType<typeof Loca>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPoint',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  // 图层里面元素的叠加效果，normal：正常透明度叠加，lighter：叠加后可能更加明亮\r\n  blend: {\r\n    type: String\r\n  } \r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PointLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PointLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          radius: 20,\r\n          color: '#fff',\r\n          unit: 'px',\r\n          borderWidth: 10,\r\n          borderColor: '#fff',\r\n          blurWidth: -1\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          radius: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.radius === undefined ? style.radius : feature.properties.radius;\r\n          },\r\n          color: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.color === undefined ? style.color : feature.properties.color;\r\n          },\r\n          unit: style.unit,\r\n          borderWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderWidth === undefined ? style.borderWidth : feature.properties.borderWidth;\r\n          },\r\n          borderColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderColor === undefined ? style.borderColor : feature.properties.borderColor;\r\n          },\r\n          blurWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.blurWidth === undefined ? style.blurWidth : feature.properties.blurWidth;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './PointLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/PointLayer/PointLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=PointLayer.vue.mjs.map\n", "import PointLayer from './PointLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPointLayer.install = (app: App) => {\r\n  app.component(PointLayer.name, PointLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPoint = PointLayer as typeof PointLayer & Plugin;\r\nexport default ElAmapLocaPoint;\r\n\r\nexport type ElAmapLocaPointInstance = InstanceType<typeof PointLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPolygon',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  cullface: {\r\n    type: String\r\n  }, // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。\r\n  acceptLight: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 面是否接受光照，光照信息在 loca 对象中配置\r\n  shininess: {\r\n    type: Number\r\n  }, // 立体网格的粗糙度，值越高，说明表面越粗糙。\r\n  hasSide: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 当面有厚度的时候，有没有侧面\r\n  hasBottom: {\r\n    type: Boolean,\r\n    default: false\r\n  },//当面有厚度的时候，有没有底面。\r\n  blockHide: {\r\n    type: Boolean,\r\n    default: true\r\n  },//是否开启被遮挡的面隐藏，默认开启，如果关闭，在有透明度的时候，会显示出被遮挡的面。\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }// 是否开启深度检测，开启后可能会影响zIndex\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PolygonLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PolygonLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          topColor: '#fff',\r\n          sideTopColor: '#fff',\r\n          sideBottomColor: '#fff',\r\n          altitude: 0,\r\n          height: 0,\r\n          texture: null,\r\n          textureSize: [20, 3],\r\n          label: undefined,\r\n          labelAltitude: 0\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          topColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.topColor === undefined ? style.topColor : feature.properties.topColor;\r\n          },\r\n          sideTopColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideTopColor === undefined ? style.sideTopColor : feature.properties.sideTopColor;\r\n          },\r\n          sideBottomColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideBottomColor === undefined ? style.sideBottomColor : feature.properties.sideBottomColor;\r\n          },\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude;\r\n          },\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          texture: style.texture,\r\n          textureSize: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.textureSize === undefined ? style.textureSize : feature.properties.textureSize;\r\n          },\r\n          label: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.label === undefined ? style.label : feature.properties.label;\r\n          },\r\n          labelAltitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.labelAltitude === undefined ? style.labelAltitude : feature.properties.labelAltitude;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './PolygonLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/PolygonLayer/PolygonLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=PolygonLayer.vue.mjs.map\n", "import PolygonLayer from './PolygonLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPolygonLayer.install = (app: App) => {\r\n  app.component(PolygonLayer.name, PolygonLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPolygon = PolygonLayer as typeof PolygonLayer & Plugin;\r\nexport default ElAmapLocaPolygon;\r\n\r\nexport type ElAmapLocaPolygonInstance = InstanceType<typeof PolygonLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPrism',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  cullface: {\r\n    type: String\r\n  }, // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。\r\n  acceptLight: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 面是否接受光照，光照信息在 loca 对象中配置\r\n  shininess: {\r\n    type: Number\r\n  }, // 立体网格的粗糙度，值越高，说明表面越粗糙。\r\n  hasSide: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 当面有厚度的时候，有没有侧面和底面\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }// 是否开启深度检测，开启后可能会影响zIndex\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PrismLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PrismLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          radius: 20,\r\n          unit: 'px',\r\n          sideNumber: 3,\r\n          rotation: 0,\r\n          altitude: 0,\r\n          height: 100,\r\n          topColor: '#fff',\r\n          sideTopColor: '#fff',\r\n          sideBottomColor: '#fff'\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          radius: style.radius,\r\n          unit: style.unit,\r\n          sideNumber: style.sideNumber,\r\n          rotation: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.rotation === undefined ? style.rotation : feature.properties.rotation;\r\n          },\r\n          altitude: style.altitude,\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          topColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.topColor === undefined ? style.topColor : feature.properties.topColor;\r\n          },\r\n          sideTopColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideTopColor === undefined ? style.sideTopColor : feature.properties.sideTopColor;\r\n          },\r\n          sideBottomColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideBottomColor === undefined ? style.sideBottomColor : feature.properties.sideBottomColor;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './PrismLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/PrismLayer/PrismLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=PrismLayer.vue.mjs.map\n", "import PrismLayer from './PrismLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPrismLayer.install = (app: App) => {\r\n  app.component(PrismLayer.name, PrismLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPrism = PrismLayer as typeof PrismLayer & Plugin;\r\nexport default ElAmapLocaPrism;\r\n\r\nexport type ElAmapLocaPrismInstance = InstanceType<typeof PrismLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPulseLine',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PulseLineLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PulseLineLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          lineWidth: 1,\r\n          headColor: 'rgba(0, 0, 0, 0.75)',\r\n          trailColor: 'rgba(0, 0, 0, 0.25)',\r\n          altitude: 0,\r\n          interval: 1,\r\n          duration: 2000\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          lineWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.lineWidth === undefined ? style.lineWidth : feature.properties.lineWidth;\r\n          },\r\n          headColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.headColor === undefined ? style.headColor : feature.properties.headColor;\r\n          },\r\n          trailColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.trailColor === undefined ? style.trailColor : feature.properties.trailColor;\r\n          },\r\n          altitude: style.altitude,\r\n          interval: style.interval,\r\n          duration: style.duration\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './PulseLineLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/PulseLineLayer/PulseLineLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=PulseLineLayer.vue.mjs.map\n", "import PulseLineLayer from './PulseLineLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPulseLineLayer.install = (app: App) => {\r\n  app.component(PulseLineLayer.name, PulseLineLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPulseLine = PulseLineLayer as typeof PulseLineLayer & Plugin;\r\nexport default ElAmapLocaPulseLine;\r\n\r\nexport type ElAmapLocaPulseLineInstance = InstanceType<typeof PulseLineLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPulseLink',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PulseLinkLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PulseLinkLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          lineColors: ['#fff'],\r\n          height: 100,\r\n          maxHeightScale: 0,\r\n          smoothSteps: 50,\r\n          lineWidth: [1, 1],\r\n          unit: 'px',\r\n          dash: [4000, 0, 4000, 0],\r\n          speed: 100,\r\n          headColor: 'rgba(0, 0, 0, 0.75)',\r\n          trailColor: 'rgba(0, 0, 0, 0.25)',\r\n          flowLength: 100\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          lineColors: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.lineColors === undefined ? style.lineColors : feature.properties.lineColors;\r\n          },\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          maxHeightScale: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.maxHeightScale === undefined ? style.maxHeightScale : feature.properties.maxHeightScale;\r\n          },\r\n          smoothSteps: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.smoothSteps === undefined ? style.smoothSteps : feature.properties.smoothSteps;\r\n          },\r\n          lineWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.lineWidth === undefined ? style.lineWidth : feature.properties.lineWidth;\r\n          },\r\n          unit: style.unit,\r\n          dash: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.dash === undefined ? style.dash : feature.properties.dash;\r\n          },\r\n          speed: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.speed === undefined ? style.speed : feature.properties.speed;\r\n          },\r\n          headColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.headColor === undefined ? style.headColor : feature.properties.headColor;\r\n          },\r\n          trailColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.trailColor === undefined ? style.trailColor : feature.properties.trailColor;\r\n          },\r\n          flowLength: style.flowLength\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './PulseLinkLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/PulseLinkLayer/PulseLinkLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=PulseLinkLayer.vue.mjs.map\n", "import PulseLinkLayer from './PulseLinkLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPulseLinkLayer.install = (app: App) => {\r\n  app.component(PulseLinkLayer.name, PulseLinkLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPulseLink = PulseLinkLayer as typeof PulseLinkLayer & Plugin;\r\nexport default ElAmapLocaPulseLink;\r\n\r\nexport type ElAmapLocaPulseLinkInstance = InstanceType<typeof PulseLinkLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaScatter',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.ScatterLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.ScatterLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          size: [20, 20],\r\n          rotation: 0,\r\n          color: 'rgba(200,200,200,1)',\r\n          altitude: 0,\r\n          borderWidth: 0,\r\n          borderColor: 'rgba(250,250,250,1)',\r\n          texture: null,\r\n          unit: 'px',\r\n          animate: false,\r\n          duration: 0\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          size: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.size === undefined ? style.size : feature.properties.size;\r\n          },\r\n          rotation: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.rotation === undefined ? style.rotation : feature.properties.rotation;\r\n          },\r\n          color: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.color === undefined ? style.color : feature.properties.color;\r\n          },\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude;\r\n          },\r\n          borderWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderWidth === undefined ? style.borderWidth : feature.properties.borderWidth;\r\n          },\r\n          borderColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderColor === undefined ? style.borderColor : feature.properties.borderColor;\r\n          },\r\n          texture: style.texture,\r\n          unit: style.unit,\r\n          animate: style.animate,\r\n          duration: style.duration\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './ScatterLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/ScatterLayer/ScatterLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=ScatterLayer.vue.mjs.map\n", "import ScatterLayer from './ScatterLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nScatterLayer.install = (app: App) => {\r\n  app.component(ScatterLayer.name, ScatterLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaScatter = ScatterLayer as typeof ScatterLayer & Plugin;\r\nexport default ElAmapLocaScatter;\r\n\r\nexport type ElAmapLocaScatterInstance = InstanceType<typeof ScatterLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaZMarker',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.ZMarkerLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.ZMarkerLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          unit: 'px',\r\n          content: '',\r\n          size: [20, 20],\r\n          rotation: 0,\r\n          alwaysFront: false,\r\n          altitude: 0\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          unit: style.unit,\r\n          content: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.content === undefined ? style.content : feature.properties.content;\r\n          },\r\n          size: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.size === undefined ? style.size : feature.properties.size;\r\n          },\r\n          rotation: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.rotation === undefined ? style.rotation : feature.properties.rotation;\r\n          },\r\n          alwaysFront: style.alwaysFront,\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude ;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './ZMarkerLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/ZMarkerLayer/ZMarkerLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=ZMarkerLayer.vue.mjs.map\n", "import ZMarkerLayer from './ZMarkerLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nZMarkerLayer.install = (app: App) => {\r\n  app.component(ZMarkerLayer.name, ZMarkerLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaZMarker = ZMarkerLayer as typeof ZMarkerLayer & Plugin;\r\nexport default ElAmapLocaZMarker;\r\n\r\nexport type ElAmapLocaZMarkerInstance = InstanceType<typeof ZMarkerLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister, buildProps} from \"@vuemap/vue-amap\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaAmbientLight',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(buildProps({\r\n  // 环境光颜色。\r\n  color: {\r\n    type: String\r\n  },\r\n  // 环境光强度。\r\n  intensity: {\r\n    type: Number\r\n  }, \r\n}));\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: any;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.AmbientLight, Loca.Container>((options, parentComponent) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.AmbientLight(options);\r\n    parentComponent.addLight($amapComponent);\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance.isDestroy){\r\n        parentInstance.$amapComponent.removeLight($amapComponent);\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './AmbientLight.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/AmbientLight/AmbientLight.vue\";\n\nexport { script as default };\n//# sourceMappingURL=AmbientLight.vue.mjs.map\n", "import AmbientLight from './AmbientLight.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nAmbientLight.install = (app: App) => {\r\n  app.component(AmbientLight.name, AmbientLight);\r\n  return app;\r\n};\r\nexport const ElAmapLocaAmbientLight = AmbientLight as typeof AmbientLight & Plugin;\r\nexport default ElAmapLocaAmbientLight;\r\n\r\nexport type ElAmapLocaAmbientLightInstance = InstanceType<typeof AmbientLight>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister, buildProps} from \"@vuemap/vue-amap\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaDirectionalLight',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(buildProps({\r\n  color: {\r\n    type: String\r\n  }, // 环境光颜色。\r\n  intensity: {\r\n    type: Number\r\n  }, // 环境光强度。\r\n  position: {\r\n    type: Array,\r\n    required: true\r\n  }, // 坐标位置\r\n  target: {\r\n    type: Array\r\n  }, // 光射向的目标位置\r\n}));\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: any;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.DirectionalLight, Loca.Container>((options, parentComponent) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.DirectionalLight(options);\r\n    parentComponent.addLight($amapComponent);\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance.isDestroy){\r\n        parentInstance.$amapComponent.removeLight($amapComponent);\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './DirectionalLight.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/DirectionalLight/DirectionalLight.vue\";\n\nexport { script as default };\n//# sourceMappingURL=DirectionalLight.vue.mjs.map\n", "import DirectionalLight from './DirectionalLight.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nDirectionalLight.install = (app: App) => {\r\n  app.component(DirectionalLight.name, DirectionalLight);\r\n  return app;\r\n};\r\nexport const ElAmapLocaDirectionalLight = DirectionalLight as typeof DirectionalLight & Plugin;\r\nexport default ElAmapLocaDirectionalLight;\r\n\r\nexport type ElAmapLocaDirectionalLightInstance = InstanceType<typeof DirectionalLight>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister, buildProps} from \"@vuemap/vue-amap\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPointLight',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(buildProps({\r\n  color: {\r\n    type: String\r\n  }, // 点光颜色。\r\n  intensity: {\r\n    type: Number\r\n  }, // 光照强度。\r\n  position: {\r\n    type: Array,\r\n    required: true\r\n  }, // 点光位置\r\n  distance: {\r\n    type: Number\r\n  }, // 距离表示从光源到光照强度为 0 的位置，0 就是光不会消失\r\n}));\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: any;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PointLight, Loca.Container>((options, parentComponent) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PointLight(options);\r\n    parentComponent.addLight($amapComponent);\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance.isDestroy){\r\n        parentInstance.$amapComponent.removeLight($amapComponent);\r\n      }\r\n      $amapComponent = null;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './PointLight.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/PointLight/PointLight.vue\";\n\nexport { script as default };\n//# sourceMappingURL=PointLight.vue.mjs.map\n", "import PointLight from './PointLight.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPointLight.install = (app: App) => {\r\n  app.component(PointLight.name, PointLight);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPointLight = PointLight as typeof PointLight & Plugin;\r\nexport default ElAmapLocaPointLight;\r\n\r\nexport type ElAmapLocaPointLightInstance = InstanceType<typeof PointLight>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaLaser',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }// 图层中的要素是否具有前后遮盖关系，默认开启\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.LaserLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.LaserLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          unit: 'px',\r\n          height: 200,\r\n          color: 'rgba(255,255,0,0.5)',\r\n          angle: 0,\r\n          lineWidth: 2,\r\n          trailLength: 30,\r\n          duration: 2000,\r\n          interval: 0,\r\n          delay: 0,\r\n          repeat: undefined\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          unit: style.unit,\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          color: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.color === undefined ? style.color : feature.properties.color;\r\n          },\r\n          angle: style.angle,\r\n          lineWidth: style.lineWidth,\r\n          trailLength: style.trailLength,\r\n          duration: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.duration === undefined ? style.duration : feature.properties.duration;\r\n          },\r\n          interval: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.interval === undefined ? style.interval : feature.properties.interval;\r\n          },\r\n          delay: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.delay === undefined ? style.delay : feature.properties.delay;\r\n          },\r\n          repeat: style.repeat\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import script from './LaserLayer.vue2.mjs';\n\nscript.__file = \"src/vue-amap-loca/packages/LaserLayer/LaserLayer.vue\";\n\nexport { script as default };\n//# sourceMappingURL=LaserLayer.vue.mjs.map\n", "import LaserLayer from './LaserLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nLaserLayer.install = (app: App) => {\r\n  app.component(LaserLayer.name, LaserLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaLaser = LaserLayer as typeof LaserLayer & Plugin;\r\nexport default ElAmapLocaLaser;\r\n\r\nexport type ElAmapLocaLaserInstance = InstanceType<typeof LaserLayer>\r\n", "import {ElAmapLocaGrid} from './packages/GridLayer'\r\nimport {ElAmapLocaHeatmap} from './packages/HeatMapLayer'\r\nimport {ElAmapLocaHexagon} from './packages/HexagonLayer'\r\nimport {ElAmapLocaIcon} from './packages/IconLayer'\r\nimport {ElAmapLocaLine} from './packages/LineLayer'\r\nimport {ElAmapLocaLink} from './packages/LinkLayer'\r\nimport {ElAmapLoca} from './packages/Loca'\r\nimport {ElAmapLocaPoint} from './packages/PointLayer'\r\nimport {ElAmapLocaPolygon} from './packages/PolygonLayer'\r\nimport {ElAmapLocaPrism} from './packages/PrismLayer'\r\nimport {ElAmapLocaPulseLine} from './packages/PulseLineLayer'\r\nimport {ElAmapLocaPulseLink} from './packages/PulseLinkLayer'\r\nimport {ElAmapLocaScatter} from './packages/ScatterLayer'\r\nimport {ElAmapLocaZMarker} from './packages/ZMarkerLayer'\r\nimport {ElAmapLocaAmbientLight} from './packages/AmbientLight'\r\nimport {ElAmapLocaDirectionalLight} from './packages/DirectionalLight'\r\nimport {ElAmapLocaPointLight} from './packages/PointLight'\r\nimport {ElAmapLocaLaser} from \"./packages/LaserLayer\"\r\n\r\nimport type { Plugin } from 'vue'\r\n\r\nexport default [\r\n  ElAmapLocaGrid,\r\n  ElAmapLocaHeatmap,\r\n  ElAmapLocaHexagon,\r\n  ElAmapLocaIcon,\r\n  ElAmapLocaLine,\r\n  ElAmapLocaLink,\r\n  ElAmapLoca,\r\n  ElAmapLocaPoint,\r\n  ElAmapLocaPolygon,\r\n  ElAmapLocaPrism,\r\n  ElAmapLocaPulseLine,\r\n  ElAmapLocaPulseLink,\r\n  ElAmapLocaScatter,\r\n  ElAmapLocaZMarker,\r\n  ElAmapLocaAmbientLight,\r\n  ElAmapLocaDirectionalLight,\r\n  ElAmapLocaPointLight,\r\n  ElAmapLocaLaser,\r\n] as Plugin[]\r\n", "import { makeInstaller } from '@vuemap/vue-amap';\r\nimport Components from './component';\r\n\r\nexport default makeInstaller([...Components]);\r\n", "import installer from './defaults';\r\nexport * from './packages';\r\nexport { default } from './defaults';\r\nexport const install = installer.install;\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;AA2Ba,IAAA,iBAAiB,CAA4C,UAIrE;AACH,SAAO,OAAO,OAAO,CAAA,GAAI,aAAa;IACpC,WAAW;MACT,MAAM;IAAA;IAER,YAAY;MACV,MAAM;IAAA;IAER,iBAAiB;MACf,MAAM,CAAC,aAAa,MAAM;MAC1B,UAAW;AACF,eAAA;MAAA;IACT;IAEF,YAAY;MACV,MAAM;IAAA;IAER,mBAAmB;MACjB,MAAM;MACN,UAAW;AACT,eAAO,CAAA;MAAC;IACV;IAEF,OAAO;MACL,MAAM;IAAA;IAER,SAAS;MACP,MAAM;IAAA;IAER,YAAY;MACV,MAAM;MACN,SAAS;IAAA;IAEX,iBAAiB;MACf,MAAM;MACN,SAAS;IAAA;IAEX,SAAS;MACP,MAAM;MACN,SAAS;IAAA;IAEX,aAAa;MACX,MAAM;MACN,SAAS;IAAA;IAEX,cAAc;MACZ,MAAM;MACN,SAAS;IAAA;EACX,GACe,KAAK;AACxB;AAEO,IAAM,kBAAkB,CAAC,QAAQ,aAAa,SAAS,YAAa;;;ACpEpE,SAAS,WAAY,SAAyB;AAC5C,SAAA;IACL,aAAc,OAAY;AACxB,eAAS,MAAM;AAlBrB,YAAA;AAmBQ,aAAI,KAAQ,QAAA,eAAA,MAAR,OAAA,SAAA,GAA0B,UAAU;AAC9B,kBAAA,eAAA,EAAiB,SAAS,KAAK;QAAA;MACzC,CACD,EAAE,KAAK;IAAA;IAEV,cAAc;AACZ,eAAS,MAAM;AACb,gBAAQ,UAAU;MAAA,CACnB,EAAE,KAAK;IAAA;IAEV,eAAe;AACb,eAAS,MAAM;AACb,gBAAQ,UAAU;MAAA,CACnB,EAAE,KAAK;IAAA;IAEV,oBAAoB;AAClB,eAAS,MAAM;AACb,gBAAQ,UAAU;MAAA,CACnB,EAAE,KAAK;IAAA;IAEV,UAAW,MAAe;AAClB,YAAA,iBAAiB,QAAQ,eAAe;AAC1C,WAAA,kBAAA,OAAA,SAAA,eAAgB,UAAQ,kBAAA,OAAA,SAAA,eAAgB,OAAM;AAC/C,SAAA,OAAO,eAAe,KAAK,QAAQ,MAAM,eAAe,IAAI,eAAe,KAAK,QAAQ,MAAM,eAAe;MAAA;IAChH;EACF;AAEJ;AAEO,SAAS,cAAe,SAM7B;AACA,MAAI,aAAa;AACjB,MAAI,aAAa;AACb,MAAA;AACJ,QAAM,EAAC,gBAAgB,gBAAgB,OAAO,OAAO,SAAA,IAAa;AAElE,QAAM,YAAY,MAAM;AACtB,QAAI,QAAQ;AACV,aAAO,QAAQ;AACN,eAAA;IAAA;AAEX,QAAI,MAAM,iBAAiB;AACtB,UAAA,OAAO,MAAM,oBAAoB,UAAS;AAClC,iBAAA,IAAI,KAAK,gBAAgB;UAChC,KAAK,MAAM;QAAA,CACZ;MAAA,OACE;AACM,iBAAA,IAAI,KAAK,gBAAgB;UAChC,MAAM,MAAM;QAAA,CACb;MAAA;IACH,WACQ,MAAM,WAAW;AAChB,eAAA,IAAI,KAAK,cAAc;QAC9B,KAAK,MAAM;MAAA,CACZ;IAAA,WACQ,MAAM,YAAY;AAClB,eAAA,IAAI,KAAK,cAAc;QAC9B,MAAM,MAAM;MAAA,CACb;IAAA,OACI;AACL,eAAS,IAAI,KAAK,cAAc,CAAA,CAC/B;IAAA;AAEH,mBAAe,UAAU,MAAM;EAAA;AAGjC,QAAM,eAAe,MAAM;AACzB,QAAI,MAAM,YAAY;AACT,iBAAA;IAAA;EACb;AAEF,QAAM,aAAa,MAAM;AACvB,QAAG,gBAAe;AACV,YAAA,MAAM,eAAe,OAAO;AAC/B,UAAA,MAAM,YAAY,MAAK;AACpB,YAAA,GAAG,SAAS,QAAQ;MAAA;AAEvB,UAAA,MAAM,gBAAgB,MAAK;AACxB,YAAA,GAAG,aAAa,YAAY;AAC5B,YAAA,GAAG,aAAa,SAAS;AACzB,YAAA,GAAG,WAAW,OAAO;AACrB,YAAA,GAAG,eAAe,WAAW;AAC7B,YAAA,GAAG,aAAa,SAAS;AACzB,YAAA,GAAG,YAAY,WAAW;MAAA;AAE7B,UAAA,MAAM,iBAAiB,MAAK;AACzB,YAAA,GAAG,cAAc,aAAa;MAAA;IACpC;EACF;AAEI,QAAA,WAAW,CAAC,MAAW;AACrB,UAAA,UAAU,YAAY,CAAC;AACvB,UAAA,SAAS,SAAS,CAAC;EAAA;AAErB,QAAA,gBAAgB,CAAC,MAAW;AAC1B,UAAA,UAAU,YAAY,CAAC;AACvB,UAAA,cAAc,SAAS,CAAC;EAAA;AAE1B,QAAA,eAAe,CAAC,MAAU;AAC9B,QAAG,cAAc,YAAW;AAC1B;IAAA;AAEI,UAAA,UAAU,YAAY,CAAC;AACvB,UAAA,aAAa,SAAS,CAAC;EAAA;AAEzB,QAAA,cAAc,CAAC,MAAU;AAC7B,WAAO,eAAe,aAAa,EAAE,MAAM,QAAA,CAAS;EAAA;AAEtD,QAAM,YAAa,MAAM;AACV,iBAAA;EAAA;AAEf,QAAM,UAAW,MAAM;AACR,iBAAA;EAAA;AAEf,QAAM,cAAe,MAAM;AACZ,iBAAA;AACA,iBAAA;EAAA;AAEf,QAAM,cAAe,MAAM;AACZ,iBAAA;EAAA;AAEf,QAAM,YAAa,MAAM;AACV,iBAAA;EAAA;AAEf,QAAM,eAAgB,MAAO;AAC3B,QAAG,gBAAe;AACV,YAAA,MAAM,eAAe,OAAO;AAC9B,UAAA,IAAI,SAAS,QAAQ;AACrB,UAAA,IAAI,cAAc,aAAa;AAC/B,UAAA,IAAI,aAAa,YAAY;AAC7B,UAAA,IAAI,aAAa,SAAS;AAC1B,UAAA,IAAI,WAAW,OAAO;AACtB,UAAA,IAAI,eAAe,WAAW;AAC9B,UAAA,IAAI,aAAa,SAAS;AAC1B,UAAA,IAAI,YAAY,WAAW;IAAA;EACjC;AAGQ,YAAA;AACD,WAAA;AACT,oBAAA,OAAA,SAAA,eAAgB,eAAe,IAAI,cAAA;AACtB,eAAA;AAEb,QAAM,oBAAoB,MAAM;AACjB,iBAAA;AACV,QAAA,EAAC,kBAAA,OAAA,SAAA,eAAgB,YAAU;AAC5B,wBAAA,OAAA,SAAA,eAAgB,eAAe,OAAO,cAAA;AACtC,qBAAe,QAAQ;IAAA;AAEzB,QAAI,QAAQ;AACV,aAAO,QAAQ;AACN,eAAA;IAAA;EACX;AAGK,SAAA;IACL;IACA;EAAA;AAEJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzKA,UAAM,QAAQ;AAyBd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA4C,CAAC,YAAY;AACxF,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,UAAU,OAAO;AAC3C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,UAAU;cACV,cAAc;cACd,iBAAiB;cACjB,UAAU;cACV,QAAQ;cACR,QAAQ;cACR,KAAK;cACL,MAAM;YAAA;AAER,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,cAAc,CAAC,OAAO,YAAY;AACxB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,iBAAiB,SAAY,MAAM,eAAe,QAAQ,WAAW;cAAA;cAEjG,iBAAiB,CAAC,OAAO,YAAY;AAC3B,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,oBAAoB,SAAY,MAAM,kBAAkB,QAAQ,WAAW;cAAA;cAEvG,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,QAAQ,CAAC,OAAO,YAAY;AAClB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,WAAW,SAAY,MAAM,SAAS,QAAQ,WAAW;cAAA;cAErF,QAAQ,MAAM;cACd,KAAK,MAAM;cACX,MAAM,MAAM;YAAA;AAEd,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAiB;UAAA;QAC3C,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;ACvHD,OAAO,SAAS;;;ACAhBA,OAAU,UAAU,CAAC,QAAa;AAC5B,MAAA,UAAUA,OAAU,MAAMA,MAAS;AAChC,SAAA;AACT;AACO,IAAM,iBAAiBA;;;;;;;;;;;;;;;;;ACQ9B,UAAM,QAAQ;AAMd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA+C,CAAC,YAAY;AAC3F,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,aAAa,OAAO;AAC9C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,QAAQ;cACR,OAAO;cACP,UAAU,EAAC,KAAK,QAAQ,MAAM,oBAAoB,KAAK,kBAAkB,KAAK,WAAW,GAAK,MAAK;cACnG,SAAS,CAAC,GAAG,CAAC;cACd,QAAQ;cACR,cAAc,CAAC,KAAK,KAAK,KAAK,GAAG;cACjC,KAAK;cACL,KAAK;cACL,MAAM;cACN,YAAY;YAAA;AAEd,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,QAAQ,CAAC,OAAO,YAAY;AAClB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,WAAW,SAAY,MAAM,SAAS,QAAQ,WAAW;cAAA;cAErF,OAAO,CAAC,OAAO,YAAY;AACjB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,UAAU,SAAY,MAAM,QAAQ,QAAQ,WAAW;cAAA;cAEnF,UAAU,MAAM;cAChB,SAAS,MAAM;cACf,QAAQ,MAAM;cACd,cAAc,MAAM;cACpB,KAAK,MAAM;cACX,KAAK,MAAM;cACX,MAAM,MAAM;cACZ,YAAY,MAAM;YAAA;AAEpB,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAiB;UAAA;QAC3C,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;AC/FDC,QAAO,SAAS;;;ACAhBC,QAAa,UAAU,CAAC,QAAa;AAC/B,MAAA,UAAUA,QAAa,MAAMA,OAAY;AACtC,SAAA;AACT;AACO,IAAM,oBAAoBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACQjC,UAAM,QAAQ;AAoBd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA+C,CAAC,YAAY;AAC3F,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,aAAa,OAAO;AAC9C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,UAAU;cACV,cAAc;cACd,iBAAiB;cACjB,UAAU;cACV,QAAQ;cACR,QAAQ;cACR,KAAK;cACL,MAAM;YAAA;AAER,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,cAAc,CAAC,OAAO,YAAY;AACxB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,iBAAiB,SAAY,MAAM,eAAe,QAAQ,WAAW;cAAA;cAEjG,iBAAiB,CAAC,OAAO,YAAY;AAC3B,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,oBAAoB,SAAY,MAAM,kBAAkB,QAAQ,WAAW;cAAA;cAEvG,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,QAAQ,CAAC,OAAO,YAAY;AAClB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,WAAW,SAAY,MAAM,SAAS,QAAQ,WAAW;cAAA;cAErF,QAAQ,MAAM;cACd,KAAK,MAAM;cACX,MAAM,MAAM;YAAA;AAEd,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;AClHDC,QAAO,SAAS;;;ACAhBC,QAAa,UAAU,CAAC,QAAa;AAC/B,MAAA,UAAUA,QAAa,MAAMA,OAAY;AACtC,SAAA;AACT;AACO,IAAM,oBAAoBA;;;;;;;;;;;;ACQjC,UAAM,QAAQ;AAEd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA4C,CAAC,YAAY;AACxF,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,UAAU,OAAO;AAC3C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,MAAM;cACN,MAAM;cACN,UAAU,CAAC,IAAI,EAAE;cACjB,UAAU;cACV,SAAS;cACT,QAAQ,CAAC,GAAG,CAAC;YAAA;AAEf,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,MAAM,MAAM;cACZ,MAAM,CAAC,OAAO,YAAY;AAChB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,SAAS,SAAY,MAAM,OAAO,QAAQ,WAAW;cAAA;cAEjF,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,SAAS,CAAC,OAAO,YAAY;AACnB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,YAAY,SAAY,MAAM,UAAU,QAAQ,WAAW;cAAA;cAEvF,QAAQ,CAAC,OAAO,YAAY;AAClB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,WAAW,SAAY,MAAM,SAAS,QAAQ,WAAW;cAAA;YACrF;AAEF,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;AC5FDC,QAAO,SAAS;;;ACAhBC,QAAU,UAAU,CAAC,QAAa;AAC5B,MAAA,UAAUA,QAAU,MAAMA,OAAS;AAChC,SAAA;AACT;AACO,IAAM,iBAAiBA;;;;;;;;;;;;ACQ9B,UAAM,QAAQ;AAEd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA4C,CAAC,YAAY;AACxF,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,UAAU,OAAO;AAC3C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,OAAO;cACP,WAAW;cACX,UAAU;cACV,aAAa;cACb,aAAa;cACb,WAAW,CAAC,IAAI,GAAG,IAAI,CAAC;YAAA;AAE1B,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,OAAO,CAAC,OAAO,YAAY;AACjB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,UAAU,SAAY,MAAM,QAAQ,QAAQ,WAAW;cAAA;cAEnF,WAAW,CAAC,OAAO,YAAY;AACrB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,cAAc,SAAY,MAAM,YAAY,QAAQ,WAAW;cAAA;cAE3F,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,aAAa,CAAC,OAAO,YAAY;AACvB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,gBAAgB,SAAY,MAAM,cAAc,QAAQ,WAAW;cAAA;cAE/F,aAAa,CAAC,OAAO,YAAY;AACvB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,gBAAgB,SAAY,MAAM,cAAc,QAAQ,WAAW;cAAA;cAE/F,WAAW,CAAC,OAAO,YAAY;AACrB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,cAAc,SAAY,MAAM,YAAY,QAAQ,WAAW;cAAA;YAC3F;AAEF,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;AC/FDC,QAAO,SAAS;;;ACAhBC,QAAU,UAAU,CAAC,QAAa;AAC5B,MAAA,UAAUA,QAAU,MAAMA,OAAS;AAChC,SAAA;AACT;AACO,IAAM,iBAAiBA;;;;;;;;;;;;ACQ9B,UAAM,QAAQ;AAEd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA4C,CAAC,YAAY;AACxF,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,UAAU,OAAO;AAC3C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,YAAY,CAAC,uBAAuB,qBAAqB;cACzD,QAAQ;cACR,aAAa;YAAA;AAEf,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,YAAY,CAAC,OAAO,YAAY;AACtB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,eAAe,SAAY,MAAM,aAAa,QAAQ,WAAW;cAAA;cAE7F,QAAQ,CAAC,OAAO,YAAY;AAClB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,WAAW,SAAY,MAAM,SAAS,QAAQ,WAAW;cAAA;cAErF,aAAa,CAAC,OAAO,YAAY;AACvB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,gBAAgB,SAAY,MAAM,cAAc,QAAQ,WAAW;cAAA;YAC/F;AAEF,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;AChFDC,QAAO,SAAS;;;ACAhBC,QAAU,UAAU,CAAC,QAAa;AAC5B,MAAA,UAAUA,QAAU,MAAMA,OAAS;AAChC,SAAA;AACT;AACO,IAAM,iBAAiBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACU9B,UAAM,qBAAoC,CAAA;AAC1C,UAAM,cAA2B;MAC/B,gBAAgB;MAChB,kBAAmB,IAAG;AACpB,2BAAmB,KAAK,EAAE;MAAA;MAE5B,WAAW;MACX,QAAQ,MAAM;AACZ,eAAO,OAAO;MAAA;IAChB;AAGF,YAAQ,YAAY,WAAW;AAE/B,UAAM,QAAQ;AA6Bd,UAAM,QAAQ;AAEV,QAAA;AACJ,QAAI,aAAa;AACjB,QAAI,aAAa;AACjB,QAAI,WAAW;AAEf,UAAM,EAAC,eAAe,eAAA,IAAkB,YAAsC,CAAC,SAAS,oBAAoB;AACnG,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,UAAU;UAClC,KAAK;QAAA,CACN;AACD,oBAAY,iBAAiB;AAC7B,YAAI,QAAQ,UAAU;AACpB,yBAAe,WAAW,QAAQ;QAAA;AAEpC,YAAI,QAAQ,UAAU;AACpB,yBAAe,WAAW,QAAQ;QAAA;AAEpC,YAAI,QAAQ,YAAY;AACtB,yBAAe,aAAa,QAAQ;QAAA;AAEtC,mBAAW,QAAQ,aAAa;AACrB,mBAAA;AACX,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA;MACA;MACA,mBAAoB;AAClB,YAAI,gBAAgB;AACL,uBAAA;AACb,cAAG,eAAe,WAAW,eAAe,QAAQ,MAAK;AACvD,2BAAe,QAAQ,KAAK;UAAA;AAE9B,yBAAe,QAAQ;AACN,2BAAA;QAAA;MACnB;IACF,CACD;AAED,UAAM,SAAS,MAAM;AACnB,aAAO,kBAAgB,OAAA,SAAA,eAAA;IAAA;AAGzB,UAAM,aAAa,MAAM;AACvB,UAAG,gBAAe;AAChB,cAAM,MAAM,OAAO;AAChB,YAAA,MAAM,YAAY,MAAK;AACpB,cAAA,GAAG,SAAS,QAAQ;QAAA;AAEvB,YAAA,MAAM,gBAAgB,MAAK;AACxB,cAAA,GAAG,aAAa,YAAY;AAC5B,cAAA,GAAG,aAAa,SAAS;AACzB,cAAA,GAAG,WAAW,OAAO;AACrB,cAAA,GAAG,eAAe,WAAW;AAC7B,cAAA,GAAG,aAAa,SAAS;AACzB,cAAA,GAAG,YAAY,WAAW;QAAA;AAE7B,YAAA,MAAM,iBAAiB,MAAK;AACzB,cAAA,GAAG,cAAc,aAAa;QAAA;MACpC;IACF;AAEI,UAAA,WAAW,CAAC,MAAM;AAChB,YAAA,WAAW,YAAY,CAAC;AACxB,YAAA,SAAS,UAAU,CAAC;IAAA;AAEtB,UAAA,gBAAgB,CAAC,MAAM;AACrB,YAAA,WAAW,YAAY,CAAC;AACxB,YAAA,cAAc,UAAU,CAAC;IAAA;AAE3B,UAAA,eAAe,CAAC,MAAM;AAC1B,UAAG,cAAc,YAAW;AAC1B;MAAA;AAEI,YAAA,WAAW,YAAY,CAAC;AACxB,YAAA,aAAa,UAAU,CAAC;IAAA;AAE1B,UAAA,cAAc,CAAC,MAAM;AACzB,YAAM,WAAkB,CAAA;AACxB,UAAG,eAAe,QAAO;AACvB,cAAM,SAAgB,CAAA;AACP,uBAAA,OAAO,QAAU,CAAK,MAAA;AACnC,iBAAO,KAAK,CAAC;QAAA,CACb;AACF,eAAO,KAAK,CAAC,GAAE,MAAM,EAAE,SAAS,EAAE,MAAM;AACxC,cAAM,WAAW,OAAO;AACxB,iBAAQ,IAAE,GAAE,IAAE,UAAS,KAAI;AACnB,gBAAA,OAAO,OAAO,CAAC,EAAE,aAAa,EAAE,MAAM,QAAA,CAAS;AACrD,cAAG,MAAK;AACN,qBAAS,KAAK,IAAI;AAClB,gBAAG,UAAS;AACV;YAAA;UACF;QACF;MACF;AAGK,aAAA;IAAA;AAET,UAAM,YAAY,MAAM;AACT,mBAAA;IAAA;AAEf,UAAM,UAAU,MAAM;AACP,mBAAA;IAAA;AAEf,UAAM,cAAc,MAAM;AACX,mBAAA;AACA,mBAAA;IAAA;AAEf,UAAM,cAAc,MAAM;AACX,mBAAA;IAAA;AAEf,UAAM,YAAY,MAAM;AACT,mBAAA;IAAA;AAEf,UAAM,eAAe,MAAM;AACzB,UAAG,gBAAe;AAChB,cAAM,MAAM,OAAO;AACf,YAAA,IAAI,SAAS,QAAQ;AACrB,YAAA,IAAI,cAAc,aAAa;AAC/B,YAAA,IAAI,aAAa,YAAY;AAC7B,YAAA,IAAI,aAAa,SAAS;AAC1B,YAAA,IAAI,WAAW,OAAO;AACtB,YAAA,IAAI,eAAe,WAAW;AAC9B,YAAA,IAAI,aAAa,SAAS;AAC1B,YAAA,IAAI,YAAY,WAAW;MAAA;IACjC;AAGW,aAAA;MACX;IAAA,CACD;;;;;;;;;;AChMDC,QAAO,SAAS;;;ACAhBC,QAAK,UAAU,CAAC,QAAa;AACvB,MAAA,UAAUA,QAAK,MAAMA,OAAI;AACtB,SAAA;AACT;AACO,IAAM,aAAaA;;;;;;;;;;;;;;;;;ACQ1B,UAAM,QAAQ;AAMd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA6C,CAAC,YAAY;AACzF,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,WAAW,OAAO;AAC5C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,QAAQ;cACR,OAAO;cACP,MAAM;cACN,aAAa;cACb,aAAa;cACb,WAAW;YAAA;AAEb,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,QAAQ,CAAC,OAAO,YAAY;AAClB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,WAAW,SAAY,MAAM,SAAS,QAAQ,WAAW;cAAA;cAErF,OAAO,CAAC,OAAO,YAAY;AACjB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,UAAU,SAAY,MAAM,QAAQ,QAAQ,WAAW;cAAA;cAEnF,MAAM,MAAM;cACZ,aAAa,CAAC,OAAO,YAAY;AACvB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,gBAAgB,SAAY,MAAM,cAAc,QAAQ,WAAW;cAAA;cAE/F,aAAa,CAAC,OAAO,YAAY;AACvB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,gBAAgB,SAAY,MAAM,cAAc,QAAQ,WAAW;cAAA;cAE/F,WAAW,CAAC,OAAO,YAAY;AACrB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,cAAc,SAAY,MAAM,YAAY,QAAQ,WAAW;cAAA;YAC3F;AAEF,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;AChGDC,QAAO,SAAS;;;ACAhBC,QAAW,UAAU,CAAC,QAAa;AAC7B,MAAA,UAAUA,QAAW,MAAMA,OAAU;AAClC,SAAA;AACT;AACO,IAAM,kBAAkBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACQ/B,UAAM,QAAQ;AA4Bd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA+C,CAAC,YAAY;AAC3F,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,aAAa,OAAO;AAC9C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,UAAU;cACV,cAAc;cACd,iBAAiB;cACjB,UAAU;cACV,QAAQ;cACR,SAAS;cACT,aAAa,CAAC,IAAI,CAAC;cACnB,OAAO;cACP,eAAe;YAAA;AAEjB,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,cAAc,CAAC,OAAO,YAAY;AACxB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,iBAAiB,SAAY,MAAM,eAAe,QAAQ,WAAW;cAAA;cAEjG,iBAAiB,CAAC,OAAO,YAAY;AAC3B,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,oBAAoB,SAAY,MAAM,kBAAkB,QAAQ,WAAW;cAAA;cAEvG,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,QAAQ,CAAC,OAAO,YAAY;AAClB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,WAAW,SAAY,MAAM,SAAS,QAAQ,WAAW;cAAA;cAErF,SAAS,MAAM;cACf,aAAa,CAAC,OAAO,YAAY;AACvB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,gBAAgB,SAAY,MAAM,cAAc,QAAQ,WAAW;cAAA;cAE/F,OAAO,CAAC,OAAO,YAAY;AACjB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,UAAU,SAAY,MAAM,QAAQ,QAAQ,WAAW;cAAA;cAEnF,eAAe,CAAC,OAAO,YAAY;AACzB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,kBAAkB,SAAY,MAAM,gBAAgB,QAAQ,WAAW;cAAA;YACnG;AAEF,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;ACrIDC,QAAO,SAAS;;;ACAhBC,QAAa,UAAU,CAAC,QAAa;AAC/B,MAAA,UAAUA,QAAa,MAAMA,OAAY;AACtC,SAAA;AACT;AACO,IAAM,oBAAoBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACQjC,UAAM,QAAQ;AAoBd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA6C,CAAC,YAAY;AACzF,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,WAAW,OAAO;AAC5C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,QAAQ;cACR,MAAM;cACN,YAAY;cACZ,UAAU;cACV,UAAU;cACV,QAAQ;cACR,UAAU;cACV,cAAc;cACd,iBAAiB;YAAA;AAEnB,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,QAAQ,MAAM;cACd,MAAM,MAAM;cACZ,YAAY,MAAM;cAClB,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,UAAU,MAAM;cAChB,QAAQ,CAAC,OAAO,YAAY;AAClB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,WAAW,SAAY,MAAM,SAAS,QAAQ,WAAW;cAAA;cAErF,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,cAAc,CAAC,OAAO,YAAY;AACxB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,iBAAiB,SAAY,MAAM,eAAe,QAAQ,WAAW;cAAA;cAEjG,iBAAiB,CAAC,OAAO,YAAY;AAC3B,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,oBAAoB,SAAY,MAAM,kBAAkB,QAAQ,WAAW;cAAA;YACvG;AAEF,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;ACpHDC,SAAO,SAAS;;;ACAhBC,SAAW,UAAU,CAAC,QAAa;AAC7B,MAAA,UAAUA,SAAW,MAAMA,QAAU;AAClC,SAAA;AACT;AACO,IAAM,kBAAkBA;;;;;;;;;;;;;;;;;ACQ/B,UAAM,QAAQ;AAMd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAAiD,CAAC,YAAY;AAC7F,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,eAAe,OAAO;AAChD,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,WAAW;cACX,WAAW;cACX,YAAY;cACZ,UAAU;cACV,UAAU;cACV,UAAU;YAAA;AAEZ,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,WAAW,CAAC,OAAO,YAAY;AACrB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,cAAc,SAAY,MAAM,YAAY,QAAQ,WAAW;cAAA;cAE3F,WAAW,CAAC,OAAO,YAAY;AACrB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,cAAc,SAAY,MAAM,YAAY,QAAQ,WAAW;cAAA;cAE3F,YAAY,CAAC,OAAO,YAAY;AACtB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,eAAe,SAAY,MAAM,aAAa,QAAQ,WAAW;cAAA;cAE7F,UAAU,MAAM;cAChB,UAAU,MAAM;cAChB,UAAU,MAAM;YAAA;AAElB,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;AC1FDC,SAAO,SAAS;;;ACAhBC,SAAe,UAAU,CAAC,QAAa;AACjC,MAAA,UAAUA,SAAe,MAAMA,QAAc;AAC1C,SAAA;AACT;AACO,IAAM,sBAAsBA;;;;;;;;;;;;;;;;;ACQnC,UAAM,QAAQ;AAMd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAAiD,CAAC,YAAY;AAC7F,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,eAAe,OAAO;AAChD,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,YAAY,CAAC,MAAM;cACnB,QAAQ;cACR,gBAAgB;cAChB,aAAa;cACb,WAAW,CAAC,GAAG,CAAC;cAChB,MAAM;cACN,MAAM,CAAC,KAAM,GAAG,KAAM,CAAC;cACvB,OAAO;cACP,WAAW;cACX,YAAY;cACZ,YAAY;YAAA;AAEd,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,YAAY,CAAC,OAAO,YAAY;AACtB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,eAAe,SAAY,MAAM,aAAa,QAAQ,WAAW;cAAA;cAE7F,QAAQ,CAAC,OAAO,YAAY;AAClB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,WAAW,SAAY,MAAM,SAAS,QAAQ,WAAW;cAAA;cAErF,gBAAgB,CAAC,OAAO,YAAY;AAC1B,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,mBAAmB,SAAY,MAAM,iBAAiB,QAAQ,WAAW;cAAA;cAErG,aAAa,CAAC,OAAO,YAAY;AACvB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,gBAAgB,SAAY,MAAM,cAAc,QAAQ,WAAW;cAAA;cAE/F,WAAW,CAAC,OAAO,YAAY;AACrB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,cAAc,SAAY,MAAM,YAAY,QAAQ,WAAW;cAAA;cAE3F,MAAM,MAAM;cACZ,MAAM,CAAC,OAAO,YAAY;AAChB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,SAAS,SAAY,MAAM,OAAO,QAAQ,WAAW;cAAA;cAEjF,OAAO,CAAC,OAAO,YAAY;AACjB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,UAAU,SAAY,MAAM,QAAQ,QAAQ,WAAW;cAAA;cAEnF,WAAW,CAAC,OAAO,YAAY;AACrB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,cAAc,SAAY,MAAM,YAAY,QAAQ,WAAW;cAAA;cAE3F,YAAY,CAAC,OAAO,YAAY;AACtB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,eAAe,SAAY,MAAM,aAAa,QAAQ,WAAW;cAAA;cAE7F,YAAY,MAAM;YAAA;AAEpB,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;ACtHDC,SAAO,SAAS;;;ACAhBC,SAAe,UAAU,CAAC,QAAa;AACjC,MAAA,UAAUA,SAAe,MAAMA,QAAc;AAC1C,SAAA;AACT;AACO,IAAM,sBAAsBA;;;;;;;;;;;;ACQnC,UAAM,QAAQ;AAEd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA+C,CAAC,YAAY;AAC3F,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,aAAa,OAAO;AAC9C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,MAAM,CAAC,IAAI,EAAE;cACb,UAAU;cACV,OAAO;cACP,UAAU;cACV,aAAa;cACb,aAAa;cACb,SAAS;cACT,MAAM;cACN,SAAS;cACT,UAAU;YAAA;AAEZ,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,MAAM,CAAC,OAAO,YAAY;AAChB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,SAAS,SAAY,MAAM,OAAO,QAAQ,WAAW;cAAA;cAEjF,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,OAAO,CAAC,OAAO,YAAY;AACjB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,UAAU,SAAY,MAAM,QAAQ,QAAQ,WAAW;cAAA;cAEnF,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,aAAa,CAAC,OAAO,YAAY;AACvB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,gBAAgB,SAAY,MAAM,cAAc,QAAQ,WAAW;cAAA;cAE/F,aAAa,CAAC,OAAO,YAAY;AACvB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,gBAAgB,SAAY,MAAM,cAAc,QAAQ,WAAW;cAAA;cAE/F,SAAS,MAAM;cACf,MAAM,MAAM;cACZ,SAAS,MAAM;cACf,UAAU,MAAM;YAAA;AAElB,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;ACvGDC,SAAO,SAAS;;;ACAhBC,SAAa,UAAU,CAAC,QAAa;AAC/B,MAAA,UAAUA,SAAa,MAAMA,QAAY;AACtC,SAAA;AACT;AACO,IAAM,oBAAoBA;;;;;;;;;;;;ACQjC,UAAM,QAAQ;AAEd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA+C,CAAC,YAAY;AAC3F,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,aAAa,OAAO;AAC9C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,MAAM;cACN,SAAS;cACT,MAAM,CAAC,IAAI,EAAE;cACb,UAAU;cACV,aAAa;cACb,UAAU;YAAA;AAEZ,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,MAAM,MAAM;cACZ,SAAS,CAAC,OAAO,YAAY;AACnB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,YAAY,SAAY,MAAM,UAAU,QAAQ,WAAW;cAAA;cAEvF,MAAM,CAAC,OAAO,YAAY;AAChB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,SAAS,SAAY,MAAM,OAAO,QAAQ,WAAW;cAAA;cAEjF,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,aAAa,MAAM;cACnB,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;YACzF;AAEF,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;ACzFDC,SAAO,SAAS;;;ACAhBC,SAAa,UAAU,CAAC,QAAa;AAC/B,MAAA,UAAUA,SAAa,MAAMA,QAAY;AACtC,SAAA;AACT;AACO,IAAM,oBAAoBA;;;;;;;;;;;;;;;;;;;;;ACgBjC,UAAM,QAAQ;AAEV,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA+C,CAAC,SAAS,oBAAoB;AAC5G,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,aAAa,OAAO;AAC9C,wBAAgB,SAAS,cAAc;AACvC,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,mBAAoB;AACd,YAAA,mBAAkB,kBAAA,OAAA,SAAA,eAAgB,iBAAgB;AACjD,cAAA,CAAC,eAAe,WAAU;AACZ,2BAAA,eAAe,YAAY,cAAc;UAAA;AAEzC,2BAAA;QAAA;MACnB;IACF,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;AC7CDC,SAAO,SAAS;;;ACAhBC,SAAa,UAAU,CAAC,QAAa;AAC/B,MAAA,UAAUA,SAAa,MAAMA,QAAY;AACtC,SAAA;AACT;AACO,IAAM,yBAAyBA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqBtC,UAAM,QAAQ;AAEV,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAAmD,CAAC,SAAS,oBAAoB;AAChH,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,iBAAiB,OAAO;AAClD,wBAAgB,SAAS,cAAc;AACvC,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,mBAAoB;AACd,YAAA,mBAAkB,kBAAA,OAAA,SAAA,eAAgB,iBAAgB;AACjD,cAAA,CAAC,eAAe,WAAU;AACZ,2BAAA,eAAe,YAAY,cAAc;UAAA;AAEzC,2BAAA;QAAA;MACnB;IACF,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;AClDDC,SAAO,SAAS;;;ACAhBC,SAAiB,UAAU,CAAC,QAAa;AACnC,MAAA,UAAUA,SAAiB,MAAMA,QAAgB;AAC9C,SAAA;AACT;AACO,IAAM,6BAA6BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqB1C,UAAM,QAAQ;AAEV,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA6C,CAAC,SAAS,oBAAoB;AAC1G,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,WAAW,OAAO;AAC5C,wBAAgB,SAAS,cAAc;AACvC,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,mBAAoB;AACd,YAAA,mBAAkB,kBAAA,OAAA,SAAA,eAAgB,iBAAgB;AACjD,cAAA,CAAC,eAAe,WAAU;AACZ,2BAAA,eAAe,YAAY,cAAc;UAAA;AAEzC,2BAAA;QAAA;MACnB;IACF,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;AClDDC,SAAO,SAAS;;;ACAhBC,SAAW,UAAU,CAAC,QAAa;AAC7B,MAAA,UAAUA,SAAW,MAAMA,QAAU;AAClC,SAAA;AACT;AACO,IAAM,uBAAuBA;;;;;;;;;;;;;;;;;;ACQpC,UAAM,QAAQ;AAMd,UAAM,QAAQ;AAEV,QAAA;AAEA,QAAA;AACA,QAAA;AAEJ,UAAM,EAAC,eAAe,eAAA,IAAkB,YAA6C,CAAC,YAAY;AACzF,aAAA,IAAI,QAAa,CAAC,YAAY;AAClB,yBAAA,IAAI,KAAK,WAAW,OAAO;AAC5C,cAAM,YAAY,cAAc;UAC9B;UACA;UACA;UACA;UACA,WAAY;AACV,kBAAM,oBAAoB;cACxB,MAAM;cACN,QAAQ;cACR,OAAO;cACP,OAAO;cACP,WAAW;cACX,aAAa;cACb,UAAU;cACV,UAAU;cACV,OAAO;cACP,QAAQ;YAAA;AAEV,kBAAM,QAAQ,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,iBAAiB;AAC1E,kBAAM,oBAAoB;cACxB,MAAM,MAAM;cACZ,QAAQ,CAAC,OAAO,YAAY;AAClB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,WAAW,SAAY,MAAM,SAAS,QAAQ,WAAW;cAAA;cAErF,OAAO,CAAC,OAAO,YAAY;AACjB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,UAAU,SAAY,MAAM,QAAQ,QAAQ,WAAW;cAAA;cAEnF,OAAO,MAAM;cACb,WAAW,MAAM;cACjB,aAAa,MAAM;cACnB,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,UAAU,CAAC,OAAO,YAAY;AACpB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,aAAa,SAAY,MAAM,WAAW,QAAQ,WAAW;cAAA;cAEzF,OAAO,CAAC,OAAO,YAAY;AACjB,wBAAA,aAAa,QAAQ,cAAc,CAAA;AAC3C,uBAAO,QAAQ,WAAW,UAAU,SAAY,MAAM,QAAQ,QAAQ,WAAW;cAAA;cAEnF,QAAQ,MAAM;YAAA;AAEhB,kBAAM,aAAa,OAAO,OAAO,CAAA,GAAI,mBAAmB,MAAM,UAAU;AACxE,2BAAe,SAAS,UAAU;UAAA;QACpC,CACD;AACD,4BAAoB,UAAU;AAC9B,qBAAa,UAAU;AACvB,gBAAQ,cAAc;MAAA,CACvB;IAAA,GAEA;MACD;MACA,iBAAiB,WAAW;QAC1B,YAAY;AACV,cAAG,YAAW;AACD,uBAAA;UAAA;QACb;QAEF,gBAAgB,MAAM;QACtB;MAAA,CACD;MACD,mBAAoB;AAClB,YAAG,mBAAkB;AACD,4BAAA;QAAA;AAEH,yBAAA;MAAA;IACnB,CACD;AAEY,aAAA;MACX;IAAA,CACD;;;;;;;;ACxGDC,SAAO,SAAS;;;ACAhBC,SAAW,UAAU,CAAC,QAAa;AAC7B,MAAA,UAAUA,SAAW,MAAMA,QAAU;AAClC,SAAA;AACT;AACO,IAAM,kBAAkBA;;;ACe/B,IAAA,aAAe;EACb;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF;;;ACrCA,IAAA,YAAe,cAAc,CAAC,GAAG,UAAU,CAAC;;;ACArC,IAAM,UAAU,UAAU;", "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "script", "HeatMapLayer", "script", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "script", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "script", "LineLayer", "script", "<PERSON><PERSON><PERSON><PERSON>", "script", "Loca", "script", "<PERSON><PERSON><PERSON><PERSON>", "script", "PolygonLayer", "script", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "script", "PulseLineLayer", "script", "PulseLinkLayer", "script", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "script", "ZMarker<PERSON><PERSON><PERSON>", "script", "AmbientLight", "script", "DirectionalLight", "script", "PointLight", "script", "Laser<PERSON><PERSON>er"]}