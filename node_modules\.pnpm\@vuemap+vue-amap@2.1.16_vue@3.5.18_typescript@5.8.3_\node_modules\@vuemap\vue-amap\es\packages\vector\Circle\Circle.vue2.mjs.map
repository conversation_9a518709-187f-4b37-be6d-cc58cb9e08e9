{"version": 3, "file": "Circle.vue2.mjs", "sources": ["../../../../../packages/vector/Circle/Circle.vue"], "sourcesContent": ["<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions, useAttrs} from 'vue';\r\nimport {debounce} from \"lodash-es\";\r\nimport {useRegister} from \"../../../mixins\";\r\nimport {useEditor} from \"../../../mixins/useEditor\";\r\nimport {\r\n  isMapInstance,\r\n  isOverlayGroupInstance,\r\n  isVectorLayerInstance\r\n} from \"../../../utils\";\r\nimport {propsTypes} from './props';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapCircle',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(propsTypes);\r\nconst emits = defineEmits(['init','update:center', 'update:radius']);\r\n\r\nlet $amapComponent: AMap.Circle;\r\nlet destroying = false;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<AMap.Circle, any>((options, parentComponent) => {\r\n  return new Promise<AMap.Circle>((resolve) => {\r\n    $amapComponent = new AMap.Circle(options);\r\n    if (isMapInstance(parentComponent)) {\r\n      parentComponent.add($amapComponent);\r\n    } else if (isOverlayGroupInstance(parentComponent)) {\r\n      parentComponent.addOverlay($amapComponent);\r\n    } else if (isVectorLayerInstance(parentComponent)) {\r\n      parentComponent.add($amapComponent);\r\n    }\r\n    bindModelEvents();\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: {\r\n    __zIndex (value: number) {\r\n      if($amapComponent){\r\n        $amapComponent.setOptions({zIndex: value});\r\n      }\r\n    },\r\n    __strokeColor (value: string) {\r\n      if($amapComponent){\r\n        $amapComponent.setOptions({strokeColor: value});\r\n      }\r\n    },\r\n    __strokeOpacity (value: number) {\r\n      if($amapComponent){\r\n        $amapComponent.setOptions({strokeOpacity: value});\r\n      }\r\n    },\r\n    __strokeWeight (value: number) {\r\n      if($amapComponent){\r\n        $amapComponent.setOptions({strokeWeight: value});\r\n      }\r\n    },\r\n    __fillColor (value: string) {\r\n      if($amapComponent){\r\n        $amapComponent.setOptions({fillColor: value});\r\n      }\r\n    },\r\n    __fillOpacity (value: number) {\r\n      if($amapComponent){\r\n        $amapComponent.setOptions({fillOpacity: value});\r\n      }\r\n    },\r\n    __strokeStyle (value: \"solid\" | \"dashed\" | undefined) {\r\n      if($amapComponent){\r\n        $amapComponent.setOptions({strokeStyle: value});\r\n      }\r\n    },\r\n    __strokeDasharray (value: any) {\r\n      if($amapComponent){\r\n        $amapComponent.setOptions({strokeDasharray: value});\r\n      }\r\n    },\r\n    __editable (flag: boolean) {\r\n      createEditor().then(() => {\r\n        flag ? resetEditor() : editor.close();\r\n      });\r\n    },\r\n    __center (center: [number, number]) {\r\n      if($amapComponent){\r\n        $amapComponent.setCenter(center);\r\n        resetEditor();\r\n      }\r\n    },\r\n    __radius (radius: number) {\r\n      if($amapComponent){\r\n        $amapComponent.setRadius(radius);\r\n        resetEditor();\r\n      }\r\n    }\r\n  },\r\n  destroyComponent () {\r\n    destroying = true;\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if (editor) {\r\n        if(!parentInstance.isDestroy){\r\n          editor.close();\r\n        }\r\n        editor = null as any;\r\n      }\r\n      if(!parentInstance.isDestroy){\r\n        if (isMapInstance(parentInstance.$amapComponent)) {\r\n          parentInstance.$amapComponent.remove($amapComponent);\r\n        } else if (isOverlayGroupInstance(parentInstance.$amapComponent)) {\r\n          parentInstance.$amapComponent.removeOverlay($amapComponent);\r\n        } else if (isVectorLayerInstance(parentInstance.$amapComponent)) {\r\n          parentInstance.$amapComponent.remove($amapComponent);\r\n        }\r\n      }\r\n      if($amapComponent.destroy){\r\n        $amapComponent.destroy();\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\nconst resetEditor = debounce(() => {\r\n  if(editor && props.editable){\r\n    editor.close();\r\n    editor.setTarget();\r\n    editor.setTarget($amapComponent);\r\n    editor.open();\r\n  }\r\n}, 50);\r\n\r\nconst bindModelEvents = () => {\r\n  $amapComponent.on('dragend',() => {\r\n    emitModel($amapComponent);\r\n  });\r\n  $amapComponent.on('touchend',() => {\r\n    emitModel($amapComponent);\r\n  });\r\n};\r\nconst emitModel = (target: AMap.Circle) => {\r\n  if(destroying){\r\n    return;\r\n  }\r\n  emits('update:center', target.getCenter().toArray());\r\n  emits('update:radius', target.getRadius());\r\n};\r\n\r\nlet editor: AMap.CircleEditor;\r\nconst attrs = useAttrs();\r\nconst createEditor = () => {\r\n  return new Promise<void>((resolve) => {\r\n    if (editor) {\r\n      resolve();\r\n    } else {\r\n      AMap.plugin(['AMap.CircleEditor'], () => {\r\n        editor = new AMap.CircleEditor(parentInstance?.$amapComponent, $amapComponent, props.editOptions);\r\n        useEditor(editor, attrs);\r\n        bindEditorModelEvents();\r\n        resolve();\r\n      });\r\n    }\r\n  });\r\n};\r\nconst bindEditorModelEvents = () => {\r\n  editor.on('addnode',(e) => {\r\n    emitModel(e.target);\r\n  });\r\n  editor.on('adjust',(e) => {\r\n    emitModel(e.target);\r\n  });\r\n  editor.on('removenode',(e) => {\r\n    emitModel(e.target);\r\n  });\r\n  editor.on('add',(e) => {\r\n    emitModel(e.target);\r\n  });\r\n  // editor.on('end',(e) => {\r\n  //   emitModel(e.target);\r\n  // });\r\n  editor.on('move',(e) => {\r\n    emitModel(e.target);\r\n  });\r\n};\r\n\r\ndefineExpose({\r\n  $$getInstance\r\n});\r\n\r\n</script>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAoBA,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AACd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAI,IAAA,cAAA,CAAA;AACJ,IAAA,IAAI,UAAa,GAAA,KAAA,CAAA;AAEjB,IAAA,MAAM,EAAC,aAAe,EAAA,cAAA,KAAkB,WAA8B,CAAA,CAAC,SAAS,eAAoB,KAAA;AAClG,MAAO,OAAA,IAAI,OAAqB,CAAA,CAAC,OAAY,KAAA;AAC3C,QAAiB,cAAA,GAAA,IAAI,IAAK,CAAA,MAAA,CAAO,OAAO,CAAA,CAAA;AACxC,QAAI,IAAA,aAAA,CAAc,eAAe,CAAG,EAAA;AAClC,UAAA,eAAA,CAAgB,IAAI,cAAc,CAAA,CAAA;AAAA,SACpC,MAAA,IAAW,sBAAuB,CAAA,eAAe,CAAG,EAAA;AAClD,UAAA,eAAA,CAAgB,WAAW,cAAc,CAAA,CAAA;AAAA,SAC3C,MAAA,IAAW,qBAAsB,CAAA,eAAe,CAAG,EAAA;AACjD,UAAA,eAAA,CAAgB,IAAI,cAAc,CAAA,CAAA;AAAA,SACpC;AACA,QAAgB,eAAA,EAAA,CAAA;AAChB,QAAA,OAAA,CAAQ,cAAc,CAAA,CAAA;AAAA,OACvB,CAAA,CAAA;AAAA,KAEA,EAAA;AAAA,MACD,KAAA;AAAA,MACA,eAAiB,EAAA;AAAA,QACf,SAAU,KAAe,EAAA;AACvB,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,UAAW,CAAA,EAAC,MAAQ,EAAA,KAAA,EAAM,CAAA,CAAA;AAAA,WAC3C;AAAA,SACF;AAAA,QACA,cAAe,KAAe,EAAA;AAC5B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,UAAW,CAAA,EAAC,WAAa,EAAA,KAAA,EAAM,CAAA,CAAA;AAAA,WAChD;AAAA,SACF;AAAA,QACA,gBAAiB,KAAe,EAAA;AAC9B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,UAAW,CAAA,EAAC,aAAe,EAAA,KAAA,EAAM,CAAA,CAAA;AAAA,WAClD;AAAA,SACF;AAAA,QACA,eAAgB,KAAe,EAAA;AAC7B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,UAAW,CAAA,EAAC,YAAc,EAAA,KAAA,EAAM,CAAA,CAAA;AAAA,WACjD;AAAA,SACF;AAAA,QACA,YAAa,KAAe,EAAA;AAC1B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,UAAW,CAAA,EAAC,SAAW,EAAA,KAAA,EAAM,CAAA,CAAA;AAAA,WAC9C;AAAA,SACF;AAAA,QACA,cAAe,KAAe,EAAA;AAC5B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,UAAW,CAAA,EAAC,WAAa,EAAA,KAAA,EAAM,CAAA,CAAA;AAAA,WAChD;AAAA,SACF;AAAA,QACA,cAAe,KAAuC,EAAA;AACpD,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,UAAW,CAAA,EAAC,WAAa,EAAA,KAAA,EAAM,CAAA,CAAA;AAAA,WAChD;AAAA,SACF;AAAA,QACA,kBAAmB,KAAY,EAAA;AAC7B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,UAAW,CAAA,EAAC,eAAiB,EAAA,KAAA,EAAM,CAAA,CAAA;AAAA,WACpD;AAAA,SACF;AAAA,QACA,WAAY,IAAe,EAAA;AACzB,UAAa,YAAA,EAAA,CAAE,KAAK,MAAM;AACxB,YAAO,IAAA,GAAA,WAAA,EAAgB,GAAA,MAAA,CAAO,KAAM,EAAA,CAAA;AAAA,WACrC,CAAA,CAAA;AAAA,SACH;AAAA,QACA,SAAU,MAA0B,EAAA;AAClC,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,UAAU,MAAM,CAAA,CAAA;AAC/B,YAAY,WAAA,EAAA,CAAA;AAAA,WACd;AAAA,SACF;AAAA,QACA,SAAU,MAAgB,EAAA;AACxB,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,UAAU,MAAM,CAAA,CAAA;AAC/B,YAAY,WAAA,EAAA,CAAA;AAAA,WACd;AAAA,SACF;AAAA,OACF;AAAA,MACA,gBAAoB,GAAA;AAClB,QAAa,UAAA,GAAA,IAAA,CAAA;AACb,QAAI,IAAA,cAAA,KAAkB,iDAAgB,cAAgB,CAAA,EAAA;AACpD,UAAA,IAAI,MAAQ,EAAA;AACV,YAAG,IAAA,CAAC,eAAe,SAAU,EAAA;AAC3B,cAAA,MAAA,CAAO,KAAM,EAAA,CAAA;AAAA,aACf;AACA,YAAS,MAAA,GAAA,IAAA,CAAA;AAAA,WACX;AACA,UAAG,IAAA,CAAC,eAAe,SAAU,EAAA;AAC3B,YAAI,IAAA,aAAA,CAAc,cAAe,CAAA,cAAc,CAAG,EAAA;AAChD,cAAe,cAAA,CAAA,cAAA,CAAe,OAAO,cAAc,CAAA,CAAA;AAAA,aAC1C,MAAA,IAAA,sBAAA,CAAuB,cAAe,CAAA,cAAc,CAAG,EAAA;AAChE,cAAe,cAAA,CAAA,cAAA,CAAe,cAAc,cAAc,CAAA,CAAA;AAAA,aACjD,MAAA,IAAA,qBAAA,CAAsB,cAAe,CAAA,cAAc,CAAG,EAAA;AAC/D,cAAe,cAAA,CAAA,cAAA,CAAe,OAAO,cAAc,CAAA,CAAA;AAAA,aACrD;AAAA,WACF;AACA,UAAA,IAAG,eAAe,OAAQ,EAAA;AACxB,YAAA,cAAA,CAAe,OAAQ,EAAA,CAAA;AAAA,WACzB;AACA,UAAiB,cAAA,GAAA,IAAA,CAAA;AAAA,SACnB;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAG,IAAA,MAAA,IAAU,MAAM,QAAS,EAAA;AAC1B,QAAA,MAAA,CAAO,KAAM,EAAA,CAAA;AACb,QAAA,MAAA,CAAO,SAAU,EAAA,CAAA;AACjB,QAAA,MAAA,CAAO,UAAU,cAAc,CAAA,CAAA;AAC/B,QAAA,MAAA,CAAO,IAAK,EAAA,CAAA;AAAA,OACd;AAAA,OACC,EAAE,CAAA,CAAA;AAEL,IAAA,MAAM,kBAAkB,MAAM;AAC5B,MAAe,cAAA,CAAA,EAAA,CAAG,WAAU,MAAM;AAChC,QAAA,SAAA,CAAU,cAAc,CAAA,CAAA;AAAA,OACzB,CAAA,CAAA;AACD,MAAe,cAAA,CAAA,EAAA,CAAG,YAAW,MAAM;AACjC,QAAA,SAAA,CAAU,cAAc,CAAA,CAAA;AAAA,OACzB,CAAA,CAAA;AAAA,KACH,CAAA;AACA,IAAM,MAAA,SAAA,GAAY,CAAC,MAAwB,KAAA;AACzC,MAAA,IAAG,UAAW,EAAA;AACZ,QAAA,OAAA;AAAA,OACF;AACA,MAAA,KAAA,CAAM,eAAiB,EAAA,MAAA,CAAO,SAAU,EAAA,CAAE,SAAS,CAAA,CAAA;AACnD,MAAM,KAAA,CAAA,eAAA,EAAiB,MAAO,CAAA,SAAA,EAAW,CAAA,CAAA;AAAA,KAC3C,CAAA;AAEA,IAAI,IAAA,MAAA,CAAA;AACJ,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AACvB,IAAA,MAAM,eAAe,MAAM;AACzB,MAAO,OAAA,IAAI,OAAc,CAAA,CAAC,OAAY,KAAA;AACpC,QAAA,IAAI,MAAQ,EAAA;AACV,UAAQ,OAAA,EAAA,CAAA;AAAA,SACH,MAAA;AACL,UAAA,IAAA,CAAK,MAAO,CAAA,CAAC,mBAAmB,CAAA,EAAG,MAAM;AACvC,YAAA,MAAA,GAAS,IAAI,IAAK,CAAA,YAAA,CAAa,iDAAgB,cAAgB,EAAA,cAAA,EAAgB,MAAM,WAAW,CAAA,CAAA;AAChG,YAAA,SAAA,CAAU,QAAQ,KAAK,CAAA,CAAA;AACvB,YAAsB,qBAAA,EAAA,CAAA;AACtB,YAAQ,OAAA,EAAA,CAAA;AAAA,WACT,CAAA,CAAA;AAAA,SACH;AAAA,OACD,CAAA,CAAA;AAAA,KACH,CAAA;AACA,IAAA,MAAM,wBAAwB,MAAM;AAClC,MAAO,MAAA,CAAA,EAAA,CAAG,SAAU,EAAA,CAAC,CAAM,KAAA;AACzB,QAAA,SAAA,CAAU,EAAE,MAAM,CAAA,CAAA;AAAA,OACnB,CAAA,CAAA;AACD,MAAO,MAAA,CAAA,EAAA,CAAG,QAAS,EAAA,CAAC,CAAM,KAAA;AACxB,QAAA,SAAA,CAAU,EAAE,MAAM,CAAA,CAAA;AAAA,OACnB,CAAA,CAAA;AACD,MAAO,MAAA,CAAA,EAAA,CAAG,YAAa,EAAA,CAAC,CAAM,KAAA;AAC5B,QAAA,SAAA,CAAU,EAAE,MAAM,CAAA,CAAA;AAAA,OACnB,CAAA,CAAA;AACD,MAAO,MAAA,CAAA,EAAA,CAAG,KAAM,EAAA,CAAC,CAAM,KAAA;AACrB,QAAA,SAAA,CAAU,EAAE,MAAM,CAAA,CAAA;AAAA,OACnB,CAAA,CAAA;AAID,MAAO,MAAA,CAAA,EAAA,CAAG,MAAO,EAAA,CAAC,CAAM,KAAA;AACtB,QAAA,SAAA,CAAU,EAAE,MAAM,CAAA,CAAA;AAAA,OACnB,CAAA,CAAA;AAAA,KACH,CAAA;AAEA,IAAa,QAAA,CAAA;AAAA,MACX,aAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;"}