{"version": 3, "sources": ["../../.pnpm/bignumber.js@9.3.1/node_modules/bignumber.js/bignumber.js", "../../.pnpm/json-bigint@1.0.0/node_modules/json-bigint/lib/stringify.js", "../../.pnpm/json-bigint@1.0.0/node_modules/json-bigint/lib/parse.js", "../../.pnpm/json-bigint@1.0.0/node_modules/json-bigint/index.js"], "sourcesContent": [";(function (globalObject) {\r\n  'use strict';\r\n\r\n/*\r\n *      bignumber.js v9.3.1\r\n *      A JavaScript library for arbitrary-precision arithmetic.\r\n *      https://github.com/MikeMcl/bignumber.js\r\n *      Copyright (c) 2025 <PERSON> <<EMAIL>>\r\n *      MIT Licensed.\r\n *\r\n *      BigNumber.prototype methods     |  BigNumber methods\r\n *                                      |\r\n *      absoluteValue            abs    |  clone\r\n *      comparedTo                      |  config               set\r\n *      decimalPlaces            dp     |      DECIMAL_PLACES\r\n *      dividedBy                div    |      ROUNDING_MODE\r\n *      dividedToIntegerBy       idiv   |      EXPONENTIAL_AT\r\n *      exponentiatedBy          pow    |      RANGE\r\n *      integerValue                    |      CRYPTO\r\n *      isEqualTo                eq     |      MODULO_MODE\r\n *      isFinite                        |      POW_PRECISION\r\n *      isGreaterThan            gt     |      FORMAT\r\n *      isGreaterThanOrEqualTo   gte    |      ALPHABET\r\n *      isInteger                       |  isBigNumber\r\n *      isLessThan               lt     |  maximum              max\r\n *      isLessThanOrEqualTo      lte    |  minimum              min\r\n *      isNaN                           |  random\r\n *      isNegative                      |  sum\r\n *      isPositive                      |\r\n *      isZero                          |\r\n *      minus                           |\r\n *      modulo                   mod    |\r\n *      multipliedBy             times  |\r\n *      negated                         |\r\n *      plus                            |\r\n *      precision                sd     |\r\n *      shiftedBy                       |\r\n *      squareRoot               sqrt   |\r\n *      toExponential                   |\r\n *      toFixed                         |\r\n *      toFormat                        |\r\n *      toFraction                      |\r\n *      toJSON                          |\r\n *      toNumber                        |\r\n *      toPrecision                     |\r\n *      toString                        |\r\n *      valueOf                         |\r\n *\r\n */\r\n\r\n\r\n  var BigNumber,\r\n    isNumeric = /^-?(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?$/i,\r\n    mathceil = Math.ceil,\r\n    mathfloor = Math.floor,\r\n\r\n    bignumberError = '[BigNumber Error] ',\r\n    tooManyDigits = bignumberError + 'Number primitive has more than 15 significant digits: ',\r\n\r\n    BASE = 1e14,\r\n    LOG_BASE = 14,\r\n    MAX_SAFE_INTEGER = 0x1fffffffffffff,         // 2^53 - 1\r\n    // MAX_INT32 = 0x7fffffff,                   // 2^31 - 1\r\n    POWS_TEN = [1, 10, 100, 1e3, 1e4, 1e5, 1e6, 1e7, 1e8, 1e9, 1e10, 1e11, 1e12, 1e13],\r\n    SQRT_BASE = 1e7,\r\n\r\n    // EDITABLE\r\n    // The limit on the value of DECIMAL_PLACES, TO_EXP_NEG, TO_EXP_POS, MIN_EXP, MAX_EXP, and\r\n    // the arguments to toExponential, toFixed, toFormat, and toPrecision.\r\n    MAX = 1E9;                                   // 0 to MAX_INT32\r\n\r\n\r\n  /*\r\n   * Create and return a BigNumber constructor.\r\n   */\r\n  function clone(configObject) {\r\n    var div, convertBase, parseNumeric,\r\n      P = BigNumber.prototype = { constructor: BigNumber, toString: null, valueOf: null },\r\n      ONE = new BigNumber(1),\r\n\r\n\r\n      //----------------------------- EDITABLE CONFIG DEFAULTS -------------------------------\r\n\r\n\r\n      // The default values below must be integers within the inclusive ranges stated.\r\n      // The values can also be changed at run-time using BigNumber.set.\r\n\r\n      // The maximum number of decimal places for operations involving division.\r\n      DECIMAL_PLACES = 20,                     // 0 to MAX\r\n\r\n      // The rounding mode used when rounding to the above decimal places, and when using\r\n      // toExponential, toFixed, toFormat and toPrecision, and round (default value).\r\n      // UP         0 Away from zero.\r\n      // DOWN       1 Towards zero.\r\n      // CEIL       2 Towards +Infinity.\r\n      // FLOOR      3 Towards -Infinity.\r\n      // HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n      // HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n      // HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n      // HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n      // HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n      ROUNDING_MODE = 4,                       // 0 to 8\r\n\r\n      // EXPONENTIAL_AT : [TO_EXP_NEG , TO_EXP_POS]\r\n\r\n      // The exponent value at and beneath which toString returns exponential notation.\r\n      // Number type: -7\r\n      TO_EXP_NEG = -7,                         // 0 to -MAX\r\n\r\n      // The exponent value at and above which toString returns exponential notation.\r\n      // Number type: 21\r\n      TO_EXP_POS = 21,                         // 0 to MAX\r\n\r\n      // RANGE : [MIN_EXP, MAX_EXP]\r\n\r\n      // The minimum exponent value, beneath which underflow to zero occurs.\r\n      // Number type: -324  (5e-324)\r\n      MIN_EXP = -1e7,                          // -1 to -MAX\r\n\r\n      // The maximum exponent value, above which overflow to Infinity occurs.\r\n      // Number type:  308  (1.7976931348623157e+308)\r\n      // For MAX_EXP > 1e7, e.g. new BigNumber('1e100000000').plus(1) may be slow.\r\n      MAX_EXP = 1e7,                           // 1 to MAX\r\n\r\n      // Whether to use cryptographically-secure random number generation, if available.\r\n      CRYPTO = false,                          // true or false\r\n\r\n      // The modulo mode used when calculating the modulus: a mod n.\r\n      // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\r\n      // The remainder (r) is calculated as: r = a - n * q.\r\n      //\r\n      // UP        0 The remainder is positive if the dividend is negative, else is negative.\r\n      // DOWN      1 The remainder has the same sign as the dividend.\r\n      //             This modulo mode is commonly known as 'truncated division' and is\r\n      //             equivalent to (a % n) in JavaScript.\r\n      // FLOOR     3 The remainder has the same sign as the divisor (Python %).\r\n      // HALF_EVEN 6 This modulo mode implements the IEEE 754 remainder function.\r\n      // EUCLID    9 Euclidian division. q = sign(n) * floor(a / abs(n)).\r\n      //             The remainder is always positive.\r\n      //\r\n      // The truncated division, floored division, Euclidian division and IEEE 754 remainder\r\n      // modes are commonly used for the modulus operation.\r\n      // Although the other rounding modes can also be used, they may not give useful results.\r\n      MODULO_MODE = 1,                         // 0 to 9\r\n\r\n      // The maximum number of significant digits of the result of the exponentiatedBy operation.\r\n      // If POW_PRECISION is 0, there will be unlimited significant digits.\r\n      POW_PRECISION = 0,                       // 0 to MAX\r\n\r\n      // The format specification used by the BigNumber.prototype.toFormat method.\r\n      FORMAT = {\r\n        prefix: '',\r\n        groupSize: 3,\r\n        secondaryGroupSize: 0,\r\n        groupSeparator: ',',\r\n        decimalSeparator: '.',\r\n        fractionGroupSize: 0,\r\n        fractionGroupSeparator: '\\xA0',        // non-breaking space\r\n        suffix: ''\r\n      },\r\n\r\n      // The alphabet used for base conversion. It must be at least 2 characters long, with no '+',\r\n      // '-', '.', whitespace, or repeated character.\r\n      // '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_'\r\n      ALPHABET = '0123456789abcdefghijklmnopqrstuvwxyz',\r\n      alphabetHasNormalDecimalDigits = true;\r\n\r\n\r\n    //------------------------------------------------------------------------------------------\r\n\r\n\r\n    // CONSTRUCTOR\r\n\r\n\r\n    /*\r\n     * The BigNumber constructor and exported function.\r\n     * Create and return a new instance of a BigNumber object.\r\n     *\r\n     * v {number|string|BigNumber} A numeric value.\r\n     * [b] {number} The base of v. Integer, 2 to ALPHABET.length inclusive.\r\n     */\r\n    function BigNumber(v, b) {\r\n      var alphabet, c, caseChanged, e, i, isNum, len, str,\r\n        x = this;\r\n\r\n      // Enable constructor call without `new`.\r\n      if (!(x instanceof BigNumber)) return new BigNumber(v, b);\r\n\r\n      if (b == null) {\r\n\r\n        if (v && v._isBigNumber === true) {\r\n          x.s = v.s;\r\n\r\n          if (!v.c || v.e > MAX_EXP) {\r\n            x.c = x.e = null;\r\n          } else if (v.e < MIN_EXP) {\r\n            x.c = [x.e = 0];\r\n          } else {\r\n            x.e = v.e;\r\n            x.c = v.c.slice();\r\n          }\r\n\r\n          return;\r\n        }\r\n\r\n        if ((isNum = typeof v == 'number') && v * 0 == 0) {\r\n\r\n          // Use `1 / n` to handle minus zero also.\r\n          x.s = 1 / v < 0 ? (v = -v, -1) : 1;\r\n\r\n          // Fast path for integers, where n < 2147483648 (2**31).\r\n          if (v === ~~v) {\r\n            for (e = 0, i = v; i >= 10; i /= 10, e++);\r\n\r\n            if (e > MAX_EXP) {\r\n              x.c = x.e = null;\r\n            } else {\r\n              x.e = e;\r\n              x.c = [v];\r\n            }\r\n\r\n            return;\r\n          }\r\n\r\n          str = String(v);\r\n        } else {\r\n\r\n          if (!isNumeric.test(str = String(v))) return parseNumeric(x, str, isNum);\r\n\r\n          x.s = str.charCodeAt(0) == 45 ? (str = str.slice(1), -1) : 1;\r\n        }\r\n\r\n        // Decimal point?\r\n        if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n        // Exponential form?\r\n        if ((i = str.search(/e/i)) > 0) {\r\n\r\n          // Determine exponent.\r\n          if (e < 0) e = i;\r\n          e += +str.slice(i + 1);\r\n          str = str.substring(0, i);\r\n        } else if (e < 0) {\r\n\r\n          // Integer.\r\n          e = str.length;\r\n        }\r\n\r\n      } else {\r\n\r\n        // '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\r\n        intCheck(b, 2, ALPHABET.length, 'Base');\r\n\r\n        // Allow exponential notation to be used with base 10 argument, while\r\n        // also rounding to DECIMAL_PLACES as with other bases.\r\n        if (b == 10 && alphabetHasNormalDecimalDigits) {\r\n          x = new BigNumber(v);\r\n          return round(x, DECIMAL_PLACES + x.e + 1, ROUNDING_MODE);\r\n        }\r\n\r\n        str = String(v);\r\n\r\n        if (isNum = typeof v == 'number') {\r\n\r\n          // Avoid potential interpretation of Infinity and NaN as base 44+ values.\r\n          if (v * 0 != 0) return parseNumeric(x, str, isNum, b);\r\n\r\n          x.s = 1 / v < 0 ? (str = str.slice(1), -1) : 1;\r\n\r\n          // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\r\n          if (BigNumber.DEBUG && str.replace(/^0\\.0*|\\./, '').length > 15) {\r\n            throw Error\r\n             (tooManyDigits + v);\r\n          }\r\n        } else {\r\n          x.s = str.charCodeAt(0) === 45 ? (str = str.slice(1), -1) : 1;\r\n        }\r\n\r\n        alphabet = ALPHABET.slice(0, b);\r\n        e = i = 0;\r\n\r\n        // Check that str is a valid base b number.\r\n        // Don't use RegExp, so alphabet can contain special characters.\r\n        for (len = str.length; i < len; i++) {\r\n          if (alphabet.indexOf(c = str.charAt(i)) < 0) {\r\n            if (c == '.') {\r\n\r\n              // If '.' is not the first character and it has not be found before.\r\n              if (i > e) {\r\n                e = len;\r\n                continue;\r\n              }\r\n            } else if (!caseChanged) {\r\n\r\n              // Allow e.g. hexadecimal 'FF' as well as 'ff'.\r\n              if (str == str.toUpperCase() && (str = str.toLowerCase()) ||\r\n                  str == str.toLowerCase() && (str = str.toUpperCase())) {\r\n                caseChanged = true;\r\n                i = -1;\r\n                e = 0;\r\n                continue;\r\n              }\r\n            }\r\n\r\n            return parseNumeric(x, String(v), isNum, b);\r\n          }\r\n        }\r\n\r\n        // Prevent later check for length on converted number.\r\n        isNum = false;\r\n        str = convertBase(str, b, 10, x.s);\r\n\r\n        // Decimal point?\r\n        if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n        else e = str.length;\r\n      }\r\n\r\n      // Determine leading zeros.\r\n      for (i = 0; str.charCodeAt(i) === 48; i++);\r\n\r\n      // Determine trailing zeros.\r\n      for (len = str.length; str.charCodeAt(--len) === 48;);\r\n\r\n      if (str = str.slice(i, ++len)) {\r\n        len -= i;\r\n\r\n        // '[BigNumber Error] Number primitive has more than 15 significant digits: {n}'\r\n        if (isNum && BigNumber.DEBUG &&\r\n          len > 15 && (v > MAX_SAFE_INTEGER || v !== mathfloor(v))) {\r\n            throw Error\r\n             (tooManyDigits + (x.s * v));\r\n        }\r\n\r\n         // Overflow?\r\n        if ((e = e - i - 1) > MAX_EXP) {\r\n\r\n          // Infinity.\r\n          x.c = x.e = null;\r\n\r\n        // Underflow?\r\n        } else if (e < MIN_EXP) {\r\n\r\n          // Zero.\r\n          x.c = [x.e = 0];\r\n        } else {\r\n          x.e = e;\r\n          x.c = [];\r\n\r\n          // Transform base\r\n\r\n          // e is the base 10 exponent.\r\n          // i is where to slice str to get the first element of the coefficient array.\r\n          i = (e + 1) % LOG_BASE;\r\n          if (e < 0) i += LOG_BASE;  // i < 1\r\n\r\n          if (i < len) {\r\n            if (i) x.c.push(+str.slice(0, i));\r\n\r\n            for (len -= LOG_BASE; i < len;) {\r\n              x.c.push(+str.slice(i, i += LOG_BASE));\r\n            }\r\n\r\n            i = LOG_BASE - (str = str.slice(i)).length;\r\n          } else {\r\n            i -= len;\r\n          }\r\n\r\n          for (; i--; str += '0');\r\n          x.c.push(+str);\r\n        }\r\n      } else {\r\n\r\n        // Zero.\r\n        x.c = [x.e = 0];\r\n      }\r\n    }\r\n\r\n\r\n    // CONSTRUCTOR PROPERTIES\r\n\r\n\r\n    BigNumber.clone = clone;\r\n\r\n    BigNumber.ROUND_UP = 0;\r\n    BigNumber.ROUND_DOWN = 1;\r\n    BigNumber.ROUND_CEIL = 2;\r\n    BigNumber.ROUND_FLOOR = 3;\r\n    BigNumber.ROUND_HALF_UP = 4;\r\n    BigNumber.ROUND_HALF_DOWN = 5;\r\n    BigNumber.ROUND_HALF_EVEN = 6;\r\n    BigNumber.ROUND_HALF_CEIL = 7;\r\n    BigNumber.ROUND_HALF_FLOOR = 8;\r\n    BigNumber.EUCLID = 9;\r\n\r\n\r\n    /*\r\n     * Configure infrequently-changing library-wide settings.\r\n     *\r\n     * Accept an object with the following optional properties (if the value of a property is\r\n     * a number, it must be an integer within the inclusive range stated):\r\n     *\r\n     *   DECIMAL_PLACES   {number}           0 to MAX\r\n     *   ROUNDING_MODE    {number}           0 to 8\r\n     *   EXPONENTIAL_AT   {number|number[]}  -MAX to MAX  or  [-MAX to 0, 0 to MAX]\r\n     *   RANGE            {number|number[]}  -MAX to MAX (not zero)  or  [-MAX to -1, 1 to MAX]\r\n     *   CRYPTO           {boolean}          true or false\r\n     *   MODULO_MODE      {number}           0 to 9\r\n     *   POW_PRECISION       {number}           0 to MAX\r\n     *   ALPHABET         {string}           A string of two or more unique characters which does\r\n     *                                       not contain '.'.\r\n     *   FORMAT           {object}           An object with some of the following properties:\r\n     *     prefix                 {string}\r\n     *     groupSize              {number}\r\n     *     secondaryGroupSize     {number}\r\n     *     groupSeparator         {string}\r\n     *     decimalSeparator       {string}\r\n     *     fractionGroupSize      {number}\r\n     *     fractionGroupSeparator {string}\r\n     *     suffix                 {string}\r\n     *\r\n     * (The values assigned to the above FORMAT object properties are not checked for validity.)\r\n     *\r\n     * E.g.\r\n     * BigNumber.config({ DECIMAL_PLACES : 20, ROUNDING_MODE : 4 })\r\n     *\r\n     * Ignore properties/parameters set to null or undefined, except for ALPHABET.\r\n     *\r\n     * Return an object with the properties current values.\r\n     */\r\n    BigNumber.config = BigNumber.set = function (obj) {\r\n      var p, v;\r\n\r\n      if (obj != null) {\r\n\r\n        if (typeof obj == 'object') {\r\n\r\n          // DECIMAL_PLACES {number} Integer, 0 to MAX inclusive.\r\n          // '[BigNumber Error] DECIMAL_PLACES {not a primitive number|not an integer|out of range}: {v}'\r\n          if (obj.hasOwnProperty(p = 'DECIMAL_PLACES')) {\r\n            v = obj[p];\r\n            intCheck(v, 0, MAX, p);\r\n            DECIMAL_PLACES = v;\r\n          }\r\n\r\n          // ROUNDING_MODE {number} Integer, 0 to 8 inclusive.\r\n          // '[BigNumber Error] ROUNDING_MODE {not a primitive number|not an integer|out of range}: {v}'\r\n          if (obj.hasOwnProperty(p = 'ROUNDING_MODE')) {\r\n            v = obj[p];\r\n            intCheck(v, 0, 8, p);\r\n            ROUNDING_MODE = v;\r\n          }\r\n\r\n          // EXPONENTIAL_AT {number|number[]}\r\n          // Integer, -MAX to MAX inclusive or\r\n          // [integer -MAX to 0 inclusive, 0 to MAX inclusive].\r\n          // '[BigNumber Error] EXPONENTIAL_AT {not a primitive number|not an integer|out of range}: {v}'\r\n          if (obj.hasOwnProperty(p = 'EXPONENTIAL_AT')) {\r\n            v = obj[p];\r\n            if (v && v.pop) {\r\n              intCheck(v[0], -MAX, 0, p);\r\n              intCheck(v[1], 0, MAX, p);\r\n              TO_EXP_NEG = v[0];\r\n              TO_EXP_POS = v[1];\r\n            } else {\r\n              intCheck(v, -MAX, MAX, p);\r\n              TO_EXP_NEG = -(TO_EXP_POS = v < 0 ? -v : v);\r\n            }\r\n          }\r\n\r\n          // RANGE {number|number[]} Non-zero integer, -MAX to MAX inclusive or\r\n          // [integer -MAX to -1 inclusive, integer 1 to MAX inclusive].\r\n          // '[BigNumber Error] RANGE {not a primitive number|not an integer|out of range|cannot be zero}: {v}'\r\n          if (obj.hasOwnProperty(p = 'RANGE')) {\r\n            v = obj[p];\r\n            if (v && v.pop) {\r\n              intCheck(v[0], -MAX, -1, p);\r\n              intCheck(v[1], 1, MAX, p);\r\n              MIN_EXP = v[0];\r\n              MAX_EXP = v[1];\r\n            } else {\r\n              intCheck(v, -MAX, MAX, p);\r\n              if (v) {\r\n                MIN_EXP = -(MAX_EXP = v < 0 ? -v : v);\r\n              } else {\r\n                throw Error\r\n                 (bignumberError + p + ' cannot be zero: ' + v);\r\n              }\r\n            }\r\n          }\r\n\r\n          // CRYPTO {boolean} true or false.\r\n          // '[BigNumber Error] CRYPTO not true or false: {v}'\r\n          // '[BigNumber Error] crypto unavailable'\r\n          if (obj.hasOwnProperty(p = 'CRYPTO')) {\r\n            v = obj[p];\r\n            if (v === !!v) {\r\n              if (v) {\r\n                if (typeof crypto != 'undefined' && crypto &&\r\n                 (crypto.getRandomValues || crypto.randomBytes)) {\r\n                  CRYPTO = v;\r\n                } else {\r\n                  CRYPTO = !v;\r\n                  throw Error\r\n                   (bignumberError + 'crypto unavailable');\r\n                }\r\n              } else {\r\n                CRYPTO = v;\r\n              }\r\n            } else {\r\n              throw Error\r\n               (bignumberError + p + ' not true or false: ' + v);\r\n            }\r\n          }\r\n\r\n          // MODULO_MODE {number} Integer, 0 to 9 inclusive.\r\n          // '[BigNumber Error] MODULO_MODE {not a primitive number|not an integer|out of range}: {v}'\r\n          if (obj.hasOwnProperty(p = 'MODULO_MODE')) {\r\n            v = obj[p];\r\n            intCheck(v, 0, 9, p);\r\n            MODULO_MODE = v;\r\n          }\r\n\r\n          // POW_PRECISION {number} Integer, 0 to MAX inclusive.\r\n          // '[BigNumber Error] POW_PRECISION {not a primitive number|not an integer|out of range}: {v}'\r\n          if (obj.hasOwnProperty(p = 'POW_PRECISION')) {\r\n            v = obj[p];\r\n            intCheck(v, 0, MAX, p);\r\n            POW_PRECISION = v;\r\n          }\r\n\r\n          // FORMAT {object}\r\n          // '[BigNumber Error] FORMAT not an object: {v}'\r\n          if (obj.hasOwnProperty(p = 'FORMAT')) {\r\n            v = obj[p];\r\n            if (typeof v == 'object') FORMAT = v;\r\n            else throw Error\r\n             (bignumberError + p + ' not an object: ' + v);\r\n          }\r\n\r\n          // ALPHABET {string}\r\n          // '[BigNumber Error] ALPHABET invalid: {v}'\r\n          if (obj.hasOwnProperty(p = 'ALPHABET')) {\r\n            v = obj[p];\r\n\r\n            // Disallow if less than two characters,\r\n            // or if it contains '+', '-', '.', whitespace, or a repeated character.\r\n            if (typeof v == 'string' && !/^.?$|[+\\-.\\s]|(.).*\\1/.test(v)) {\r\n              alphabetHasNormalDecimalDigits = v.slice(0, 10) == '0123456789';\r\n              ALPHABET = v;\r\n            } else {\r\n              throw Error\r\n               (bignumberError + p + ' invalid: ' + v);\r\n            }\r\n          }\r\n\r\n        } else {\r\n\r\n          // '[BigNumber Error] Object expected: {v}'\r\n          throw Error\r\n           (bignumberError + 'Object expected: ' + obj);\r\n        }\r\n      }\r\n\r\n      return {\r\n        DECIMAL_PLACES: DECIMAL_PLACES,\r\n        ROUNDING_MODE: ROUNDING_MODE,\r\n        EXPONENTIAL_AT: [TO_EXP_NEG, TO_EXP_POS],\r\n        RANGE: [MIN_EXP, MAX_EXP],\r\n        CRYPTO: CRYPTO,\r\n        MODULO_MODE: MODULO_MODE,\r\n        POW_PRECISION: POW_PRECISION,\r\n        FORMAT: FORMAT,\r\n        ALPHABET: ALPHABET\r\n      };\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if v is a BigNumber instance, otherwise return false.\r\n     *\r\n     * If BigNumber.DEBUG is true, throw if a BigNumber instance is not well-formed.\r\n     *\r\n     * v {any}\r\n     *\r\n     * '[BigNumber Error] Invalid BigNumber: {v}'\r\n     */\r\n    BigNumber.isBigNumber = function (v) {\r\n      if (!v || v._isBigNumber !== true) return false;\r\n      if (!BigNumber.DEBUG) return true;\r\n\r\n      var i, n,\r\n        c = v.c,\r\n        e = v.e,\r\n        s = v.s;\r\n\r\n      out: if ({}.toString.call(c) == '[object Array]') {\r\n\r\n        if ((s === 1 || s === -1) && e >= -MAX && e <= MAX && e === mathfloor(e)) {\r\n\r\n          // If the first element is zero, the BigNumber value must be zero.\r\n          if (c[0] === 0) {\r\n            if (e === 0 && c.length === 1) return true;\r\n            break out;\r\n          }\r\n\r\n          // Calculate number of digits that c[0] should have, based on the exponent.\r\n          i = (e + 1) % LOG_BASE;\r\n          if (i < 1) i += LOG_BASE;\r\n\r\n          // Calculate number of digits of c[0].\r\n          //if (Math.ceil(Math.log(c[0] + 1) / Math.LN10) == i) {\r\n          if (String(c[0]).length == i) {\r\n\r\n            for (i = 0; i < c.length; i++) {\r\n              n = c[i];\r\n              if (n < 0 || n >= BASE || n !== mathfloor(n)) break out;\r\n            }\r\n\r\n            // Last element cannot be zero, unless it is the only element.\r\n            if (n !== 0) return true;\r\n          }\r\n        }\r\n\r\n      // Infinity/NaN\r\n      } else if (c === null && e === null && (s === null || s === 1 || s === -1)) {\r\n        return true;\r\n      }\r\n\r\n      throw Error\r\n        (bignumberError + 'Invalid BigNumber: ' + v);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a new BigNumber whose value is the maximum of the arguments.\r\n     *\r\n     * arguments {number|string|BigNumber}\r\n     */\r\n    BigNumber.maximum = BigNumber.max = function () {\r\n      return maxOrMin(arguments, -1);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a new BigNumber whose value is the minimum of the arguments.\r\n     *\r\n     * arguments {number|string|BigNumber}\r\n     */\r\n    BigNumber.minimum = BigNumber.min = function () {\r\n      return maxOrMin(arguments, 1);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a new BigNumber with a random value equal to or greater than 0 and less than 1,\r\n     * and with dp, or DECIMAL_PLACES if dp is omitted, decimal places (or less if trailing\r\n     * zeros are produced).\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp}'\r\n     * '[BigNumber Error] crypto unavailable'\r\n     */\r\n    BigNumber.random = (function () {\r\n      var pow2_53 = 0x20000000000000;\r\n\r\n      // Return a 53 bit integer n, where 0 <= n < 9007199254740992.\r\n      // Check if Math.random() produces more than 32 bits of randomness.\r\n      // If it does, assume at least 53 bits are produced, otherwise assume at least 30 bits.\r\n      // 0x40000000 is 2^30, 0x800000 is 2^23, 0x1fffff is 2^21 - 1.\r\n      var random53bitInt = (Math.random() * pow2_53) & 0x1fffff\r\n       ? function () { return mathfloor(Math.random() * pow2_53); }\r\n       : function () { return ((Math.random() * 0x40000000 | 0) * 0x800000) +\r\n         (Math.random() * 0x800000 | 0); };\r\n\r\n      return function (dp) {\r\n        var a, b, e, k, v,\r\n          i = 0,\r\n          c = [],\r\n          rand = new BigNumber(ONE);\r\n\r\n        if (dp == null) dp = DECIMAL_PLACES;\r\n        else intCheck(dp, 0, MAX);\r\n\r\n        k = mathceil(dp / LOG_BASE);\r\n\r\n        if (CRYPTO) {\r\n\r\n          // Browsers supporting crypto.getRandomValues.\r\n          if (crypto.getRandomValues) {\r\n\r\n            a = crypto.getRandomValues(new Uint32Array(k *= 2));\r\n\r\n            for (; i < k;) {\r\n\r\n              // 53 bits:\r\n              // ((Math.pow(2, 32) - 1) * Math.pow(2, 21)).toString(2)\r\n              // 11111 11111111 11111111 11111111 11100000 00000000 00000000\r\n              // ((Math.pow(2, 32) - 1) >>> 11).toString(2)\r\n              //                                     11111 11111111 11111111\r\n              // 0x20000 is 2^21.\r\n              v = a[i] * 0x20000 + (a[i + 1] >>> 11);\r\n\r\n              // Rejection sampling:\r\n              // 0 <= v < 9007199254740992\r\n              // Probability that v >= 9e15, is\r\n              // 7199254740992 / 9007199254740992 ~= 0.0008, i.e. 1 in 1251\r\n              if (v >= 9e15) {\r\n                b = crypto.getRandomValues(new Uint32Array(2));\r\n                a[i] = b[0];\r\n                a[i + 1] = b[1];\r\n              } else {\r\n\r\n                // 0 <= v <= 8999999999999999\r\n                // 0 <= (v % 1e14) <= 99999999999999\r\n                c.push(v % 1e14);\r\n                i += 2;\r\n              }\r\n            }\r\n            i = k / 2;\r\n\r\n          // Node.js supporting crypto.randomBytes.\r\n          } else if (crypto.randomBytes) {\r\n\r\n            // buffer\r\n            a = crypto.randomBytes(k *= 7);\r\n\r\n            for (; i < k;) {\r\n\r\n              // 0x1000000000000 is 2^48, 0x10000000000 is 2^40\r\n              // 0x100000000 is 2^32, 0x1000000 is 2^24\r\n              // 11111 11111111 11111111 11111111 11111111 11111111 11111111\r\n              // 0 <= v < 9007199254740992\r\n              v = ((a[i] & 31) * 0x1000000000000) + (a[i + 1] * 0x10000000000) +\r\n                 (a[i + 2] * 0x100000000) + (a[i + 3] * 0x1000000) +\r\n                 (a[i + 4] << 16) + (a[i + 5] << 8) + a[i + 6];\r\n\r\n              if (v >= 9e15) {\r\n                crypto.randomBytes(7).copy(a, i);\r\n              } else {\r\n\r\n                // 0 <= (v % 1e14) <= 99999999999999\r\n                c.push(v % 1e14);\r\n                i += 7;\r\n              }\r\n            }\r\n            i = k / 7;\r\n          } else {\r\n            CRYPTO = false;\r\n            throw Error\r\n             (bignumberError + 'crypto unavailable');\r\n          }\r\n        }\r\n\r\n        // Use Math.random.\r\n        if (!CRYPTO) {\r\n\r\n          for (; i < k;) {\r\n            v = random53bitInt();\r\n            if (v < 9e15) c[i++] = v % 1e14;\r\n          }\r\n        }\r\n\r\n        k = c[--i];\r\n        dp %= LOG_BASE;\r\n\r\n        // Convert trailing digits to zeros according to dp.\r\n        if (k && dp) {\r\n          v = POWS_TEN[LOG_BASE - dp];\r\n          c[i] = mathfloor(k / v) * v;\r\n        }\r\n\r\n        // Remove trailing elements which are zero.\r\n        for (; c[i] === 0; c.pop(), i--);\r\n\r\n        // Zero?\r\n        if (i < 0) {\r\n          c = [e = 0];\r\n        } else {\r\n\r\n          // Remove leading elements which are zero and adjust exponent accordingly.\r\n          for (e = -1 ; c[0] === 0; c.splice(0, 1), e -= LOG_BASE);\r\n\r\n          // Count the digits of the first element of c to determine leading zeros, and...\r\n          for (i = 1, v = c[0]; v >= 10; v /= 10, i++);\r\n\r\n          // adjust the exponent accordingly.\r\n          if (i < LOG_BASE) e -= LOG_BASE - i;\r\n        }\r\n\r\n        rand.e = e;\r\n        rand.c = c;\r\n        return rand;\r\n      };\r\n    })();\r\n\r\n\r\n    /*\r\n     * Return a BigNumber whose value is the sum of the arguments.\r\n     *\r\n     * arguments {number|string|BigNumber}\r\n     */\r\n    BigNumber.sum = function () {\r\n      var i = 1,\r\n        args = arguments,\r\n        sum = new BigNumber(args[0]);\r\n      for (; i < args.length;) sum = sum.plus(args[i++]);\r\n      return sum;\r\n    };\r\n\r\n\r\n    // PRIVATE FUNCTIONS\r\n\r\n\r\n    // Called by BigNumber and BigNumber.prototype.toString.\r\n    convertBase = (function () {\r\n      var decimal = '0123456789';\r\n\r\n      /*\r\n       * Convert string of baseIn to an array of numbers of baseOut.\r\n       * Eg. toBaseOut('255', 10, 16) returns [15, 15].\r\n       * Eg. toBaseOut('ff', 16, 10) returns [2, 5, 5].\r\n       */\r\n      function toBaseOut(str, baseIn, baseOut, alphabet) {\r\n        var j,\r\n          arr = [0],\r\n          arrL,\r\n          i = 0,\r\n          len = str.length;\r\n\r\n        for (; i < len;) {\r\n          for (arrL = arr.length; arrL--; arr[arrL] *= baseIn);\r\n\r\n          arr[0] += alphabet.indexOf(str.charAt(i++));\r\n\r\n          for (j = 0; j < arr.length; j++) {\r\n\r\n            if (arr[j] > baseOut - 1) {\r\n              if (arr[j + 1] == null) arr[j + 1] = 0;\r\n              arr[j + 1] += arr[j] / baseOut | 0;\r\n              arr[j] %= baseOut;\r\n            }\r\n          }\r\n        }\r\n\r\n        return arr.reverse();\r\n      }\r\n\r\n      // Convert a numeric string of baseIn to a numeric string of baseOut.\r\n      // If the caller is toString, we are converting from base 10 to baseOut.\r\n      // If the caller is BigNumber, we are converting from baseIn to base 10.\r\n      return function (str, baseIn, baseOut, sign, callerIsToString) {\r\n        var alphabet, d, e, k, r, x, xc, y,\r\n          i = str.indexOf('.'),\r\n          dp = DECIMAL_PLACES,\r\n          rm = ROUNDING_MODE;\r\n\r\n        // Non-integer.\r\n        if (i >= 0) {\r\n          k = POW_PRECISION;\r\n\r\n          // Unlimited precision.\r\n          POW_PRECISION = 0;\r\n          str = str.replace('.', '');\r\n          y = new BigNumber(baseIn);\r\n          x = y.pow(str.length - i);\r\n          POW_PRECISION = k;\r\n\r\n          // Convert str as if an integer, then restore the fraction part by dividing the\r\n          // result by its base raised to a power.\r\n\r\n          y.c = toBaseOut(toFixedPoint(coeffToString(x.c), x.e, '0'),\r\n           10, baseOut, decimal);\r\n          y.e = y.c.length;\r\n        }\r\n\r\n        // Convert the number as integer.\r\n\r\n        xc = toBaseOut(str, baseIn, baseOut, callerIsToString\r\n         ? (alphabet = ALPHABET, decimal)\r\n         : (alphabet = decimal, ALPHABET));\r\n\r\n        // xc now represents str as an integer and converted to baseOut. e is the exponent.\r\n        e = k = xc.length;\r\n\r\n        // Remove trailing zeros.\r\n        for (; xc[--k] == 0; xc.pop());\r\n\r\n        // Zero?\r\n        if (!xc[0]) return alphabet.charAt(0);\r\n\r\n        // Does str represent an integer? If so, no need for the division.\r\n        if (i < 0) {\r\n          --e;\r\n        } else {\r\n          x.c = xc;\r\n          x.e = e;\r\n\r\n          // The sign is needed for correct rounding.\r\n          x.s = sign;\r\n          x = div(x, y, dp, rm, baseOut);\r\n          xc = x.c;\r\n          r = x.r;\r\n          e = x.e;\r\n        }\r\n\r\n        // xc now represents str converted to baseOut.\r\n\r\n        // The index of the rounding digit.\r\n        d = e + dp + 1;\r\n\r\n        // The rounding digit: the digit to the right of the digit that may be rounded up.\r\n        i = xc[d];\r\n\r\n        // Look at the rounding digits and mode to determine whether to round up.\r\n\r\n        k = baseOut / 2;\r\n        r = r || d < 0 || xc[d + 1] != null;\r\n\r\n        r = rm < 4 ? (i != null || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n              : i > k || i == k &&(rm == 4 || r || rm == 6 && xc[d - 1] & 1 ||\r\n               rm == (x.s < 0 ? 8 : 7));\r\n\r\n        // If the index of the rounding digit is not greater than zero, or xc represents\r\n        // zero, then the result of the base conversion is zero or, if rounding up, a value\r\n        // such as 0.00001.\r\n        if (d < 1 || !xc[0]) {\r\n\r\n          // 1^-dp or 0\r\n          str = r ? toFixedPoint(alphabet.charAt(1), -dp, alphabet.charAt(0)) : alphabet.charAt(0);\r\n        } else {\r\n\r\n          // Truncate xc to the required number of decimal places.\r\n          xc.length = d;\r\n\r\n          // Round up?\r\n          if (r) {\r\n\r\n            // Rounding up may mean the previous digit has to be rounded up and so on.\r\n            for (--baseOut; ++xc[--d] > baseOut;) {\r\n              xc[d] = 0;\r\n\r\n              if (!d) {\r\n                ++e;\r\n                xc = [1].concat(xc);\r\n              }\r\n            }\r\n          }\r\n\r\n          // Determine trailing zeros.\r\n          for (k = xc.length; !xc[--k];);\r\n\r\n          // E.g. [4, 11, 15] becomes 4bf.\r\n          for (i = 0, str = ''; i <= k; str += alphabet.charAt(xc[i++]));\r\n\r\n          // Add leading zeros, decimal point and trailing zeros as required.\r\n          str = toFixedPoint(str, e, alphabet.charAt(0));\r\n        }\r\n\r\n        // The caller will add the sign.\r\n        return str;\r\n      };\r\n    })();\r\n\r\n\r\n    // Perform division in the specified base. Called by div and convertBase.\r\n    div = (function () {\r\n\r\n      // Assume non-zero x and k.\r\n      function multiply(x, k, base) {\r\n        var m, temp, xlo, xhi,\r\n          carry = 0,\r\n          i = x.length,\r\n          klo = k % SQRT_BASE,\r\n          khi = k / SQRT_BASE | 0;\r\n\r\n        for (x = x.slice(); i--;) {\r\n          xlo = x[i] % SQRT_BASE;\r\n          xhi = x[i] / SQRT_BASE | 0;\r\n          m = khi * xlo + xhi * klo;\r\n          temp = klo * xlo + ((m % SQRT_BASE) * SQRT_BASE) + carry;\r\n          carry = (temp / base | 0) + (m / SQRT_BASE | 0) + khi * xhi;\r\n          x[i] = temp % base;\r\n        }\r\n\r\n        if (carry) x = [carry].concat(x);\r\n\r\n        return x;\r\n      }\r\n\r\n      function compare(a, b, aL, bL) {\r\n        var i, cmp;\r\n\r\n        if (aL != bL) {\r\n          cmp = aL > bL ? 1 : -1;\r\n        } else {\r\n\r\n          for (i = cmp = 0; i < aL; i++) {\r\n\r\n            if (a[i] != b[i]) {\r\n              cmp = a[i] > b[i] ? 1 : -1;\r\n              break;\r\n            }\r\n          }\r\n        }\r\n\r\n        return cmp;\r\n      }\r\n\r\n      function subtract(a, b, aL, base) {\r\n        var i = 0;\r\n\r\n        // Subtract b from a.\r\n        for (; aL--;) {\r\n          a[aL] -= i;\r\n          i = a[aL] < b[aL] ? 1 : 0;\r\n          a[aL] = i * base + a[aL] - b[aL];\r\n        }\r\n\r\n        // Remove leading zeros.\r\n        for (; !a[0] && a.length > 1; a.splice(0, 1));\r\n      }\r\n\r\n      // x: dividend, y: divisor.\r\n      return function (x, y, dp, rm, base) {\r\n        var cmp, e, i, more, n, prod, prodL, q, qc, rem, remL, rem0, xi, xL, yc0,\r\n          yL, yz,\r\n          s = x.s == y.s ? 1 : -1,\r\n          xc = x.c,\r\n          yc = y.c;\r\n\r\n        // Either NaN, Infinity or 0?\r\n        if (!xc || !xc[0] || !yc || !yc[0]) {\r\n\r\n          return new BigNumber(\r\n\r\n           // Return NaN if either NaN, or both Infinity or 0.\r\n           !x.s || !y.s || (xc ? yc && xc[0] == yc[0] : !yc) ? NaN :\r\n\r\n            // Return ±0 if x is ±0 or y is ±Infinity, or return ±Infinity as y is ±0.\r\n            xc && xc[0] == 0 || !yc ? s * 0 : s / 0\r\n         );\r\n        }\r\n\r\n        q = new BigNumber(s);\r\n        qc = q.c = [];\r\n        e = x.e - y.e;\r\n        s = dp + e + 1;\r\n\r\n        if (!base) {\r\n          base = BASE;\r\n          e = bitFloor(x.e / LOG_BASE) - bitFloor(y.e / LOG_BASE);\r\n          s = s / LOG_BASE | 0;\r\n        }\r\n\r\n        // Result exponent may be one less then the current value of e.\r\n        // The coefficients of the BigNumbers from convertBase may have trailing zeros.\r\n        for (i = 0; yc[i] == (xc[i] || 0); i++);\r\n\r\n        if (yc[i] > (xc[i] || 0)) e--;\r\n\r\n        if (s < 0) {\r\n          qc.push(1);\r\n          more = true;\r\n        } else {\r\n          xL = xc.length;\r\n          yL = yc.length;\r\n          i = 0;\r\n          s += 2;\r\n\r\n          // Normalise xc and yc so highest order digit of yc is >= base / 2.\r\n\r\n          n = mathfloor(base / (yc[0] + 1));\r\n\r\n          // Not necessary, but to handle odd bases where yc[0] == (base / 2) - 1.\r\n          // if (n > 1 || n++ == 1 && yc[0] < base / 2) {\r\n          if (n > 1) {\r\n            yc = multiply(yc, n, base);\r\n            xc = multiply(xc, n, base);\r\n            yL = yc.length;\r\n            xL = xc.length;\r\n          }\r\n\r\n          xi = yL;\r\n          rem = xc.slice(0, yL);\r\n          remL = rem.length;\r\n\r\n          // Add zeros to make remainder as long as divisor.\r\n          for (; remL < yL; rem[remL++] = 0);\r\n          yz = yc.slice();\r\n          yz = [0].concat(yz);\r\n          yc0 = yc[0];\r\n          if (yc[1] >= base / 2) yc0++;\r\n          // Not necessary, but to prevent trial digit n > base, when using base 3.\r\n          // else if (base == 3 && yc0 == 1) yc0 = 1 + 1e-15;\r\n\r\n          do {\r\n            n = 0;\r\n\r\n            // Compare divisor and remainder.\r\n            cmp = compare(yc, rem, yL, remL);\r\n\r\n            // If divisor < remainder.\r\n            if (cmp < 0) {\r\n\r\n              // Calculate trial digit, n.\r\n\r\n              rem0 = rem[0];\r\n              if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\r\n\r\n              // n is how many times the divisor goes into the current remainder.\r\n              n = mathfloor(rem0 / yc0);\r\n\r\n              //  Algorithm:\r\n              //  product = divisor multiplied by trial digit (n).\r\n              //  Compare product and remainder.\r\n              //  If product is greater than remainder:\r\n              //    Subtract divisor from product, decrement trial digit.\r\n              //  Subtract product from remainder.\r\n              //  If product was less than remainder at the last compare:\r\n              //    Compare new remainder and divisor.\r\n              //    If remainder is greater than divisor:\r\n              //      Subtract divisor from remainder, increment trial digit.\r\n\r\n              if (n > 1) {\r\n\r\n                // n may be > base only when base is 3.\r\n                if (n >= base) n = base - 1;\r\n\r\n                // product = divisor * trial digit.\r\n                prod = multiply(yc, n, base);\r\n                prodL = prod.length;\r\n                remL = rem.length;\r\n\r\n                // Compare product and remainder.\r\n                // If product > remainder then trial digit n too high.\r\n                // n is 1 too high about 5% of the time, and is not known to have\r\n                // ever been more than 1 too high.\r\n                while (compare(prod, rem, prodL, remL) == 1) {\r\n                  n--;\r\n\r\n                  // Subtract divisor from product.\r\n                  subtract(prod, yL < prodL ? yz : yc, prodL, base);\r\n                  prodL = prod.length;\r\n                  cmp = 1;\r\n                }\r\n              } else {\r\n\r\n                // n is 0 or 1, cmp is -1.\r\n                // If n is 0, there is no need to compare yc and rem again below,\r\n                // so change cmp to 1 to avoid it.\r\n                // If n is 1, leave cmp as -1, so yc and rem are compared again.\r\n                if (n == 0) {\r\n\r\n                  // divisor < remainder, so n must be at least 1.\r\n                  cmp = n = 1;\r\n                }\r\n\r\n                // product = divisor\r\n                prod = yc.slice();\r\n                prodL = prod.length;\r\n              }\r\n\r\n              if (prodL < remL) prod = [0].concat(prod);\r\n\r\n              // Subtract product from remainder.\r\n              subtract(rem, prod, remL, base);\r\n              remL = rem.length;\r\n\r\n               // If product was < remainder.\r\n              if (cmp == -1) {\r\n\r\n                // Compare divisor and new remainder.\r\n                // If divisor < new remainder, subtract divisor from remainder.\r\n                // Trial digit n too low.\r\n                // n is 1 too low about 5% of the time, and very rarely 2 too low.\r\n                while (compare(yc, rem, yL, remL) < 1) {\r\n                  n++;\r\n\r\n                  // Subtract divisor from remainder.\r\n                  subtract(rem, yL < remL ? yz : yc, remL, base);\r\n                  remL = rem.length;\r\n                }\r\n              }\r\n            } else if (cmp === 0) {\r\n              n++;\r\n              rem = [0];\r\n            } // else cmp === 1 and n will be 0\r\n\r\n            // Add the next digit, n, to the result array.\r\n            qc[i++] = n;\r\n\r\n            // Update the remainder.\r\n            if (rem[0]) {\r\n              rem[remL++] = xc[xi] || 0;\r\n            } else {\r\n              rem = [xc[xi]];\r\n              remL = 1;\r\n            }\r\n          } while ((xi++ < xL || rem[0] != null) && s--);\r\n\r\n          more = rem[0] != null;\r\n\r\n          // Leading zero?\r\n          if (!qc[0]) qc.splice(0, 1);\r\n        }\r\n\r\n        if (base == BASE) {\r\n\r\n          // To calculate q.e, first get the number of digits of qc[0].\r\n          for (i = 1, s = qc[0]; s >= 10; s /= 10, i++);\r\n\r\n          round(q, dp + (q.e = i + e * LOG_BASE - 1) + 1, rm, more);\r\n\r\n        // Caller is convertBase.\r\n        } else {\r\n          q.e = e;\r\n          q.r = +more;\r\n        }\r\n\r\n        return q;\r\n      };\r\n    })();\r\n\r\n\r\n    /*\r\n     * Return a string representing the value of BigNumber n in fixed-point or exponential\r\n     * notation rounded to the specified decimal places or significant digits.\r\n     *\r\n     * n: a BigNumber.\r\n     * i: the index of the last digit required (i.e. the digit that may be rounded up).\r\n     * rm: the rounding mode.\r\n     * id: 1 (toExponential) or 2 (toPrecision).\r\n     */\r\n    function format(n, i, rm, id) {\r\n      var c0, e, ne, len, str;\r\n\r\n      if (rm == null) rm = ROUNDING_MODE;\r\n      else intCheck(rm, 0, 8);\r\n\r\n      if (!n.c) return n.toString();\r\n\r\n      c0 = n.c[0];\r\n      ne = n.e;\r\n\r\n      if (i == null) {\r\n        str = coeffToString(n.c);\r\n        str = id == 1 || id == 2 && (ne <= TO_EXP_NEG || ne >= TO_EXP_POS)\r\n         ? toExponential(str, ne)\r\n         : toFixedPoint(str, ne, '0');\r\n      } else {\r\n        n = round(new BigNumber(n), i, rm);\r\n\r\n        // n.e may have changed if the value was rounded up.\r\n        e = n.e;\r\n\r\n        str = coeffToString(n.c);\r\n        len = str.length;\r\n\r\n        // toPrecision returns exponential notation if the number of significant digits\r\n        // specified is less than the number of digits necessary to represent the integer\r\n        // part of the value in fixed-point notation.\r\n\r\n        // Exponential notation.\r\n        if (id == 1 || id == 2 && (i <= e || e <= TO_EXP_NEG)) {\r\n\r\n          // Append zeros?\r\n          for (; len < i; str += '0', len++);\r\n          str = toExponential(str, e);\r\n\r\n        // Fixed-point notation.\r\n        } else {\r\n          i -= ne + (id === 2 && e > ne);\r\n          str = toFixedPoint(str, e, '0');\r\n\r\n          // Append zeros?\r\n          if (e + 1 > len) {\r\n            if (--i > 0) for (str += '.'; i--; str += '0');\r\n          } else {\r\n            i += e - len;\r\n            if (i > 0) {\r\n              if (e + 1 == len) str += '.';\r\n              for (; i--; str += '0');\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      return n.s < 0 && c0 ? '-' + str : str;\r\n    }\r\n\r\n\r\n    // Handle BigNumber.max and BigNumber.min.\r\n    // If any number is NaN, return NaN.\r\n    function maxOrMin(args, n) {\r\n      var k, y,\r\n        i = 1,\r\n        x = new BigNumber(args[0]);\r\n\r\n      for (; i < args.length; i++) {\r\n        y = new BigNumber(args[i]);\r\n        if (!y.s || (k = compare(x, y)) === n || k === 0 && x.s === n) {\r\n          x = y;\r\n        }\r\n      }\r\n\r\n      return x;\r\n    }\r\n\r\n\r\n    /*\r\n     * Strip trailing zeros, calculate base 10 exponent and check against MIN_EXP and MAX_EXP.\r\n     * Called by minus, plus and times.\r\n     */\r\n    function normalise(n, c, e) {\r\n      var i = 1,\r\n        j = c.length;\r\n\r\n       // Remove trailing zeros.\r\n      for (; !c[--j]; c.pop());\r\n\r\n      // Calculate the base 10 exponent. First get the number of digits of c[0].\r\n      for (j = c[0]; j >= 10; j /= 10, i++);\r\n\r\n      // Overflow?\r\n      if ((e = i + e * LOG_BASE - 1) > MAX_EXP) {\r\n\r\n        // Infinity.\r\n        n.c = n.e = null;\r\n\r\n      // Underflow?\r\n      } else if (e < MIN_EXP) {\r\n\r\n        // Zero.\r\n        n.c = [n.e = 0];\r\n      } else {\r\n        n.e = e;\r\n        n.c = c;\r\n      }\r\n\r\n      return n;\r\n    }\r\n\r\n\r\n    // Handle values that fail the validity test in BigNumber.\r\n    parseNumeric = (function () {\r\n      var basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i,\r\n        dotAfter = /^([^.]+)\\.$/,\r\n        dotBefore = /^\\.([^.]+)$/,\r\n        isInfinityOrNaN = /^-?(Infinity|NaN)$/,\r\n        whitespaceOrPlus = /^\\s*\\+(?=[\\w.])|^\\s+|\\s+$/g;\r\n\r\n      return function (x, str, isNum, b) {\r\n        var base,\r\n          s = isNum ? str : str.replace(whitespaceOrPlus, '');\r\n\r\n        // No exception on ±Infinity or NaN.\r\n        if (isInfinityOrNaN.test(s)) {\r\n          x.s = isNaN(s) ? null : s < 0 ? -1 : 1;\r\n        } else {\r\n          if (!isNum) {\r\n\r\n            // basePrefix = /^(-?)0([xbo])(?=\\w[\\w.]*$)/i\r\n            s = s.replace(basePrefix, function (m, p1, p2) {\r\n              base = (p2 = p2.toLowerCase()) == 'x' ? 16 : p2 == 'b' ? 2 : 8;\r\n              return !b || b == base ? p1 : m;\r\n            });\r\n\r\n            if (b) {\r\n              base = b;\r\n\r\n              // E.g. '1.' to '1', '.1' to '0.1'\r\n              s = s.replace(dotAfter, '$1').replace(dotBefore, '0.$1');\r\n            }\r\n\r\n            if (str != s) return new BigNumber(s, base);\r\n          }\r\n\r\n          // '[BigNumber Error] Not a number: {n}'\r\n          // '[BigNumber Error] Not a base {b} number: {n}'\r\n          if (BigNumber.DEBUG) {\r\n            throw Error\r\n              (bignumberError + 'Not a' + (b ? ' base ' + b : '') + ' number: ' + str);\r\n          }\r\n\r\n          // NaN\r\n          x.s = null;\r\n        }\r\n\r\n        x.c = x.e = null;\r\n      }\r\n    })();\r\n\r\n\r\n    /*\r\n     * Round x to sd significant digits using rounding mode rm. Check for over/under-flow.\r\n     * If r is truthy, it is known that there are more digits after the rounding digit.\r\n     */\r\n    function round(x, sd, rm, r) {\r\n      var d, i, j, k, n, ni, rd,\r\n        xc = x.c,\r\n        pows10 = POWS_TEN;\r\n\r\n      // if x is not Infinity or NaN...\r\n      if (xc) {\r\n\r\n        // rd is the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n        // n is a base 1e14 number, the value of the element of array x.c containing rd.\r\n        // ni is the index of n within x.c.\r\n        // d is the number of digits of n.\r\n        // i is the index of rd within n including leading zeros.\r\n        // j is the actual index of rd within n (if < 0, rd is a leading zero).\r\n        out: {\r\n\r\n          // Get the number of digits of the first element of xc.\r\n          for (d = 1, k = xc[0]; k >= 10; k /= 10, d++);\r\n          i = sd - d;\r\n\r\n          // If the rounding digit is in the first element of xc...\r\n          if (i < 0) {\r\n            i += LOG_BASE;\r\n            j = sd;\r\n            n = xc[ni = 0];\r\n\r\n            // Get the rounding digit at index j of n.\r\n            rd = mathfloor(n / pows10[d - j - 1] % 10);\r\n          } else {\r\n            ni = mathceil((i + 1) / LOG_BASE);\r\n\r\n            if (ni >= xc.length) {\r\n\r\n              if (r) {\r\n\r\n                // Needed by sqrt.\r\n                for (; xc.length <= ni; xc.push(0));\r\n                n = rd = 0;\r\n                d = 1;\r\n                i %= LOG_BASE;\r\n                j = i - LOG_BASE + 1;\r\n              } else {\r\n                break out;\r\n              }\r\n            } else {\r\n              n = k = xc[ni];\r\n\r\n              // Get the number of digits of n.\r\n              for (d = 1; k >= 10; k /= 10, d++);\r\n\r\n              // Get the index of rd within n.\r\n              i %= LOG_BASE;\r\n\r\n              // Get the index of rd within n, adjusted for leading zeros.\r\n              // The number of leading zeros of n is given by LOG_BASE - d.\r\n              j = i - LOG_BASE + d;\r\n\r\n              // Get the rounding digit at index j of n.\r\n              rd = j < 0 ? 0 : mathfloor(n / pows10[d - j - 1] % 10);\r\n            }\r\n          }\r\n\r\n          r = r || sd < 0 ||\r\n\r\n          // Are there any non-zero digits after the rounding digit?\r\n          // The expression  n % pows10[d - j - 1]  returns all digits of n to the right\r\n          // of the digit at j, e.g. if n is 908714 and j is 2, the expression gives 714.\r\n           xc[ni + 1] != null || (j < 0 ? n : n % pows10[d - j - 1]);\r\n\r\n          r = rm < 4\r\n           ? (rd || r) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n           : rd > 5 || rd == 5 && (rm == 4 || r || rm == 6 &&\r\n\r\n            // Check whether the digit to the left of the rounding digit is odd.\r\n            ((i > 0 ? j > 0 ? n / pows10[d - j] : 0 : xc[ni - 1]) % 10) & 1 ||\r\n             rm == (x.s < 0 ? 8 : 7));\r\n\r\n          if (sd < 1 || !xc[0]) {\r\n            xc.length = 0;\r\n\r\n            if (r) {\r\n\r\n              // Convert sd to decimal places.\r\n              sd -= x.e + 1;\r\n\r\n              // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n              xc[0] = pows10[(LOG_BASE - sd % LOG_BASE) % LOG_BASE];\r\n              x.e = -sd || 0;\r\n            } else {\r\n\r\n              // Zero.\r\n              xc[0] = x.e = 0;\r\n            }\r\n\r\n            return x;\r\n          }\r\n\r\n          // Remove excess digits.\r\n          if (i == 0) {\r\n            xc.length = ni;\r\n            k = 1;\r\n            ni--;\r\n          } else {\r\n            xc.length = ni + 1;\r\n            k = pows10[LOG_BASE - i];\r\n\r\n            // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n            // j > 0 means i > number of leading zeros of n.\r\n            xc[ni] = j > 0 ? mathfloor(n / pows10[d - j] % pows10[j]) * k : 0;\r\n          }\r\n\r\n          // Round up?\r\n          if (r) {\r\n\r\n            for (; ;) {\r\n\r\n              // If the digit to be rounded up is in the first element of xc...\r\n              if (ni == 0) {\r\n\r\n                // i will be the length of xc[0] before k is added.\r\n                for (i = 1, j = xc[0]; j >= 10; j /= 10, i++);\r\n                j = xc[0] += k;\r\n                for (k = 1; j >= 10; j /= 10, k++);\r\n\r\n                // if i != k the length has increased.\r\n                if (i != k) {\r\n                  x.e++;\r\n                  if (xc[0] == BASE) xc[0] = 1;\r\n                }\r\n\r\n                break;\r\n              } else {\r\n                xc[ni] += k;\r\n                if (xc[ni] != BASE) break;\r\n                xc[ni--] = 0;\r\n                k = 1;\r\n              }\r\n            }\r\n          }\r\n\r\n          // Remove trailing zeros.\r\n          for (i = xc.length; xc[--i] === 0; xc.pop());\r\n        }\r\n\r\n        // Overflow? Infinity.\r\n        if (x.e > MAX_EXP) {\r\n          x.c = x.e = null;\r\n\r\n        // Underflow? Zero.\r\n        } else if (x.e < MIN_EXP) {\r\n          x.c = [x.e = 0];\r\n        }\r\n      }\r\n\r\n      return x;\r\n    }\r\n\r\n\r\n    function valueOf(n) {\r\n      var str,\r\n        e = n.e;\r\n\r\n      if (e === null) return n.toString();\r\n\r\n      str = coeffToString(n.c);\r\n\r\n      str = e <= TO_EXP_NEG || e >= TO_EXP_POS\r\n        ? toExponential(str, e)\r\n        : toFixedPoint(str, e, '0');\r\n\r\n      return n.s < 0 ? '-' + str : str;\r\n    }\r\n\r\n\r\n    // PROTOTYPE/INSTANCE METHODS\r\n\r\n\r\n    /*\r\n     * Return a new BigNumber whose value is the absolute value of this BigNumber.\r\n     */\r\n    P.absoluteValue = P.abs = function () {\r\n      var x = new BigNumber(this);\r\n      if (x.s < 0) x.s = 1;\r\n      return x;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return\r\n     *   1 if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n     *   -1 if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n     *   0 if they have the same value,\r\n     *   or null if the value of either is NaN.\r\n     */\r\n    P.comparedTo = function (y, b) {\r\n      return compare(this, new BigNumber(y, b));\r\n    };\r\n\r\n\r\n    /*\r\n     * If dp is undefined or null or true or false, return the number of decimal places of the\r\n     * value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n     *\r\n     * Otherwise, if dp is a number, return a new BigNumber whose value is the value of this\r\n     * BigNumber rounded to a maximum of dp decimal places using rounding mode rm, or\r\n     * ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * [dp] {number} Decimal places: integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     */\r\n    P.decimalPlaces = P.dp = function (dp, rm) {\r\n      var c, n, v,\r\n        x = this;\r\n\r\n      if (dp != null) {\r\n        intCheck(dp, 0, MAX);\r\n        if (rm == null) rm = ROUNDING_MODE;\r\n        else intCheck(rm, 0, 8);\r\n\r\n        return round(new BigNumber(x), dp + x.e + 1, rm);\r\n      }\r\n\r\n      if (!(c = x.c)) return null;\r\n      n = ((v = c.length - 1) - bitFloor(this.e / LOG_BASE)) * LOG_BASE;\r\n\r\n      // Subtract the number of trailing zeros of the last number.\r\n      if (v = c[v]) for (; v % 10 == 0; v /= 10, n--);\r\n      if (n < 0) n = 0;\r\n\r\n      return n;\r\n    };\r\n\r\n\r\n    /*\r\n     *  n / 0 = I\r\n     *  n / N = N\r\n     *  n / I = 0\r\n     *  0 / n = 0\r\n     *  0 / 0 = N\r\n     *  0 / N = N\r\n     *  0 / I = 0\r\n     *  N / n = N\r\n     *  N / 0 = N\r\n     *  N / N = N\r\n     *  N / I = N\r\n     *  I / n = I\r\n     *  I / 0 = I\r\n     *  I / N = N\r\n     *  I / I = N\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber divided by the value of\r\n     * BigNumber(y, b), rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n     */\r\n    P.dividedBy = P.div = function (y, b) {\r\n      return div(this, new BigNumber(y, b), DECIMAL_PLACES, ROUNDING_MODE);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a new BigNumber whose value is the integer part of dividing the value of this\r\n     * BigNumber by the value of BigNumber(y, b).\r\n     */\r\n    P.dividedToIntegerBy = P.idiv = function (y, b) {\r\n      return div(this, new BigNumber(y, b), 0, 1);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a BigNumber whose value is the value of this BigNumber exponentiated by n.\r\n     *\r\n     * If m is present, return the result modulo m.\r\n     * If n is negative round according to DECIMAL_PLACES and ROUNDING_MODE.\r\n     * If POW_PRECISION is non-zero and m is not present, round to POW_PRECISION using ROUNDING_MODE.\r\n     *\r\n     * The modular power operation works efficiently when x, n, and m are integers, otherwise it\r\n     * is equivalent to calculating x.exponentiatedBy(n).modulo(m) with a POW_PRECISION of 0.\r\n     *\r\n     * n {number|string|BigNumber} The exponent. An integer.\r\n     * [m] {number|string|BigNumber} The modulus.\r\n     *\r\n     * '[BigNumber Error] Exponent not an integer: {n}'\r\n     */\r\n    P.exponentiatedBy = P.pow = function (n, m) {\r\n      var half, isModExp, i, k, more, nIsBig, nIsNeg, nIsOdd, y,\r\n        x = this;\r\n\r\n      n = new BigNumber(n);\r\n\r\n      // Allow NaN and ±Infinity, but not other non-integers.\r\n      if (n.c && !n.isInteger()) {\r\n        throw Error\r\n          (bignumberError + 'Exponent not an integer: ' + valueOf(n));\r\n      }\r\n\r\n      if (m != null) m = new BigNumber(m);\r\n\r\n      // Exponent of MAX_SAFE_INTEGER is 15.\r\n      nIsBig = n.e > 14;\r\n\r\n      // If x is NaN, ±Infinity, ±0 or ±1, or n is ±Infinity, NaN or ±0.\r\n      if (!x.c || !x.c[0] || x.c[0] == 1 && !x.e && x.c.length == 1 || !n.c || !n.c[0]) {\r\n\r\n        // The sign of the result of pow when x is negative depends on the evenness of n.\r\n        // If +n overflows to ±Infinity, the evenness of n would be not be known.\r\n        y = new BigNumber(Math.pow(+valueOf(x), nIsBig ? n.s * (2 - isOdd(n)) : +valueOf(n)));\r\n        return m ? y.mod(m) : y;\r\n      }\r\n\r\n      nIsNeg = n.s < 0;\r\n\r\n      if (m) {\r\n\r\n        // x % m returns NaN if abs(m) is zero, or m is NaN.\r\n        if (m.c ? !m.c[0] : !m.s) return new BigNumber(NaN);\r\n\r\n        isModExp = !nIsNeg && x.isInteger() && m.isInteger();\r\n\r\n        if (isModExp) x = x.mod(m);\r\n\r\n      // Overflow to ±Infinity: >=2**1e10 or >=1.0000024**1e15.\r\n      // Underflow to ±0: <=0.79**1e10 or <=0.9999975**1e15.\r\n      } else if (n.e > 9 && (x.e > 0 || x.e < -1 || (x.e == 0\r\n        // [1, 240000000]\r\n        ? x.c[0] > 1 || nIsBig && x.c[1] >= 24e7\r\n        // [80000000000000]  [99999750000000]\r\n        : x.c[0] < 8e13 || nIsBig && x.c[0] <= 9999975e7))) {\r\n\r\n        // If x is negative and n is odd, k = -0, else k = 0.\r\n        k = x.s < 0 && isOdd(n) ? -0 : 0;\r\n\r\n        // If x >= 1, k = ±Infinity.\r\n        if (x.e > -1) k = 1 / k;\r\n\r\n        // If n is negative return ±0, else return ±Infinity.\r\n        return new BigNumber(nIsNeg ? 1 / k : k);\r\n\r\n      } else if (POW_PRECISION) {\r\n\r\n        // Truncating each coefficient array to a length of k after each multiplication\r\n        // equates to truncating significant digits to POW_PRECISION + [28, 41],\r\n        // i.e. there will be a minimum of 28 guard digits retained.\r\n        k = mathceil(POW_PRECISION / LOG_BASE + 2);\r\n      }\r\n\r\n      if (nIsBig) {\r\n        half = new BigNumber(0.5);\r\n        if (nIsNeg) n.s = 1;\r\n        nIsOdd = isOdd(n);\r\n      } else {\r\n        i = Math.abs(+valueOf(n));\r\n        nIsOdd = i % 2;\r\n      }\r\n\r\n      y = new BigNumber(ONE);\r\n\r\n      // Performs 54 loop iterations for n of 9007199254740991.\r\n      for (; ;) {\r\n\r\n        if (nIsOdd) {\r\n          y = y.times(x);\r\n          if (!y.c) break;\r\n\r\n          if (k) {\r\n            if (y.c.length > k) y.c.length = k;\r\n          } else if (isModExp) {\r\n            y = y.mod(m);    //y = y.minus(div(y, m, 0, MODULO_MODE).times(m));\r\n          }\r\n        }\r\n\r\n        if (i) {\r\n          i = mathfloor(i / 2);\r\n          if (i === 0) break;\r\n          nIsOdd = i % 2;\r\n        } else {\r\n          n = n.times(half);\r\n          round(n, n.e + 1, 1);\r\n\r\n          if (n.e > 14) {\r\n            nIsOdd = isOdd(n);\r\n          } else {\r\n            i = +valueOf(n);\r\n            if (i === 0) break;\r\n            nIsOdd = i % 2;\r\n          }\r\n        }\r\n\r\n        x = x.times(x);\r\n\r\n        if (k) {\r\n          if (x.c && x.c.length > k) x.c.length = k;\r\n        } else if (isModExp) {\r\n          x = x.mod(m);    //x = x.minus(div(x, m, 0, MODULO_MODE).times(m));\r\n        }\r\n      }\r\n\r\n      if (isModExp) return y;\r\n      if (nIsNeg) y = ONE.div(y);\r\n\r\n      return m ? y.mod(m) : k ? round(y, POW_PRECISION, ROUNDING_MODE, more) : y;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a new BigNumber whose value is the value of this BigNumber rounded to an integer\r\n     * using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {rm}'\r\n     */\r\n    P.integerValue = function (rm) {\r\n      var n = new BigNumber(this);\r\n      if (rm == null) rm = ROUNDING_MODE;\r\n      else intCheck(rm, 0, 8);\r\n      return round(n, n.e + 1, rm);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is equal to the value of BigNumber(y, b),\r\n     * otherwise return false.\r\n     */\r\n    P.isEqualTo = P.eq = function (y, b) {\r\n      return compare(this, new BigNumber(y, b)) === 0;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is a finite number, otherwise return false.\r\n     */\r\n    P.isFinite = function () {\r\n      return !!this.c;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is greater than the value of BigNumber(y, b),\r\n     * otherwise return false.\r\n     */\r\n    P.isGreaterThan = P.gt = function (y, b) {\r\n      return compare(this, new BigNumber(y, b)) > 0;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is greater than or equal to the value of\r\n     * BigNumber(y, b), otherwise return false.\r\n     */\r\n    P.isGreaterThanOrEqualTo = P.gte = function (y, b) {\r\n      return (b = compare(this, new BigNumber(y, b))) === 1 || b === 0;\r\n\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is an integer, otherwise return false.\r\n     */\r\n    P.isInteger = function () {\r\n      return !!this.c && bitFloor(this.e / LOG_BASE) > this.c.length - 2;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is less than the value of BigNumber(y, b),\r\n     * otherwise return false.\r\n     */\r\n    P.isLessThan = P.lt = function (y, b) {\r\n      return compare(this, new BigNumber(y, b)) < 0;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is less than or equal to the value of\r\n     * BigNumber(y, b), otherwise return false.\r\n     */\r\n    P.isLessThanOrEqualTo = P.lte = function (y, b) {\r\n      return (b = compare(this, new BigNumber(y, b))) === -1 || b === 0;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is NaN, otherwise return false.\r\n     */\r\n    P.isNaN = function () {\r\n      return !this.s;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is negative, otherwise return false.\r\n     */\r\n    P.isNegative = function () {\r\n      return this.s < 0;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is positive, otherwise return false.\r\n     */\r\n    P.isPositive = function () {\r\n      return this.s > 0;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return true if the value of this BigNumber is 0 or -0, otherwise return false.\r\n     */\r\n    P.isZero = function () {\r\n      return !!this.c && this.c[0] == 0;\r\n    };\r\n\r\n\r\n    /*\r\n     *  n - 0 = n\r\n     *  n - N = N\r\n     *  n - I = -I\r\n     *  0 - n = -n\r\n     *  0 - 0 = 0\r\n     *  0 - N = N\r\n     *  0 - I = -I\r\n     *  N - n = N\r\n     *  N - 0 = N\r\n     *  N - N = N\r\n     *  N - I = N\r\n     *  I - n = I\r\n     *  I - 0 = I\r\n     *  I - N = N\r\n     *  I - I = N\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber minus the value of\r\n     * BigNumber(y, b).\r\n     */\r\n    P.minus = function (y, b) {\r\n      var i, j, t, xLTy,\r\n        x = this,\r\n        a = x.s;\r\n\r\n      y = new BigNumber(y, b);\r\n      b = y.s;\r\n\r\n      // Either NaN?\r\n      if (!a || !b) return new BigNumber(NaN);\r\n\r\n      // Signs differ?\r\n      if (a != b) {\r\n        y.s = -b;\r\n        return x.plus(y);\r\n      }\r\n\r\n      var xe = x.e / LOG_BASE,\r\n        ye = y.e / LOG_BASE,\r\n        xc = x.c,\r\n        yc = y.c;\r\n\r\n      if (!xe || !ye) {\r\n\r\n        // Either Infinity?\r\n        if (!xc || !yc) return xc ? (y.s = -b, y) : new BigNumber(yc ? x : NaN);\r\n\r\n        // Either zero?\r\n        if (!xc[0] || !yc[0]) {\r\n\r\n          // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\r\n          return yc[0] ? (y.s = -b, y) : new BigNumber(xc[0] ? x :\r\n\r\n           // IEEE 754 (2008) 6.3: n - n = -0 when rounding to -Infinity\r\n           ROUNDING_MODE == 3 ? -0 : 0);\r\n        }\r\n      }\r\n\r\n      xe = bitFloor(xe);\r\n      ye = bitFloor(ye);\r\n      xc = xc.slice();\r\n\r\n      // Determine which is the bigger number.\r\n      if (a = xe - ye) {\r\n\r\n        if (xLTy = a < 0) {\r\n          a = -a;\r\n          t = xc;\r\n        } else {\r\n          ye = xe;\r\n          t = yc;\r\n        }\r\n\r\n        t.reverse();\r\n\r\n        // Prepend zeros to equalise exponents.\r\n        for (b = a; b--; t.push(0));\r\n        t.reverse();\r\n      } else {\r\n\r\n        // Exponents equal. Check digit by digit.\r\n        j = (xLTy = (a = xc.length) < (b = yc.length)) ? a : b;\r\n\r\n        for (a = b = 0; b < j; b++) {\r\n\r\n          if (xc[b] != yc[b]) {\r\n            xLTy = xc[b] < yc[b];\r\n            break;\r\n          }\r\n        }\r\n      }\r\n\r\n      // x < y? Point xc to the array of the bigger number.\r\n      if (xLTy) {\r\n        t = xc;\r\n        xc = yc;\r\n        yc = t;\r\n        y.s = -y.s;\r\n      }\r\n\r\n      b = (j = yc.length) - (i = xc.length);\r\n\r\n      // Append zeros to xc if shorter.\r\n      // No need to add zeros to yc if shorter as subtract only needs to start at yc.length.\r\n      if (b > 0) for (; b--; xc[i++] = 0);\r\n      b = BASE - 1;\r\n\r\n      // Subtract yc from xc.\r\n      for (; j > a;) {\r\n\r\n        if (xc[--j] < yc[j]) {\r\n          for (i = j; i && !xc[--i]; xc[i] = b);\r\n          --xc[i];\r\n          xc[j] += BASE;\r\n        }\r\n\r\n        xc[j] -= yc[j];\r\n      }\r\n\r\n      // Remove leading zeros and adjust exponent accordingly.\r\n      for (; xc[0] == 0; xc.splice(0, 1), --ye);\r\n\r\n      // Zero?\r\n      if (!xc[0]) {\r\n\r\n        // Following IEEE 754 (2008) 6.3,\r\n        // n - n = +0  but  n - n = -0  when rounding towards -Infinity.\r\n        y.s = ROUNDING_MODE == 3 ? -1 : 1;\r\n        y.c = [y.e = 0];\r\n        return y;\r\n      }\r\n\r\n      // No need to check for Infinity as +x - +y != Infinity && -x - -y != Infinity\r\n      // for finite x and y.\r\n      return normalise(y, xc, ye);\r\n    };\r\n\r\n\r\n    /*\r\n     *   n % 0 =  N\r\n     *   n % N =  N\r\n     *   n % I =  n\r\n     *   0 % n =  0\r\n     *  -0 % n = -0\r\n     *   0 % 0 =  N\r\n     *   0 % N =  N\r\n     *   0 % I =  0\r\n     *   N % n =  N\r\n     *   N % 0 =  N\r\n     *   N % N =  N\r\n     *   N % I =  N\r\n     *   I % n =  N\r\n     *   I % 0 =  N\r\n     *   I % N =  N\r\n     *   I % I =  N\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber modulo the value of\r\n     * BigNumber(y, b). The result depends on the value of MODULO_MODE.\r\n     */\r\n    P.modulo = P.mod = function (y, b) {\r\n      var q, s,\r\n        x = this;\r\n\r\n      y = new BigNumber(y, b);\r\n\r\n      // Return NaN if x is Infinity or NaN, or y is NaN or zero.\r\n      if (!x.c || !y.s || y.c && !y.c[0]) {\r\n        return new BigNumber(NaN);\r\n\r\n      // Return x if y is Infinity or x is zero.\r\n      } else if (!y.c || x.c && !x.c[0]) {\r\n        return new BigNumber(x);\r\n      }\r\n\r\n      if (MODULO_MODE == 9) {\r\n\r\n        // Euclidian division: q = sign(y) * floor(x / abs(y))\r\n        // r = x - qy    where  0 <= r < abs(y)\r\n        s = y.s;\r\n        y.s = 1;\r\n        q = div(x, y, 0, 3);\r\n        y.s = s;\r\n        q.s *= s;\r\n      } else {\r\n        q = div(x, y, 0, MODULO_MODE);\r\n      }\r\n\r\n      y = x.minus(q.times(y));\r\n\r\n      // To match JavaScript %, ensure sign of zero is sign of dividend.\r\n      if (!y.c[0] && MODULO_MODE == 1) y.s = x.s;\r\n\r\n      return y;\r\n    };\r\n\r\n\r\n    /*\r\n     *  n * 0 = 0\r\n     *  n * N = N\r\n     *  n * I = I\r\n     *  0 * n = 0\r\n     *  0 * 0 = 0\r\n     *  0 * N = N\r\n     *  0 * I = N\r\n     *  N * n = N\r\n     *  N * 0 = N\r\n     *  N * N = N\r\n     *  N * I = N\r\n     *  I * n = I\r\n     *  I * 0 = N\r\n     *  I * N = N\r\n     *  I * I = I\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber multiplied by the value\r\n     * of BigNumber(y, b).\r\n     */\r\n    P.multipliedBy = P.times = function (y, b) {\r\n      var c, e, i, j, k, m, xcL, xlo, xhi, ycL, ylo, yhi, zc,\r\n        base, sqrtBase,\r\n        x = this,\r\n        xc = x.c,\r\n        yc = (y = new BigNumber(y, b)).c;\r\n\r\n      // Either NaN, ±Infinity or ±0?\r\n      if (!xc || !yc || !xc[0] || !yc[0]) {\r\n\r\n        // Return NaN if either is NaN, or one is 0 and the other is Infinity.\r\n        if (!x.s || !y.s || xc && !xc[0] && !yc || yc && !yc[0] && !xc) {\r\n          y.c = y.e = y.s = null;\r\n        } else {\r\n          y.s *= x.s;\r\n\r\n          // Return ±Infinity if either is ±Infinity.\r\n          if (!xc || !yc) {\r\n            y.c = y.e = null;\r\n\r\n          // Return ±0 if either is ±0.\r\n          } else {\r\n            y.c = [0];\r\n            y.e = 0;\r\n          }\r\n        }\r\n\r\n        return y;\r\n      }\r\n\r\n      e = bitFloor(x.e / LOG_BASE) + bitFloor(y.e / LOG_BASE);\r\n      y.s *= x.s;\r\n      xcL = xc.length;\r\n      ycL = yc.length;\r\n\r\n      // Ensure xc points to longer array and xcL to its length.\r\n      if (xcL < ycL) {\r\n        zc = xc;\r\n        xc = yc;\r\n        yc = zc;\r\n        i = xcL;\r\n        xcL = ycL;\r\n        ycL = i;\r\n      }\r\n\r\n      // Initialise the result array with zeros.\r\n      for (i = xcL + ycL, zc = []; i--; zc.push(0));\r\n\r\n      base = BASE;\r\n      sqrtBase = SQRT_BASE;\r\n\r\n      for (i = ycL; --i >= 0;) {\r\n        c = 0;\r\n        ylo = yc[i] % sqrtBase;\r\n        yhi = yc[i] / sqrtBase | 0;\r\n\r\n        for (k = xcL, j = i + k; j > i;) {\r\n          xlo = xc[--k] % sqrtBase;\r\n          xhi = xc[k] / sqrtBase | 0;\r\n          m = yhi * xlo + xhi * ylo;\r\n          xlo = ylo * xlo + ((m % sqrtBase) * sqrtBase) + zc[j] + c;\r\n          c = (xlo / base | 0) + (m / sqrtBase | 0) + yhi * xhi;\r\n          zc[j--] = xlo % base;\r\n        }\r\n\r\n        zc[j] = c;\r\n      }\r\n\r\n      if (c) {\r\n        ++e;\r\n      } else {\r\n        zc.splice(0, 1);\r\n      }\r\n\r\n      return normalise(y, zc, e);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a new BigNumber whose value is the value of this BigNumber negated,\r\n     * i.e. multiplied by -1.\r\n     */\r\n    P.negated = function () {\r\n      var x = new BigNumber(this);\r\n      x.s = -x.s || null;\r\n      return x;\r\n    };\r\n\r\n\r\n    /*\r\n     *  n + 0 = n\r\n     *  n + N = N\r\n     *  n + I = I\r\n     *  0 + n = n\r\n     *  0 + 0 = 0\r\n     *  0 + N = N\r\n     *  0 + I = I\r\n     *  N + n = N\r\n     *  N + 0 = N\r\n     *  N + N = N\r\n     *  N + I = N\r\n     *  I + n = I\r\n     *  I + 0 = I\r\n     *  I + N = N\r\n     *  I + I = I\r\n     *\r\n     * Return a new BigNumber whose value is the value of this BigNumber plus the value of\r\n     * BigNumber(y, b).\r\n     */\r\n    P.plus = function (y, b) {\r\n      var t,\r\n        x = this,\r\n        a = x.s;\r\n\r\n      y = new BigNumber(y, b);\r\n      b = y.s;\r\n\r\n      // Either NaN?\r\n      if (!a || !b) return new BigNumber(NaN);\r\n\r\n      // Signs differ?\r\n       if (a != b) {\r\n        y.s = -b;\r\n        return x.minus(y);\r\n      }\r\n\r\n      var xe = x.e / LOG_BASE,\r\n        ye = y.e / LOG_BASE,\r\n        xc = x.c,\r\n        yc = y.c;\r\n\r\n      if (!xe || !ye) {\r\n\r\n        // Return ±Infinity if either ±Infinity.\r\n        if (!xc || !yc) return new BigNumber(a / 0);\r\n\r\n        // Either zero?\r\n        // Return y if y is non-zero, x if x is non-zero, or zero if both are zero.\r\n        if (!xc[0] || !yc[0]) return yc[0] ? y : new BigNumber(xc[0] ? x : a * 0);\r\n      }\r\n\r\n      xe = bitFloor(xe);\r\n      ye = bitFloor(ye);\r\n      xc = xc.slice();\r\n\r\n      // Prepend zeros to equalise exponents. Faster to use reverse then do unshifts.\r\n      if (a = xe - ye) {\r\n        if (a > 0) {\r\n          ye = xe;\r\n          t = yc;\r\n        } else {\r\n          a = -a;\r\n          t = xc;\r\n        }\r\n\r\n        t.reverse();\r\n        for (; a--; t.push(0));\r\n        t.reverse();\r\n      }\r\n\r\n      a = xc.length;\r\n      b = yc.length;\r\n\r\n      // Point xc to the longer array, and b to the shorter length.\r\n      if (a - b < 0) {\r\n        t = yc;\r\n        yc = xc;\r\n        xc = t;\r\n        b = a;\r\n      }\r\n\r\n      // Only start adding at yc.length - 1 as the further digits of xc can be ignored.\r\n      for (a = 0; b;) {\r\n        a = (xc[--b] = xc[b] + yc[b] + a) / BASE | 0;\r\n        xc[b] = BASE === xc[b] ? 0 : xc[b] % BASE;\r\n      }\r\n\r\n      if (a) {\r\n        xc = [a].concat(xc);\r\n        ++ye;\r\n      }\r\n\r\n      // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n      // ye = MAX_EXP + 1 possible\r\n      return normalise(y, xc, ye);\r\n    };\r\n\r\n\r\n    /*\r\n     * If sd is undefined or null or true or false, return the number of significant digits of\r\n     * the value of this BigNumber, or null if the value of this BigNumber is ±Infinity or NaN.\r\n     * If sd is true include integer-part trailing zeros in the count.\r\n     *\r\n     * Otherwise, if sd is a number, return a new BigNumber whose value is the value of this\r\n     * BigNumber rounded to a maximum of sd significant digits using rounding mode rm, or\r\n     * ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * sd {number|boolean} number: significant digits: integer, 1 to MAX inclusive.\r\n     *                     boolean: whether to count integer-part trailing zeros: true or false.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n     */\r\n    P.precision = P.sd = function (sd, rm) {\r\n      var c, n, v,\r\n        x = this;\r\n\r\n      if (sd != null && sd !== !!sd) {\r\n        intCheck(sd, 1, MAX);\r\n        if (rm == null) rm = ROUNDING_MODE;\r\n        else intCheck(rm, 0, 8);\r\n\r\n        return round(new BigNumber(x), sd, rm);\r\n      }\r\n\r\n      if (!(c = x.c)) return null;\r\n      v = c.length - 1;\r\n      n = v * LOG_BASE + 1;\r\n\r\n      if (v = c[v]) {\r\n\r\n        // Subtract the number of trailing zeros of the last element.\r\n        for (; v % 10 == 0; v /= 10, n--);\r\n\r\n        // Add the number of digits of the first element.\r\n        for (v = c[0]; v >= 10; v /= 10, n++);\r\n      }\r\n\r\n      if (sd && x.e + 1 > n) n = x.e + 1;\r\n\r\n      return n;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a new BigNumber whose value is the value of this BigNumber shifted by k places\r\n     * (powers of 10). Shift to the right if n > 0, and to the left if n < 0.\r\n     *\r\n     * k {number} Integer, -MAX_SAFE_INTEGER to MAX_SAFE_INTEGER inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {k}'\r\n     */\r\n    P.shiftedBy = function (k) {\r\n      intCheck(k, -MAX_SAFE_INTEGER, MAX_SAFE_INTEGER);\r\n      return this.times('1e' + k);\r\n    };\r\n\r\n\r\n    /*\r\n     *  sqrt(-n) =  N\r\n     *  sqrt(N) =  N\r\n     *  sqrt(-I) =  N\r\n     *  sqrt(I) =  I\r\n     *  sqrt(0) =  0\r\n     *  sqrt(-0) = -0\r\n     *\r\n     * Return a new BigNumber whose value is the square root of the value of this BigNumber,\r\n     * rounded according to DECIMAL_PLACES and ROUNDING_MODE.\r\n     */\r\n    P.squareRoot = P.sqrt = function () {\r\n      var m, n, r, rep, t,\r\n        x = this,\r\n        c = x.c,\r\n        s = x.s,\r\n        e = x.e,\r\n        dp = DECIMAL_PLACES + 4,\r\n        half = new BigNumber('0.5');\r\n\r\n      // Negative/NaN/Infinity/zero?\r\n      if (s !== 1 || !c || !c[0]) {\r\n        return new BigNumber(!s || s < 0 && (!c || c[0]) ? NaN : c ? x : 1 / 0);\r\n      }\r\n\r\n      // Initial estimate.\r\n      s = Math.sqrt(+valueOf(x));\r\n\r\n      // Math.sqrt underflow/overflow?\r\n      // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n      if (s == 0 || s == 1 / 0) {\r\n        n = coeffToString(c);\r\n        if ((n.length + e) % 2 == 0) n += '0';\r\n        s = Math.sqrt(+n);\r\n        e = bitFloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n        if (s == 1 / 0) {\r\n          n = '5e' + e;\r\n        } else {\r\n          n = s.toExponential();\r\n          n = n.slice(0, n.indexOf('e') + 1) + e;\r\n        }\r\n\r\n        r = new BigNumber(n);\r\n      } else {\r\n        r = new BigNumber(s + '');\r\n      }\r\n\r\n      // Check for zero.\r\n      // r could be zero if MIN_EXP is changed after the this value was created.\r\n      // This would cause a division by zero (x/t) and hence Infinity below, which would cause\r\n      // coeffToString to throw.\r\n      if (r.c[0]) {\r\n        e = r.e;\r\n        s = e + dp;\r\n        if (s < 3) s = 0;\r\n\r\n        // Newton-Raphson iteration.\r\n        for (; ;) {\r\n          t = r;\r\n          r = half.times(t.plus(div(x, t, dp, 1)));\r\n\r\n          if (coeffToString(t.c).slice(0, s) === (n = coeffToString(r.c)).slice(0, s)) {\r\n\r\n            // The exponent of r may here be one less than the final result exponent,\r\n            // e.g 0.0009999 (e-4) --> 0.001 (e-3), so adjust s so the rounding digits\r\n            // are indexed correctly.\r\n            if (r.e < e) --s;\r\n            n = n.slice(s - 3, s + 1);\r\n\r\n            // The 4th rounding digit may be in error by -1 so if the 4 rounding digits\r\n            // are 9999 or 4999 (i.e. approaching a rounding boundary) continue the\r\n            // iteration.\r\n            if (n == '9999' || !rep && n == '4999') {\r\n\r\n              // On the first iteration only, check to see if rounding up gives the\r\n              // exact result as the nines may infinitely repeat.\r\n              if (!rep) {\r\n                round(t, t.e + DECIMAL_PLACES + 2, 0);\r\n\r\n                if (t.times(t).eq(x)) {\r\n                  r = t;\r\n                  break;\r\n                }\r\n              }\r\n\r\n              dp += 4;\r\n              s += 4;\r\n              rep = 1;\r\n            } else {\r\n\r\n              // If rounding digits are null, 0{0,4} or 50{0,3}, check for exact\r\n              // result. If not, then there are further digits and m will be truthy.\r\n              if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n                // Truncate to the first rounding digit.\r\n                round(r, r.e + DECIMAL_PLACES + 2, 1);\r\n                m = !r.times(r).eq(x);\r\n              }\r\n\r\n              break;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      return round(r, r.e + DECIMAL_PLACES + 1, ROUNDING_MODE, m);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a string representing the value of this BigNumber in exponential notation and\r\n     * rounded using ROUNDING_MODE to dp fixed decimal places.\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     */\r\n    P.toExponential = function (dp, rm) {\r\n      if (dp != null) {\r\n        intCheck(dp, 0, MAX);\r\n        dp++;\r\n      }\r\n      return format(this, dp, rm, 1);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a string representing the value of this BigNumber in fixed-point notation rounding\r\n     * to dp fixed decimal places using rounding mode rm, or ROUNDING_MODE if rm is omitted.\r\n     *\r\n     * Note: as with JavaScript's number type, (-0).toFixed(0) is '0',\r\n     * but e.g. (-0.00001).toFixed(0) is '-0'.\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     */\r\n    P.toFixed = function (dp, rm) {\r\n      if (dp != null) {\r\n        intCheck(dp, 0, MAX);\r\n        dp = dp + this.e + 1;\r\n      }\r\n      return format(this, dp, rm);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a string representing the value of this BigNumber in fixed-point notation rounded\r\n     * using rm or ROUNDING_MODE to dp decimal places, and formatted according to the properties\r\n     * of the format or FORMAT object (see BigNumber.set).\r\n     *\r\n     * The formatting object may contain some or all of the properties shown below.\r\n     *\r\n     * FORMAT = {\r\n     *   prefix: '',\r\n     *   groupSize: 3,\r\n     *   secondaryGroupSize: 0,\r\n     *   groupSeparator: ',',\r\n     *   decimalSeparator: '.',\r\n     *   fractionGroupSize: 0,\r\n     *   fractionGroupSeparator: '\\xA0',      // non-breaking space\r\n     *   suffix: ''\r\n     * };\r\n     *\r\n     * [dp] {number} Decimal places. Integer, 0 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     * [format] {object} Formatting options. See FORMAT pbject above.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {dp|rm}'\r\n     * '[BigNumber Error] Argument not an object: {format}'\r\n     */\r\n    P.toFormat = function (dp, rm, format) {\r\n      var str,\r\n        x = this;\r\n\r\n      if (format == null) {\r\n        if (dp != null && rm && typeof rm == 'object') {\r\n          format = rm;\r\n          rm = null;\r\n        } else if (dp && typeof dp == 'object') {\r\n          format = dp;\r\n          dp = rm = null;\r\n        } else {\r\n          format = FORMAT;\r\n        }\r\n      } else if (typeof format != 'object') {\r\n        throw Error\r\n          (bignumberError + 'Argument not an object: ' + format);\r\n      }\r\n\r\n      str = x.toFixed(dp, rm);\r\n\r\n      if (x.c) {\r\n        var i,\r\n          arr = str.split('.'),\r\n          g1 = +format.groupSize,\r\n          g2 = +format.secondaryGroupSize,\r\n          groupSeparator = format.groupSeparator || '',\r\n          intPart = arr[0],\r\n          fractionPart = arr[1],\r\n          isNeg = x.s < 0,\r\n          intDigits = isNeg ? intPart.slice(1) : intPart,\r\n          len = intDigits.length;\r\n\r\n        if (g2) {\r\n          i = g1;\r\n          g1 = g2;\r\n          g2 = i;\r\n          len -= i;\r\n        }\r\n\r\n        if (g1 > 0 && len > 0) {\r\n          i = len % g1 || g1;\r\n          intPart = intDigits.substr(0, i);\r\n          for (; i < len; i += g1) intPart += groupSeparator + intDigits.substr(i, g1);\r\n          if (g2 > 0) intPart += groupSeparator + intDigits.slice(i);\r\n          if (isNeg) intPart = '-' + intPart;\r\n        }\r\n\r\n        str = fractionPart\r\n         ? intPart + (format.decimalSeparator || '') + ((g2 = +format.fractionGroupSize)\r\n          ? fractionPart.replace(new RegExp('\\\\d{' + g2 + '}\\\\B', 'g'),\r\n           '$&' + (format.fractionGroupSeparator || ''))\r\n          : fractionPart)\r\n         : intPart;\r\n      }\r\n\r\n      return (format.prefix || '') + str + (format.suffix || '');\r\n    };\r\n\r\n\r\n    /*\r\n     * Return an array of two BigNumbers representing the value of this BigNumber as a simple\r\n     * fraction with an integer numerator and an integer denominator.\r\n     * The denominator will be a positive non-zero value less than or equal to the specified\r\n     * maximum denominator. If a maximum denominator is not specified, the denominator will be\r\n     * the lowest value necessary to represent the number exactly.\r\n     *\r\n     * [md] {number|string|BigNumber} Integer >= 1, or Infinity. The maximum denominator.\r\n     *\r\n     * '[BigNumber Error] Argument {not an integer|out of range} : {md}'\r\n     */\r\n    P.toFraction = function (md) {\r\n      var d, d0, d1, d2, e, exp, n, n0, n1, q, r, s,\r\n        x = this,\r\n        xc = x.c;\r\n\r\n      if (md != null) {\r\n        n = new BigNumber(md);\r\n\r\n        // Throw if md is less than one or is not an integer, unless it is Infinity.\r\n        if (!n.isInteger() && (n.c || n.s !== 1) || n.lt(ONE)) {\r\n          throw Error\r\n            (bignumberError + 'Argument ' +\r\n              (n.isInteger() ? 'out of range: ' : 'not an integer: ') + valueOf(n));\r\n        }\r\n      }\r\n\r\n      if (!xc) return new BigNumber(x);\r\n\r\n      d = new BigNumber(ONE);\r\n      n1 = d0 = new BigNumber(ONE);\r\n      d1 = n0 = new BigNumber(ONE);\r\n      s = coeffToString(xc);\r\n\r\n      // Determine initial denominator.\r\n      // d is a power of 10 and the minimum max denominator that specifies the value exactly.\r\n      e = d.e = s.length - x.e - 1;\r\n      d.c[0] = POWS_TEN[(exp = e % LOG_BASE) < 0 ? LOG_BASE + exp : exp];\r\n      md = !md || n.comparedTo(d) > 0 ? (e > 0 ? d : n1) : n;\r\n\r\n      exp = MAX_EXP;\r\n      MAX_EXP = 1 / 0;\r\n      n = new BigNumber(s);\r\n\r\n      // n0 = d1 = 0\r\n      n0.c[0] = 0;\r\n\r\n      for (; ;)  {\r\n        q = div(n, d, 0, 1);\r\n        d2 = d0.plus(q.times(d1));\r\n        if (d2.comparedTo(md) == 1) break;\r\n        d0 = d1;\r\n        d1 = d2;\r\n        n1 = n0.plus(q.times(d2 = n1));\r\n        n0 = d2;\r\n        d = n.minus(q.times(d2 = d));\r\n        n = d2;\r\n      }\r\n\r\n      d2 = div(md.minus(d0), d1, 0, 1);\r\n      n0 = n0.plus(d2.times(n1));\r\n      d0 = d0.plus(d2.times(d1));\r\n      n0.s = n1.s = x.s;\r\n      e = e * 2;\r\n\r\n      // Determine which fraction is closer to x, n0/d0 or n1/d1\r\n      r = div(n1, d1, e, ROUNDING_MODE).minus(x).abs().comparedTo(\r\n          div(n0, d0, e, ROUNDING_MODE).minus(x).abs()) < 1 ? [n1, d1] : [n0, d0];\r\n\r\n      MAX_EXP = exp;\r\n\r\n      return r;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return the value of this BigNumber converted to a number primitive.\r\n     */\r\n    P.toNumber = function () {\r\n      return +valueOf(this);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a string representing the value of this BigNumber rounded to sd significant digits\r\n     * using rounding mode rm or ROUNDING_MODE. If sd is less than the number of digits\r\n     * necessary to represent the integer part of the value in fixed-point notation, then use\r\n     * exponential notation.\r\n     *\r\n     * [sd] {number} Significant digits. Integer, 1 to MAX inclusive.\r\n     * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n     *\r\n     * '[BigNumber Error] Argument {not a primitive number|not an integer|out of range}: {sd|rm}'\r\n     */\r\n    P.toPrecision = function (sd, rm) {\r\n      if (sd != null) intCheck(sd, 1, MAX);\r\n      return format(this, sd, rm, 2);\r\n    };\r\n\r\n\r\n    /*\r\n     * Return a string representing the value of this BigNumber in base b, or base 10 if b is\r\n     * omitted. If a base is specified, including base 10, round according to DECIMAL_PLACES and\r\n     * ROUNDING_MODE. If a base is not specified, and this BigNumber has a positive exponent\r\n     * that is equal to or greater than TO_EXP_POS, or a negative exponent equal to or less than\r\n     * TO_EXP_NEG, return exponential notation.\r\n     *\r\n     * [b] {number} Integer, 2 to ALPHABET.length inclusive.\r\n     *\r\n     * '[BigNumber Error] Base {not a primitive number|not an integer|out of range}: {b}'\r\n     */\r\n    P.toString = function (b) {\r\n      var str,\r\n        n = this,\r\n        s = n.s,\r\n        e = n.e;\r\n\r\n      // Infinity or NaN?\r\n      if (e === null) {\r\n        if (s) {\r\n          str = 'Infinity';\r\n          if (s < 0) str = '-' + str;\r\n        } else {\r\n          str = 'NaN';\r\n        }\r\n      } else {\r\n        if (b == null) {\r\n          str = e <= TO_EXP_NEG || e >= TO_EXP_POS\r\n           ? toExponential(coeffToString(n.c), e)\r\n           : toFixedPoint(coeffToString(n.c), e, '0');\r\n        } else if (b === 10 && alphabetHasNormalDecimalDigits) {\r\n          n = round(new BigNumber(n), DECIMAL_PLACES + e + 1, ROUNDING_MODE);\r\n          str = toFixedPoint(coeffToString(n.c), n.e, '0');\r\n        } else {\r\n          intCheck(b, 2, ALPHABET.length, 'Base');\r\n          str = convertBase(toFixedPoint(coeffToString(n.c), e, '0'), 10, b, s, true);\r\n        }\r\n\r\n        if (s < 0 && n.c[0]) str = '-' + str;\r\n      }\r\n\r\n      return str;\r\n    };\r\n\r\n\r\n    /*\r\n     * Return as toString, but do not accept a base argument, and include the minus sign for\r\n     * negative zero.\r\n     */\r\n    P.valueOf = P.toJSON = function () {\r\n      return valueOf(this);\r\n    };\r\n\r\n\r\n    P._isBigNumber = true;\r\n\r\n    if (configObject != null) BigNumber.set(configObject);\r\n\r\n    return BigNumber;\r\n  }\r\n\r\n\r\n  // PRIVATE HELPER FUNCTIONS\r\n\r\n  // These functions don't need access to variables,\r\n  // e.g. DECIMAL_PLACES, in the scope of the `clone` function above.\r\n\r\n\r\n  function bitFloor(n) {\r\n    var i = n | 0;\r\n    return n > 0 || n === i ? i : i - 1;\r\n  }\r\n\r\n\r\n  // Return a coefficient array as a string of base 10 digits.\r\n  function coeffToString(a) {\r\n    var s, z,\r\n      i = 1,\r\n      j = a.length,\r\n      r = a[0] + '';\r\n\r\n    for (; i < j;) {\r\n      s = a[i++] + '';\r\n      z = LOG_BASE - s.length;\r\n      for (; z--; s = '0' + s);\r\n      r += s;\r\n    }\r\n\r\n    // Determine trailing zeros.\r\n    for (j = r.length; r.charCodeAt(--j) === 48;);\r\n\r\n    return r.slice(0, j + 1 || 1);\r\n  }\r\n\r\n\r\n  // Compare the value of BigNumbers x and y.\r\n  function compare(x, y) {\r\n    var a, b,\r\n      xc = x.c,\r\n      yc = y.c,\r\n      i = x.s,\r\n      j = y.s,\r\n      k = x.e,\r\n      l = y.e;\r\n\r\n    // Either NaN?\r\n    if (!i || !j) return null;\r\n\r\n    a = xc && !xc[0];\r\n    b = yc && !yc[0];\r\n\r\n    // Either zero?\r\n    if (a || b) return a ? b ? 0 : -j : i;\r\n\r\n    // Signs differ?\r\n    if (i != j) return i;\r\n\r\n    a = i < 0;\r\n    b = k == l;\r\n\r\n    // Either Infinity?\r\n    if (!xc || !yc) return b ? 0 : !xc ^ a ? 1 : -1;\r\n\r\n    // Compare exponents.\r\n    if (!b) return k > l ^ a ? 1 : -1;\r\n\r\n    j = (k = xc.length) < (l = yc.length) ? k : l;\r\n\r\n    // Compare digit by digit.\r\n    for (i = 0; i < j; i++) if (xc[i] != yc[i]) return xc[i] > yc[i] ^ a ? 1 : -1;\r\n\r\n    // Compare lengths.\r\n    return k == l ? 0 : k > l ^ a ? 1 : -1;\r\n  }\r\n\r\n\r\n  /*\r\n   * Check that n is a primitive number, an integer, and in range, otherwise throw.\r\n   */\r\n  function intCheck(n, min, max, name) {\r\n    if (n < min || n > max || n !== mathfloor(n)) {\r\n      throw Error\r\n       (bignumberError + (name || 'Argument') + (typeof n == 'number'\r\n         ? n < min || n > max ? ' out of range: ' : ' not an integer: '\r\n         : ' not a primitive number: ') + String(n));\r\n    }\r\n  }\r\n\r\n\r\n  // Assumes finite n.\r\n  function isOdd(n) {\r\n    var k = n.c.length - 1;\r\n    return bitFloor(n.e / LOG_BASE) == k && n.c[k] % 2 != 0;\r\n  }\r\n\r\n\r\n  function toExponential(str, e) {\r\n    return (str.length > 1 ? str.charAt(0) + '.' + str.slice(1) : str) +\r\n     (e < 0 ? 'e' : 'e+') + e;\r\n  }\r\n\r\n\r\n  function toFixedPoint(str, e, z) {\r\n    var len, zs;\r\n\r\n    // Negative exponent?\r\n    if (e < 0) {\r\n\r\n      // Prepend zeros.\r\n      for (zs = z + '.'; ++e; zs += z);\r\n      str = zs + str;\r\n\r\n    // Positive exponent\r\n    } else {\r\n      len = str.length;\r\n\r\n      // Append zeros.\r\n      if (++e > len) {\r\n        for (zs = z, e -= len; --e; zs += z);\r\n        str += zs;\r\n      } else if (e < len) {\r\n        str = str.slice(0, e) + '.' + str.slice(e);\r\n      }\r\n    }\r\n\r\n    return str;\r\n  }\r\n\r\n\r\n  // EXPORT\r\n\r\n\r\n  BigNumber = clone();\r\n  BigNumber['default'] = BigNumber.BigNumber = BigNumber;\r\n\r\n  // AMD.\r\n  if (typeof define == 'function' && define.amd) {\r\n    define(function () { return BigNumber; });\r\n\r\n  // Node.js and other environments that support module.exports.\r\n  } else if (typeof module != 'undefined' && module.exports) {\r\n    module.exports = BigNumber;\r\n\r\n  // Browser.\r\n  } else {\r\n    if (!globalObject) {\r\n      globalObject = typeof self != 'undefined' && self ? self : window;\r\n    }\r\n\r\n    globalObject.BigNumber = BigNumber;\r\n  }\r\n})(this);\r\n", "var BigNumber = require('bignumber.js');\n\n/*\n    json2.js\n    2013-05-26\n\n    Public Domain.\n\n    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\n    See http://www.JSON.org/js.html\n\n\n    This code should be minified before deployment.\n    See http://javascript.crockford.com/jsmin.html\n\n    USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n    NOT CONTROL.\n\n\n    This file creates a global JSON object containing two methods: stringify\n    and parse.\n\n        JSON.stringify(value, replacer, space)\n            value       any JavaScript value, usually an object or array.\n\n            replacer    an optional parameter that determines how object\n                        values are stringified for objects. It can be a\n                        function or an array of strings.\n\n            space       an optional parameter that specifies the indentation\n                        of nested structures. If it is omitted, the text will\n                        be packed without extra whitespace. If it is a number,\n                        it will specify the number of spaces to indent at each\n                        level. If it is a string (such as '\\t' or '&nbsp;'),\n                        it contains the characters used to indent at each level.\n\n            This method produces a JSON text from a JavaScript value.\n\n            When an object value is found, if the object contains a toJSON\n            method, its toJSON method will be called and the result will be\n            stringified. A toJSON method does not serialize: it returns the\n            value represented by the name/value pair that should be serialized,\n            or undefined if nothing should be serialized. The toJSON method\n            will be passed the key associated with the value, and this will be\n            bound to the value\n\n            For example, this would serialize Dates as ISO strings.\n\n                Date.prototype.toJSON = function (key) {\n                    function f(n) {\n                        // Format integers to have at least two digits.\n                        return n < 10 ? '0' + n : n;\n                    }\n\n                    return this.getUTCFullYear()   + '-' +\n                         f(this.getUTCMonth() + 1) + '-' +\n                         f(this.getUTCDate())      + 'T' +\n                         f(this.getUTCHours())     + ':' +\n                         f(this.getUTCMinutes())   + ':' +\n                         f(this.getUTCSeconds())   + 'Z';\n                };\n\n            You can provide an optional replacer method. It will be passed the\n            key and value of each member, with this bound to the containing\n            object. The value that is returned from your method will be\n            serialized. If your method returns undefined, then the member will\n            be excluded from the serialization.\n\n            If the replacer parameter is an array of strings, then it will be\n            used to select the members to be serialized. It filters the results\n            such that only members with keys listed in the replacer array are\n            stringified.\n\n            Values that do not have JSON representations, such as undefined or\n            functions, will not be serialized. Such values in objects will be\n            dropped; in arrays they will be replaced with null. You can use\n            a replacer function to replace those with JSON values.\n            JSON.stringify(undefined) returns undefined.\n\n            The optional space parameter produces a stringification of the\n            value that is filled with line breaks and indentation to make it\n            easier to read.\n\n            If the space parameter is a non-empty string, then that string will\n            be used for indentation. If the space parameter is a number, then\n            the indentation will be that many spaces.\n\n            Example:\n\n            text = JSON.stringify(['e', {pluribus: 'unum'}]);\n            // text is '[\"e\",{\"pluribus\":\"unum\"}]'\n\n\n            text = JSON.stringify(['e', {pluribus: 'unum'}], null, '\\t');\n            // text is '[\\n\\t\"e\",\\n\\t{\\n\\t\\t\"pluribus\": \"unum\"\\n\\t}\\n]'\n\n            text = JSON.stringify([new Date()], function (key, value) {\n                return this[key] instanceof Date ?\n                    'Date(' + this[key] + ')' : value;\n            });\n            // text is '[\"Date(---current time---)\"]'\n\n\n        JSON.parse(text, reviver)\n            This method parses a JSON text to produce an object or array.\n            It can throw a SyntaxError exception.\n\n            The optional reviver parameter is a function that can filter and\n            transform the results. It receives each of the keys and values,\n            and its return value is used instead of the original value.\n            If it returns what it received, then the structure is not modified.\n            If it returns undefined then the member is deleted.\n\n            Example:\n\n            // Parse the text. Values that look like ISO date strings will\n            // be converted to Date objects.\n\n            myData = JSON.parse(text, function (key, value) {\n                var a;\n                if (typeof value === 'string') {\n                    a =\n/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n                    if (a) {\n                        return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n                            +a[5], +a[6]));\n                    }\n                }\n                return value;\n            });\n\n            myData = JSON.parse('[\"Date(09/09/2001)\"]', function (key, value) {\n                var d;\n                if (typeof value === 'string' &&\n                        value.slice(0, 5) === 'Date(' &&\n                        value.slice(-1) === ')') {\n                    d = new Date(value.slice(5, -1));\n                    if (d) {\n                        return d;\n                    }\n                }\n                return value;\n            });\n\n\n    This is a reference implementation. You are free to copy, modify, or\n    redistribute.\n*/\n\n/*jslint evil: true, regexp: true */\n\n/*members \"\", \"\\b\", \"\\t\", \"\\n\", \"\\f\", \"\\r\", \"\\\"\", JSON, \"\\\\\", apply,\n    call, charCodeAt, getUTCDate, getUTCFullYear, getUTCHours,\n    getUTCMinutes, getUTCMonth, getUTCSeconds, hasOwnProperty, join,\n    lastIndex, length, parse, prototype, push, replace, slice, stringify,\n    test, toJSON, toString, valueOf\n*/\n\n\n// Create a JSON object only if one does not already exist. We create the\n// methods in a closure to avoid creating global variables.\n\nvar JSON = module.exports;\n\n(function () {\n    'use strict';\n\n    function f(n) {\n        // Format integers to have at least two digits.\n        return n < 10 ? '0' + n : n;\n    }\n\n    var cx = /[\\u0000\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g,\n        escapable = /[\\\\\\\"\\x00-\\x1f\\x7f-\\x9f\\u00ad\\u0600-\\u0604\\u070f\\u17b4\\u17b5\\u200c-\\u200f\\u2028-\\u202f\\u2060-\\u206f\\ufeff\\ufff0-\\uffff]/g,\n        gap,\n        indent,\n        meta = {    // table of character substitutions\n            '\\b': '\\\\b',\n            '\\t': '\\\\t',\n            '\\n': '\\\\n',\n            '\\f': '\\\\f',\n            '\\r': '\\\\r',\n            '\"' : '\\\\\"',\n            '\\\\': '\\\\\\\\'\n        },\n        rep;\n\n\n    function quote(string) {\n\n// If the string contains no control characters, no quote characters, and no\n// backslash characters, then we can safely slap some quotes around it.\n// Otherwise we must also replace the offending characters with safe escape\n// sequences.\n\n        escapable.lastIndex = 0;\n        return escapable.test(string) ? '\"' + string.replace(escapable, function (a) {\n            var c = meta[a];\n            return typeof c === 'string'\n                ? c\n                : '\\\\u' + ('0000' + a.charCodeAt(0).toString(16)).slice(-4);\n        }) + '\"' : '\"' + string + '\"';\n    }\n\n\n    function str(key, holder) {\n\n// Produce a string from holder[key].\n\n        var i,          // The loop counter.\n            k,          // The member key.\n            v,          // The member value.\n            length,\n            mind = gap,\n            partial,\n            value = holder[key],\n            isBigNumber = value != null && (value instanceof BigNumber || BigNumber.isBigNumber(value));\n\n// If the value has a toJSON method, call it to obtain a replacement value.\n\n        if (value && typeof value === 'object' &&\n                typeof value.toJSON === 'function') {\n            value = value.toJSON(key);\n        }\n\n// If we were called with a replacer function, then call the replacer to\n// obtain a replacement value.\n\n        if (typeof rep === 'function') {\n            value = rep.call(holder, key, value);\n        }\n\n// What happens next depends on the value's type.\n\n        switch (typeof value) {\n        case 'string':\n            if (isBigNumber) {\n                return value;\n            } else {\n                return quote(value);\n            }\n\n        case 'number':\n\n// JSON numbers must be finite. Encode non-finite numbers as null.\n\n            return isFinite(value) ? String(value) : 'null';\n\n        case 'boolean':\n        case 'null':\n        case 'bigint':\n\n// If the value is a boolean or null, convert it to a string. Note:\n// typeof null does not produce 'null'. The case is included here in\n// the remote chance that this gets fixed someday.\n\n            return String(value);\n\n// If the type is 'object', we might be dealing with an object or an array or\n// null.\n\n        case 'object':\n\n// Due to a specification blunder in ECMAScript, typeof null is 'object',\n// so watch out for that case.\n\n            if (!value) {\n                return 'null';\n            }\n\n// Make an array to hold the partial results of stringifying this object value.\n\n            gap += indent;\n            partial = [];\n\n// Is the value an array?\n\n            if (Object.prototype.toString.apply(value) === '[object Array]') {\n\n// The value is an array. Stringify every element. Use null as a placeholder\n// for non-JSON values.\n\n                length = value.length;\n                for (i = 0; i < length; i += 1) {\n                    partial[i] = str(i, value) || 'null';\n                }\n\n// Join all of the elements together, separated with commas, and wrap them in\n// brackets.\n\n                v = partial.length === 0\n                    ? '[]'\n                    : gap\n                    ? '[\\n' + gap + partial.join(',\\n' + gap) + '\\n' + mind + ']'\n                    : '[' + partial.join(',') + ']';\n                gap = mind;\n                return v;\n            }\n\n// If the replacer is an array, use it to select the members to be stringified.\n\n            if (rep && typeof rep === 'object') {\n                length = rep.length;\n                for (i = 0; i < length; i += 1) {\n                    if (typeof rep[i] === 'string') {\n                        k = rep[i];\n                        v = str(k, value);\n                        if (v) {\n                            partial.push(quote(k) + (gap ? ': ' : ':') + v);\n                        }\n                    }\n                }\n            } else {\n\n// Otherwise, iterate through all of the keys in the object.\n\n                Object.keys(value).forEach(function(k) {\n                    var v = str(k, value);\n                    if (v) {\n                        partial.push(quote(k) + (gap ? ': ' : ':') + v);\n                    }\n                });\n            }\n\n// Join all of the member texts together, separated with commas,\n// and wrap them in braces.\n\n            v = partial.length === 0\n                ? '{}'\n                : gap\n                ? '{\\n' + gap + partial.join(',\\n' + gap) + '\\n' + mind + '}'\n                : '{' + partial.join(',') + '}';\n            gap = mind;\n            return v;\n        }\n    }\n\n// If the JSON object does not yet have a stringify method, give it one.\n\n    if (typeof JSON.stringify !== 'function') {\n        JSON.stringify = function (value, replacer, space) {\n\n// The stringify method takes a value and an optional replacer, and an optional\n// space parameter, and returns a JSON text. The replacer can be a function\n// that can replace values, or an array of strings that will select the keys.\n// A default replacer method can be provided. Use of the space parameter can\n// produce text that is more easily readable.\n\n            var i;\n            gap = '';\n            indent = '';\n\n// If the space parameter is a number, make an indent string containing that\n// many spaces.\n\n            if (typeof space === 'number') {\n                for (i = 0; i < space; i += 1) {\n                    indent += ' ';\n                }\n\n// If the space parameter is a string, it will be used as the indent string.\n\n            } else if (typeof space === 'string') {\n                indent = space;\n            }\n\n// If there is a replacer, it must be a function or an array.\n// Otherwise, throw an error.\n\n            rep = replacer;\n            if (replacer && typeof replacer !== 'function' &&\n                    (typeof replacer !== 'object' ||\n                    typeof replacer.length !== 'number')) {\n                throw new Error('JSON.stringify');\n            }\n\n// Make a fake root object containing our value under the key of ''.\n// Return the result of stringifying the value.\n\n            return str('', {'': value});\n        };\n    }\n}());\n", "var BigNumber = null;\n\n// regexpxs extracted from\n// (c) BSD-3-Clause\n// https://github.com/fastify/secure-json-parse/graphs/contributors and https://github.com/hapijs/bourne/graphs/contributors\n\nconst suspectProtoRx = /(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])(?:p|\\\\u0070)(?:r|\\\\u0072)(?:o|\\\\u006[Ff])(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:_|\\\\u005[Ff])(?:_|\\\\u005[Ff])/;\nconst suspectConstructorRx = /(?:c|\\\\u0063)(?:o|\\\\u006[Ff])(?:n|\\\\u006[Ee])(?:s|\\\\u0073)(?:t|\\\\u0074)(?:r|\\\\u0072)(?:u|\\\\u0075)(?:c|\\\\u0063)(?:t|\\\\u0074)(?:o|\\\\u006[Ff])(?:r|\\\\u0072)/;\n\n/*\n    json_parse.js\n    2012-06-20\n\n    Public Domain.\n\n    NO WARRANTY EXPRESSED OR IMPLIED. USE AT YOUR OWN RISK.\n\n    This file creates a json_parse function.\n    During create you can (optionally) specify some behavioural switches\n\n        require('json-bigint')(options)\n\n            The optional options parameter holds switches that drive certain\n            aspects of the parsing process:\n            * options.strict = true will warn about duplicate-key usage in the json.\n              The default (strict = false) will silently ignore those and overwrite\n              values for keys that are in duplicate use.\n\n    The resulting function follows this signature:\n        json_parse(text, reviver)\n            This method parses a JSON text to produce an object or array.\n            It can throw a SyntaxError exception.\n\n            The optional reviver parameter is a function that can filter and\n            transform the results. It receives each of the keys and values,\n            and its return value is used instead of the original value.\n            If it returns what it received, then the structure is not modified.\n            If it returns undefined then the member is deleted.\n\n            Example:\n\n            // Parse the text. Values that look like ISO date strings will\n            // be converted to Date objects.\n\n            myData = json_parse(text, function (key, value) {\n                var a;\n                if (typeof value === 'string') {\n                    a =\n/^(\\d{4})-(\\d{2})-(\\d{2})T(\\d{2}):(\\d{2}):(\\d{2}(?:\\.\\d*)?)Z$/.exec(value);\n                    if (a) {\n                        return new Date(Date.UTC(+a[1], +a[2] - 1, +a[3], +a[4],\n                            +a[5], +a[6]));\n                    }\n                }\n                return value;\n            });\n\n    This is a reference implementation. You are free to copy, modify, or\n    redistribute.\n\n    This code should be minified before deployment.\n    See http://javascript.crockford.com/jsmin.html\n\n    USE YOUR OWN COPY. IT IS EXTREMELY UNWISE TO LOAD CODE FROM SERVERS YOU DO\n    NOT CONTROL.\n*/\n\n/*members \"\", \"\\\"\", \"\\/\", \"\\\\\", at, b, call, charAt, f, fromCharCode,\n    hasOwnProperty, message, n, name, prototype, push, r, t, text\n*/\n\nvar json_parse = function (options) {\n  'use strict';\n\n  // This is a function that can parse a JSON text, producing a JavaScript\n  // data structure. It is a simple, recursive descent parser. It does not use\n  // eval or regular expressions, so it can be used as a model for implementing\n  // a JSON parser in other languages.\n\n  // We are defining the function inside of another function to avoid creating\n  // global variables.\n\n  // Default options one can override by passing options to the parse()\n  var _options = {\n    strict: false, // not being strict means do not generate syntax errors for \"duplicate key\"\n    storeAsString: false, // toggles whether the values should be stored as BigNumber (default) or a string\n    alwaysParseAsBig: false, // toggles whether all numbers should be Big\n    useNativeBigInt: false, // toggles whether to use native BigInt instead of bignumber.js\n    protoAction: 'error',\n    constructorAction: 'error',\n  };\n\n  // If there are options, then use them to override the default _options\n  if (options !== undefined && options !== null) {\n    if (options.strict === true) {\n      _options.strict = true;\n    }\n    if (options.storeAsString === true) {\n      _options.storeAsString = true;\n    }\n    _options.alwaysParseAsBig =\n      options.alwaysParseAsBig === true ? options.alwaysParseAsBig : false;\n    _options.useNativeBigInt =\n      options.useNativeBigInt === true ? options.useNativeBigInt : false;\n\n    if (typeof options.constructorAction !== 'undefined') {\n      if (\n        options.constructorAction === 'error' ||\n        options.constructorAction === 'ignore' ||\n        options.constructorAction === 'preserve'\n      ) {\n        _options.constructorAction = options.constructorAction;\n      } else {\n        throw new Error(\n          `Incorrect value for constructorAction option, must be \"error\", \"ignore\" or undefined but passed ${options.constructorAction}`\n        );\n      }\n    }\n\n    if (typeof options.protoAction !== 'undefined') {\n      if (\n        options.protoAction === 'error' ||\n        options.protoAction === 'ignore' ||\n        options.protoAction === 'preserve'\n      ) {\n        _options.protoAction = options.protoAction;\n      } else {\n        throw new Error(\n          `Incorrect value for protoAction option, must be \"error\", \"ignore\" or undefined but passed ${options.protoAction}`\n        );\n      }\n    }\n  }\n\n  var at, // The index of the current character\n    ch, // The current character\n    escapee = {\n      '\"': '\"',\n      '\\\\': '\\\\',\n      '/': '/',\n      b: '\\b',\n      f: '\\f',\n      n: '\\n',\n      r: '\\r',\n      t: '\\t',\n    },\n    text,\n    error = function (m) {\n      // Call error when something is wrong.\n\n      throw {\n        name: 'SyntaxError',\n        message: m,\n        at: at,\n        text: text,\n      };\n    },\n    next = function (c) {\n      // If a c parameter is provided, verify that it matches the current character.\n\n      if (c && c !== ch) {\n        error(\"Expected '\" + c + \"' instead of '\" + ch + \"'\");\n      }\n\n      // Get the next character. When there are no more characters,\n      // return the empty string.\n\n      ch = text.charAt(at);\n      at += 1;\n      return ch;\n    },\n    number = function () {\n      // Parse a number value.\n\n      var number,\n        string = '';\n\n      if (ch === '-') {\n        string = '-';\n        next('-');\n      }\n      while (ch >= '0' && ch <= '9') {\n        string += ch;\n        next();\n      }\n      if (ch === '.') {\n        string += '.';\n        while (next() && ch >= '0' && ch <= '9') {\n          string += ch;\n        }\n      }\n      if (ch === 'e' || ch === 'E') {\n        string += ch;\n        next();\n        if (ch === '-' || ch === '+') {\n          string += ch;\n          next();\n        }\n        while (ch >= '0' && ch <= '9') {\n          string += ch;\n          next();\n        }\n      }\n      number = +string;\n      if (!isFinite(number)) {\n        error('Bad number');\n      } else {\n        if (BigNumber == null) BigNumber = require('bignumber.js');\n        //if (number > 9007199254740992 || number < -9007199254740992)\n        // Bignumber has stricter check: everything with length > 15 digits disallowed\n        if (string.length > 15)\n          return _options.storeAsString\n            ? string\n            : _options.useNativeBigInt\n            ? BigInt(string)\n            : new BigNumber(string);\n        else\n          return !_options.alwaysParseAsBig\n            ? number\n            : _options.useNativeBigInt\n            ? BigInt(number)\n            : new BigNumber(number);\n      }\n    },\n    string = function () {\n      // Parse a string value.\n\n      var hex,\n        i,\n        string = '',\n        uffff;\n\n      // When parsing for string values, we must look for \" and \\ characters.\n\n      if (ch === '\"') {\n        var startAt = at;\n        while (next()) {\n          if (ch === '\"') {\n            if (at - 1 > startAt) string += text.substring(startAt, at - 1);\n            next();\n            return string;\n          }\n          if (ch === '\\\\') {\n            if (at - 1 > startAt) string += text.substring(startAt, at - 1);\n            next();\n            if (ch === 'u') {\n              uffff = 0;\n              for (i = 0; i < 4; i += 1) {\n                hex = parseInt(next(), 16);\n                if (!isFinite(hex)) {\n                  break;\n                }\n                uffff = uffff * 16 + hex;\n              }\n              string += String.fromCharCode(uffff);\n            } else if (typeof escapee[ch] === 'string') {\n              string += escapee[ch];\n            } else {\n              break;\n            }\n            startAt = at;\n          }\n        }\n      }\n      error('Bad string');\n    },\n    white = function () {\n      // Skip whitespace.\n\n      while (ch && ch <= ' ') {\n        next();\n      }\n    },\n    word = function () {\n      // true, false, or null.\n\n      switch (ch) {\n        case 't':\n          next('t');\n          next('r');\n          next('u');\n          next('e');\n          return true;\n        case 'f':\n          next('f');\n          next('a');\n          next('l');\n          next('s');\n          next('e');\n          return false;\n        case 'n':\n          next('n');\n          next('u');\n          next('l');\n          next('l');\n          return null;\n      }\n      error(\"Unexpected '\" + ch + \"'\");\n    },\n    value, // Place holder for the value function.\n    array = function () {\n      // Parse an array value.\n\n      var array = [];\n\n      if (ch === '[') {\n        next('[');\n        white();\n        if (ch === ']') {\n          next(']');\n          return array; // empty array\n        }\n        while (ch) {\n          array.push(value());\n          white();\n          if (ch === ']') {\n            next(']');\n            return array;\n          }\n          next(',');\n          white();\n        }\n      }\n      error('Bad array');\n    },\n    object = function () {\n      // Parse an object value.\n\n      var key,\n        object = Object.create(null);\n\n      if (ch === '{') {\n        next('{');\n        white();\n        if (ch === '}') {\n          next('}');\n          return object; // empty object\n        }\n        while (ch) {\n          key = string();\n          white();\n          next(':');\n          if (\n            _options.strict === true &&\n            Object.hasOwnProperty.call(object, key)\n          ) {\n            error('Duplicate key \"' + key + '\"');\n          }\n\n          if (suspectProtoRx.test(key) === true) {\n            if (_options.protoAction === 'error') {\n              error('Object contains forbidden prototype property');\n            } else if (_options.protoAction === 'ignore') {\n              value();\n            } else {\n              object[key] = value();\n            }\n          } else if (suspectConstructorRx.test(key) === true) {\n            if (_options.constructorAction === 'error') {\n              error('Object contains forbidden constructor property');\n            } else if (_options.constructorAction === 'ignore') {\n              value();\n            } else {\n              object[key] = value();\n            }\n          } else {\n            object[key] = value();\n          }\n\n          white();\n          if (ch === '}') {\n            next('}');\n            return object;\n          }\n          next(',');\n          white();\n        }\n      }\n      error('Bad object');\n    };\n\n  value = function () {\n    // Parse a JSON value. It could be an object, an array, a string, a number,\n    // or a word.\n\n    white();\n    switch (ch) {\n      case '{':\n        return object();\n      case '[':\n        return array();\n      case '\"':\n        return string();\n      case '-':\n        return number();\n      default:\n        return ch >= '0' && ch <= '9' ? number() : word();\n    }\n  };\n\n  // Return the json_parse function. It will have access to all of the above\n  // functions and variables.\n\n  return function (source, reviver) {\n    var result;\n\n    text = source + '';\n    at = 0;\n    ch = ' ';\n    result = value();\n    white();\n    if (ch) {\n      error('Syntax error');\n    }\n\n    // If there is a reviver function, we recursively walk the new structure,\n    // passing each name/value pair to the reviver function for possible\n    // transformation, starting with a temporary root object that holds the result\n    // in an empty key. If there is not a reviver function, we simply return the\n    // result.\n\n    return typeof reviver === 'function'\n      ? (function walk(holder, key) {\n          var k,\n            v,\n            value = holder[key];\n          if (value && typeof value === 'object') {\n            Object.keys(value).forEach(function (k) {\n              v = walk(value, k);\n              if (v !== undefined) {\n                value[k] = v;\n              } else {\n                delete value[k];\n              }\n            });\n          }\n          return reviver.call(holder, key, value);\n        })({ '': result }, '')\n      : result;\n  };\n};\n\nmodule.exports = json_parse;\n", "var json_stringify = require('./lib/stringify.js').stringify;\nvar json_parse     = require('./lib/parse.js');\n\nmodule.exports = function(options) {\n    return  {\n        parse: json_parse(options),\n        stringify: json_stringify\n    }\n};\n//create the default method members with no options applied for backwards compatibility\nmodule.exports.parse = json_parse();\nmodule.exports.stringify = json_stringify;\n"], "mappings": ";;;;;AAAA;AAAA;AAAC,KAAC,SAAU,cAAc;AACxB;AAkDA,UAAI,WACF,YAAY,8CACZ,WAAW,KAAK,MAChB,YAAY,KAAK,OAEjB,iBAAiB,sBACjB,gBAAgB,iBAAiB,0DAEjC,OAAO,MACP,WAAW,IACX,mBAAmB,kBAEnB,WAAW,CAAC,GAAG,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,MAAM,MAAM,MAAM,IAAI,GACjF,YAAY,KAKZ,MAAM;AAMR,eAAS,MAAM,cAAc;AAC3B,YAAI,KAAK,aAAa,cACpB,IAAIA,WAAU,YAAY,EAAE,aAAaA,YAAW,UAAU,MAAM,SAAS,KAAK,GAClF,MAAM,IAAIA,WAAU,CAAC,GAUrB,iBAAiB,IAajB,gBAAgB,GAMhB,aAAa,IAIb,aAAa,IAMb,UAAU,MAKV,UAAU,KAGV,SAAS,OAkBT,cAAc,GAId,gBAAgB,GAGhB,SAAS;AAAA,UACP,QAAQ;AAAA,UACR,WAAW;AAAA,UACX,oBAAoB;AAAA,UACpB,gBAAgB;AAAA,UAChB,kBAAkB;AAAA,UAClB,mBAAmB;AAAA,UACnB,wBAAwB;AAAA;AAAA,UACxB,QAAQ;AAAA,QACV,GAKA,WAAW,wCACX,iCAAiC;AAgBnC,iBAASA,WAAU,GAAG,GAAG;AACvB,cAAI,UAAU,GAAG,aAAa,GAAG,GAAG,OAAO,KAAK,KAC9C,IAAI;AAGN,cAAI,EAAE,aAAaA;AAAY,mBAAO,IAAIA,WAAU,GAAG,CAAC;AAExD,cAAI,KAAK,MAAM;AAEb,gBAAI,KAAK,EAAE,iBAAiB,MAAM;AAChC,gBAAE,IAAI,EAAE;AAER,kBAAI,CAAC,EAAE,KAAK,EAAE,IAAI,SAAS;AACzB,kBAAE,IAAI,EAAE,IAAI;AAAA,cACd,WAAW,EAAE,IAAI,SAAS;AACxB,kBAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,cAChB,OAAO;AACL,kBAAE,IAAI,EAAE;AACR,kBAAE,IAAI,EAAE,EAAE,MAAM;AAAA,cAClB;AAEA;AAAA,YACF;AAEA,iBAAK,QAAQ,OAAO,KAAK,aAAa,IAAI,KAAK,GAAG;AAGhD,gBAAE,IAAI,IAAI,IAAI,KAAK,IAAI,CAAC,GAAG,MAAM;AAGjC,kBAAI,MAAM,CAAC,CAAC,GAAG;AACb,qBAAK,IAAI,GAAG,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI;AAAI;AAEzC,oBAAI,IAAI,SAAS;AACf,oBAAE,IAAI,EAAE,IAAI;AAAA,gBACd,OAAO;AACL,oBAAE,IAAI;AACN,oBAAE,IAAI,CAAC,CAAC;AAAA,gBACV;AAEA;AAAA,cACF;AAEA,oBAAM,OAAO,CAAC;AAAA,YAChB,OAAO;AAEL,kBAAI,CAAC,UAAU,KAAK,MAAM,OAAO,CAAC,CAAC;AAAG,uBAAO,aAAa,GAAG,KAAK,KAAK;AAEvE,gBAAE,IAAI,IAAI,WAAW,CAAC,KAAK,MAAM,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM;AAAA,YAC7D;AAGA,iBAAK,IAAI,IAAI,QAAQ,GAAG,KAAK;AAAI,oBAAM,IAAI,QAAQ,KAAK,EAAE;AAG1D,iBAAK,IAAI,IAAI,OAAO,IAAI,KAAK,GAAG;AAG9B,kBAAI,IAAI;AAAG,oBAAI;AACf,mBAAK,CAAC,IAAI,MAAM,IAAI,CAAC;AACrB,oBAAM,IAAI,UAAU,GAAG,CAAC;AAAA,YAC1B,WAAW,IAAI,GAAG;AAGhB,kBAAI,IAAI;AAAA,YACV;AAAA,UAEF,OAAO;AAGL,qBAAS,GAAG,GAAG,SAAS,QAAQ,MAAM;AAItC,gBAAI,KAAK,MAAM,gCAAgC;AAC7C,kBAAI,IAAIA,WAAU,CAAC;AACnB,qBAAO,MAAM,GAAG,iBAAiB,EAAE,IAAI,GAAG,aAAa;AAAA,YACzD;AAEA,kBAAM,OAAO,CAAC;AAEd,gBAAI,QAAQ,OAAO,KAAK,UAAU;AAGhC,kBAAI,IAAI,KAAK;AAAG,uBAAO,aAAa,GAAG,KAAK,OAAO,CAAC;AAEpD,gBAAE,IAAI,IAAI,IAAI,KAAK,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM;AAG7C,kBAAIA,WAAU,SAAS,IAAI,QAAQ,aAAa,EAAE,EAAE,SAAS,IAAI;AAC/D,sBAAM,MACJ,gBAAgB,CAAC;AAAA,cACrB;AAAA,YACF,OAAO;AACL,gBAAE,IAAI,IAAI,WAAW,CAAC,MAAM,MAAM,MAAM,IAAI,MAAM,CAAC,GAAG,MAAM;AAAA,YAC9D;AAEA,uBAAW,SAAS,MAAM,GAAG,CAAC;AAC9B,gBAAI,IAAI;AAIR,iBAAK,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AACnC,kBAAI,SAAS,QAAQ,IAAI,IAAI,OAAO,CAAC,CAAC,IAAI,GAAG;AAC3C,oBAAI,KAAK,KAAK;AAGZ,sBAAI,IAAI,GAAG;AACT,wBAAI;AACJ;AAAA,kBACF;AAAA,gBACF,WAAW,CAAC,aAAa;AAGvB,sBAAI,OAAO,IAAI,YAAY,MAAM,MAAM,IAAI,YAAY,MACnD,OAAO,IAAI,YAAY,MAAM,MAAM,IAAI,YAAY,IAAI;AACzD,kCAAc;AACd,wBAAI;AACJ,wBAAI;AACJ;AAAA,kBACF;AAAA,gBACF;AAEA,uBAAO,aAAa,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;AAAA,cAC5C;AAAA,YACF;AAGA,oBAAQ;AACR,kBAAM,YAAY,KAAK,GAAG,IAAI,EAAE,CAAC;AAGjC,iBAAK,IAAI,IAAI,QAAQ,GAAG,KAAK;AAAI,oBAAM,IAAI,QAAQ,KAAK,EAAE;AAAA;AACrD,kBAAI,IAAI;AAAA,UACf;AAGA,eAAK,IAAI,GAAG,IAAI,WAAW,CAAC,MAAM,IAAI;AAAI;AAG1C,eAAK,MAAM,IAAI,QAAQ,IAAI,WAAW,EAAE,GAAG,MAAM;AAAI;AAErD,cAAI,MAAM,IAAI,MAAM,GAAG,EAAE,GAAG,GAAG;AAC7B,mBAAO;AAGP,gBAAI,SAASA,WAAU,SACrB,MAAM,OAAO,IAAI,oBAAoB,MAAM,UAAU,CAAC,IAAI;AACxD,oBAAM,MACJ,gBAAiB,EAAE,IAAI,CAAE;AAAA,YAC/B;AAGA,iBAAK,IAAI,IAAI,IAAI,KAAK,SAAS;AAG7B,gBAAE,IAAI,EAAE,IAAI;AAAA,YAGd,WAAW,IAAI,SAAS;AAGtB,gBAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,YAChB,OAAO;AACL,gBAAE,IAAI;AACN,gBAAE,IAAI,CAAC;AAMP,mBAAK,IAAI,KAAK;AACd,kBAAI,IAAI;AAAG,qBAAK;AAEhB,kBAAI,IAAI,KAAK;AACX,oBAAI;AAAG,oBAAE,EAAE,KAAK,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC;AAEhC,qBAAK,OAAO,UAAU,IAAI,OAAM;AAC9B,oBAAE,EAAE,KAAK,CAAC,IAAI,MAAM,GAAG,KAAK,QAAQ,CAAC;AAAA,gBACvC;AAEA,oBAAI,YAAY,MAAM,IAAI,MAAM,CAAC,GAAG;AAAA,cACtC,OAAO;AACL,qBAAK;AAAA,cACP;AAEA,qBAAO,KAAK,OAAO;AAAI;AACvB,gBAAE,EAAE,KAAK,CAAC,GAAG;AAAA,YACf;AAAA,UACF,OAAO;AAGL,cAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,UAChB;AAAA,QACF;AAMA,QAAAA,WAAU,QAAQ;AAElB,QAAAA,WAAU,WAAW;AACrB,QAAAA,WAAU,aAAa;AACvB,QAAAA,WAAU,aAAa;AACvB,QAAAA,WAAU,cAAc;AACxB,QAAAA,WAAU,gBAAgB;AAC1B,QAAAA,WAAU,kBAAkB;AAC5B,QAAAA,WAAU,kBAAkB;AAC5B,QAAAA,WAAU,kBAAkB;AAC5B,QAAAA,WAAU,mBAAmB;AAC7B,QAAAA,WAAU,SAAS;AAqCnB,QAAAA,WAAU,SAASA,WAAU,MAAM,SAAU,KAAK;AAChD,cAAI,GAAG;AAEP,cAAI,OAAO,MAAM;AAEf,gBAAI,OAAO,OAAO,UAAU;AAI1B,kBAAI,IAAI,eAAe,IAAI,gBAAgB,GAAG;AAC5C,oBAAI,IAAI,CAAC;AACT,yBAAS,GAAG,GAAG,KAAK,CAAC;AACrB,iCAAiB;AAAA,cACnB;AAIA,kBAAI,IAAI,eAAe,IAAI,eAAe,GAAG;AAC3C,oBAAI,IAAI,CAAC;AACT,yBAAS,GAAG,GAAG,GAAG,CAAC;AACnB,gCAAgB;AAAA,cAClB;AAMA,kBAAI,IAAI,eAAe,IAAI,gBAAgB,GAAG;AAC5C,oBAAI,IAAI,CAAC;AACT,oBAAI,KAAK,EAAE,KAAK;AACd,2BAAS,EAAE,CAAC,GAAG,CAAC,KAAK,GAAG,CAAC;AACzB,2BAAS,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC;AACxB,+BAAa,EAAE,CAAC;AAChB,+BAAa,EAAE,CAAC;AAAA,gBAClB,OAAO;AACL,2BAAS,GAAG,CAAC,KAAK,KAAK,CAAC;AACxB,+BAAa,EAAE,aAAa,IAAI,IAAI,CAAC,IAAI;AAAA,gBAC3C;AAAA,cACF;AAKA,kBAAI,IAAI,eAAe,IAAI,OAAO,GAAG;AACnC,oBAAI,IAAI,CAAC;AACT,oBAAI,KAAK,EAAE,KAAK;AACd,2BAAS,EAAE,CAAC,GAAG,CAAC,KAAK,IAAI,CAAC;AAC1B,2BAAS,EAAE,CAAC,GAAG,GAAG,KAAK,CAAC;AACxB,4BAAU,EAAE,CAAC;AACb,4BAAU,EAAE,CAAC;AAAA,gBACf,OAAO;AACL,2BAAS,GAAG,CAAC,KAAK,KAAK,CAAC;AACxB,sBAAI,GAAG;AACL,8BAAU,EAAE,UAAU,IAAI,IAAI,CAAC,IAAI;AAAA,kBACrC,OAAO;AACL,0BAAM,MACJ,iBAAiB,IAAI,sBAAsB,CAAC;AAAA,kBAChD;AAAA,gBACF;AAAA,cACF;AAKA,kBAAI,IAAI,eAAe,IAAI,QAAQ,GAAG;AACpC,oBAAI,IAAI,CAAC;AACT,oBAAI,MAAM,CAAC,CAAC,GAAG;AACb,sBAAI,GAAG;AACL,wBAAI,OAAO,UAAU,eAAe,WAClC,OAAO,mBAAmB,OAAO,cAAc;AAC/C,+BAAS;AAAA,oBACX,OAAO;AACL,+BAAS,CAAC;AACV,4BAAM,MACJ,iBAAiB,oBAAoB;AAAA,oBACzC;AAAA,kBACF,OAAO;AACL,6BAAS;AAAA,kBACX;AAAA,gBACF,OAAO;AACL,wBAAM,MACJ,iBAAiB,IAAI,yBAAyB,CAAC;AAAA,gBACnD;AAAA,cACF;AAIA,kBAAI,IAAI,eAAe,IAAI,aAAa,GAAG;AACzC,oBAAI,IAAI,CAAC;AACT,yBAAS,GAAG,GAAG,GAAG,CAAC;AACnB,8BAAc;AAAA,cAChB;AAIA,kBAAI,IAAI,eAAe,IAAI,eAAe,GAAG;AAC3C,oBAAI,IAAI,CAAC;AACT,yBAAS,GAAG,GAAG,KAAK,CAAC;AACrB,gCAAgB;AAAA,cAClB;AAIA,kBAAI,IAAI,eAAe,IAAI,QAAQ,GAAG;AACpC,oBAAI,IAAI,CAAC;AACT,oBAAI,OAAO,KAAK;AAAU,2BAAS;AAAA;AAC9B,wBAAM,MACT,iBAAiB,IAAI,qBAAqB,CAAC;AAAA,cAC/C;AAIA,kBAAI,IAAI,eAAe,IAAI,UAAU,GAAG;AACtC,oBAAI,IAAI,CAAC;AAIT,oBAAI,OAAO,KAAK,YAAY,CAAC,wBAAwB,KAAK,CAAC,GAAG;AAC5D,mDAAiC,EAAE,MAAM,GAAG,EAAE,KAAK;AACnD,6BAAW;AAAA,gBACb,OAAO;AACL,wBAAM,MACJ,iBAAiB,IAAI,eAAe,CAAC;AAAA,gBACzC;AAAA,cACF;AAAA,YAEF,OAAO;AAGL,oBAAM,MACJ,iBAAiB,sBAAsB,GAAG;AAAA,YAC9C;AAAA,UACF;AAEA,iBAAO;AAAA,YACL;AAAA,YACA;AAAA,YACA,gBAAgB,CAAC,YAAY,UAAU;AAAA,YACvC,OAAO,CAAC,SAAS,OAAO;AAAA,YACxB;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF;AAYA,QAAAA,WAAU,cAAc,SAAU,GAAG;AACnC,cAAI,CAAC,KAAK,EAAE,iBAAiB;AAAM,mBAAO;AAC1C,cAAI,CAACA,WAAU;AAAO,mBAAO;AAE7B,cAAI,GAAG,GACL,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE;AAER;AAAK,gBAAI,CAAC,EAAE,SAAS,KAAK,CAAC,KAAK,kBAAkB;AAEhD,mBAAK,MAAM,KAAK,MAAM,OAAO,KAAK,CAAC,OAAO,KAAK,OAAO,MAAM,UAAU,CAAC,GAAG;AAGxE,oBAAI,EAAE,CAAC,MAAM,GAAG;AACd,sBAAI,MAAM,KAAK,EAAE,WAAW;AAAG,2BAAO;AACtC,wBAAM;AAAA,gBACR;AAGA,qBAAK,IAAI,KAAK;AACd,oBAAI,IAAI;AAAG,uBAAK;AAIhB,oBAAI,OAAO,EAAE,CAAC,CAAC,EAAE,UAAU,GAAG;AAE5B,uBAAK,IAAI,GAAG,IAAI,EAAE,QAAQ,KAAK;AAC7B,wBAAI,EAAE,CAAC;AACP,wBAAI,IAAI,KAAK,KAAK,QAAQ,MAAM,UAAU,CAAC;AAAG,4BAAM;AAAA,kBACtD;AAGA,sBAAI,MAAM;AAAG,2BAAO;AAAA,gBACtB;AAAA,cACF;AAAA,YAGF,WAAW,MAAM,QAAQ,MAAM,SAAS,MAAM,QAAQ,MAAM,KAAK,MAAM,KAAK;AAC1E,qBAAO;AAAA,YACT;AAEA,gBAAM,MACH,iBAAiB,wBAAwB,CAAC;AAAA,QAC/C;AAQA,QAAAA,WAAU,UAAUA,WAAU,MAAM,WAAY;AAC9C,iBAAO,SAAS,WAAW,EAAE;AAAA,QAC/B;AAQA,QAAAA,WAAU,UAAUA,WAAU,MAAM,WAAY;AAC9C,iBAAO,SAAS,WAAW,CAAC;AAAA,QAC9B;AAaA,QAAAA,WAAU,SAAU,WAAY;AAC9B,cAAI,UAAU;AAMd,cAAI,iBAAkB,KAAK,OAAO,IAAI,UAAW,UAC9C,WAAY;AAAE,mBAAO,UAAU,KAAK,OAAO,IAAI,OAAO;AAAA,UAAG,IACzD,WAAY;AAAE,oBAAS,KAAK,OAAO,IAAI,aAAa,KAAK,WACxD,KAAK,OAAO,IAAI,UAAW;AAAA,UAAI;AAEnC,iBAAO,SAAU,IAAI;AACnB,gBAAI,GAAG,GAAG,GAAG,GAAG,GACd,IAAI,GACJ,IAAI,CAAC,GACL,OAAO,IAAIA,WAAU,GAAG;AAE1B,gBAAI,MAAM;AAAM,mBAAK;AAAA;AAChB,uBAAS,IAAI,GAAG,GAAG;AAExB,gBAAI,SAAS,KAAK,QAAQ;AAE1B,gBAAI,QAAQ;AAGV,kBAAI,OAAO,iBAAiB;AAE1B,oBAAI,OAAO,gBAAgB,IAAI,YAAY,KAAK,CAAC,CAAC;AAElD,uBAAO,IAAI,KAAI;AAQb,sBAAI,EAAE,CAAC,IAAI,UAAW,EAAE,IAAI,CAAC,MAAM;AAMnC,sBAAI,KAAK,MAAM;AACb,wBAAI,OAAO,gBAAgB,IAAI,YAAY,CAAC,CAAC;AAC7C,sBAAE,CAAC,IAAI,EAAE,CAAC;AACV,sBAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AAAA,kBAChB,OAAO;AAIL,sBAAE,KAAK,IAAI,IAAI;AACf,yBAAK;AAAA,kBACP;AAAA,gBACF;AACA,oBAAI,IAAI;AAAA,cAGV,WAAW,OAAO,aAAa;AAG7B,oBAAI,OAAO,YAAY,KAAK,CAAC;AAE7B,uBAAO,IAAI,KAAI;AAMb,uBAAM,EAAE,CAAC,IAAI,MAAM,kBAAoB,EAAE,IAAI,CAAC,IAAI,gBAC9C,EAAE,IAAI,CAAC,IAAI,aAAgB,EAAE,IAAI,CAAC,IAAI,YACtC,EAAE,IAAI,CAAC,KAAK,OAAO,EAAE,IAAI,CAAC,KAAK,KAAK,EAAE,IAAI,CAAC;AAE/C,sBAAI,KAAK,MAAM;AACb,2BAAO,YAAY,CAAC,EAAE,KAAK,GAAG,CAAC;AAAA,kBACjC,OAAO;AAGL,sBAAE,KAAK,IAAI,IAAI;AACf,yBAAK;AAAA,kBACP;AAAA,gBACF;AACA,oBAAI,IAAI;AAAA,cACV,OAAO;AACL,yBAAS;AACT,sBAAM,MACJ,iBAAiB,oBAAoB;AAAA,cACzC;AAAA,YACF;AAGA,gBAAI,CAAC,QAAQ;AAEX,qBAAO,IAAI,KAAI;AACb,oBAAI,eAAe;AACnB,oBAAI,IAAI;AAAM,oBAAE,GAAG,IAAI,IAAI;AAAA,cAC7B;AAAA,YACF;AAEA,gBAAI,EAAE,EAAE,CAAC;AACT,kBAAM;AAGN,gBAAI,KAAK,IAAI;AACX,kBAAI,SAAS,WAAW,EAAE;AAC1B,gBAAE,CAAC,IAAI,UAAU,IAAI,CAAC,IAAI;AAAA,YAC5B;AAGA,mBAAO,EAAE,CAAC,MAAM,GAAG,EAAE,IAAI,GAAG;AAAI;AAGhC,gBAAI,IAAI,GAAG;AACT,kBAAI,CAAC,IAAI,CAAC;AAAA,YACZ,OAAO;AAGL,mBAAK,IAAI,IAAK,EAAE,CAAC,MAAM,GAAG,EAAE,OAAO,GAAG,CAAC,GAAG,KAAK;AAAS;AAGxD,mBAAK,IAAI,GAAG,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI;AAAI;AAG5C,kBAAI,IAAI;AAAU,qBAAK,WAAW;AAAA,YACpC;AAEA,iBAAK,IAAI;AACT,iBAAK,IAAI;AACT,mBAAO;AAAA,UACT;AAAA,QACF,EAAG;AAQH,QAAAA,WAAU,MAAM,WAAY;AAC1B,cAAI,IAAI,GACN,OAAO,WACP,MAAM,IAAIA,WAAU,KAAK,CAAC,CAAC;AAC7B,iBAAO,IAAI,KAAK;AAAS,kBAAM,IAAI,KAAK,KAAK,GAAG,CAAC;AACjD,iBAAO;AAAA,QACT;AAOA,sBAAe,WAAY;AACzB,cAAI,UAAU;AAOd,mBAAS,UAAU,KAAK,QAAQ,SAAS,UAAU;AACjD,gBAAI,GACF,MAAM,CAAC,CAAC,GACR,MACA,IAAI,GACJ,MAAM,IAAI;AAEZ,mBAAO,IAAI,OAAM;AACf,mBAAK,OAAO,IAAI,QAAQ,QAAQ,IAAI,IAAI,KAAK;AAAO;AAEpD,kBAAI,CAAC,KAAK,SAAS,QAAQ,IAAI,OAAO,GAAG,CAAC;AAE1C,mBAAK,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AAE/B,oBAAI,IAAI,CAAC,IAAI,UAAU,GAAG;AACxB,sBAAI,IAAI,IAAI,CAAC,KAAK;AAAM,wBAAI,IAAI,CAAC,IAAI;AACrC,sBAAI,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,UAAU;AACjC,sBAAI,CAAC,KAAK;AAAA,gBACZ;AAAA,cACF;AAAA,YACF;AAEA,mBAAO,IAAI,QAAQ;AAAA,UACrB;AAKA,iBAAO,SAAU,KAAK,QAAQ,SAAS,MAAM,kBAAkB;AAC7D,gBAAI,UAAU,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,GAC/B,IAAI,IAAI,QAAQ,GAAG,GACnB,KAAK,gBACL,KAAK;AAGP,gBAAI,KAAK,GAAG;AACV,kBAAI;AAGJ,8BAAgB;AAChB,oBAAM,IAAI,QAAQ,KAAK,EAAE;AACzB,kBAAI,IAAIA,WAAU,MAAM;AACxB,kBAAI,EAAE,IAAI,IAAI,SAAS,CAAC;AACxB,8BAAgB;AAKhB,gBAAE,IAAI;AAAA,gBAAU,aAAa,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG;AAAA,gBACxD;AAAA,gBAAI;AAAA,gBAAS;AAAA,cAAO;AACrB,gBAAE,IAAI,EAAE,EAAE;AAAA,YACZ;AAIA,iBAAK,UAAU,KAAK,QAAQ,SAAS,oBACjC,WAAW,UAAU,YACrB,WAAW,SAAS,SAAS;AAGjC,gBAAI,IAAI,GAAG;AAGX,mBAAO,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,IAAI;AAAE;AAG9B,gBAAI,CAAC,GAAG,CAAC;AAAG,qBAAO,SAAS,OAAO,CAAC;AAGpC,gBAAI,IAAI,GAAG;AACT,gBAAE;AAAA,YACJ,OAAO;AACL,gBAAE,IAAI;AACN,gBAAE,IAAI;AAGN,gBAAE,IAAI;AACN,kBAAI,IAAI,GAAG,GAAG,IAAI,IAAI,OAAO;AAC7B,mBAAK,EAAE;AACP,kBAAI,EAAE;AACN,kBAAI,EAAE;AAAA,YACR;AAKA,gBAAI,IAAI,KAAK;AAGb,gBAAI,GAAG,CAAC;AAIR,gBAAI,UAAU;AACd,gBAAI,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK;AAE/B,gBAAI,KAAK,KAAK,KAAK,QAAQ,OAAO,MAAM,KAAK,OAAO,EAAE,IAAI,IAAI,IAAI,MAC1D,IAAI,KAAK,KAAK,MAAK,MAAM,KAAK,KAAK,MAAM,KAAK,GAAG,IAAI,CAAC,IAAI,KAC3D,OAAO,EAAE,IAAI,IAAI,IAAI;AAK5B,gBAAI,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG;AAGnB,oBAAM,IAAI,aAAa,SAAS,OAAO,CAAC,GAAG,CAAC,IAAI,SAAS,OAAO,CAAC,CAAC,IAAI,SAAS,OAAO,CAAC;AAAA,YACzF,OAAO;AAGL,iBAAG,SAAS;AAGZ,kBAAI,GAAG;AAGL,qBAAK,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,IAAI,WAAU;AACpC,qBAAG,CAAC,IAAI;AAER,sBAAI,CAAC,GAAG;AACN,sBAAE;AACF,yBAAK,CAAC,CAAC,EAAE,OAAO,EAAE;AAAA,kBACpB;AAAA,gBACF;AAAA,cACF;AAGA,mBAAK,IAAI,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;AAAG;AAG9B,mBAAK,IAAI,GAAG,MAAM,IAAI,KAAK,GAAG,OAAO,SAAS,OAAO,GAAG,GAAG,CAAC;AAAE;AAG9D,oBAAM,aAAa,KAAK,GAAG,SAAS,OAAO,CAAC,CAAC;AAAA,YAC/C;AAGA,mBAAO;AAAA,UACT;AAAA,QACF,EAAG;AAIH,cAAO,WAAY;AAGjB,mBAAS,SAAS,GAAG,GAAG,MAAM;AAC5B,gBAAI,GAAG,MAAM,KAAK,KAChB,QAAQ,GACR,IAAI,EAAE,QACN,MAAM,IAAI,WACV,MAAM,IAAI,YAAY;AAExB,iBAAK,IAAI,EAAE,MAAM,GAAG,OAAM;AACxB,oBAAM,EAAE,CAAC,IAAI;AACb,oBAAM,EAAE,CAAC,IAAI,YAAY;AACzB,kBAAI,MAAM,MAAM,MAAM;AACtB,qBAAO,MAAM,MAAQ,IAAI,YAAa,YAAa;AACnD,uBAAS,OAAO,OAAO,MAAM,IAAI,YAAY,KAAK,MAAM;AACxD,gBAAE,CAAC,IAAI,OAAO;AAAA,YAChB;AAEA,gBAAI;AAAO,kBAAI,CAAC,KAAK,EAAE,OAAO,CAAC;AAE/B,mBAAO;AAAA,UACT;AAEA,mBAASC,SAAQ,GAAG,GAAG,IAAI,IAAI;AAC7B,gBAAI,GAAG;AAEP,gBAAI,MAAM,IAAI;AACZ,oBAAM,KAAK,KAAK,IAAI;AAAA,YACtB,OAAO;AAEL,mBAAK,IAAI,MAAM,GAAG,IAAI,IAAI,KAAK;AAE7B,oBAAI,EAAE,CAAC,KAAK,EAAE,CAAC,GAAG;AAChB,wBAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI;AACxB;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAEA,mBAAO;AAAA,UACT;AAEA,mBAAS,SAAS,GAAG,GAAG,IAAI,MAAM;AAChC,gBAAI,IAAI;AAGR,mBAAO,QAAO;AACZ,gBAAE,EAAE,KAAK;AACT,kBAAI,EAAE,EAAE,IAAI,EAAE,EAAE,IAAI,IAAI;AACxB,gBAAE,EAAE,IAAI,IAAI,OAAO,EAAE,EAAE,IAAI,EAAE,EAAE;AAAA,YACjC;AAGA,mBAAO,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,GAAG,EAAE,OAAO,GAAG,CAAC;AAAE;AAAA,UAC/C;AAGA,iBAAO,SAAU,GAAG,GAAG,IAAI,IAAI,MAAM;AACnC,gBAAI,KAAK,GAAG,GAAG,MAAM,GAAG,MAAM,OAAO,GAAG,IAAI,KAAK,MAAM,MAAM,IAAI,IAAI,KACnE,IAAI,IACJ,IAAI,EAAE,KAAK,EAAE,IAAI,IAAI,IACrB,KAAK,EAAE,GACP,KAAK,EAAE;AAGT,gBAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG;AAElC,qBAAO,IAAID;AAAA;AAAA,gBAGV,CAAC,EAAE,KAAK,CAAC,EAAE,MAAM,KAAK,MAAM,GAAG,CAAC,KAAK,GAAG,CAAC,IAAI,CAAC,MAAM;AAAA;AAAA,kBAGnD,MAAM,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK,IAAI,IAAI,IAAI;AAAA;AAAA,cACzC;AAAA,YACD;AAEA,gBAAI,IAAIA,WAAU,CAAC;AACnB,iBAAK,EAAE,IAAI,CAAC;AACZ,gBAAI,EAAE,IAAI,EAAE;AACZ,gBAAI,KAAK,IAAI;AAEb,gBAAI,CAAC,MAAM;AACT,qBAAO;AACP,kBAAI,SAAS,EAAE,IAAI,QAAQ,IAAI,SAAS,EAAE,IAAI,QAAQ;AACtD,kBAAI,IAAI,WAAW;AAAA,YACrB;AAIA,iBAAK,IAAI,GAAG,GAAG,CAAC,MAAM,GAAG,CAAC,KAAK,IAAI;AAAI;AAEvC,gBAAI,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK;AAAI;AAE1B,gBAAI,IAAI,GAAG;AACT,iBAAG,KAAK,CAAC;AACT,qBAAO;AAAA,YACT,OAAO;AACL,mBAAK,GAAG;AACR,mBAAK,GAAG;AACR,kBAAI;AACJ,mBAAK;AAIL,kBAAI,UAAU,QAAQ,GAAG,CAAC,IAAI,EAAE;AAIhC,kBAAI,IAAI,GAAG;AACT,qBAAK,SAAS,IAAI,GAAG,IAAI;AACzB,qBAAK,SAAS,IAAI,GAAG,IAAI;AACzB,qBAAK,GAAG;AACR,qBAAK,GAAG;AAAA,cACV;AAEA,mBAAK;AACL,oBAAM,GAAG,MAAM,GAAG,EAAE;AACpB,qBAAO,IAAI;AAGX,qBAAO,OAAO,IAAI,IAAI,MAAM,IAAI;AAAE;AAClC,mBAAK,GAAG,MAAM;AACd,mBAAK,CAAC,CAAC,EAAE,OAAO,EAAE;AAClB,oBAAM,GAAG,CAAC;AACV,kBAAI,GAAG,CAAC,KAAK,OAAO;AAAG;AAIvB,iBAAG;AACD,oBAAI;AAGJ,sBAAMC,SAAQ,IAAI,KAAK,IAAI,IAAI;AAG/B,oBAAI,MAAM,GAAG;AAIX,yBAAO,IAAI,CAAC;AACZ,sBAAI,MAAM;AAAM,2BAAO,OAAO,QAAQ,IAAI,CAAC,KAAK;AAGhD,sBAAI,UAAU,OAAO,GAAG;AAaxB,sBAAI,IAAI,GAAG;AAGT,wBAAI,KAAK;AAAM,0BAAI,OAAO;AAG1B,2BAAO,SAAS,IAAI,GAAG,IAAI;AAC3B,4BAAQ,KAAK;AACb,2BAAO,IAAI;AAMX,2BAAOA,SAAQ,MAAM,KAAK,OAAO,IAAI,KAAK,GAAG;AAC3C;AAGA,+BAAS,MAAM,KAAK,QAAQ,KAAK,IAAI,OAAO,IAAI;AAChD,8BAAQ,KAAK;AACb,4BAAM;AAAA,oBACR;AAAA,kBACF,OAAO;AAML,wBAAI,KAAK,GAAG;AAGV,4BAAM,IAAI;AAAA,oBACZ;AAGA,2BAAO,GAAG,MAAM;AAChB,4BAAQ,KAAK;AAAA,kBACf;AAEA,sBAAI,QAAQ;AAAM,2BAAO,CAAC,CAAC,EAAE,OAAO,IAAI;AAGxC,2BAAS,KAAK,MAAM,MAAM,IAAI;AAC9B,yBAAO,IAAI;AAGX,sBAAI,OAAO,IAAI;AAMb,2BAAOA,SAAQ,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG;AACrC;AAGA,+BAAS,KAAK,KAAK,OAAO,KAAK,IAAI,MAAM,IAAI;AAC7C,6BAAO,IAAI;AAAA,oBACb;AAAA,kBACF;AAAA,gBACF,WAAW,QAAQ,GAAG;AACpB;AACA,wBAAM,CAAC,CAAC;AAAA,gBACV;AAGA,mBAAG,GAAG,IAAI;AAGV,oBAAI,IAAI,CAAC,GAAG;AACV,sBAAI,MAAM,IAAI,GAAG,EAAE,KAAK;AAAA,gBAC1B,OAAO;AACL,wBAAM,CAAC,GAAG,EAAE,CAAC;AACb,yBAAO;AAAA,gBACT;AAAA,cACF,UAAU,OAAO,MAAM,IAAI,CAAC,KAAK,SAAS;AAE1C,qBAAO,IAAI,CAAC,KAAK;AAGjB,kBAAI,CAAC,GAAG,CAAC;AAAG,mBAAG,OAAO,GAAG,CAAC;AAAA,YAC5B;AAEA,gBAAI,QAAQ,MAAM;AAGhB,mBAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI;AAAI;AAE7C,oBAAM,GAAG,MAAM,EAAE,IAAI,IAAI,IAAI,WAAW,KAAK,GAAG,IAAI,IAAI;AAAA,YAG1D,OAAO;AACL,gBAAE,IAAI;AACN,gBAAE,IAAI,CAAC;AAAA,YACT;AAEA,mBAAO;AAAA,UACT;AAAA,QACF,EAAG;AAYH,iBAAS,OAAO,GAAG,GAAG,IAAI,IAAI;AAC5B,cAAI,IAAI,GAAG,IAAI,KAAK;AAEpB,cAAI,MAAM;AAAM,iBAAK;AAAA;AAChB,qBAAS,IAAI,GAAG,CAAC;AAEtB,cAAI,CAAC,EAAE;AAAG,mBAAO,EAAE,SAAS;AAE5B,eAAK,EAAE,EAAE,CAAC;AACV,eAAK,EAAE;AAEP,cAAI,KAAK,MAAM;AACb,kBAAM,cAAc,EAAE,CAAC;AACvB,kBAAM,MAAM,KAAK,MAAM,MAAM,MAAM,cAAc,MAAM,cACpD,cAAc,KAAK,EAAE,IACrB,aAAa,KAAK,IAAI,GAAG;AAAA,UAC9B,OAAO;AACL,gBAAI,MAAM,IAAID,WAAU,CAAC,GAAG,GAAG,EAAE;AAGjC,gBAAI,EAAE;AAEN,kBAAM,cAAc,EAAE,CAAC;AACvB,kBAAM,IAAI;AAOV,gBAAI,MAAM,KAAK,MAAM,MAAM,KAAK,KAAK,KAAK,aAAa;AAGrD,qBAAO,MAAM,GAAG,OAAO,KAAK;AAAM;AAClC,oBAAM,cAAc,KAAK,CAAC;AAAA,YAG5B,OAAO;AACL,mBAAK,MAAM,OAAO,KAAK,IAAI;AAC3B,oBAAM,aAAa,KAAK,GAAG,GAAG;AAG9B,kBAAI,IAAI,IAAI,KAAK;AACf,oBAAI,EAAE,IAAI;AAAG,uBAAK,OAAO,KAAK,KAAK,OAAO;AAAI;AAAA,cAChD,OAAO;AACL,qBAAK,IAAI;AACT,oBAAI,IAAI,GAAG;AACT,sBAAI,IAAI,KAAK;AAAK,2BAAO;AACzB,yBAAO,KAAK,OAAO;AAAI;AAAA,gBACzB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,EAAE,IAAI,KAAK,KAAK,MAAM,MAAM;AAAA,QACrC;AAKA,iBAAS,SAAS,MAAM,GAAG;AACzB,cAAI,GAAG,GACL,IAAI,GACJ,IAAI,IAAIA,WAAU,KAAK,CAAC,CAAC;AAE3B,iBAAO,IAAI,KAAK,QAAQ,KAAK;AAC3B,gBAAI,IAAIA,WAAU,KAAK,CAAC,CAAC;AACzB,gBAAI,CAAC,EAAE,MAAM,IAAI,QAAQ,GAAG,CAAC,OAAO,KAAK,MAAM,KAAK,EAAE,MAAM,GAAG;AAC7D,kBAAI;AAAA,YACN;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAOA,iBAAS,UAAU,GAAG,GAAG,GAAG;AAC1B,cAAI,IAAI,GACN,IAAI,EAAE;AAGR,iBAAO,CAAC,EAAE,EAAE,CAAC,GAAG,EAAE,IAAI;AAAE;AAGxB,eAAK,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI;AAAI;AAGrC,eAAK,IAAI,IAAI,IAAI,WAAW,KAAK,SAAS;AAGxC,cAAE,IAAI,EAAE,IAAI;AAAA,UAGd,WAAW,IAAI,SAAS;AAGtB,cAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,UAChB,OAAO;AACL,cAAE,IAAI;AACN,cAAE,IAAI;AAAA,UACR;AAEA,iBAAO;AAAA,QACT;AAIA,uBAAgB,WAAY;AAC1B,cAAI,aAAa,+BACf,WAAW,eACX,YAAY,eACZ,kBAAkB,sBAClB,mBAAmB;AAErB,iBAAO,SAAU,GAAG,KAAK,OAAO,GAAG;AACjC,gBAAI,MACF,IAAI,QAAQ,MAAM,IAAI,QAAQ,kBAAkB,EAAE;AAGpD,gBAAI,gBAAgB,KAAK,CAAC,GAAG;AAC3B,gBAAE,IAAI,MAAM,CAAC,IAAI,OAAO,IAAI,IAAI,KAAK;AAAA,YACvC,OAAO;AACL,kBAAI,CAAC,OAAO;AAGV,oBAAI,EAAE,QAAQ,YAAY,SAAU,GAAG,IAAI,IAAI;AAC7C,0BAAQ,KAAK,GAAG,YAAY,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI;AAC7D,yBAAO,CAAC,KAAK,KAAK,OAAO,KAAK;AAAA,gBAChC,CAAC;AAED,oBAAI,GAAG;AACL,yBAAO;AAGP,sBAAI,EAAE,QAAQ,UAAU,IAAI,EAAE,QAAQ,WAAW,MAAM;AAAA,gBACzD;AAEA,oBAAI,OAAO;AAAG,yBAAO,IAAIA,WAAU,GAAG,IAAI;AAAA,cAC5C;AAIA,kBAAIA,WAAU,OAAO;AACnB,sBAAM,MACH,iBAAiB,WAAW,IAAI,WAAW,IAAI,MAAM,cAAc,GAAG;AAAA,cAC3E;AAGA,gBAAE,IAAI;AAAA,YACR;AAEA,cAAE,IAAI,EAAE,IAAI;AAAA,UACd;AAAA,QACF,EAAG;AAOH,iBAAS,MAAM,GAAG,IAAI,IAAI,GAAG;AAC3B,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,IAAI,IACrB,KAAK,EAAE,GACP,SAAS;AAGX,cAAI,IAAI;AAQN,iBAAK;AAGH,mBAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI;AAAI;AAC7C,kBAAI,KAAK;AAGT,kBAAI,IAAI,GAAG;AACT,qBAAK;AACL,oBAAI;AACJ,oBAAI,GAAG,KAAK,CAAC;AAGb,qBAAK,UAAU,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE;AAAA,cAC3C,OAAO;AACL,qBAAK,UAAU,IAAI,KAAK,QAAQ;AAEhC,oBAAI,MAAM,GAAG,QAAQ;AAEnB,sBAAI,GAAG;AAGL,2BAAO,GAAG,UAAU,IAAI,GAAG,KAAK,CAAC;AAAE;AACnC,wBAAI,KAAK;AACT,wBAAI;AACJ,yBAAK;AACL,wBAAI,IAAI,WAAW;AAAA,kBACrB,OAAO;AACL,0BAAM;AAAA,kBACR;AAAA,gBACF,OAAO;AACL,sBAAI,IAAI,GAAG,EAAE;AAGb,uBAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI;AAAI;AAGlC,uBAAK;AAIL,sBAAI,IAAI,WAAW;AAGnB,uBAAK,IAAI,IAAI,IAAI,UAAU,IAAI,OAAO,IAAI,IAAI,CAAC,IAAI,EAAE;AAAA,gBACvD;AAAA,cACF;AAEA,kBAAI,KAAK,KAAK;AAAA;AAAA;AAAA,cAKb,GAAG,KAAK,CAAC,KAAK,SAAS,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,CAAC;AAExD,kBAAI,KAAK,KACL,MAAM,OAAO,MAAM,KAAK,OAAO,EAAE,IAAI,IAAI,IAAI,MAC9C,KAAK,KAAK,MAAM,MAAM,MAAM,KAAK,KAAK,MAAM;AAAA,eAG3C,IAAI,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,IAAI,GAAG,KAAK,CAAC,KAAK,KAAM,KAC7D,OAAO,EAAE,IAAI,IAAI,IAAI;AAExB,kBAAI,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG;AACpB,mBAAG,SAAS;AAEZ,oBAAI,GAAG;AAGL,wBAAM,EAAE,IAAI;AAGZ,qBAAG,CAAC,IAAI,QAAQ,WAAW,KAAK,YAAY,QAAQ;AACpD,oBAAE,IAAI,CAAC,MAAM;AAAA,gBACf,OAAO;AAGL,qBAAG,CAAC,IAAI,EAAE,IAAI;AAAA,gBAChB;AAEA,uBAAO;AAAA,cACT;AAGA,kBAAI,KAAK,GAAG;AACV,mBAAG,SAAS;AACZ,oBAAI;AACJ;AAAA,cACF,OAAO;AACL,mBAAG,SAAS,KAAK;AACjB,oBAAI,OAAO,WAAW,CAAC;AAIvB,mBAAG,EAAE,IAAI,IAAI,IAAI,UAAU,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC,CAAC,IAAI,IAAI;AAAA,cAClE;AAGA,kBAAI,GAAG;AAEL,2BAAU;AAGR,sBAAI,MAAM,GAAG;AAGX,yBAAK,IAAI,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI;AAAI;AAC7C,wBAAI,GAAG,CAAC,KAAK;AACb,yBAAK,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI;AAAI;AAGlC,wBAAI,KAAK,GAAG;AACV,wBAAE;AACF,0BAAI,GAAG,CAAC,KAAK;AAAM,2BAAG,CAAC,IAAI;AAAA,oBAC7B;AAEA;AAAA,kBACF,OAAO;AACL,uBAAG,EAAE,KAAK;AACV,wBAAI,GAAG,EAAE,KAAK;AAAM;AACpB,uBAAG,IAAI,IAAI;AACX,wBAAI;AAAA,kBACN;AAAA,gBACF;AAAA,cACF;AAGA,mBAAK,IAAI,GAAG,QAAQ,GAAG,EAAE,CAAC,MAAM,GAAG,GAAG,IAAI;AAAE;AAAA,YAC9C;AAGA,gBAAI,EAAE,IAAI,SAAS;AACjB,gBAAE,IAAI,EAAE,IAAI;AAAA,YAGd,WAAW,EAAE,IAAI,SAAS;AACxB,gBAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AAAA,YAChB;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAGA,iBAAS,QAAQ,GAAG;AAClB,cAAI,KACF,IAAI,EAAE;AAER,cAAI,MAAM;AAAM,mBAAO,EAAE,SAAS;AAElC,gBAAM,cAAc,EAAE,CAAC;AAEvB,gBAAM,KAAK,cAAc,KAAK,aAC1B,cAAc,KAAK,CAAC,IACpB,aAAa,KAAK,GAAG,GAAG;AAE5B,iBAAO,EAAE,IAAI,IAAI,MAAM,MAAM;AAAA,QAC/B;AASA,UAAE,gBAAgB,EAAE,MAAM,WAAY;AACpC,cAAI,IAAI,IAAIA,WAAU,IAAI;AAC1B,cAAI,EAAE,IAAI;AAAG,cAAE,IAAI;AACnB,iBAAO;AAAA,QACT;AAUA,UAAE,aAAa,SAAU,GAAG,GAAG;AAC7B,iBAAO,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC;AAAA,QAC1C;AAgBA,UAAE,gBAAgB,EAAE,KAAK,SAAU,IAAI,IAAI;AACzC,cAAI,GAAG,GAAG,GACR,IAAI;AAEN,cAAI,MAAM,MAAM;AACd,qBAAS,IAAI,GAAG,GAAG;AACnB,gBAAI,MAAM;AAAM,mBAAK;AAAA;AAChB,uBAAS,IAAI,GAAG,CAAC;AAEtB,mBAAO,MAAM,IAAIA,WAAU,CAAC,GAAG,KAAK,EAAE,IAAI,GAAG,EAAE;AAAA,UACjD;AAEA,cAAI,EAAE,IAAI,EAAE;AAAI,mBAAO;AACvB,gBAAM,IAAI,EAAE,SAAS,KAAK,SAAS,KAAK,IAAI,QAAQ,KAAK;AAGzD,cAAI,IAAI,EAAE,CAAC;AAAG,mBAAO,IAAI,MAAM,GAAG,KAAK,IAAI;AAAI;AAC/C,cAAI,IAAI;AAAG,gBAAI;AAEf,iBAAO;AAAA,QACT;AAuBA,UAAE,YAAY,EAAE,MAAM,SAAU,GAAG,GAAG;AACpC,iBAAO,IAAI,MAAM,IAAIA,WAAU,GAAG,CAAC,GAAG,gBAAgB,aAAa;AAAA,QACrE;AAOA,UAAE,qBAAqB,EAAE,OAAO,SAAU,GAAG,GAAG;AAC9C,iBAAO,IAAI,MAAM,IAAIA,WAAU,GAAG,CAAC,GAAG,GAAG,CAAC;AAAA,QAC5C;AAkBA,UAAE,kBAAkB,EAAE,MAAM,SAAU,GAAG,GAAG;AAC1C,cAAI,MAAM,UAAU,GAAG,GAAG,MAAM,QAAQ,QAAQ,QAAQ,GACtD,IAAI;AAEN,cAAI,IAAIA,WAAU,CAAC;AAGnB,cAAI,EAAE,KAAK,CAAC,EAAE,UAAU,GAAG;AACzB,kBAAM,MACH,iBAAiB,8BAA8B,QAAQ,CAAC,CAAC;AAAA,UAC9D;AAEA,cAAI,KAAK;AAAM,gBAAI,IAAIA,WAAU,CAAC;AAGlC,mBAAS,EAAE,IAAI;AAGf,cAAI,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,KAAK,CAAC,EAAE,KAAK,EAAE,EAAE,UAAU,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG;AAIhF,gBAAI,IAAIA,WAAU,KAAK,IAAI,CAAC,QAAQ,CAAC,GAAG,SAAS,EAAE,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC;AACpF,mBAAO,IAAI,EAAE,IAAI,CAAC,IAAI;AAAA,UACxB;AAEA,mBAAS,EAAE,IAAI;AAEf,cAAI,GAAG;AAGL,gBAAI,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE;AAAG,qBAAO,IAAIA,WAAU,GAAG;AAElD,uBAAW,CAAC,UAAU,EAAE,UAAU,KAAK,EAAE,UAAU;AAEnD,gBAAI;AAAU,kBAAI,EAAE,IAAI,CAAC;AAAA,UAI3B,WAAW,EAAE,IAAI,MAAM,EAAE,IAAI,KAAK,EAAE,IAAI,OAAO,EAAE,KAAK,IAElD,EAAE,EAAE,CAAC,IAAI,KAAK,UAAU,EAAE,EAAE,CAAC,KAAK,OAElC,EAAE,EAAE,CAAC,IAAI,QAAQ,UAAU,EAAE,EAAE,CAAC,KAAK,aAAa;AAGpD,gBAAI,EAAE,IAAI,KAAK,MAAM,CAAC,IAAI,KAAK;AAG/B,gBAAI,EAAE,IAAI;AAAI,kBAAI,IAAI;AAGtB,mBAAO,IAAIA,WAAU,SAAS,IAAI,IAAI,CAAC;AAAA,UAEzC,WAAW,eAAe;AAKxB,gBAAI,SAAS,gBAAgB,WAAW,CAAC;AAAA,UAC3C;AAEA,cAAI,QAAQ;AACV,mBAAO,IAAIA,WAAU,GAAG;AACxB,gBAAI;AAAQ,gBAAE,IAAI;AAClB,qBAAS,MAAM,CAAC;AAAA,UAClB,OAAO;AACL,gBAAI,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC;AACxB,qBAAS,IAAI;AAAA,UACf;AAEA,cAAI,IAAIA,WAAU,GAAG;AAGrB,qBAAU;AAER,gBAAI,QAAQ;AACV,kBAAI,EAAE,MAAM,CAAC;AACb,kBAAI,CAAC,EAAE;AAAG;AAEV,kBAAI,GAAG;AACL,oBAAI,EAAE,EAAE,SAAS;AAAG,oBAAE,EAAE,SAAS;AAAA,cACnC,WAAW,UAAU;AACnB,oBAAI,EAAE,IAAI,CAAC;AAAA,cACb;AAAA,YACF;AAEA,gBAAI,GAAG;AACL,kBAAI,UAAU,IAAI,CAAC;AACnB,kBAAI,MAAM;AAAG;AACb,uBAAS,IAAI;AAAA,YACf,OAAO;AACL,kBAAI,EAAE,MAAM,IAAI;AAChB,oBAAM,GAAG,EAAE,IAAI,GAAG,CAAC;AAEnB,kBAAI,EAAE,IAAI,IAAI;AACZ,yBAAS,MAAM,CAAC;AAAA,cAClB,OAAO;AACL,oBAAI,CAAC,QAAQ,CAAC;AACd,oBAAI,MAAM;AAAG;AACb,yBAAS,IAAI;AAAA,cACf;AAAA,YACF;AAEA,gBAAI,EAAE,MAAM,CAAC;AAEb,gBAAI,GAAG;AACL,kBAAI,EAAE,KAAK,EAAE,EAAE,SAAS;AAAG,kBAAE,EAAE,SAAS;AAAA,YAC1C,WAAW,UAAU;AACnB,kBAAI,EAAE,IAAI,CAAC;AAAA,YACb;AAAA,UACF;AAEA,cAAI;AAAU,mBAAO;AACrB,cAAI;AAAQ,gBAAI,IAAI,IAAI,CAAC;AAEzB,iBAAO,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,MAAM,GAAG,eAAe,eAAe,IAAI,IAAI;AAAA,QAC3E;AAWA,UAAE,eAAe,SAAU,IAAI;AAC7B,cAAI,IAAI,IAAIA,WAAU,IAAI;AAC1B,cAAI,MAAM;AAAM,iBAAK;AAAA;AAChB,qBAAS,IAAI,GAAG,CAAC;AACtB,iBAAO,MAAM,GAAG,EAAE,IAAI,GAAG,EAAE;AAAA,QAC7B;AAOA,UAAE,YAAY,EAAE,KAAK,SAAU,GAAG,GAAG;AACnC,iBAAO,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC,MAAM;AAAA,QAChD;AAMA,UAAE,WAAW,WAAY;AACvB,iBAAO,CAAC,CAAC,KAAK;AAAA,QAChB;AAOA,UAAE,gBAAgB,EAAE,KAAK,SAAU,GAAG,GAAG;AACvC,iBAAO,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC,IAAI;AAAA,QAC9C;AAOA,UAAE,yBAAyB,EAAE,MAAM,SAAU,GAAG,GAAG;AACjD,kBAAQ,IAAI,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC,OAAO,KAAK,MAAM;AAAA,QAEjE;AAMA,UAAE,YAAY,WAAY;AACxB,iBAAO,CAAC,CAAC,KAAK,KAAK,SAAS,KAAK,IAAI,QAAQ,IAAI,KAAK,EAAE,SAAS;AAAA,QACnE;AAOA,UAAE,aAAa,EAAE,KAAK,SAAU,GAAG,GAAG;AACpC,iBAAO,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC,IAAI;AAAA,QAC9C;AAOA,UAAE,sBAAsB,EAAE,MAAM,SAAU,GAAG,GAAG;AAC9C,kBAAQ,IAAI,QAAQ,MAAM,IAAIA,WAAU,GAAG,CAAC,CAAC,OAAO,MAAM,MAAM;AAAA,QAClE;AAMA,UAAE,QAAQ,WAAY;AACpB,iBAAO,CAAC,KAAK;AAAA,QACf;AAMA,UAAE,aAAa,WAAY;AACzB,iBAAO,KAAK,IAAI;AAAA,QAClB;AAMA,UAAE,aAAa,WAAY;AACzB,iBAAO,KAAK,IAAI;AAAA,QAClB;AAMA,UAAE,SAAS,WAAY;AACrB,iBAAO,CAAC,CAAC,KAAK,KAAK,KAAK,EAAE,CAAC,KAAK;AAAA,QAClC;AAuBA,UAAE,QAAQ,SAAU,GAAG,GAAG;AACxB,cAAI,GAAG,GAAG,GAAG,MACX,IAAI,MACJ,IAAI,EAAE;AAER,cAAI,IAAIA,WAAU,GAAG,CAAC;AACtB,cAAI,EAAE;AAGN,cAAI,CAAC,KAAK,CAAC;AAAG,mBAAO,IAAIA,WAAU,GAAG;AAGtC,cAAI,KAAK,GAAG;AACV,cAAE,IAAI,CAAC;AACP,mBAAO,EAAE,KAAK,CAAC;AAAA,UACjB;AAEA,cAAI,KAAK,EAAE,IAAI,UACb,KAAK,EAAE,IAAI,UACX,KAAK,EAAE,GACP,KAAK,EAAE;AAET,cAAI,CAAC,MAAM,CAAC,IAAI;AAGd,gBAAI,CAAC,MAAM,CAAC;AAAI,qBAAO,MAAM,EAAE,IAAI,CAAC,GAAG,KAAK,IAAIA,WAAU,KAAK,IAAI,GAAG;AAGtE,gBAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AAGpB,qBAAO,GAAG,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,KAAK,IAAIA,WAAU,GAAG,CAAC,IAAI;AAAA;AAAA,gBAGpD,iBAAiB,IAAI,KAAK;AAAA,eAAC;AAAA,YAC9B;AAAA,UACF;AAEA,eAAK,SAAS,EAAE;AAChB,eAAK,SAAS,EAAE;AAChB,eAAK,GAAG,MAAM;AAGd,cAAI,IAAI,KAAK,IAAI;AAEf,gBAAI,OAAO,IAAI,GAAG;AAChB,kBAAI,CAAC;AACL,kBAAI;AAAA,YACN,OAAO;AACL,mBAAK;AACL,kBAAI;AAAA,YACN;AAEA,cAAE,QAAQ;AAGV,iBAAK,IAAI,GAAG,KAAK,EAAE,KAAK,CAAC;AAAE;AAC3B,cAAE,QAAQ;AAAA,UACZ,OAAO;AAGL,iBAAK,QAAQ,IAAI,GAAG,WAAW,IAAI,GAAG,WAAW,IAAI;AAErD,iBAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK;AAE1B,kBAAI,GAAG,CAAC,KAAK,GAAG,CAAC,GAAG;AAClB,uBAAO,GAAG,CAAC,IAAI,GAAG,CAAC;AACnB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAGA,cAAI,MAAM;AACR,gBAAI;AACJ,iBAAK;AACL,iBAAK;AACL,cAAE,IAAI,CAAC,EAAE;AAAA,UACX;AAEA,eAAK,IAAI,GAAG,WAAW,IAAI,GAAG;AAI9B,cAAI,IAAI;AAAG,mBAAO,KAAK,GAAG,GAAG,IAAI;AAAE;AACnC,cAAI,OAAO;AAGX,iBAAO,IAAI,KAAI;AAEb,gBAAI,GAAG,EAAE,CAAC,IAAI,GAAG,CAAC,GAAG;AACnB,mBAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI;AAAE;AACrC,gBAAE,GAAG,CAAC;AACN,iBAAG,CAAC,KAAK;AAAA,YACX;AAEA,eAAG,CAAC,KAAK,GAAG,CAAC;AAAA,UACf;AAGA,iBAAO,GAAG,CAAC,KAAK,GAAG,GAAG,OAAO,GAAG,CAAC,GAAG,EAAE;AAAG;AAGzC,cAAI,CAAC,GAAG,CAAC,GAAG;AAIV,cAAE,IAAI,iBAAiB,IAAI,KAAK;AAChC,cAAE,IAAI,CAAC,EAAE,IAAI,CAAC;AACd,mBAAO;AAAA,UACT;AAIA,iBAAO,UAAU,GAAG,IAAI,EAAE;AAAA,QAC5B;AAwBA,UAAE,SAAS,EAAE,MAAM,SAAU,GAAG,GAAG;AACjC,cAAI,GAAG,GACL,IAAI;AAEN,cAAI,IAAIA,WAAU,GAAG,CAAC;AAGtB,cAAI,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG;AAClC,mBAAO,IAAIA,WAAU,GAAG;AAAA,UAG1B,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG;AACjC,mBAAO,IAAIA,WAAU,CAAC;AAAA,UACxB;AAEA,cAAI,eAAe,GAAG;AAIpB,gBAAI,EAAE;AACN,cAAE,IAAI;AACN,gBAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAClB,cAAE,IAAI;AACN,cAAE,KAAK;AAAA,UACT,OAAO;AACL,gBAAI,IAAI,GAAG,GAAG,GAAG,WAAW;AAAA,UAC9B;AAEA,cAAI,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;AAGtB,cAAI,CAAC,EAAE,EAAE,CAAC,KAAK,eAAe;AAAG,cAAE,IAAI,EAAE;AAEzC,iBAAO;AAAA,QACT;AAuBA,UAAE,eAAe,EAAE,QAAQ,SAAU,GAAG,GAAG;AACzC,cAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAClD,MAAM,UACN,IAAI,MACJ,KAAK,EAAE,GACP,MAAM,IAAI,IAAIA,WAAU,GAAG,CAAC,GAAG;AAGjC,cAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG;AAGlC,gBAAI,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;AAC9D,gBAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AAAA,YACpB,OAAO;AACL,gBAAE,KAAK,EAAE;AAGT,kBAAI,CAAC,MAAM,CAAC,IAAI;AACd,kBAAE,IAAI,EAAE,IAAI;AAAA,cAGd,OAAO;AACL,kBAAE,IAAI,CAAC,CAAC;AACR,kBAAE,IAAI;AAAA,cACR;AAAA,YACF;AAEA,mBAAO;AAAA,UACT;AAEA,cAAI,SAAS,EAAE,IAAI,QAAQ,IAAI,SAAS,EAAE,IAAI,QAAQ;AACtD,YAAE,KAAK,EAAE;AACT,gBAAM,GAAG;AACT,gBAAM,GAAG;AAGT,cAAI,MAAM,KAAK;AACb,iBAAK;AACL,iBAAK;AACL,iBAAK;AACL,gBAAI;AACJ,kBAAM;AACN,kBAAM;AAAA,UACR;AAGA,eAAK,IAAI,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AAAE;AAE7C,iBAAO;AACP,qBAAW;AAEX,eAAK,IAAI,KAAK,EAAE,KAAK,KAAI;AACvB,gBAAI;AACJ,kBAAM,GAAG,CAAC,IAAI;AACd,kBAAM,GAAG,CAAC,IAAI,WAAW;AAEzB,iBAAK,IAAI,KAAK,IAAI,IAAI,GAAG,IAAI,KAAI;AAC/B,oBAAM,GAAG,EAAE,CAAC,IAAI;AAChB,oBAAM,GAAG,CAAC,IAAI,WAAW;AACzB,kBAAI,MAAM,MAAM,MAAM;AACtB,oBAAM,MAAM,MAAQ,IAAI,WAAY,WAAY,GAAG,CAAC,IAAI;AACxD,mBAAK,MAAM,OAAO,MAAM,IAAI,WAAW,KAAK,MAAM;AAClD,iBAAG,GAAG,IAAI,MAAM;AAAA,YAClB;AAEA,eAAG,CAAC,IAAI;AAAA,UACV;AAEA,cAAI,GAAG;AACL,cAAE;AAAA,UACJ,OAAO;AACL,eAAG,OAAO,GAAG,CAAC;AAAA,UAChB;AAEA,iBAAO,UAAU,GAAG,IAAI,CAAC;AAAA,QAC3B;AAOA,UAAE,UAAU,WAAY;AACtB,cAAI,IAAI,IAAIA,WAAU,IAAI;AAC1B,YAAE,IAAI,CAAC,EAAE,KAAK;AACd,iBAAO;AAAA,QACT;AAuBA,UAAE,OAAO,SAAU,GAAG,GAAG;AACvB,cAAI,GACF,IAAI,MACJ,IAAI,EAAE;AAER,cAAI,IAAIA,WAAU,GAAG,CAAC;AACtB,cAAI,EAAE;AAGN,cAAI,CAAC,KAAK,CAAC;AAAG,mBAAO,IAAIA,WAAU,GAAG;AAGrC,cAAI,KAAK,GAAG;AACX,cAAE,IAAI,CAAC;AACP,mBAAO,EAAE,MAAM,CAAC;AAAA,UAClB;AAEA,cAAI,KAAK,EAAE,IAAI,UACb,KAAK,EAAE,IAAI,UACX,KAAK,EAAE,GACP,KAAK,EAAE;AAET,cAAI,CAAC,MAAM,CAAC,IAAI;AAGd,gBAAI,CAAC,MAAM,CAAC;AAAI,qBAAO,IAAIA,WAAU,IAAI,CAAC;AAI1C,gBAAI,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC;AAAG,qBAAO,GAAG,CAAC,IAAI,IAAI,IAAIA,WAAU,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC;AAAA,UAC1E;AAEA,eAAK,SAAS,EAAE;AAChB,eAAK,SAAS,EAAE;AAChB,eAAK,GAAG,MAAM;AAGd,cAAI,IAAI,KAAK,IAAI;AACf,gBAAI,IAAI,GAAG;AACT,mBAAK;AACL,kBAAI;AAAA,YACN,OAAO;AACL,kBAAI,CAAC;AACL,kBAAI;AAAA,YACN;AAEA,cAAE,QAAQ;AACV,mBAAO,KAAK,EAAE,KAAK,CAAC;AAAE;AACtB,cAAE,QAAQ;AAAA,UACZ;AAEA,cAAI,GAAG;AACP,cAAI,GAAG;AAGP,cAAI,IAAI,IAAI,GAAG;AACb,gBAAI;AACJ,iBAAK;AACL,iBAAK;AACL,gBAAI;AAAA,UACN;AAGA,eAAK,IAAI,GAAG,KAAI;AACd,iBAAK,GAAG,EAAE,CAAC,IAAI,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,OAAO;AAC3C,eAAG,CAAC,IAAI,SAAS,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,IAAI;AAAA,UACvC;AAEA,cAAI,GAAG;AACL,iBAAK,CAAC,CAAC,EAAE,OAAO,EAAE;AAClB,cAAE;AAAA,UACJ;AAIA,iBAAO,UAAU,GAAG,IAAI,EAAE;AAAA,QAC5B;AAkBA,UAAE,YAAY,EAAE,KAAK,SAAU,IAAI,IAAI;AACrC,cAAI,GAAG,GAAG,GACR,IAAI;AAEN,cAAI,MAAM,QAAQ,OAAO,CAAC,CAAC,IAAI;AAC7B,qBAAS,IAAI,GAAG,GAAG;AACnB,gBAAI,MAAM;AAAM,mBAAK;AAAA;AAChB,uBAAS,IAAI,GAAG,CAAC;AAEtB,mBAAO,MAAM,IAAIA,WAAU,CAAC,GAAG,IAAI,EAAE;AAAA,UACvC;AAEA,cAAI,EAAE,IAAI,EAAE;AAAI,mBAAO;AACvB,cAAI,EAAE,SAAS;AACf,cAAI,IAAI,WAAW;AAEnB,cAAI,IAAI,EAAE,CAAC,GAAG;AAGZ,mBAAO,IAAI,MAAM,GAAG,KAAK,IAAI;AAAI;AAGjC,iBAAK,IAAI,EAAE,CAAC,GAAG,KAAK,IAAI,KAAK,IAAI;AAAI;AAAA,UACvC;AAEA,cAAI,MAAM,EAAE,IAAI,IAAI;AAAG,gBAAI,EAAE,IAAI;AAEjC,iBAAO;AAAA,QACT;AAWA,UAAE,YAAY,SAAU,GAAG;AACzB,mBAAS,GAAG,CAAC,kBAAkB,gBAAgB;AAC/C,iBAAO,KAAK,MAAM,OAAO,CAAC;AAAA,QAC5B;AAcA,UAAE,aAAa,EAAE,OAAO,WAAY;AAClC,cAAI,GAAG,GAAG,GAAG,KAAK,GAChB,IAAI,MACJ,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,KAAK,iBAAiB,GACtB,OAAO,IAAIA,WAAU,KAAK;AAG5B,cAAI,MAAM,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;AAC1B,mBAAO,IAAIA,WAAU,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC;AAAA,UACxE;AAGA,cAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,CAAC;AAIzB,cAAI,KAAK,KAAK,KAAK,IAAI,GAAG;AACxB,gBAAI,cAAc,CAAC;AACnB,iBAAK,EAAE,SAAS,KAAK,KAAK;AAAG,mBAAK;AAClC,gBAAI,KAAK,KAAK,CAAC,CAAC;AAChB,gBAAI,UAAU,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,IAAI;AAE1C,gBAAI,KAAK,IAAI,GAAG;AACd,kBAAI,OAAO;AAAA,YACb,OAAO;AACL,kBAAI,EAAE,cAAc;AACpB,kBAAI,EAAE,MAAM,GAAG,EAAE,QAAQ,GAAG,IAAI,CAAC,IAAI;AAAA,YACvC;AAEA,gBAAI,IAAIA,WAAU,CAAC;AAAA,UACrB,OAAO;AACL,gBAAI,IAAIA,WAAU,IAAI,EAAE;AAAA,UAC1B;AAMA,cAAI,EAAE,EAAE,CAAC,GAAG;AACV,gBAAI,EAAE;AACN,gBAAI,IAAI;AACR,gBAAI,IAAI;AAAG,kBAAI;AAGf,uBAAU;AACR,kBAAI;AACJ,kBAAI,KAAK,MAAM,EAAE,KAAK,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;AAEvC,kBAAI,cAAc,EAAE,CAAC,EAAE,MAAM,GAAG,CAAC,OAAO,IAAI,cAAc,EAAE,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG;AAK3E,oBAAI,EAAE,IAAI;AAAG,oBAAE;AACf,oBAAI,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC;AAKxB,oBAAI,KAAK,UAAU,CAAC,OAAO,KAAK,QAAQ;AAItC,sBAAI,CAAC,KAAK;AACR,0BAAM,GAAG,EAAE,IAAI,iBAAiB,GAAG,CAAC;AAEpC,wBAAI,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC,GAAG;AACpB,0BAAI;AACJ;AAAA,oBACF;AAAA,kBACF;AAEA,wBAAM;AACN,uBAAK;AACL,wBAAM;AAAA,gBACR,OAAO;AAIL,sBAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,KAAK,KAAK;AAG7C,0BAAM,GAAG,EAAE,IAAI,iBAAiB,GAAG,CAAC;AACpC,wBAAI,CAAC,EAAE,MAAM,CAAC,EAAE,GAAG,CAAC;AAAA,kBACtB;AAEA;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAEA,iBAAO,MAAM,GAAG,EAAE,IAAI,iBAAiB,GAAG,eAAe,CAAC;AAAA,QAC5D;AAYA,UAAE,gBAAgB,SAAU,IAAI,IAAI;AAClC,cAAI,MAAM,MAAM;AACd,qBAAS,IAAI,GAAG,GAAG;AACnB;AAAA,UACF;AACA,iBAAO,OAAO,MAAM,IAAI,IAAI,CAAC;AAAA,QAC/B;AAeA,UAAE,UAAU,SAAU,IAAI,IAAI;AAC5B,cAAI,MAAM,MAAM;AACd,qBAAS,IAAI,GAAG,GAAG;AACnB,iBAAK,KAAK,KAAK,IAAI;AAAA,UACrB;AACA,iBAAO,OAAO,MAAM,IAAI,EAAE;AAAA,QAC5B;AA4BA,UAAE,WAAW,SAAU,IAAI,IAAIE,SAAQ;AACrC,cAAI,KACF,IAAI;AAEN,cAAIA,WAAU,MAAM;AAClB,gBAAI,MAAM,QAAQ,MAAM,OAAO,MAAM,UAAU;AAC7C,cAAAA,UAAS;AACT,mBAAK;AAAA,YACP,WAAW,MAAM,OAAO,MAAM,UAAU;AACtC,cAAAA,UAAS;AACT,mBAAK,KAAK;AAAA,YACZ,OAAO;AACL,cAAAA,UAAS;AAAA,YACX;AAAA,UACF,WAAW,OAAOA,WAAU,UAAU;AACpC,kBAAM,MACH,iBAAiB,6BAA6BA,OAAM;AAAA,UACzD;AAEA,gBAAM,EAAE,QAAQ,IAAI,EAAE;AAEtB,cAAI,EAAE,GAAG;AACP,gBAAI,GACF,MAAM,IAAI,MAAM,GAAG,GACnB,KAAK,CAACA,QAAO,WACb,KAAK,CAACA,QAAO,oBACb,iBAAiBA,QAAO,kBAAkB,IAC1C,UAAU,IAAI,CAAC,GACf,eAAe,IAAI,CAAC,GACpB,QAAQ,EAAE,IAAI,GACd,YAAY,QAAQ,QAAQ,MAAM,CAAC,IAAI,SACvC,MAAM,UAAU;AAElB,gBAAI,IAAI;AACN,kBAAI;AACJ,mBAAK;AACL,mBAAK;AACL,qBAAO;AAAA,YACT;AAEA,gBAAI,KAAK,KAAK,MAAM,GAAG;AACrB,kBAAI,MAAM,MAAM;AAChB,wBAAU,UAAU,OAAO,GAAG,CAAC;AAC/B,qBAAO,IAAI,KAAK,KAAK;AAAI,2BAAW,iBAAiB,UAAU,OAAO,GAAG,EAAE;AAC3E,kBAAI,KAAK;AAAG,2BAAW,iBAAiB,UAAU,MAAM,CAAC;AACzD,kBAAI;AAAO,0BAAU,MAAM;AAAA,YAC7B;AAEA,kBAAM,eACH,WAAWA,QAAO,oBAAoB,QAAQ,KAAK,CAACA,QAAO,qBAC1D,aAAa;AAAA,cAAQ,IAAI,OAAO,SAAS,KAAK,QAAQ,GAAG;AAAA,cAC1D,QAAQA,QAAO,0BAA0B;AAAA,YAAG,IAC3C,gBACD;AAAA,UACL;AAEA,kBAAQA,QAAO,UAAU,MAAM,OAAOA,QAAO,UAAU;AAAA,QACzD;AAcA,UAAE,aAAa,SAAU,IAAI;AAC3B,cAAI,GAAG,IAAI,IAAI,IAAI,GAAG,KAAK,GAAG,IAAI,IAAI,GAAG,GAAG,GAC1C,IAAI,MACJ,KAAK,EAAE;AAET,cAAI,MAAM,MAAM;AACd,gBAAI,IAAIF,WAAU,EAAE;AAGpB,gBAAI,CAAC,EAAE,UAAU,MAAM,EAAE,KAAK,EAAE,MAAM,MAAM,EAAE,GAAG,GAAG,GAAG;AACrD,oBAAM,MACH,iBAAiB,eACf,EAAE,UAAU,IAAI,mBAAmB,sBAAsB,QAAQ,CAAC,CAAC;AAAA,YAC1E;AAAA,UACF;AAEA,cAAI,CAAC;AAAI,mBAAO,IAAIA,WAAU,CAAC;AAE/B,cAAI,IAAIA,WAAU,GAAG;AACrB,eAAK,KAAK,IAAIA,WAAU,GAAG;AAC3B,eAAK,KAAK,IAAIA,WAAU,GAAG;AAC3B,cAAI,cAAc,EAAE;AAIpB,cAAI,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI;AAC3B,YAAE,EAAE,CAAC,IAAI,UAAU,MAAM,IAAI,YAAY,IAAI,WAAW,MAAM,GAAG;AACjE,eAAK,CAAC,MAAM,EAAE,WAAW,CAAC,IAAI,IAAK,IAAI,IAAI,IAAI,KAAM;AAErD,gBAAM;AACN,oBAAU,IAAI;AACd,cAAI,IAAIA,WAAU,CAAC;AAGnB,aAAG,EAAE,CAAC,IAAI;AAEV,qBAAW;AACT,gBAAI,IAAI,GAAG,GAAG,GAAG,CAAC;AAClB,iBAAK,GAAG,KAAK,EAAE,MAAM,EAAE,CAAC;AACxB,gBAAI,GAAG,WAAW,EAAE,KAAK;AAAG;AAC5B,iBAAK;AACL,iBAAK;AACL,iBAAK,GAAG,KAAK,EAAE,MAAM,KAAK,EAAE,CAAC;AAC7B,iBAAK;AACL,gBAAI,EAAE,MAAM,EAAE,MAAM,KAAK,CAAC,CAAC;AAC3B,gBAAI;AAAA,UACN;AAEA,eAAK,IAAI,GAAG,MAAM,EAAE,GAAG,IAAI,GAAG,CAAC;AAC/B,eAAK,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC;AACzB,eAAK,GAAG,KAAK,GAAG,MAAM,EAAE,CAAC;AACzB,aAAG,IAAI,GAAG,IAAI,EAAE;AAChB,cAAI,IAAI;AAGR,cAAI,IAAI,IAAI,IAAI,GAAG,aAAa,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE;AAAA,YAC7C,IAAI,IAAI,IAAI,GAAG,aAAa,EAAE,MAAM,CAAC,EAAE,IAAI;AAAA,UAAC,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE;AAE1E,oBAAU;AAEV,iBAAO;AAAA,QACT;AAMA,UAAE,WAAW,WAAY;AACvB,iBAAO,CAAC,QAAQ,IAAI;AAAA,QACtB;AAcA,UAAE,cAAc,SAAU,IAAI,IAAI;AAChC,cAAI,MAAM;AAAM,qBAAS,IAAI,GAAG,GAAG;AACnC,iBAAO,OAAO,MAAM,IAAI,IAAI,CAAC;AAAA,QAC/B;AAcA,UAAE,WAAW,SAAU,GAAG;AACxB,cAAI,KACF,IAAI,MACJ,IAAI,EAAE,GACN,IAAI,EAAE;AAGR,cAAI,MAAM,MAAM;AACd,gBAAI,GAAG;AACL,oBAAM;AACN,kBAAI,IAAI;AAAG,sBAAM,MAAM;AAAA,YACzB,OAAO;AACL,oBAAM;AAAA,YACR;AAAA,UACF,OAAO;AACL,gBAAI,KAAK,MAAM;AACb,oBAAM,KAAK,cAAc,KAAK,aAC3B,cAAc,cAAc,EAAE,CAAC,GAAG,CAAC,IACnC,aAAa,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG;AAAA,YAC5C,WAAW,MAAM,MAAM,gCAAgC;AACrD,kBAAI,MAAM,IAAIA,WAAU,CAAC,GAAG,iBAAiB,IAAI,GAAG,aAAa;AACjE,oBAAM,aAAa,cAAc,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG;AAAA,YACjD,OAAO;AACL,uBAAS,GAAG,GAAG,SAAS,QAAQ,MAAM;AACtC,oBAAM,YAAY,aAAa,cAAc,EAAE,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,IAAI;AAAA,YAC5E;AAEA,gBAAI,IAAI,KAAK,EAAE,EAAE,CAAC;AAAG,oBAAM,MAAM;AAAA,UACnC;AAEA,iBAAO;AAAA,QACT;AAOA,UAAE,UAAU,EAAE,SAAS,WAAY;AACjC,iBAAO,QAAQ,IAAI;AAAA,QACrB;AAGA,UAAE,eAAe;AAEjB,YAAI,gBAAgB;AAAM,UAAAA,WAAU,IAAI,YAAY;AAEpD,eAAOA;AAAA,MACT;AASA,eAAS,SAAS,GAAG;AACnB,YAAI,IAAI,IAAI;AACZ,eAAO,IAAI,KAAK,MAAM,IAAI,IAAI,IAAI;AAAA,MACpC;AAIA,eAAS,cAAc,GAAG;AACxB,YAAI,GAAG,GACL,IAAI,GACJ,IAAI,EAAE,QACN,IAAI,EAAE,CAAC,IAAI;AAEb,eAAO,IAAI,KAAI;AACb,cAAI,EAAE,GAAG,IAAI;AACb,cAAI,WAAW,EAAE;AACjB,iBAAO,KAAK,IAAI,MAAM;AAAE;AACxB,eAAK;AAAA,QACP;AAGA,aAAK,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,MAAM;AAAI;AAE7C,eAAO,EAAE,MAAM,GAAG,IAAI,KAAK,CAAC;AAAA,MAC9B;AAIA,eAAS,QAAQ,GAAG,GAAG;AACrB,YAAI,GAAG,GACL,KAAK,EAAE,GACP,KAAK,EAAE,GACP,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE,GACN,IAAI,EAAE;AAGR,YAAI,CAAC,KAAK,CAAC;AAAG,iBAAO;AAErB,YAAI,MAAM,CAAC,GAAG,CAAC;AACf,YAAI,MAAM,CAAC,GAAG,CAAC;AAGf,YAAI,KAAK;AAAG,iBAAO,IAAI,IAAI,IAAI,CAAC,IAAI;AAGpC,YAAI,KAAK;AAAG,iBAAO;AAEnB,YAAI,IAAI;AACR,YAAI,KAAK;AAGT,YAAI,CAAC,MAAM,CAAC;AAAI,iBAAO,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI;AAG7C,YAAI,CAAC;AAAG,iBAAO,IAAI,IAAI,IAAI,IAAI;AAE/B,aAAK,IAAI,GAAG,WAAW,IAAI,GAAG,UAAU,IAAI;AAG5C,aAAK,IAAI,GAAG,IAAI,GAAG;AAAK,cAAI,GAAG,CAAC,KAAK,GAAG,CAAC;AAAG,mBAAO,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI;AAG3E,eAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAAA,MACtC;AAMA,eAAS,SAAS,GAAG,KAAK,KAAK,MAAM;AACnC,YAAI,IAAI,OAAO,IAAI,OAAO,MAAM,UAAU,CAAC,GAAG;AAC5C,gBAAM,MACJ,kBAAkB,QAAQ,eAAe,OAAO,KAAK,WAClD,IAAI,OAAO,IAAI,MAAM,oBAAoB,sBACzC,+BAA+B,OAAO,CAAC,CAAC;AAAA,QAC/C;AAAA,MACF;AAIA,eAAS,MAAM,GAAG;AAChB,YAAI,IAAI,EAAE,EAAE,SAAS;AACrB,eAAO,SAAS,EAAE,IAAI,QAAQ,KAAK,KAAK,EAAE,EAAE,CAAC,IAAI,KAAK;AAAA,MACxD;AAGA,eAAS,cAAc,KAAK,GAAG;AAC7B,gBAAQ,IAAI,SAAS,IAAI,IAAI,OAAO,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,QAC5D,IAAI,IAAI,MAAM,QAAQ;AAAA,MAC1B;AAGA,eAAS,aAAa,KAAK,GAAG,GAAG;AAC/B,YAAI,KAAK;AAGT,YAAI,IAAI,GAAG;AAGT,eAAK,KAAK,IAAI,KAAK,EAAE,GAAG,MAAM;AAAE;AAChC,gBAAM,KAAK;AAAA,QAGb,OAAO;AACL,gBAAM,IAAI;AAGV,cAAI,EAAE,IAAI,KAAK;AACb,iBAAK,KAAK,GAAG,KAAK,KAAK,EAAE,GAAG,MAAM;AAAE;AACpC,mBAAO;AAAA,UACT,WAAW,IAAI,KAAK;AAClB,kBAAM,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,MAAM,CAAC;AAAA,UAC3C;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAMA,kBAAY,MAAM;AAClB,gBAAU,SAAS,IAAI,UAAU,YAAY;AAG7C,UAAI,OAAO,UAAU,cAAc,OAAO,KAAK;AAC7C,eAAO,WAAY;AAAE,iBAAO;AAAA,QAAW,CAAC;AAAA,MAG1C,WAAW,OAAO,UAAU,eAAe,OAAO,SAAS;AACzD,eAAO,UAAU;AAAA,MAGnB,OAAO;AACL,YAAI,CAAC,cAAc;AACjB,yBAAe,OAAO,QAAQ,eAAe,OAAO,OAAO;AAAA,QAC7D;AAEA,qBAAa,YAAY;AAAA,MAC3B;AAAA,IACF,GAAG,OAAI;AAAA;AAAA;;;ACz2FP;AAAA;AAAA,QAAI,YAAY;AAmKhB,QAAI,OAAO,OAAO;AAElB,KAAC,WAAY;AACT;AAEA,eAAS,EAAE,GAAG;AAEV,eAAO,IAAI,KAAK,MAAM,IAAI;AAAA,MAC9B;AAEA,UAAI,KAAK,4GACL,YAAY,4HACZ,KACA,QACA,OAAO;AAAA;AAAA,QACH,MAAM;AAAA,QACN,KAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,MAAM;AAAA,QACN,KAAM;AAAA,QACN,MAAM;AAAA,MACV,GACA;AAGJ,eAAS,MAAM,QAAQ;AAOnB,kBAAU,YAAY;AACtB,eAAO,UAAU,KAAK,MAAM,IAAI,MAAM,OAAO,QAAQ,WAAW,SAAU,GAAG;AACzE,cAAI,IAAI,KAAK,CAAC;AACd,iBAAO,OAAO,MAAM,WACd,IACA,SAAS,SAAS,EAAE,WAAW,CAAC,EAAE,SAAS,EAAE,GAAG,MAAM,EAAE;AAAA,QAClE,CAAC,IAAI,MAAM,MAAM,SAAS;AAAA,MAC9B;AAGA,eAAS,IAAI,KAAK,QAAQ;AAItB,YAAI,GACA,GACA,GACA,QACA,OAAO,KACP,SACA,QAAQ,OAAO,GAAG,GAClB,cAAc,SAAS,SAAS,iBAAiB,aAAa,UAAU,YAAY,KAAK;AAI7F,YAAI,SAAS,OAAO,UAAU,YACtB,OAAO,MAAM,WAAW,YAAY;AACxC,kBAAQ,MAAM,OAAO,GAAG;AAAA,QAC5B;AAKA,YAAI,OAAO,QAAQ,YAAY;AAC3B,kBAAQ,IAAI,KAAK,QAAQ,KAAK,KAAK;AAAA,QACvC;AAIA,gBAAQ,OAAO,OAAO;AAAA,UACtB,KAAK;AACD,gBAAI,aAAa;AACb,qBAAO;AAAA,YACX,OAAO;AACH,qBAAO,MAAM,KAAK;AAAA,YACtB;AAAA,UAEJ,KAAK;AAID,mBAAO,SAAS,KAAK,IAAI,OAAO,KAAK,IAAI;AAAA,UAE7C,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAMD,mBAAO,OAAO,KAAK;AAAA,UAKvB,KAAK;AAKD,gBAAI,CAAC,OAAO;AACR,qBAAO;AAAA,YACX;AAIA,mBAAO;AACP,sBAAU,CAAC;AAIX,gBAAI,OAAO,UAAU,SAAS,MAAM,KAAK,MAAM,kBAAkB;AAK7D,uBAAS,MAAM;AACf,mBAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,wBAAQ,CAAC,IAAI,IAAI,GAAG,KAAK,KAAK;AAAA,cAClC;AAKA,kBAAI,QAAQ,WAAW,IACjB,OACA,MACA,QAAQ,MAAM,QAAQ,KAAK,QAAQ,GAAG,IAAI,OAAO,OAAO,MACxD,MAAM,QAAQ,KAAK,GAAG,IAAI;AAChC,oBAAM;AACN,qBAAO;AAAA,YACX;AAIA,gBAAI,OAAO,OAAO,QAAQ,UAAU;AAChC,uBAAS,IAAI;AACb,mBAAK,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAC5B,oBAAI,OAAO,IAAI,CAAC,MAAM,UAAU;AAC5B,sBAAI,IAAI,CAAC;AACT,sBAAI,IAAI,GAAG,KAAK;AAChB,sBAAI,GAAG;AACH,4BAAQ,KAAK,MAAM,CAAC,KAAK,MAAM,OAAO,OAAO,CAAC;AAAA,kBAClD;AAAA,gBACJ;AAAA,cACJ;AAAA,YACJ,OAAO;AAIH,qBAAO,KAAK,KAAK,EAAE,QAAQ,SAASG,IAAG;AACnC,oBAAIC,KAAI,IAAID,IAAG,KAAK;AACpB,oBAAIC,IAAG;AACH,0BAAQ,KAAK,MAAMD,EAAC,KAAK,MAAM,OAAO,OAAOC,EAAC;AAAA,gBAClD;AAAA,cACJ,CAAC;AAAA,YACL;AAKA,gBAAI,QAAQ,WAAW,IACjB,OACA,MACA,QAAQ,MAAM,QAAQ,KAAK,QAAQ,GAAG,IAAI,OAAO,OAAO,MACxD,MAAM,QAAQ,KAAK,GAAG,IAAI;AAChC,kBAAM;AACN,mBAAO;AAAA,QACX;AAAA,MACJ;AAIA,UAAI,OAAO,KAAK,cAAc,YAAY;AACtC,aAAK,YAAY,SAAU,OAAO,UAAU,OAAO;AAQ/C,cAAI;AACJ,gBAAM;AACN,mBAAS;AAKT,cAAI,OAAO,UAAU,UAAU;AAC3B,iBAAK,IAAI,GAAG,IAAI,OAAO,KAAK,GAAG;AAC3B,wBAAU;AAAA,YACd;AAAA,UAIJ,WAAW,OAAO,UAAU,UAAU;AAClC,qBAAS;AAAA,UACb;AAKA,gBAAM;AACN,cAAI,YAAY,OAAO,aAAa,eAC3B,OAAO,aAAa,YACrB,OAAO,SAAS,WAAW,WAAW;AAC1C,kBAAM,IAAI,MAAM,gBAAgB;AAAA,UACpC;AAKA,iBAAO,IAAI,IAAI,EAAC,IAAI,MAAK,CAAC;AAAA,QAC9B;AAAA,MACJ;AAAA,IACJ,GAAE;AAAA;AAAA;;;AC/XF;AAAA;AAAA,QAAI,YAAY;AAMhB,QAAM,iBAAiB;AACvB,QAAM,uBAAuB;AAgE7B,QAAI,aAAa,SAAU,SAAS;AAClC;AAWA,UAAI,WAAW;AAAA,QACb,QAAQ;AAAA;AAAA,QACR,eAAe;AAAA;AAAA,QACf,kBAAkB;AAAA;AAAA,QAClB,iBAAiB;AAAA;AAAA,QACjB,aAAa;AAAA,QACb,mBAAmB;AAAA,MACrB;AAGA,UAAI,YAAY,UAAa,YAAY,MAAM;AAC7C,YAAI,QAAQ,WAAW,MAAM;AAC3B,mBAAS,SAAS;AAAA,QACpB;AACA,YAAI,QAAQ,kBAAkB,MAAM;AAClC,mBAAS,gBAAgB;AAAA,QAC3B;AACA,iBAAS,mBACP,QAAQ,qBAAqB,OAAO,QAAQ,mBAAmB;AACjE,iBAAS,kBACP,QAAQ,oBAAoB,OAAO,QAAQ,kBAAkB;AAE/D,YAAI,OAAO,QAAQ,sBAAsB,aAAa;AACpD,cACE,QAAQ,sBAAsB,WAC9B,QAAQ,sBAAsB,YAC9B,QAAQ,sBAAsB,YAC9B;AACA,qBAAS,oBAAoB,QAAQ;AAAA,UACvC,OAAO;AACL,kBAAM,IAAI;AAAA,cACR,mGAAmG,QAAQ,iBAAiB;AAAA,YAC9H;AAAA,UACF;AAAA,QACF;AAEA,YAAI,OAAO,QAAQ,gBAAgB,aAAa;AAC9C,cACE,QAAQ,gBAAgB,WACxB,QAAQ,gBAAgB,YACxB,QAAQ,gBAAgB,YACxB;AACA,qBAAS,cAAc,QAAQ;AAAA,UACjC,OAAO;AACL,kBAAM,IAAI;AAAA,cACR,6FAA6F,QAAQ,WAAW;AAAA,YAClH;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,UAAI,IACF,IACA,UAAU;AAAA,QACR,KAAK;AAAA,QACL,MAAM;AAAA,QACN,KAAK;AAAA,QACL,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,QACH,GAAG;AAAA,MACL,GACA,MACA,QAAQ,SAAU,GAAG;AAGnB,cAAM;AAAA,UACJ,MAAM;AAAA,UACN,SAAS;AAAA,UACT;AAAA,UACA;AAAA,QACF;AAAA,MACF,GACA,OAAO,SAAU,GAAG;AAGlB,YAAI,KAAK,MAAM,IAAI;AACjB,gBAAM,eAAe,IAAI,mBAAmB,KAAK,GAAG;AAAA,QACtD;AAKA,aAAK,KAAK,OAAO,EAAE;AACnB,cAAM;AACN,eAAO;AAAA,MACT,GACA,SAAS,WAAY;AAGnB,YAAIC,SACFC,UAAS;AAEX,YAAI,OAAO,KAAK;AACd,UAAAA,UAAS;AACT,eAAK,GAAG;AAAA,QACV;AACA,eAAO,MAAM,OAAO,MAAM,KAAK;AAC7B,UAAAA,WAAU;AACV,eAAK;AAAA,QACP;AACA,YAAI,OAAO,KAAK;AACd,UAAAA,WAAU;AACV,iBAAO,KAAK,KAAK,MAAM,OAAO,MAAM,KAAK;AACvC,YAAAA,WAAU;AAAA,UACZ;AAAA,QACF;AACA,YAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,UAAAA,WAAU;AACV,eAAK;AACL,cAAI,OAAO,OAAO,OAAO,KAAK;AAC5B,YAAAA,WAAU;AACV,iBAAK;AAAA,UACP;AACA,iBAAO,MAAM,OAAO,MAAM,KAAK;AAC7B,YAAAA,WAAU;AACV,iBAAK;AAAA,UACP;AAAA,QACF;AACA,QAAAD,UAAS,CAACC;AACV,YAAI,CAAC,SAASD,OAAM,GAAG;AACrB,gBAAM,YAAY;AAAA,QACpB,OAAO;AACL,cAAI,aAAa;AAAM,wBAAY;AAGnC,cAAIC,QAAO,SAAS;AAClB,mBAAO,SAAS,gBACZA,UACA,SAAS,kBACT,OAAOA,OAAM,IACb,IAAI,UAAUA,OAAM;AAAA;AAExB,mBAAO,CAAC,SAAS,mBACbD,UACA,SAAS,kBACT,OAAOA,OAAM,IACb,IAAI,UAAUA,OAAM;AAAA,QAC5B;AAAA,MACF,GACA,SAAS,WAAY;AAGnB,YAAI,KACF,GACAC,UAAS,IACT;AAIF,YAAI,OAAO,KAAK;AACd,cAAI,UAAU;AACd,iBAAO,KAAK,GAAG;AACb,gBAAI,OAAO,KAAK;AACd,kBAAI,KAAK,IAAI;AAAS,gBAAAA,WAAU,KAAK,UAAU,SAAS,KAAK,CAAC;AAC9D,mBAAK;AACL,qBAAOA;AAAA,YACT;AACA,gBAAI,OAAO,MAAM;AACf,kBAAI,KAAK,IAAI;AAAS,gBAAAA,WAAU,KAAK,UAAU,SAAS,KAAK,CAAC;AAC9D,mBAAK;AACL,kBAAI,OAAO,KAAK;AACd,wBAAQ;AACR,qBAAK,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG;AACzB,wBAAM,SAAS,KAAK,GAAG,EAAE;AACzB,sBAAI,CAAC,SAAS,GAAG,GAAG;AAClB;AAAA,kBACF;AACA,0BAAQ,QAAQ,KAAK;AAAA,gBACvB;AACA,gBAAAA,WAAU,OAAO,aAAa,KAAK;AAAA,cACrC,WAAW,OAAO,QAAQ,EAAE,MAAM,UAAU;AAC1C,gBAAAA,WAAU,QAAQ,EAAE;AAAA,cACtB,OAAO;AACL;AAAA,cACF;AACA,wBAAU;AAAA,YACZ;AAAA,UACF;AAAA,QACF;AACA,cAAM,YAAY;AAAA,MACpB,GACA,QAAQ,WAAY;AAGlB,eAAO,MAAM,MAAM,KAAK;AACtB,eAAK;AAAA,QACP;AAAA,MACF,GACA,OAAO,WAAY;AAGjB,gBAAQ,IAAI;AAAA,UACV,KAAK;AACH,iBAAK,GAAG;AACR,iBAAK,GAAG;AACR,iBAAK,GAAG;AACR,iBAAK,GAAG;AACR,mBAAO;AAAA,UACT,KAAK;AACH,iBAAK,GAAG;AACR,iBAAK,GAAG;AACR,iBAAK,GAAG;AACR,iBAAK,GAAG;AACR,iBAAK,GAAG;AACR,mBAAO;AAAA,UACT,KAAK;AACH,iBAAK,GAAG;AACR,iBAAK,GAAG;AACR,iBAAK,GAAG;AACR,iBAAK,GAAG;AACR,mBAAO;AAAA,QACX;AACA,cAAM,iBAAiB,KAAK,GAAG;AAAA,MACjC,GACA,OACA,QAAQ,WAAY;AAGlB,YAAIC,SAAQ,CAAC;AAEb,YAAI,OAAO,KAAK;AACd,eAAK,GAAG;AACR,gBAAM;AACN,cAAI,OAAO,KAAK;AACd,iBAAK,GAAG;AACR,mBAAOA;AAAA,UACT;AACA,iBAAO,IAAI;AACT,YAAAA,OAAM,KAAK,MAAM,CAAC;AAClB,kBAAM;AACN,gBAAI,OAAO,KAAK;AACd,mBAAK,GAAG;AACR,qBAAOA;AAAA,YACT;AACA,iBAAK,GAAG;AACR,kBAAM;AAAA,UACR;AAAA,QACF;AACA,cAAM,WAAW;AAAA,MACnB,GACA,SAAS,WAAY;AAGnB,YAAI,KACFC,UAAS,uBAAO,OAAO,IAAI;AAE7B,YAAI,OAAO,KAAK;AACd,eAAK,GAAG;AACR,gBAAM;AACN,cAAI,OAAO,KAAK;AACd,iBAAK,GAAG;AACR,mBAAOA;AAAA,UACT;AACA,iBAAO,IAAI;AACT,kBAAM,OAAO;AACb,kBAAM;AACN,iBAAK,GAAG;AACR,gBACE,SAAS,WAAW,QACpB,OAAO,eAAe,KAAKA,SAAQ,GAAG,GACtC;AACA,oBAAM,oBAAoB,MAAM,GAAG;AAAA,YACrC;AAEA,gBAAI,eAAe,KAAK,GAAG,MAAM,MAAM;AACrC,kBAAI,SAAS,gBAAgB,SAAS;AACpC,sBAAM,8CAA8C;AAAA,cACtD,WAAW,SAAS,gBAAgB,UAAU;AAC5C,sBAAM;AAAA,cACR,OAAO;AACL,gBAAAA,QAAO,GAAG,IAAI,MAAM;AAAA,cACtB;AAAA,YACF,WAAW,qBAAqB,KAAK,GAAG,MAAM,MAAM;AAClD,kBAAI,SAAS,sBAAsB,SAAS;AAC1C,sBAAM,gDAAgD;AAAA,cACxD,WAAW,SAAS,sBAAsB,UAAU;AAClD,sBAAM;AAAA,cACR,OAAO;AACL,gBAAAA,QAAO,GAAG,IAAI,MAAM;AAAA,cACtB;AAAA,YACF,OAAO;AACL,cAAAA,QAAO,GAAG,IAAI,MAAM;AAAA,YACtB;AAEA,kBAAM;AACN,gBAAI,OAAO,KAAK;AACd,mBAAK,GAAG;AACR,qBAAOA;AAAA,YACT;AACA,iBAAK,GAAG;AACR,kBAAM;AAAA,UACR;AAAA,QACF;AACA,cAAM,YAAY;AAAA,MACpB;AAEF,cAAQ,WAAY;AAIlB,cAAM;AACN,gBAAQ,IAAI;AAAA,UACV,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB,KAAK;AACH,mBAAO,MAAM;AAAA,UACf,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB;AACE,mBAAO,MAAM,OAAO,MAAM,MAAM,OAAO,IAAI,KAAK;AAAA,QACpD;AAAA,MACF;AAKA,aAAO,SAAU,QAAQ,SAAS;AAChC,YAAI;AAEJ,eAAO,SAAS;AAChB,aAAK;AACL,aAAK;AACL,iBAAS,MAAM;AACf,cAAM;AACN,YAAI,IAAI;AACN,gBAAM,cAAc;AAAA,QACtB;AAQA,eAAO,OAAO,YAAY,aACrB,SAAS,KAAK,QAAQ,KAAK;AAC1B,cAAI,GACF,GACAC,SAAQ,OAAO,GAAG;AACpB,cAAIA,UAAS,OAAOA,WAAU,UAAU;AACtC,mBAAO,KAAKA,MAAK,EAAE,QAAQ,SAAUC,IAAG;AACtC,kBAAI,KAAKD,QAAOC,EAAC;AACjB,kBAAI,MAAM,QAAW;AACnB,gBAAAD,OAAMC,EAAC,IAAI;AAAA,cACb,OAAO;AACL,uBAAOD,OAAMC,EAAC;AAAA,cAChB;AAAA,YACF,CAAC;AAAA,UACH;AACA,iBAAO,QAAQ,KAAK,QAAQ,KAAKD,MAAK;AAAA,QACxC,EAAG,EAAE,IAAI,OAAO,GAAG,EAAE,IACrB;AAAA,MACN;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC1bjB;AAAA;AAAA,QAAI,iBAAiB,oBAA8B;AACnD,QAAI,aAAiB;AAErB,WAAO,UAAU,SAAS,SAAS;AAC/B,aAAQ;AAAA,QACJ,OAAO,WAAW,OAAO;AAAA,QACzB,WAAW;AAAA,MACf;AAAA,IACJ;AAEA,WAAO,QAAQ,QAAQ,WAAW;AAClC,WAAO,QAAQ,YAAY;AAAA;AAAA;", "names": ["BigNumber", "compare", "format", "k", "v", "number", "string", "array", "object", "value", "k"]}