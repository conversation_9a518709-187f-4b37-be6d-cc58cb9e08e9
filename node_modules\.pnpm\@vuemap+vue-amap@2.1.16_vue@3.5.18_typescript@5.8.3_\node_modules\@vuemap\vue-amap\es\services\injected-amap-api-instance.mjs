import AMapAPILoader from './amap-api-loader.mjs';
export { resetJsApi } from './amap-api-loader.mjs';

let lazyAMapApiLoaderInstance = null;
const initAMapApiLoader = (config) => {
  if (lazyAMapApiLoaderInstance)
    return;
  if (!lazyAMapApiLoaderInstance) {
    if (config.offline) {
      lazyAMapApiLoaderInstance = new Promise((resolve) => {
        console.log("@vuemap/vue-amap\u79BB\u7EBF\u90E8\u7F72");
        resolve(window.AMap);
      });
    } else {
      lazyAMapApiLoaderInstance = AMapAPILoader(config);
    }
  }
  lazyAMapApiLoaderInstance.then();
};

export { initAMapApiLoader, lazyAMapApiLoaderInstance };
//# sourceMappingURL=injected-amap-api-instance.mjs.map
