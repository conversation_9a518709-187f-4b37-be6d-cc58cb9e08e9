{"version": 3, "file": "injected-amap-api-instance.mjs", "sources": ["../../../services/injected-amap-api-instance.ts"], "sourcesContent": ["import AMapAPILoader from './amap-api-loader';\r\nlet lazyAMapApiLoaderInstance = null as any;\r\n\r\ninterface AMapLoaderOptions {\r\n  key: string, // 申请好的Web端开发者Key，首次调用 load 时必填\r\n  version ?: string, // 指定要加载的 JSAPI 的版本，缺省时默认为 2.0.0\r\n  plugins ?: string[], // 需要使用的的插件列表，如比例尺'AMap.Scale'等\r\n  Loca ?: {\r\n    version ?: string\r\n  },\r\n  AMapUI ?: {\r\n    version?: string; // AMapUI 缺省 1.1\r\n    plugins?: string[]; // 需要加载的 AMapUI ui插件\r\n  };\r\n  serviceHost ?: string\r\n  securityJsCode ?: string\r\n  offline ?: boolean //是否离线部署\r\n}\r\n\r\nexport const initAMapApiLoader = (config : AMapLoaderOptions) => {\r\n  if (lazyAMapApiLoaderInstance) return;\r\n  if (!lazyAMapApiLoaderInstance){\r\n    if(config.offline){\r\n      lazyAMapApiLoaderInstance = new Promise(resolve => {\r\n        console.log('@vuemap/vue-amap离线部署');\r\n        resolve((window as any).AMap);\r\n      });\r\n    }else{\r\n      lazyAMapApiLoaderInstance = AMapAPILoader(config);\r\n    }\r\n  }\r\n  lazyAMapApiLoaderInstance.then();\r\n};\r\nexport {lazyAMapApiLoaderInstance};\r\nexport {resetJsApi} from './amap-api-loader';\r\n"], "names": [], "mappings": ";;;AACA,IAAI,yBAA4B,GAAA,KAAA;AAkBnB,MAAA,iBAAA,GAAoB,CAAC,MAA+B,KAAA;AAC/D,EAAI,IAAA,yBAAA;AAA2B,IAAA,OAAA;AAC/B,EAAA,IAAI,CAAC,yBAA0B,EAAA;AAC7B,IAAA,IAAG,OAAO,OAAQ,EAAA;AAChB,MAA4B,yBAAA,GAAA,IAAI,QAAQ,CAAW,OAAA,KAAA;AACjD,QAAA,OAAA,CAAQ,IAAI,0CAAsB,CAAA,CAAA;AAClC,QAAA,OAAA,CAAS,OAAe,IAAI,CAAA,CAAA;AAAA,OAC7B,CAAA,CAAA;AAAA,KACE,MAAA;AACH,MAAA,yBAAA,GAA4B,cAAc,MAAM,CAAA,CAAA;AAAA,KAClD;AAAA,GACF;AACA,EAAA,yBAAA,CAA0B,IAAK,EAAA,CAAA;AACjC;;;;"}