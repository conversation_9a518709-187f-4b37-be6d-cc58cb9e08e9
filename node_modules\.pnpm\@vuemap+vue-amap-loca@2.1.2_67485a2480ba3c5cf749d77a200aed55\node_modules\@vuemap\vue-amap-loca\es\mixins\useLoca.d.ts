import type { IProvideType } from '@vuemap/vue-amap';
interface IUseWatchFnType {
    setSource: () => void;
    $amapComponent: () => any;
    props: any;
}
export interface IUseLocaTypes {
    $$getInstance: () => any;
    parentInstance: IProvideType;
}
export declare function useWatchFn(options: IUseWatchFnType): {
    __layerStyle(style: any): void;
    __sourceUrl(): void;
    __sourceData(): void;
    __geoBufferSource(): void;
    __visible(flag: boolean): void;
};
export declare function useLocaEvents(options: {
    parentInstance?: IProvideType;
    $amapComponent: any;
    emits: any;
    props: any;
    setStyle: () => void;
}): {
    _destroyComponent: () => void;
    setSource: () => void;
};
export {};
