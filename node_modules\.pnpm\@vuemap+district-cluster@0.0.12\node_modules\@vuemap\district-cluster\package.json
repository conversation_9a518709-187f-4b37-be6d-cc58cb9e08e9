{"name": "@vuemap/district-cluster", "version": "0.0.12", "description": "高德地图amap的区划聚合图层插件", "keywords": ["amap", "高德地图", "区划聚合"], "exports": {".": {"require": "./dist/index-cjs.js", "import": "./dist/index.mjs"}, "./es": "./dist/index.mjs", "./lib": "./dist/index-cjs.js", "./*": "./*"}, "main": "dist/index-cjs.js", "module": "dist/index.mjs", "unpkg": "dist/index.js", "jsdelivr": "dist/index.js", "packageManager": "pnpm@7.9.4", "engines": {"node": ">= 16"}, "scripts": {"build": "npm run clean:dist && cross-env NODE_ENV=production rollup -c rollup.config.js", "dev": "npm run clean:dist && cross-env NODE_ENV=development rollup -c rollup.config.js -w", "clean:dist": "<PERSON><PERSON><PERSON> dist", "lint": "eslint --fix --ext .js,.vue src/", "prettier": "prettier --write src/", "prepublishOnly": "npm run build"}, "browserslist": ["> 1%", "not ie 11", "not op_mini all"], "repository": "https://github.com/yangyanggu/amap-district-cluster", "author": "guyangyang <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/yangyanggu/amap-district-cluster/issues"}, "homepage": "https://github.com/yangyanggu/amap-district-cluster", "dependencies": {"@vuemap/amap-jsapi-types": "^0.0.16", "@turf/helpers": "^6.5.0", "@turf/intersect": "^6.5.0", "topojson-client": "3.1.0"}, "devDependencies": {"@pnpm/logger": "4.0.0", "@pnpm/types": "8.5.0", "@rollup/plugin-commonjs": "^22.0.2", "@rollup/plugin-node-resolve": "^13.3.0", "@rollup/plugin-replace": "4.0.0", "@rollup/plugin-typescript": "^8.3.0", "@types/lodash-es": "4.17.6", "@types/topojson-client": "3.1.1", "@typescript-eslint/eslint-plugin": "5.35.1", "@typescript-eslint/parser": "5.35.1", "cross-env": "^7.0.3", "esbuild": "^0.15.5", "eslint": "8.22.0", "eslint-config-prettier": "8.3.0", "eslint-define-config": "1.6.0", "eslint-plugin-import": "2.25.3", "eslint-plugin-prettier": "^4.2.1", "fast-glob": "3.2.7", "fs-extra": "10.0.0", "gulp": "4.0.2", "lint-staged": "^12.4.0", "magic-string": "^0.25.7", "prettier": "^2.7.1", "rimraf": "3.0.2", "rollup": "^2.78.1", "rollup-plugin-esbuild": "4.9.3", "rollup-plugin-filesize": "9.1.2", "rollup-plugin-livereload": "^2.0.5", "rollup-plugin-serve": "^2.0.1", "typescript": "^4.7.4"}, "types": "dist/index.d.ts", "files": ["dist", "src"]}