/// <reference types="@vuemap/amap-jsapi-types" />
import Ellipse from './Ellipse.vue';
import type { Plugin } from "vue";
export declare const ElAmapEllipse: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        center: {
            type: ArrayConstructor;
            required: true;
        };
        radius: {
            type: ArrayConstructor;
            required: true;
        };
        bubble: {
            type: BooleanConstructor;
            default: boolean;
        };
        cursor: {
            type: StringConstructor;
        };
        strokeColor: {
            type: StringConstructor;
        };
        strokeOpacity: {
            type: NumberConstructor;
        };
        strokeWeight: {
            type: NumberConstructor;
        };
        fillColor: {
            type: StringConstructor;
        };
        fillOpacity: {
            type: NumberConstructor;
        };
        draggable: {
            type: BooleanConstructor;
            default: boolean;
        };
        extData: {
            type: ObjectConstructor;
            default: () => null;
        };
        strokeStyle: {
            type: import("vue").PropType<"solid" | "dashed">;
            validator: (value: string) => boolean;
        };
        strokeDasharray: {
            type: ArrayConstructor;
        };
        editable: {
            type: BooleanConstructor;
            default: boolean;
        };
        editOptions: {
            type: ObjectConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
        "onUpdate:center"?: ((...args: any[]) => any) | undefined;
        "onUpdate:radius"?: ((...args: any[]) => any) | undefined;
    }, {
        props: import("@vue/shared").LooseRequired<{
            readonly center: unknown[];
            readonly radius: unknown[];
            readonly draggable: boolean;
            readonly bubble: boolean;
            readonly extData: Record<string, any>;
            readonly editable: boolean;
            readonly visible?: boolean | undefined;
            readonly zIndex?: number | undefined;
            readonly reEventWhenUpdate?: boolean | undefined;
            readonly extraOptions?: any;
            readonly cursor?: string | undefined;
            readonly strokeColor?: string | undefined;
            readonly strokeOpacity?: number | undefined;
            readonly strokeWeight?: number | undefined;
            readonly strokeStyle?: "solid" | "dashed" | undefined;
            readonly strokeDasharray?: unknown[] | undefined;
            readonly editOptions?: Record<string, any> | undefined;
            readonly fillColor?: string | undefined;
            readonly fillOpacity?: number | undefined;
            readonly onInit?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:center"?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:radius"?: ((...args: any[]) => any) | undefined;
        } & {}>;
        emits: (event: "init" | "update:center" | "update:radius", ...args: any[]) => void;
        $amapComponent: AMap.Ellipse;
        destroying: boolean;
        $$getInstance: () => AMap.Ellipse;
        parentInstance: import("../../..").IProvideType | undefined;
        resetEditor: import("lodash").DebouncedFunc<() => void>;
        bindModelEvents: () => void;
        emitModel: (target: AMap.Ellipse) => void;
        editor: AMap.EllipseEditor;
        attrs: {
            [x: string]: unknown;
        };
        createEditor: () => Promise<void>;
        bindEditorModelEvents: () => void;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("init" | "update:center" | "update:radius")[], import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        center: {
            type: ArrayConstructor;
            required: true;
        };
        radius: {
            type: ArrayConstructor;
            required: true;
        };
        bubble: {
            type: BooleanConstructor;
            default: boolean;
        };
        cursor: {
            type: StringConstructor;
        };
        strokeColor: {
            type: StringConstructor;
        };
        strokeOpacity: {
            type: NumberConstructor;
        };
        strokeWeight: {
            type: NumberConstructor;
        };
        fillColor: {
            type: StringConstructor;
        };
        fillOpacity: {
            type: NumberConstructor;
        };
        draggable: {
            type: BooleanConstructor;
            default: boolean;
        };
        extData: {
            type: ObjectConstructor;
            default: () => null;
        };
        strokeStyle: {
            type: import("vue").PropType<"solid" | "dashed">;
            validator: (value: string) => boolean;
        };
        strokeDasharray: {
            type: ArrayConstructor;
        };
        editable: {
            type: BooleanConstructor;
            default: boolean;
        };
        editOptions: {
            type: ObjectConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
        "onUpdate:center"?: ((...args: any[]) => any) | undefined;
        "onUpdate:radius"?: ((...args: any[]) => any) | undefined;
    }, {
        draggable: boolean;
        bubble: boolean;
        extData: Record<string, any>;
        editable: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        center: {
            type: ArrayConstructor;
            required: true;
        };
        radius: {
            type: ArrayConstructor;
            required: true;
        };
        bubble: {
            type: BooleanConstructor;
            default: boolean;
        };
        cursor: {
            type: StringConstructor;
        };
        strokeColor: {
            type: StringConstructor;
        };
        strokeOpacity: {
            type: NumberConstructor;
        };
        strokeWeight: {
            type: NumberConstructor;
        };
        fillColor: {
            type: StringConstructor;
        };
        fillOpacity: {
            type: NumberConstructor;
        };
        draggable: {
            type: BooleanConstructor;
            default: boolean;
        };
        extData: {
            type: ObjectConstructor;
            default: () => null;
        };
        strokeStyle: {
            type: import("vue").PropType<"solid" | "dashed">;
            validator: (value: string) => boolean;
        };
        strokeDasharray: {
            type: ArrayConstructor;
        };
        editable: {
            type: BooleanConstructor;
            default: boolean;
        };
        editOptions: {
            type: ObjectConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
        "onUpdate:center"?: ((...args: any[]) => any) | undefined;
        "onUpdate:radius"?: ((...args: any[]) => any) | undefined;
    }, {
        props: import("@vue/shared").LooseRequired<{
            readonly center: unknown[];
            readonly radius: unknown[];
            readonly draggable: boolean;
            readonly bubble: boolean;
            readonly extData: Record<string, any>;
            readonly editable: boolean;
            readonly visible?: boolean | undefined;
            readonly zIndex?: number | undefined;
            readonly reEventWhenUpdate?: boolean | undefined;
            readonly extraOptions?: any;
            readonly cursor?: string | undefined;
            readonly strokeColor?: string | undefined;
            readonly strokeOpacity?: number | undefined;
            readonly strokeWeight?: number | undefined;
            readonly strokeStyle?: "solid" | "dashed" | undefined;
            readonly strokeDasharray?: unknown[] | undefined;
            readonly editOptions?: Record<string, any> | undefined;
            readonly fillColor?: string | undefined;
            readonly fillOpacity?: number | undefined;
            readonly onInit?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:center"?: ((...args: any[]) => any) | undefined;
            readonly "onUpdate:radius"?: ((...args: any[]) => any) | undefined;
        } & {}>;
        emits: (event: "init" | "update:center" | "update:radius", ...args: any[]) => void;
        $amapComponent: AMap.Ellipse;
        destroying: boolean;
        $$getInstance: () => AMap.Ellipse;
        parentInstance: import("../../..").IProvideType | undefined;
        resetEditor: import("lodash").DebouncedFunc<() => void>;
        bindModelEvents: () => void;
        emitModel: (target: AMap.Ellipse) => void;
        editor: AMap.EllipseEditor;
        attrs: {
            [x: string]: unknown;
        };
        createEditor: () => Promise<void>;
        bindEditorModelEvents: () => void;
    }, {}, {}, {}, {
        draggable: boolean;
        bubble: boolean;
        extData: Record<string, any>;
        editable: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    center: {
        type: ArrayConstructor;
        required: true;
    };
    radius: {
        type: ArrayConstructor;
        required: true;
    };
    bubble: {
        type: BooleanConstructor;
        default: boolean;
    };
    cursor: {
        type: StringConstructor;
    };
    strokeColor: {
        type: StringConstructor;
    };
    strokeOpacity: {
        type: NumberConstructor;
    };
    strokeWeight: {
        type: NumberConstructor;
    };
    fillColor: {
        type: StringConstructor;
    };
    fillOpacity: {
        type: NumberConstructor;
    };
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    extData: {
        type: ObjectConstructor;
        default: () => null;
    };
    strokeStyle: {
        type: import("vue").PropType<"solid" | "dashed">;
        validator: (value: string) => boolean;
    };
    strokeDasharray: {
        type: ArrayConstructor;
    };
    editable: {
        type: BooleanConstructor;
        default: boolean;
    };
    editOptions: {
        type: ObjectConstructor;
    };
} & {
    visible: import("../../..").IPropOptions<boolean>;
    zIndex: import("../../..").IPropOptions<number>;
    reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
    extraOptions: import("../../..").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
    "onUpdate:center"?: ((...args: any[]) => any) | undefined;
    "onUpdate:radius"?: ((...args: any[]) => any) | undefined;
}, {
    props: import("@vue/shared").LooseRequired<{
        readonly center: unknown[];
        readonly radius: unknown[];
        readonly draggable: boolean;
        readonly bubble: boolean;
        readonly extData: Record<string, any>;
        readonly editable: boolean;
        readonly visible?: boolean | undefined;
        readonly zIndex?: number | undefined;
        readonly reEventWhenUpdate?: boolean | undefined;
        readonly extraOptions?: any;
        readonly cursor?: string | undefined;
        readonly strokeColor?: string | undefined;
        readonly strokeOpacity?: number | undefined;
        readonly strokeWeight?: number | undefined;
        readonly strokeStyle?: "solid" | "dashed" | undefined;
        readonly strokeDasharray?: unknown[] | undefined;
        readonly editOptions?: Record<string, any> | undefined;
        readonly fillColor?: string | undefined;
        readonly fillOpacity?: number | undefined;
        readonly onInit?: ((...args: any[]) => any) | undefined;
        readonly "onUpdate:center"?: ((...args: any[]) => any) | undefined;
        readonly "onUpdate:radius"?: ((...args: any[]) => any) | undefined;
    } & {}>;
    emits: (event: "init" | "update:center" | "update:radius", ...args: any[]) => void;
    $amapComponent: AMap.Ellipse;
    destroying: boolean;
    $$getInstance: () => AMap.Ellipse;
    parentInstance: import("../../..").IProvideType | undefined;
    resetEditor: import("lodash").DebouncedFunc<() => void>;
    bindModelEvents: () => void;
    emitModel: (target: AMap.Ellipse) => void;
    editor: AMap.EllipseEditor;
    attrs: {
        [x: string]: unknown;
    };
    createEditor: () => Promise<void>;
    bindEditorModelEvents: () => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("init" | "update:center" | "update:radius")[], "init" | "update:center" | "update:radius", {
    draggable: boolean;
    bubble: boolean;
    extData: Record<string, any>;
    editable: boolean;
}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Plugin<any[]>;
export default ElAmapEllipse;
export declare type ElAmapEllipseInstance = InstanceType<typeof Ellipse>;
