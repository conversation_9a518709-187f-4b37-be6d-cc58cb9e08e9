'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('./Geolocation.vue.js');
var Geolocation_vue_vue_type_script_setup_true_lang = require('./Geolocation.vue2.js');

Geolocation_vue_vue_type_script_setup_true_lang.default.install = (app) => {
  app.component(Geolocation_vue_vue_type_script_setup_true_lang.default.name, Geolocation_vue_vue_type_script_setup_true_lang.default);
  return app;
};
const ElAmapControlGeolocation = Geolocation_vue_vue_type_script_setup_true_lang.default;

exports.ElAmapControlGeolocation = ElAmapControlGeolocation;
exports.default = ElAmapControlGeolocation;
//# sourceMappingURL=index.js.map
