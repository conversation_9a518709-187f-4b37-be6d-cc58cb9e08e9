{"version": 3, "file": "color-picker2.mjs", "sources": ["../../../../../../packages/components/color-picker/src/color-picker.vue"], "sourcesContent": ["<template>\n  <el-tooltip\n    ref=\"popper\"\n    :visible=\"showPicker\"\n    :show-arrow=\"false\"\n    :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n    :offset=\"0\"\n    :gpu-acceleration=\"false\"\n    :popper-class=\"[ns.be('picker', 'panel'), ns.b('dropdown'), popperClass]\"\n    :stop-popper-mouse-event=\"false\"\n    effect=\"light\"\n    trigger=\"click\"\n    :transition=\"`${ns.namespace.value}-zoom-in-top`\"\n    persistent\n    @hide=\"setShowPicker(false)\"\n  >\n    <template #content>\n      <div v-click-outside=\"handleClickOutside\" @keydown.esc=\"handleEsc\">\n        <div :class=\"ns.be('dropdown', 'main-wrapper')\">\n          <hue-slider ref=\"hue\" class=\"hue-slider\" :color=\"color\" vertical />\n          <sv-panel ref=\"sv\" :color=\"color\" />\n        </div>\n        <alpha-slider v-if=\"showAlpha\" ref=\"alpha\" :color=\"color\" />\n        <predefine\n          v-if=\"predefine\"\n          ref=\"predefine\"\n          :color=\"color\"\n          :colors=\"predefine\"\n        />\n        <div :class=\"ns.be('dropdown', 'btns')\">\n          <span :class=\"ns.be('dropdown', 'value')\">\n            <el-input\n              ref=\"inputRef\"\n              v-model=\"customInput\"\n              :validate-event=\"false\"\n              size=\"small\"\n              @keyup.enter=\"handleConfirm\"\n              @blur=\"handleConfirm\"\n            />\n          </span>\n          <el-button\n            :class=\"ns.be('dropdown', 'link-btn')\"\n            text\n            size=\"small\"\n            @click=\"clear\"\n          >\n            {{ t('el.colorpicker.clear') }}\n          </el-button>\n          <el-button\n            plain\n            size=\"small\"\n            :class=\"ns.be('dropdown', 'btn')\"\n            @click=\"confirmValue\"\n          >\n            {{ t('el.colorpicker.confirm') }}\n          </el-button>\n        </div>\n      </div>\n    </template>\n    <template #default>\n      <div\n        :id=\"buttonId\"\n        ref=\"triggerRef\"\n        :class=\"btnKls\"\n        role=\"button\"\n        :aria-label=\"buttonAriaLabel\"\n        :aria-labelledby=\"buttonAriaLabelledby\"\n        :aria-description=\"\n          t('el.colorpicker.description', { color: modelValue || '' })\n        \"\n        :aria-disabled=\"colorDisabled\"\n        :tabindex=\"colorDisabled ? -1 : tabindex\"\n        @keydown=\"handleKeyDown\"\n        @focus=\"handleFocus\"\n        @blur=\"handleBlur\"\n      >\n        <div v-if=\"colorDisabled\" :class=\"ns.be('picker', 'mask')\" />\n        <div :class=\"ns.be('picker', 'trigger')\" @click=\"handleTrigger\">\n          <span :class=\"[ns.be('picker', 'color'), ns.is('alpha', showAlpha)]\">\n            <span\n              :class=\"ns.be('picker', 'color-inner')\"\n              :style=\"{\n                backgroundColor: displayedColor,\n              }\"\n            >\n              <el-icon\n                v-show=\"modelValue || showPanelColor\"\n                :class=\"[ns.be('picker', 'icon'), ns.is('icon-arrow-down')]\"\n              >\n                <arrow-down />\n              </el-icon>\n              <el-icon\n                v-show=\"!modelValue && !showPanelColor\"\n                :class=\"[ns.be('picker', 'empty'), ns.is('icon-close')]\"\n              >\n                <close />\n              </el-icon>\n            </span>\n          </span>\n        </div>\n      </div>\n    </template>\n  </el-tooltip>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { debounce } from 'lodash-unified'\nimport { ElButton } from '@element-plus/components/button'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ClickOutside as vClickOutside } from '@element-plus/directives'\nimport { ElTooltip } from '@element-plus/components/tooltip'\nimport { ElInput } from '@element-plus/components/input'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport {\n  useFocusController,\n  useLocale,\n  useNamespace,\n} from '@element-plus/hooks'\nimport { EVENT_CODE, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { debugWarn } from '@element-plus/utils'\nimport { ArrowDown, Close } from '@element-plus/icons-vue'\nimport AlphaSlider from './components/alpha-slider.vue'\nimport HueSlider from './components/hue-slider.vue'\nimport Predefine from './components/predefine.vue'\nimport SvPanel from './components/sv-panel.vue'\nimport Color from './utils/color'\nimport {\n  colorPickerContextKey,\n  colorPickerEmits,\n  colorPickerProps,\n} from './color-picker'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\ndefineOptions({\n  name: 'ElColorPicker',\n})\nconst props = defineProps(colorPickerProps)\nconst emit = defineEmits(colorPickerEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('color')\nconst { formItem } = useFormItem()\nconst colorSize = useFormSize()\nconst colorDisabled = useFormDisabled()\n\nconst { inputId: buttonId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext: formItem,\n})\n\nconst hue = ref<InstanceType<typeof HueSlider>>()\nconst sv = ref<InstanceType<typeof SvPanel>>()\nconst alpha = ref<InstanceType<typeof AlphaSlider>>()\nconst popper = ref<TooltipInstance>()\nconst triggerRef = ref()\nconst inputRef = ref()\n\nconst {\n  isFocused,\n  handleFocus: _handleFocus,\n  handleBlur,\n} = useFocusController(triggerRef, {\n  beforeBlur(event) {\n    return popper.value?.isFocusInsideContent(event)\n  },\n  afterBlur() {\n    setShowPicker(false)\n    resetColor()\n  },\n})\n\nconst handleFocus = (event: FocusEvent) => {\n  if (colorDisabled.value) return blur()\n  _handleFocus(event)\n}\n\n// active-change is used to prevent modelValue changes from triggering.\nlet shouldActiveChange = true\n\nconst color = reactive(\n  new Color({\n    enableAlpha: props.showAlpha,\n    format: props.colorFormat || '',\n    value: props.modelValue,\n  })\n) as Color\n\nconst showPicker = ref(false)\nconst showPanelColor = ref(false)\nconst customInput = ref('')\n\nconst displayedColor = computed(() => {\n  if (!props.modelValue && !showPanelColor.value) {\n    return 'transparent'\n  }\n  return displayedRgb(color, props.showAlpha)\n})\n\nconst currentColor = computed(() => {\n  return !props.modelValue && !showPanelColor.value ? '' : color.value\n})\n\nconst buttonAriaLabel = computed<string | undefined>(() => {\n  return !isLabeledByFormItem.value\n    ? props.label || t('el.colorpicker.defaultLabel')\n    : undefined\n})\n\nconst buttonAriaLabelledby = computed<string | undefined>(() => {\n  return isLabeledByFormItem.value ? formItem?.labelId : undefined\n})\n\nconst btnKls = computed(() => {\n  return [\n    ns.b('picker'),\n    ns.is('disabled', colorDisabled.value),\n    ns.bm('picker', colorSize.value),\n    ns.is('focused', isFocused.value),\n  ]\n})\n\nfunction displayedRgb(color: Color, showAlpha: boolean) {\n  if (!(color instanceof Color)) {\n    throw new TypeError('color should be instance of _color Class')\n  }\n\n  const { r, g, b } = color.toRgb()\n  return showAlpha\n    ? `rgba(${r}, ${g}, ${b}, ${color.get('alpha') / 100})`\n    : `rgb(${r}, ${g}, ${b})`\n}\n\nfunction setShowPicker(value: boolean) {\n  showPicker.value = value\n}\n\nconst debounceSetShowPicker = debounce(setShowPicker, 100, { leading: true })\n\nfunction show() {\n  if (colorDisabled.value) return\n  setShowPicker(true)\n}\n\nfunction hide() {\n  debounceSetShowPicker(false)\n  resetColor()\n}\n\nfunction resetColor() {\n  nextTick(() => {\n    if (props.modelValue) {\n      color.fromString(props.modelValue)\n    } else {\n      color.value = ''\n      nextTick(() => {\n        showPanelColor.value = false\n      })\n    }\n  })\n}\n\nfunction handleTrigger() {\n  if (colorDisabled.value) return\n  debounceSetShowPicker(!showPicker.value)\n}\n\nfunction handleConfirm() {\n  color.fromString(customInput.value)\n}\n\nfunction confirmValue() {\n  const value = color.value\n  emit(UPDATE_MODEL_EVENT, value)\n  emit('change', value)\n  if (props.validateEvent) {\n    formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n  debounceSetShowPicker(false)\n  // check if modelValue change, if not change, then reset color.\n  nextTick(() => {\n    const newColor = new Color({\n      enableAlpha: props.showAlpha,\n      format: props.colorFormat || '',\n      value: props.modelValue,\n    })\n    if (!color.compare(newColor)) {\n      resetColor()\n    }\n  })\n}\n\nfunction clear() {\n  debounceSetShowPicker(false)\n  emit(UPDATE_MODEL_EVENT, null)\n  emit('change', null)\n  if (props.modelValue !== null && props.validateEvent) {\n    formItem?.validate('change').catch((err) => debugWarn(err))\n  }\n  resetColor()\n}\n\nfunction handleClickOutside(event: Event) {\n  if (!showPicker.value) return\n  hide()\n\n  if (isFocused.value) {\n    const _event = new FocusEvent('focus', event)\n    handleBlur(_event)\n  }\n}\n\nfunction handleEsc(event: KeyboardEvent) {\n  event.preventDefault()\n  event.stopPropagation()\n  setShowPicker(false)\n  resetColor()\n}\n\nfunction handleKeyDown(event: KeyboardEvent) {\n  switch (event.code) {\n    case EVENT_CODE.enter:\n    case EVENT_CODE.space:\n      event.preventDefault()\n      event.stopPropagation()\n      show()\n      inputRef.value.focus()\n      break\n    case EVENT_CODE.esc:\n      handleEsc(event)\n      break\n  }\n}\n\nfunction focus() {\n  triggerRef.value.focus()\n}\n\nfunction blur() {\n  triggerRef.value.blur()\n}\n\nonMounted(() => {\n  if (props.modelValue) {\n    customInput.value = currentColor.value\n  }\n})\n\nwatch(\n  () => props.modelValue,\n  (newVal) => {\n    if (!newVal) {\n      showPanelColor.value = false\n    } else if (newVal && newVal !== color.value) {\n      shouldActiveChange = false\n      color.fromString(newVal)\n    }\n  }\n)\n\nwatch(\n  () => currentColor.value,\n  (val) => {\n    customInput.value = val\n    shouldActiveChange && emit('activeChange', val)\n    shouldActiveChange = true\n  }\n)\n\nwatch(\n  () => color.value,\n  () => {\n    if (!props.modelValue && !showPanelColor.value) {\n      showPanelColor.value = true\n    }\n  }\n)\n\nwatch(\n  () => showPicker.value,\n  () => {\n    nextTick(() => {\n      hue.value?.update()\n      sv.value?.update()\n      alpha.value?.update()\n    })\n  }\n)\n\nprovide(colorPickerContextKey, {\n  currentColor,\n})\n\ndefineExpose({\n  /**\n   * @description current color object\n   */\n  color,\n  /**\n   * @description manually show ColorPicker\n   */\n  show,\n  /**\n   * @description manually hide ColorPicker\n   */\n  hide,\n  /**\n   * @description focus the input element\n   */\n  focus,\n  /**\n   * @description blur the input element\n   */\n  blur,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAmJc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAM,MAAA,EAAE,MAAM,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA,CAAA;AAC/B,IAAM,MAAA,EAAE,aAAa,WAAY,EAAA,CAAA;AACjC,IAAA,MAAM,YAAY,WAAY,EAAA,CAAA;AAC9B,IAAA,MAAM,gBAAgB,eAAgB,EAAA,CAAA;AAEtC,IAAA,MAAM,EAAE,OAAA,EAAS,QAAU,EAAA,mBAAA,EAAA,GAAwB,mBAAmB,KAAO,EAAA;AAAA,MAC3E,eAAiB,EAAA,QAAA;AAAA,KAClB,CAAA,CAAA;AAED,IAAA,MAAM,MAAM,GAAoC,EAAA,CAAA;AAChD,IAAA,MAAM,KAAK,GAAkC,EAAA,CAAA;AAC7C,IAAA,MAAM,QAAQ,GAAsC,EAAA,CAAA;AACpD,IAAA,MAAM,SAAS,GAAqB,EAAA,CAAA;AACpC,IAAA,MAAM,aAAa,GAAI,EAAA,CAAA;AACvB,IAAA,MAAM,WAAW,GAAI,EAAA,CAAA;AAErB,IAAM,MAAA;AAAA,MACJ,SAAA;AAAA,MACA,WAAa,EAAA,YAAA;AAAA,MACb,UAAA;AAAA,KAAA,GACE,mBAAmB,UAAY,EAAA;AAAA,MACjC,WAAW,KAAO,EAAA;AAChB,QAAO,IAAA,EAAA,CAAA;AAAwC,QACjD,OAAA,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,oBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACY;AACV,MAAA,SAAA,GAAA;AACA,QAAW,aAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACb,UAAA,EAAA,CAAA;AAAA,OACD;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAI,WAAc,GAAA,CAAA,KAAA,KAAA;AAAO,MAAA,IAAA,aAAY,CAAA,KAAA;AACrC,QAAA,OAAA,IAAa,EAAK,CAAA;AAAA,MACpB,YAAA,CAAA,KAAA,CAAA,CAAA;AAGA,KAAA,CAAA;AAEA,IAAM,IAAA,kBACJ,GAAA,IAAU,CAAA;AAAA,IAAA,sBACW,CAAA,IAAA,KAAA,CAAA;AAAA,MACnB,kBAA6B,CAAA,SAAA;AAAA,MAC7B,QAAa,KAAA,CAAA,WAAA,IAAA,EAAA;AAAA,MAEjB,KAAA,EAAA,KAAA,CAAA,UAAA;AAEA,KAAM,CAAA,CAAA,CAAA;AACN,IAAM,MAAA,UAAA,GAAA,GAAA,CAAiB,KAAS,CAAA,CAAA;AAChC,IAAM,MAAA,cAAc,MAAM,CAAA,KAAA,CAAA,CAAA;AAE1B,IAAM,MAAA,WAAA,GAAA,GAAiB;AACrB,IAAA,MAAI,cAAqB,GAAA;AACvB,MAAO,IAAA,CAAA,KAAA,CAAA,UAAA,IAAA,CAAA,cAAA,CAAA,KAAA,EAAA;AAAA,QACT,OAAA,aAAA,CAAA;AACA,OAAO;AAAmC,MAC3C,OAAA,YAAA,CAAA,KAAA,EAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,YAAc,GAAA,QAAA,CAAc,MAAgB;AAAmB,MAChE,OAAA,CAAA,KAAA,CAAA,UAAA,IAAA,CAAA,cAAA,CAAA,KAAA,GAAA,EAAA,GAAA,KAAA,CAAA,KAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAA,MAAA,eAA4B,GAAA,QAAA,CAAA;AAExB,MACL,OAAA,CAAA,mBAAA,CAAA,KAAA,GAAA,KAAA,CAAA,KAAA,IAAA,CAAA,CAAA,6BAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,oBAAA,GAAoB,QAAQ,CAAA,MAAA;AAAoB,MACxD,OAAA,mBAAA,CAAA,KAAA,GAAA,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,OAAA,GAAA,KAAA,CAAA,CAAA;AAED,KAAM,CAAA,CAAA;AACJ,IAAO,MAAA,MAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACL,OAAK;AAAQ,QACb,EAAG,CAAA,CAAA,CAAA,QAAe,CAAA;AAAmB,QACrC,EAAG,CAAA,EAAA,CAAG,UAAU,EAAA,aAAe,CAAA,KAAA,CAAA;AAAA,QAC/B,EAAG,CAAA,EAAA,CAAG,QAAW,EAAA,SAAA,CAAA,KAAe,CAAA;AAAA,QAClC,EAAA,CAAA,EAAA,CAAA,SAAA,EAAA,SAAA,CAAA,KAAA,CAAA;AAAA,OACD,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAI,4BAA2B,EAAA,SAAA,EAAA;AAC7B,MAAM,IAAA,EAAA,MAAI,YAAoD,KAAA,CAAA,EAAA;AAAA,QAChE,MAAA,IAAA,SAAA,CAAA,0CAAA,CAAA,CAAA;AAEA,OAAA;AACA,MAAA,MAAA,EACI,CAAA,EAAA,CAAA,EAAA,CAAA,EAAA,GAAA,MAAA,CAAQ,KAAM,EAAA,CAAA;AACK,MACzB,OAAA,SAAA,GAAA,CAAA,KAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,MAAA,CAAA,GAAA,CAAA,OAAA,CAAA,GAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAEA,KAAA;AACE,IAAA,SAAA,aAAmB,CAAA,KAAA,EAAA;AAAA,MACrB,UAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAA;AAEA,IAAgB,MAAA,qBAAA,GAAA,QAAA,CAAA,aAAA,EAAA,GAAA,EAAA,EAAA,OAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACd,IAAA,SAAkB,IAAA,GAAA;AAAO,MAAA,IAAA,aAAA,CAAA,KAAA;AACzB,QAAA,OAAA;AAAkB,MACpB,aAAA,CAAA,IAAA,CAAA,CAAA;AAEA,KAAgB;AACd,IAAA,SAAA,IAAA,GAAA;AACA,MAAW,qBAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACb,UAAA,EAAA,CAAA;AAEA,KAAsB;AACpB,IAAA,SAAA,UAAe,GAAA;AACb,MAAA,eAAsB;AACpB,QAAM,IAAA,KAAA,CAAA;AAA2B,UAC5B,KAAA,CAAA,UAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AACL,SAAA,MAAA;AACA,UAAA,KAAA,CAAA,KAAe,GAAA,EAAA,CAAA;AACb,UAAA,QAAA,CAAA,MAAA;AAAuB,YACxB,cAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAAA,WACH,CAAA,CAAA;AAAA,SACD;AAAA,OACH,CAAA,CAAA;AAEA,KAAyB;AACvB,IAAA,SAAkB,aAAA,GAAA;AAAO,MAAA,IAAA,aAAA,CAAA,KAAA;AACzB,QAAsB,OAAA;AAAiB,MACzC,qBAAA,CAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAyB;AACvB,IAAM,SAAA;AAA4B,MACpC,KAAA,CAAA,UAAA,CAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AAEA,KAAwB;AACtB,IAAA,qBAAoB,GAAA;AACpB,MAAA,yBAAyB,CAAK;AAC9B,MAAA,IAAA,CAAK,kBAAe,EAAA,KAAA,CAAA,CAAA;AACpB,MAAA,IAAI,SAAqB,EAAA,KAAA,CAAA,CAAA;AACvB,MAAU,IAAA,KAAA,CAAA;AAAgD,QAC5D,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,OAAA;AAEA,MAAA,qBAAe,CAAA,KAAA,CAAA,CAAA;AACb,MAAM,QAAA,CAAA,MAAA;AAAqB,QAAA,iBACN,IAAA,KAAA,CAAA;AAAA,UACnB,kBAA6B,CAAA,SAAA;AAAA,UAC7B,QAAa,KAAA,CAAA,WAAA,IAAA,EAAA;AAAA,UACd,KAAA,EAAA,KAAA,CAAA,UAAA;AACD,SAAA,CAAA,CAAA;AACE,QAAW,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,QAAA,CAAA,EAAA;AAAA,UACb,UAAA,EAAA,CAAA;AAAA,SACD;AAAA,OACH,CAAA,CAAA;AAEA,KAAiB;AACf,IAAA,SAAA,KAAA,GAAA;AACA,MAAA,2BAA6B,CAAA,CAAA;AAC7B,MAAA,IAAA,CAAK,kBAAc,EAAA,IAAA,CAAA,CAAA;AACnB,MAAA,IAAI,CAAM,QAAA,EAAA,IAAA,CAAA,CAAA;AACR,MAAU,IAAA,KAAA,CAAA,UAAA,SAAiB,IAAE,KAAO,CAAQ,aAAA,EAAA;AAAc,QAC5D,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,OAAW;AAAA,MACb,UAAA,EAAA,CAAA;AAEA,KAAA;AACE,IAAA,SAAgB,kBAAA,CAAA,KAAA,EAAA;AAAO,MAAA,IAAA,CAAA,UAAA,CAAA,KAAA;AACvB,QAAK,OAAA;AAEL,MAAA,IAAI;AACF,MAAA,IAAA,SAAe,CAAA,KAAA,EAAI;AACnB,QAAA,MAAA,MAAiB,GAAA,IAAA,UAAA,CAAA,OAAA,EAAA,KAAA,CAAA,CAAA;AAAA,QACnB,UAAA,CAAA,MAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAA;AACE,IAAA,SAAqB,SAAA,CAAA,KAAA,EAAA;AACrB,MAAA,KAAA,CAAM,cAAgB,EAAA,CAAA;AACtB,MAAA,KAAA,CAAA,eAAmB,EAAA,CAAA;AACnB,MAAW,aAAA,CAAA,KAAA,CAAA,CAAA;AAAA,MACb,UAAA,EAAA,CAAA;AAEA,KAAA;AACE,IAAA,SAAA,aAAc,CAAA,KAAA,EAAA;AAAA,MAAA,QACI,KAAA,CAAA,IAAA;AAAA,QAAA,KACX,UAAW,CAAA,KAAA,CAAA;AACd,QAAA,KAAA,UAAqB,CAAA,KAAA;AACrB,UAAA,KAAA,CAAM,cAAgB,EAAA,CAAA;AACtB,UAAK,KAAA,CAAA,eAAA,EAAA,CAAA;AACL,UAAA,IAAA,EAAA,CAAA;AACA,UAAA,QAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA;AAAA,UAAA,MACc;AACd,QAAA,KAAA,UAAe,CAAA,GAAA;AACf,UAAA,SAAA,CAAA,KAAA,CAAA,CAAA;AAAA,UAAA,MAAA;AAAA,OAEN;AAEA,KAAiB;AACf,IAAA,SAAA;AAAuB,MACzB,UAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA;AAEA,KAAgB;AACd,IAAA,SAAA,IAAW;AAAW,MACxB,UAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA;AAEA,KAAA;AACE,IAAA,gBAAsB;AACpB,MAAA,IAAA,KAAA,CAAA,YAAoB;AAAa,QACnC,WAAA,CAAA,KAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AAAA,OACD;AAED,KAAA,CAAA,CAAA;AAGI,IAAA,KAAA,CAAI,MAAS,KAAA,CAAA,UAAA,EAAA,CAAA,MAAA,KAAA;AACX,MAAA,IAAA,CAAA,MAAA,EAAA;AAAuB,QACd,cAAA,CAAA,KAAqB,GAAA,KAAA,CAAA;AAC9B,OAAqB,MAAA,IAAA,MAAA,IAAA,MAAA,KAAA,KAAA,CAAA,KAAA,EAAA;AACrB,QAAA,kBAAuB,GAAA,KAAA,CAAA;AAAA,QACzB,KAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA;AAAA,OAEJ;AAEA,KAAA,CAAA,CAAA;AAGI,IAAA,KAAA,CAAA,MAAA,YAAoB,CAAA,KAAA,EAAA,CAAA,GAAA,KAAA;AACpB,MAAsB,WAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AACtB,MAAqB,kBAAA,IAAA,IAAA,CAAA,cAAA,EAAA,GAAA,CAAA,CAAA;AAAA,MAEzB,kBAAA,GAAA,IAAA,CAAA;AAEA,KACE,CAAA,CAAA;AAEE,IAAA,KAAA,CAAI,MAAC,KAAoB,CAAA,KAAA,EAAA;AACvB,MAAA,IAAA,CAAA,KAAA,CAAA,UAAuB,IAAA,CAAA,cAAA,CAAA,KAAA,EAAA;AAAA,QACzB,cAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAAA,OAEJ;AAEA,KACE,CAAA,CAAA;AAEE,IAAA,KAAA,CAAA,MAAe,UAAA,CAAA,KAAA,EAAA,MAAA;AACb,MAAA,eAAkB;AAClB,QAAA,UAAU,EAAO,EAAA,CAAA;AACjB,QAAA,CAAA,EAAA,GAAM,SAAc,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AAAA,QACrB,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AAAA,QAEL,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AAEA,OAAA,CAAA,CAAA;AAA+B,KAC7B,CAAA,CAAA;AAAA,IACF,OAAC,CAAA,qBAAA,EAAA;AAED,MAAa,YAAA;AAAA,KAIX,CAAA,CAAA;AAAA,IAIA,MAAA,CAAA;AAAA,MAIA,KAAA;AAAA,MAIA,IAAA;AAAA,MAIA,IAAA;AAAA,MACD,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}