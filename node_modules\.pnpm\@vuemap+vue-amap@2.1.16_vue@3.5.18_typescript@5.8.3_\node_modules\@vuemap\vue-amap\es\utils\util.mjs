function isMapInstance(instance) {
  if (!instance) {
    return false;
  }
  return instance instanceof AMap.Map;
}
function isOverlayGroupInstance(instance) {
  if (!instance) {
    return false;
  }
  return instance instanceof AMap.OverlayGroup;
}
function isIndoorMapInstance(instance) {
  if (!instance) {
    return false;
  }
  return instance instanceof AMap.IndoorMap;
}
function isLabelsLayerInstance(instance) {
  if (!instance) {
    return false;
  }
  return instance instanceof AMap.LabelsLayer;
}
function isVectorLayerInstance(instance) {
  if (!instance) {
    return false;
  }
  return instance instanceof AMap.VectorLayer;
}
function convertEventToLowerCase(functionName) {
  if (!functionName || functionName.length < 4) {
    return functionName;
  }
  const func = functionName.substring(3, functionName.length);
  const firstLetter = functionName[2].toLowerCase();
  return firstLetter + func;
}
const eventReg = /^on[A-Z]+/;
function loadScript(url, callback) {
  if (!url) {
    throw new Error("\u8BF7\u4F20\u5165url");
  }
  const script = document.createElement("script");
  script.type = "text/javascript";
  script.async = true;
  script.defer = true;
  script.src = url;
  document.body.appendChild(script);
  if (callback) {
    script.addEventListener("load", () => {
      callback();
    });
  }
}
function convertLnglat(lnglat) {
  if (Array.isArray(lnglat)) {
    return lnglat.map(convertLnglat);
  }
  return lnglat.toArray();
}
function upperCamelCase(prop) {
  if (!prop) {
    return prop;
  }
  return prop.charAt(0).toUpperCase() + prop.slice(1);
}

export { convertEventToLowerCase, convertLnglat, eventReg, isIndoorMapInstance, isLabelsLayerInstance, isMapInstance, isOverlayGroupInstance, isVectorLayerInstance, loadScript, upperCamelCase };
//# sourceMappingURL=util.mjs.map
