#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="D:\鲁朗\eco-ai-preception-monitor\node_modules\.pnpm\topojson-client@3.1.0\node_modules\topojson-client\bin\node_modules;D:\鲁朗\eco-ai-preception-monitor\node_modules\.pnpm\topojson-client@3.1.0\node_modules\topojson-client\node_modules;D:\鲁朗\eco-ai-preception-monitor\node_modules\.pnpm\topojson-client@3.1.0\node_modules;D:\鲁朗\eco-ai-preception-monitor\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/bin/node_modules:/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/node_modules:/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/topojson-client@3.1.0/node_modules:/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../../../../../topojson-client@3.1.0/node_modules/topojson-client/bin/topoquantize" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../../../../../topojson-client@3.1.0/node_modules/topojson-client/bin/topoquantize" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../../../../../topojson-client@3.1.0/node_modules/topojson-client/bin/topoquantize" $args
  } else {
    & "node$exe"  "$basedir/../../../../../../topojson-client@3.1.0/node_modules/topojson-client/bin/topoquantize" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
