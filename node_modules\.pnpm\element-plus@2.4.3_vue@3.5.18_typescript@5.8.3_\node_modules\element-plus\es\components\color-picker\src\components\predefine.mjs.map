{"version": 3, "file": "predefine.mjs", "sources": ["../../../../../../../packages/components/color-picker/src/components/predefine.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div :class=\"ns.e('colors')\">\n      <div\n        v-for=\"(item, index) in rgbaColors\"\n        :key=\"colors[index]\"\n        :class=\"[\n          ns.e('color-selector'),\n          ns.is('alpha', item._alpha < 100),\n          { selected: item.selected },\n        ]\"\n        @click=\"handleSelect(index)\"\n      >\n        <div :style=\"{ backgroundColor: item.value }\" />\n      </div>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, inject, ref, watch, watchEffect } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { colorPickerContextKey } from '../color-picker'\nimport Color from '../utils/color'\n\nimport type { PropType, Ref } from 'vue'\n\nexport default defineComponent({\n  props: {\n    colors: {\n      type: Array as PropType<string[]>,\n      required: true,\n    },\n    color: {\n      type: Object as PropType<Color>,\n      required: true,\n    },\n  },\n  setup(props) {\n    const ns = useNamespace('color-predefine')\n    const { currentColor } = inject(colorPickerContextKey)!\n\n    const rgbaColors = ref(parseColors(props.colors, props.color)) as Ref<\n      Color[]\n    >\n\n    watch(\n      () => currentColor.value,\n      (val) => {\n        const color = new Color()\n        color.fromString(val)\n\n        rgbaColors.value.forEach((item) => {\n          item.selected = color.compare(item)\n        })\n      }\n    )\n\n    watchEffect(() => {\n      rgbaColors.value = parseColors(props.colors, props.color)\n    })\n\n    function handleSelect(index: number) {\n      props.color.fromString(props.colors[index])\n    }\n\n    function parseColors(colors: string[], color: Color) {\n      return colors.map((value) => {\n        const c = new Color()\n        c.enableAlpha = true\n        c.format = 'rgba'\n        c.fromString(value)\n        c.selected = c.value === color.value\n        return c\n      })\n    }\n    return {\n      rgbaColors,\n      handleSelect,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["_createElementBlock", "_normalizeClass", "_createElementVNode", "_openBlock", "_Fragment", "_renderList", "_normalizeStyle"], "mappings": ";;;;;;;AA2BA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,KAAO,EAAA;AAAA,IACL,MAAQ,EAAA;AAAA,MACN,IAAM,EAAA,KAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,GACF;AAAA,EACA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAK,aAAa,iBAAiB,CAAA,CAAA;AACzC,IAAM,MAAA,EAAE,YAAiB,EAAA,GAAA,MAAA,CAAO,qBAAqB,CAAA,CAAA;AAErD,IAAA,MAAM,aAAa,GAAI,CAAA,WAAA,CAAY,MAAM,MAAQ,EAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAI7D,IAAA,KAAA,CACE,MAAM,YAAA,CAAa,KACnB,EAAA,CAAC,GAAQ,KAAA;AACP,MAAM,MAAA,KAAA,GAAQ,IAAI,KAAM,EAAA,CAAA;AACxB,MAAA,KAAA,CAAM,WAAW,GAAG,CAAA,CAAA;AAEpB,MAAW,UAAA,CAAA,KAAA,CAAM,OAAQ,CAAA,CAAC,IAAS,KAAA;AACjC,QAAK,IAAA,CAAA,QAAA,GAAW,KAAM,CAAA,OAAA,CAAQ,IAAI,CAAA,CAAA;AAAA,OACnC,CAAA,CAAA;AAAA,KAEL,CAAA,CAAA;AAEA,IAAA,WAAA,CAAY,MAAM;AAChB,MAAA,UAAA,CAAW,KAAQ,GAAA,WAAA,CAAY,KAAM,CAAA,MAAA,EAAQ,MAAM,KAAK,CAAA,CAAA;AAAA,KACzD,CAAA,CAAA;AAED,IAAA,SAAA,YAAA,CAAsB,KAAe,EAAA;AACnC,MAAA,KAAA,CAAM,KAAM,CAAA,UAAA,CAAW,KAAM,CAAA,MAAA,CAAO,KAAM,CAAA,CAAA,CAAA;AAAA,KAC5C;AAEA,IAAA,SAAA,WAAA,CAAqB,QAAkB,KAAc,EAAA;AACnD,MAAO,OAAA,MAAA,CAAO,GAAI,CAAA,CAAC,KAAU,KAAA;AAC3B,QAAM,MAAA,CAAA,GAAI,IAAI,KAAM,EAAA,CAAA;AACpB,QAAA,CAAA,CAAE,WAAc,GAAA,IAAA,CAAA;AAChB,QAAA,CAAA,CAAE,MAAS,GAAA,MAAA,CAAA;AACX,QAAA,CAAA,CAAE,WAAW,KAAK,CAAA,CAAA;AAClB,QAAE,CAAA,CAAA,QAAA,GAAW,CAAE,CAAA,KAAA,KAAU,KAAM,CAAA,KAAA,CAAA;AAC/B,QAAO,OAAA,CAAA,CAAA;AAAA,OACR,CAAA,CAAA;AAAA,KACH;AACA,IAAO,OAAA;AAAA,MACL,UAAA;AAAA,MACA,YAAA;AAAA,MACA,EAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;;sBAjFCA,kBAeM,CAAA,KAAA,EAAA;AAAA,IAfA,KAAA,EAAKC,cAAE,CAAA,IAAA,CAAA,EAAA,CAAG,CAAC,EAAA,CAAA;AAAA,GAAA,EAAA;IACfC,kBAaM,CAAA,KAAA,EAAA;AAAA,MAbA,KAAA,EAAKD,cAAE,CAAA,IAAA,CAAA,EAAA,CAAG,CAAC,CAAA,QAAA,CAAA,CAAA;AAAA,KAAA,EAAA;AACf,OAAAE,SAAA,CAAA,IAAA,CAAA,EAAAH,kBAAA,CAWMI,QAVoB,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,UAAA,EAAU,CAA1B,IAAA,EAAM,KAAK,KAAA;4BADrBL,kBAWM,CAAA,KAAA,EAAA;AAAA,UATH,KAAK,IAAO,CAAA,MAAA,CAAA,KAAA,CAAA;AAAA,UACZ,KAAK,EAAAC,cAAA,CAAA;AAAA,YAAc,QAAG,CAAC,CAAA,gBAAA,CAAA;AAAA,YAA8B,IAAG,CAAA,EAAA,CAAA,EAAA,CAAE,OAAU,EAAA,IAAA,CAAK,MAAM,GAAA,GAAA,CAAA;AAAA,YAAA,EAAA,QAAA,EAA+B,KAAK,QAAQ,EAAA;AAAA,WAAA,CAAA;UAK3H,OAAK,EAAA,CAAA,MAAA,KAAE,kBAAa,KAAK,CAAA;AAAA,SAAA,EAAA;UAE1BC,kBAAgD,CAAA,KAAA,EAAA;AAAA,YAA1C,KAAA,EAAKI,cAAqB,CAAA,EAAA,eAAA,EAAA,IAAA,CAAK,KAAK,EAAA,CAAA;AAAA,WAAA,EAAA,IAAA,EAAA,CAAA,CAAA;;;;;;;;;;"}