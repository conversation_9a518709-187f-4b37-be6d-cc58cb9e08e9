'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
require('../../../mixins/index.js');
var props = require('./props.js');
var useRegister = require('../../../mixins/useRegister.js');

var script = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "ElAmapControlGeolocation",
    inheritAttrs: false
  },
  __name: "Geolocation",
  props: props.propsType,
  emits: ["init", "complete"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const emitComplete = (e) => {
      emits("complete", e);
    };
    const { $$getInstance, parentInstance } = useRegister.useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.Geolocation"], () => {
          $amapComponent = new AMap.Geolocation(options);
          parentComponent.addControl($amapComponent);
          $amapComponent.on("complete", emitComplete);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        $amapComponent.off("complete", emitComplete);
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeControl($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return null;
    };
  }
});

exports.default = script;
//# sourceMappingURL=Geolocation.vue2.js.map
