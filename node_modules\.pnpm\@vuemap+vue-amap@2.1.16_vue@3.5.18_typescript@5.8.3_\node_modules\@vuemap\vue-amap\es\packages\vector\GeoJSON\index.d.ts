/// <reference types="@vuemap/amap-jsapi-types" />
import GeoJSON from './GeoJSON.vue';
import type { Plugin } from "vue";
export declare const ElAmapGeojson: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        geo: {
            type: ObjectConstructor;
            required: true;
        };
        markerOptions: {
            type: import("vue").PropType<AMap.MarkerOptions>;
        };
        getMarker: {
            type: FunctionConstructor;
        };
        polylineOptions: {
            type: import("vue").PropType<AMap.PolylineOptions>;
        };
        getPolyline: {
            type: FunctionConstructor;
        };
        polygonOptions: {
            type: import("vue").PropType<AMap.PolygonOptions>;
        };
        getPolygon: {
            type: FunctionConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {
        props: import("@vue/shared").LooseRequired<{
            readonly geo: Record<string, any>;
            readonly visible?: boolean | undefined;
            readonly zIndex?: number | undefined;
            readonly reEventWhenUpdate?: boolean | undefined;
            readonly extraOptions?: any;
            readonly markerOptions?: AMap.MarkerOptions | undefined;
            readonly getMarker?: Function | undefined;
            readonly polylineOptions?: AMap.PolylineOptions | undefined;
            readonly getPolyline?: Function | undefined;
            readonly polygonOptions?: AMap.PolygonOptions | undefined;
            readonly getPolygon?: Function | undefined;
            readonly onInit?: ((...args: any[]) => any) | undefined;
        } & {}>;
        emits: (event: "init", ...args: any[]) => void;
        $amapComponent: AMap.GeoJSON;
        $$getInstance: () => AMap.GeoJSON;
        parentInstance: import("../../..").IProvideType | undefined;
        createMarker: (geojson: any, lnglat: [number, number]) => AMap.Marker;
        createPolyline: (geojson: any, lnglat: any) => AMap.Polyline;
        createPolygon: (geojson: any, lnglat: any) => AMap.Polygon;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        geo: {
            type: ObjectConstructor;
            required: true;
        };
        markerOptions: {
            type: import("vue").PropType<AMap.MarkerOptions>;
        };
        getMarker: {
            type: FunctionConstructor;
        };
        polylineOptions: {
            type: import("vue").PropType<AMap.PolylineOptions>;
        };
        getPolyline: {
            type: FunctionConstructor;
        };
        polygonOptions: {
            type: import("vue").PropType<AMap.PolygonOptions>;
        };
        getPolygon: {
            type: FunctionConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {}, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        geo: {
            type: ObjectConstructor;
            required: true;
        };
        markerOptions: {
            type: import("vue").PropType<AMap.MarkerOptions>;
        };
        getMarker: {
            type: FunctionConstructor;
        };
        polylineOptions: {
            type: import("vue").PropType<AMap.PolylineOptions>;
        };
        getPolyline: {
            type: FunctionConstructor;
        };
        polygonOptions: {
            type: import("vue").PropType<AMap.PolygonOptions>;
        };
        getPolygon: {
            type: FunctionConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {
        props: import("@vue/shared").LooseRequired<{
            readonly geo: Record<string, any>;
            readonly visible?: boolean | undefined;
            readonly zIndex?: number | undefined;
            readonly reEventWhenUpdate?: boolean | undefined;
            readonly extraOptions?: any;
            readonly markerOptions?: AMap.MarkerOptions | undefined;
            readonly getMarker?: Function | undefined;
            readonly polylineOptions?: AMap.PolylineOptions | undefined;
            readonly getPolyline?: Function | undefined;
            readonly polygonOptions?: AMap.PolygonOptions | undefined;
            readonly getPolygon?: Function | undefined;
            readonly onInit?: ((...args: any[]) => any) | undefined;
        } & {}>;
        emits: (event: "init", ...args: any[]) => void;
        $amapComponent: AMap.GeoJSON;
        $$getInstance: () => AMap.GeoJSON;
        parentInstance: import("../../..").IProvideType | undefined;
        createMarker: (geojson: any, lnglat: [number, number]) => AMap.Marker;
        createPolyline: (geojson: any, lnglat: any) => AMap.Polyline;
        createPolygon: (geojson: any, lnglat: any) => AMap.Polygon;
    }, {}, {}, {}, {}>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    geo: {
        type: ObjectConstructor;
        required: true;
    };
    markerOptions: {
        type: import("vue").PropType<AMap.MarkerOptions>;
    };
    getMarker: {
        type: FunctionConstructor;
    };
    polylineOptions: {
        type: import("vue").PropType<AMap.PolylineOptions>;
    };
    getPolyline: {
        type: FunctionConstructor;
    };
    polygonOptions: {
        type: import("vue").PropType<AMap.PolygonOptions>;
    };
    getPolygon: {
        type: FunctionConstructor;
    };
} & {
    visible: import("../../..").IPropOptions<boolean>;
    zIndex: import("../../..").IPropOptions<number>;
    reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
    extraOptions: import("../../..").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
}, {
    props: import("@vue/shared").LooseRequired<{
        readonly geo: Record<string, any>;
        readonly visible?: boolean | undefined;
        readonly zIndex?: number | undefined;
        readonly reEventWhenUpdate?: boolean | undefined;
        readonly extraOptions?: any;
        readonly markerOptions?: AMap.MarkerOptions | undefined;
        readonly getMarker?: Function | undefined;
        readonly polylineOptions?: AMap.PolylineOptions | undefined;
        readonly getPolyline?: Function | undefined;
        readonly polygonOptions?: AMap.PolygonOptions | undefined;
        readonly getPolygon?: Function | undefined;
        readonly onInit?: ((...args: any[]) => any) | undefined;
    } & {}>;
    emits: (event: "init", ...args: any[]) => void;
    $amapComponent: AMap.GeoJSON;
    $$getInstance: () => AMap.GeoJSON;
    parentInstance: import("../../..").IProvideType | undefined;
    createMarker: (geojson: any, lnglat: [number, number]) => AMap.Marker;
    createPolyline: (geojson: any, lnglat: any) => AMap.Polyline;
    createPolygon: (geojson: any, lnglat: any) => AMap.Polygon;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], "init", {}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Plugin<any[]>;
export default ElAmapGeojson;
export declare type ElAmapGeojsonInstance = InstanceType<typeof GeoJSON>;
