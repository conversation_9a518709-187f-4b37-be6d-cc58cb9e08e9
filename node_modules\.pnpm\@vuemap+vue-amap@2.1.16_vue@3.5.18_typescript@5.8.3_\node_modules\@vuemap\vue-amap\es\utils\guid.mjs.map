{"version": 3, "file": "guid.mjs", "sources": ["../../../utils/guid.ts"], "sourcesContent": ["export default function guid () {\r\n  const s: string[] = [];\r\n  const hexDigits = '0123456789abcdef';\r\n  for (let i = 0; i < 36; i++) {\r\n    s[i] = hexDigits.charAt(Math.floor(Math.random() * 0x10));\r\n  }\r\n  s[8] = s[13] = s[18] = s[23] = '-';\r\n\r\n  return s.join('');\r\n}\r\n"], "names": [], "mappings": "AAAA,SAAwB,IAAQ,GAAA;AAC9B,EAAA,MAAM,IAAc,EAAC,CAAA;AACrB,EAAA,MAAM,SAAY,GAAA,kBAAA,CAAA;AAClB,EAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,EAAA,EAAI,CAAK,EAAA,EAAA;AAC3B,IAAE,CAAA,CAAA,CAAC,CAAI,GAAA,SAAA,CAAU,MAAO,CAAA,IAAA,CAAK,MAAM,IAAK,CAAA,MAAA,EAAW,GAAA,EAAI,CAAC,CAAA,CAAA;AAAA,GAC1D;AACA,EAAE,CAAA,CAAA,CAAC,CAAI,GAAA,CAAA,CAAE,EAAE,CAAA,GAAI,EAAE,EAAE,CAAA,GAAI,CAAE,CAAA,EAAE,CAAI,GAAA,GAAA,CAAA;AAE/B,EAAO,OAAA,CAAA,CAAE,KAAK,EAAE,CAAA,CAAA;AAClB;;;;"}