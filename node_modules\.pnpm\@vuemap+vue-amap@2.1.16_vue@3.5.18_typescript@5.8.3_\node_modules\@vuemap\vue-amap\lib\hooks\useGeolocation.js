'use strict';

function useGeolocation(options) {
  return new Promise((resolve) => {
    AMap.plugin("AMap.Geolocation", function() {
      const geolocation = new AMap.Geolocation(options);
      const getCurrentPosition = () => {
        return new Promise((resolve2, reject) => {
          geolocation.getCurrentPosition(function(status, result) {
            if (status === "complete" && result.info === "SUCCESS") {
              resolve2(result);
            } else {
              reject({
                status,
                result
              });
            }
          });
        });
      };
      const getCityInfo = () => {
        return new Promise((resolve2, reject) => {
          geolocation.getCityInfo(function(status, result) {
            if (status === "complete" && result.info === "SUCCESS") {
              resolve2(result);
            } else {
              reject({
                status,
                result
              });
            }
          });
        });
      };
      resolve({
        getCurrentPosition,
        getCityInfo
      });
    });
  });
}

exports.useGeolocation = useGeolocation;
//# sourceMappingURL=useGeolocation.js.map
