#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/bin/node_modules:/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/node_modules:/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/topojson-client@3.1.0/node_modules:/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/bin/node_modules:/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/node_modules:/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/topojson-client@3.1.0/node_modules:/mnt/d/鲁朗/eco-ai-preception-monitor/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../topojson-client@3.1.0/node_modules/topojson-client/bin/topomerge" "$@"
else
  exec node  "$basedir/../../../../../../topojson-client@3.1.0/node_modules/topojson-client/bin/topomerge" "$@"
fi
