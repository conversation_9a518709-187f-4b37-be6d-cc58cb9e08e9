import { defineComponent, useAttrs, openBlock, createElementBlock } from 'vue';
import { debounce } from 'lodash-es';
import '../../../mixins/index.mjs';
import { useEditor } from '../../../mixins/useEditor.mjs';
import '../../../utils/index.mjs';
import { propsTypes } from './props.mjs';
import { useRegister } from '../../../mixins/useRegister.mjs';
import { isMapInstance, isOverlayGroupInstance, isVectorLayerInstance } from '../../../utils/util.mjs';

var script = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapCircle",
    inheritAttrs: false
  },
  __name: "Circle",
  props: propsTypes,
  emits: ["init", "update:center", "update:radius"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let destroying = false;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.Circle(options);
        if (isMapInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        } else if (isOverlayGroupInstance(parentComponent)) {
          parentComponent.addOverlay($amapComponent);
        } else if (isVectorLayerInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        }
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __zIndex(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ zIndex: value });
          }
        },
        __strokeColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeColor: value });
          }
        },
        __strokeOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeOpacity: value });
          }
        },
        __strokeWeight(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeWeight: value });
          }
        },
        __fillColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillColor: value });
          }
        },
        __fillOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillOpacity: value });
          }
        },
        __strokeStyle(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeStyle: value });
          }
        },
        __strokeDasharray(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeDasharray: value });
          }
        },
        __editable(flag) {
          createEditor().then(() => {
            flag ? resetEditor() : editor.close();
          });
        },
        __center(center) {
          if ($amapComponent) {
            $amapComponent.setCenter(center);
            resetEditor();
          }
        },
        __radius(radius) {
          if ($amapComponent) {
            $amapComponent.setRadius(radius);
            resetEditor();
          }
        }
      },
      destroyComponent() {
        destroying = true;
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (editor) {
            if (!parentInstance.isDestroy) {
              editor.close();
            }
            editor = null;
          }
          if (!parentInstance.isDestroy) {
            if (isMapInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            } else if (isOverlayGroupInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.removeOverlay($amapComponent);
            } else if (isVectorLayerInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            }
          }
          if ($amapComponent.destroy) {
            $amapComponent.destroy();
          }
          $amapComponent = null;
        }
      }
    });
    const resetEditor = debounce(() => {
      if (editor && props.editable) {
        editor.close();
        editor.setTarget();
        editor.setTarget($amapComponent);
        editor.open();
      }
    }, 50);
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitModel($amapComponent);
      });
      $amapComponent.on("touchend", () => {
        emitModel($amapComponent);
      });
    };
    const emitModel = (target) => {
      if (destroying) {
        return;
      }
      emits("update:center", target.getCenter().toArray());
      emits("update:radius", target.getRadius());
    };
    let editor;
    const attrs = useAttrs();
    const createEditor = () => {
      return new Promise((resolve) => {
        if (editor) {
          resolve();
        } else {
          AMap.plugin(["AMap.CircleEditor"], () => {
            editor = new AMap.CircleEditor(parentInstance == null ? void 0 : parentInstance.$amapComponent, $amapComponent, props.editOptions);
            useEditor(editor, attrs);
            bindEditorModelEvents();
            resolve();
          });
        }
      });
    };
    const bindEditorModelEvents = () => {
      editor.on("addnode", (e) => {
        emitModel(e.target);
      });
      editor.on("adjust", (e) => {
        emitModel(e.target);
      });
      editor.on("removenode", (e) => {
        emitModel(e.target);
      });
      editor.on("add", (e) => {
        emitModel(e.target);
      });
      editor.on("move", (e) => {
        emitModel(e.target);
      });
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

export { script as default };
//# sourceMappingURL=Circle.vue2.mjs.map
