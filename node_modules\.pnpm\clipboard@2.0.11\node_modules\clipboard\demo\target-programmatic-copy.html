<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>target-programmatic-copy</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
  </head>
  <body>
    <!-- 1. Define some markup -->
    <textarea id="bar">hello</textarea>
    <button id="btn">
      Copy
    </button>

    <!-- 2. Include library -->
    <script src="../dist/clipboard.min.js"></script>

    <!-- 3. Instantiate clipboard -->
    <script>
      var btn = document.querySelector('#btn');

      btn.addEventListener('click', () => {
        const textCopied = ClipboardJS.copy(document.querySelector('#bar'));
        console.log('copied!', textCopied);
      })
    </script>
  </body>
</html>
