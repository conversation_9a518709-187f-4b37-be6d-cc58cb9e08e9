{"version": 3, "file": "index.mjs", "sources": ["../../../../packages/DirectionalLight/index.ts"], "sourcesContent": ["import DirectionalLight from './DirectionalLight.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nDirectionalLight.install = (app: App) => {\r\n  app.component(DirectionalLight.name, DirectionalLight);\r\n  return app;\r\n};\r\nexport const ElAmapLocaDirectionalLight = DirectionalLight as typeof DirectionalLight & Plugin;\r\nexport default ElAmapLocaDirectionalLight;\r\n\r\nexport type ElAmapLocaDirectionalLightInstance = InstanceType<typeof DirectionalLight>\r\n"], "names": ["DirectionalLight"], "mappings": ";;;AAEAA,MAAiB,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AACvC,EAAI,GAAA,CAAA,SAAA,CAAUA,MAAiB,CAAA,IAAA,EAAMA,MAAgB,CAAA,CAAA;AACrD,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AACO,MAAM,0BAA6B,GAAAA;;;;"}