'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
require('../../../mixins/index.js');
var props = require('./props.js');
var useRegister = require('../../../mixins/useRegister.js');

var script = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "ElAmapControlHawkEye",
    inheritAttrs: false
  },
  __name: "Hawk<PERSON><PERSON>",
  props: props.propsTypes,
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister.useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.HawkEye"], () => {
          $amapComponent = new AMap.HawkEye(options);
          parentComponent.addControl($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      watchRedirectFn: {
        __isOpen(flag) {
          !flag ? $amapComponent.close() : $amapComponent.open();
        }
      },
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeControl($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return null;
    };
  }
});

exports.default = script;
//# sourceMappingURL=HawkEye.vue2.js.map
