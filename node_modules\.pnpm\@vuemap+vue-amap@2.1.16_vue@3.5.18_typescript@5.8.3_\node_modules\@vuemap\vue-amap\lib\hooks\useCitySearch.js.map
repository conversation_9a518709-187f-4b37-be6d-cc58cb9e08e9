{"version": 3, "file": "useCitySearch.js", "sources": ["../../../hooks/useCitySearch.ts"], "sourcesContent": ["export interface CitySearchPromise {\r\n    getLocalCity: () => Promise<AMap.CitySearchLocalCityResult>\r\n}\r\n\r\n/**\r\n * IP定位插件hook，用于进行粗略的基于IP定位\r\n */\r\nexport function useCitySearch () {\r\n    return new Promise<CitySearchPromise>((resolve) => {\r\n        AMap.plugin('AMap.CitySearch', function () {\r\n            const citySearch = new AMap.CitySearch();\r\n            const getLocalCity = () => {\r\n                return new Promise<AMap.CitySearchLocalCityResult>((resolve, reject) => {\r\n                    citySearch.getLocalCity(function (status, result) {\r\n                        if (status === 'complete' && result.info === 'OK') {\r\n                            resolve(result);\r\n                        } else {\r\n                            reject({\r\n                                status,\r\n                                result\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n            };\r\n            resolve({\r\n                getLocalCity\r\n            });\r\n        });\r\n    });\r\n}"], "names": ["resolve"], "mappings": ";;AAOO,SAAS,aAAiB,GAAA;AAC7B,EAAO,OAAA,IAAI,OAA2B,CAAA,CAAC,OAAY,KAAA;AAC/C,IAAK,IAAA,CAAA,MAAA,CAAO,mBAAmB,WAAY;AACvC,MAAM,MAAA,UAAA,GAAa,IAAI,IAAA,CAAK,UAAW,EAAA,CAAA;AACvC,MAAA,MAAM,eAAe,MAAM;AACvB,QAAA,OAAO,IAAI,OAAA,CAAwC,CAACA,QAAAA,EAAS,MAAW,KAAA;AACpE,UAAW,UAAA,CAAA,YAAA,CAAa,SAAU,MAAA,EAAQ,MAAQ,EAAA;AAC9C,YAAA,IAAI,MAAW,KAAA,UAAA,IAAc,MAAO,CAAA,IAAA,KAAS,IAAM,EAAA;AAC/C,cAAAA,SAAQ,MAAM,CAAA,CAAA;AAAA,aACX,MAAA;AACH,cAAO,MAAA,CAAA;AAAA,gBACH,MAAA;AAAA,gBACA,MAAA;AAAA,eACH,CAAA,CAAA;AAAA,aACL;AAAA,WACH,CAAA,CAAA;AAAA,SACJ,CAAA,CAAA;AAAA,OACL,CAAA;AACA,MAAQ,OAAA,CAAA;AAAA,QACJ,YAAA;AAAA,OACH,CAAA,CAAA;AAAA,KACJ,CAAA,CAAA;AAAA,GACJ,CAAA,CAAA;AACL;;;;"}