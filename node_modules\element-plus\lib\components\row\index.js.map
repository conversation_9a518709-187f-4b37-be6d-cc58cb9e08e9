{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/row/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Row from './src/row.vue'\n\nexport const ElRow = withInstall(Row)\nexport default ElRow\n\nexport * from './src/row'\nexport * from './src/constants'\n"], "names": ["withInstall", "Row"], "mappings": ";;;;;;;;;;AAEY,MAAC,KAAK,GAAGA,mBAAW,CAACC,gBAAG;;;;;;;;;"}