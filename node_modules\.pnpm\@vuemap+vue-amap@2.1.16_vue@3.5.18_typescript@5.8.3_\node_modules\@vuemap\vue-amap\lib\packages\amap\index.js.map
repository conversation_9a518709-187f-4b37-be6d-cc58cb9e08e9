{"version": 3, "file": "index.js", "sources": ["../../../../packages/amap/index.ts"], "sourcesContent": ["import Amap from './amap.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nAmap.install = (app: App) => {\r\n  app.component(Amap.name, Amap);\r\n  return app;\r\n};\r\nexport const ElAmap = Amap as typeof Amap & Plugin;\r\nexport default ElAmap;\r\n\r\nexport type ElAmapInstance = InstanceType<typeof Amap>\r\n"], "names": ["Amap"], "mappings": ";;;;;;;AAEAA,gDAAK,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AAC3B,EAAI,GAAA,CAAA,SAAA,CAAUA,gDAAK,CAAA,IAAA,EAAMA,gDAAI,CAAA,CAAA;AAC7B,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AACO,MAAM,MAAS,GAAAA;;;;;"}