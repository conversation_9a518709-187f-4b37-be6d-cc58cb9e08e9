{"version": 3, "file": "convert-helper.mjs", "sources": ["../../../utils/convert-helper.js"], "sourcesContent": ["export function toPixel(arr) {\r\n  return new AMap.Pixel(arr[0], arr[1]);\r\n}\r\n\r\nexport function toSize(arr) {\r\n  return new AMap.Size(arr[0], arr[1]);\r\n}\r\n\r\nexport function pixelTo(pixel) {\r\n  if (Array.isArray(pixel)) return pixel;\r\n  return [pixel.getX(), pixel.getY()];\r\n}\r\n\r\nexport function toLngLat(arr) {\r\n  return new AMap.LngLat(arr[0], arr[1]);\r\n}\r\n\r\nexport function lngLatTo(lngLat) {\r\n  if (!lngLat) return;\r\n  if (Array.isArray(lngLat)) return lngLat.slice();\r\n  return [lngLat.getLng(), lngLat.getLat()];\r\n}\r\n\r\n/**\r\n * @param arrs 二重数组 southWest, northEast\r\n */\r\nexport function toBounds(arrs) {\r\n  return new AMap.Bounds(toLngLat(arrs[0]), toLngLat(arrs[1]));\r\n}\r\n"], "names": [], "mappings": "AAAO,SAAS,QAAQ,GAAK,EAAA;AAC3B,EAAO,OAAA,IAAI,KAAK,KAAM,CAAA,GAAA,CAAI,CAAC,CAAG,EAAA,GAAA,CAAI,CAAC,CAAC,CAAA,CAAA;AACtC,CAAA;AAEO,SAAS,OAAO,GAAK,EAAA;AAC1B,EAAO,OAAA,IAAI,KAAK,IAAK,CAAA,GAAA,CAAI,CAAC,CAAG,EAAA,GAAA,CAAI,CAAC,CAAC,CAAA,CAAA;AACrC,CAAA;AAEO,SAAS,QAAQ,KAAO,EAAA;AAC7B,EAAI,IAAA,KAAA,CAAM,QAAQ,KAAK,CAAA;AAAG,IAAO,OAAA,KAAA,CAAA;AACjC,EAAA,OAAO,CAAC,KAAM,CAAA,IAAA,EAAQ,EAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AACpC,CAAA;AAEO,SAAS,SAAS,GAAK,EAAA;AAC5B,EAAO,OAAA,IAAI,KAAK,MAAO,CAAA,GAAA,CAAI,CAAC,CAAG,EAAA,GAAA,CAAI,CAAC,CAAC,CAAA,CAAA;AACvC,CAAA;AAEO,SAAS,SAAS,MAAQ,EAAA;AAC/B,EAAA,IAAI,CAAC,MAAA;AAAQ,IAAA,OAAA;AACb,EAAI,IAAA,KAAA,CAAM,QAAQ,MAAM,CAAA;AAAG,IAAA,OAAO,OAAO,KAAM,EAAA,CAAA;AAC/C,EAAA,OAAO,CAAC,MAAO,CAAA,MAAA,EAAU,EAAA,MAAA,CAAO,QAAQ,CAAA,CAAA;AAC1C,CAAA;AAKO,SAAS,SAAS,IAAM,EAAA;AAC7B,EAAA,OAAO,IAAI,IAAA,CAAK,MAAO,CAAA,QAAA,CAAS,IAAK,CAAA,CAAC,CAAC,CAAA,EAAG,QAAS,CAAA,IAAA,CAAK,CAAC,CAAC,CAAC,CAAA,CAAA;AAC7D;;;;"}