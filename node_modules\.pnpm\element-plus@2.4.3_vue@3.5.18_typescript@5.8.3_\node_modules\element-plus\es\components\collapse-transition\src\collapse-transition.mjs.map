{"version": 3, "file": "collapse-transition.mjs", "sources": ["../../../../../../packages/components/collapse-transition/src/collapse-transition.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b()\" v-on=\"on\">\n    <slot />\n  </transition>\n</template>\n<script lang=\"ts\" setup>\nimport { useNamespace } from '@element-plus/hooks'\nimport type { RendererElement } from '@vue/runtime-core'\n\ndefineOptions({\n  name: 'ElCollapseTransition',\n})\n\nconst ns = useNamespace('collapse-transition')\n\nconst reset = (el: RendererElement) => {\n  el.style.maxHeight = ''\n  el.style.overflow = el.dataset.oldOverflow\n  el.style.paddingTop = el.dataset.oldPaddingTop\n  el.style.paddingBottom = el.dataset.oldPaddingBottom\n}\n\nconst on = {\n  beforeEnter(el: RendererElement) {\n    if (!el.dataset) el.dataset = {}\n\n    el.dataset.oldPaddingTop = el.style.paddingTop\n    el.dataset.oldPaddingBottom = el.style.paddingBottom\n\n    el.style.maxHeight = 0\n    el.style.paddingTop = 0\n    el.style.paddingBottom = 0\n  },\n\n  enter(el: RendererElement) {\n    el.dataset.oldOverflow = el.style.overflow\n    if (el.scrollHeight !== 0) {\n      el.style.maxHeight = `${el.scrollHeight}px`\n    } else {\n      el.style.maxHeight = 0\n    }\n    el.style.paddingTop = el.dataset.oldPaddingTop\n    el.style.paddingBottom = el.dataset.oldPaddingBottom\n    el.style.overflow = 'hidden'\n  },\n\n  afterEnter(el: RendererElement) {\n    el.style.maxHeight = ''\n    el.style.overflow = el.dataset.oldOverflow\n  },\n\n  enterCancelled(el: RendererElement) {\n    reset(el)\n  },\n\n  beforeLeave(el: RendererElement) {\n    if (!el.dataset) el.dataset = {}\n    el.dataset.oldPaddingTop = el.style.paddingTop\n    el.dataset.oldPaddingBottom = el.style.paddingBottom\n    el.dataset.oldOverflow = el.style.overflow\n\n    el.style.maxHeight = `${el.scrollHeight}px`\n    el.style.overflow = 'hidden'\n  },\n\n  leave(el: RendererElement) {\n    if (el.scrollHeight !== 0) {\n      el.style.maxHeight = 0\n      el.style.paddingTop = 0\n      el.style.paddingBottom = 0\n    }\n  },\n\n  afterLeave(el: RendererElement) {\n    reset(el)\n  },\n\n  leaveCancelled(el: RendererElement) {\n    reset(el)\n  },\n}\n</script>\n"], "names": [], "mappings": ";;;;;mCASc,CAAA;AAAA,EACZ,IAAM,EAAA,sBAAA;AACR,CAAA,CAAA,CAAA;;;;AAEA,IAAM,MAAA,EAAA,GAAK,aAAa,qBAAqB,CAAA,CAAA;AAE7C,IAAM,MAAA,KAAA,GAAQ,CAAC,EAAwB,KAAA;AACrC,MAAA,EAAA,CAAG,MAAM,SAAY,GAAA,EAAA,CAAA;AACrB,MAAG,EAAA,CAAA,KAAA,CAAM,QAAW,GAAA,EAAA,CAAG,OAAQ,CAAA,WAAA,CAAA;AAC/B,MAAG,EAAA,CAAA,KAAA,CAAM,UAAa,GAAA,EAAA,CAAG,OAAQ,CAAA,aAAA,CAAA;AACjC,MAAG,EAAA,CAAA,KAAA,CAAM,aAAgB,GAAA,EAAA,CAAG,OAAQ,CAAA,gBAAA,CAAA;AAAA,KACtC,CAAA;AAEA,IAAA,MAAM,EAAK,GAAA;AAAA,MACT,YAAY,EAAqB,EAAA;AAC/B,QAAA,IAAI,CAAC,EAAG,CAAA,OAAA;AAAS,UAAA,EAAA,CAAG,UAAU,EAAC,CAAA;AAE/B,QAAG,EAAA,CAAA,OAAA,CAAQ,aAAgB,GAAA,EAAA,CAAG,KAAM,CAAA,UAAA,CAAA;AACpC,QAAG,EAAA,CAAA,OAAA,CAAQ,gBAAmB,GAAA,EAAA,CAAG,KAAM,CAAA,aAAA,CAAA;AAEvC,QAAA,EAAA,CAAG,MAAM,SAAY,GAAA,CAAA,CAAA;AACrB,QAAA,EAAA,CAAG,MAAM,UAAa,GAAA,CAAA,CAAA;AACtB,QAAA,EAAA,CAAG,MAAM,aAAgB,GAAA,CAAA,CAAA;AAAA,OAC3B;AAAA,MAEA,MAAM,EAAqB,EAAA;AACzB,QAAG,EAAA,CAAA,OAAA,CAAQ,WAAc,GAAA,EAAA,CAAG,KAAM,CAAA,QAAA,CAAA;AAClC,QAAI,IAAA,EAAA,CAAG,iBAAiB,CAAG,EAAA;AACzB,UAAG,EAAA,CAAA,KAAA,CAAM,SAAY,GAAA,CAAA,EAAG,EAAG,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAAA,SACtB,MAAA;AACL,UAAA,EAAA,CAAG,MAAM,SAAY,GAAA,CAAA,CAAA;AAAA,SACvB;AACA,QAAG,EAAA,CAAA,KAAA,CAAM,UAAa,GAAA,EAAA,CAAG,OAAQ,CAAA,aAAA,CAAA;AACjC,QAAG,EAAA,CAAA,KAAA,CAAM,aAAgB,GAAA,EAAA,CAAG,OAAQ,CAAA,gBAAA,CAAA;AACpC,QAAA,EAAA,CAAG,MAAM,QAAW,GAAA,QAAA,CAAA;AAAA,OACtB;AAAA,MAEA,WAAW,EAAqB,EAAA;AAC9B,QAAA,EAAA,CAAG,MAAM,SAAY,GAAA,EAAA,CAAA;AACrB,QAAG,EAAA,CAAA,KAAA,CAAM,QAAW,GAAA,EAAA,CAAG,OAAQ,CAAA,WAAA,CAAA;AAAA,OACjC;AAAA,MAEA,eAAe,EAAqB,EAAA;AAClC,QAAA,KAAA,CAAM,EAAE,CAAA,CAAA;AAAA,OACV;AAAA,MAEA,YAAY,EAAqB,EAAA;AAC/B,QAAA,IAAI,CAAC,EAAG,CAAA,OAAA;AAAS,UAAA,EAAA,CAAG,UAAU,EAAC,CAAA;AAC/B,QAAG,EAAA,CAAA,OAAA,CAAQ,aAAgB,GAAA,EAAA,CAAG,KAAM,CAAA,UAAA,CAAA;AACpC,QAAG,EAAA,CAAA,OAAA,CAAQ,gBAAmB,GAAA,EAAA,CAAG,KAAM,CAAA,aAAA,CAAA;AACvC,QAAG,EAAA,CAAA,OAAA,CAAQ,WAAc,GAAA,EAAA,CAAG,KAAM,CAAA,QAAA,CAAA;AAElC,QAAG,EAAA,CAAA,KAAA,CAAM,SAAY,GAAA,CAAA,EAAG,EAAG,CAAA,YAAA,CAAA,EAAA,CAAA,CAAA;AAC3B,QAAA,EAAA,CAAG,MAAM,QAAW,GAAA,QAAA,CAAA;AAAA,OACtB;AAAA,MAEA,MAAM,EAAqB,EAAA;AACzB,QAAI,IAAA,EAAA,CAAG,iBAAiB,CAAG,EAAA;AACzB,UAAA,EAAA,CAAG,MAAM,SAAY,GAAA,CAAA,CAAA;AACrB,UAAA,EAAA,CAAG,MAAM,UAAa,GAAA,CAAA,CAAA;AACtB,UAAA,EAAA,CAAG,MAAM,aAAgB,GAAA,CAAA,CAAA;AAAA,SAC3B;AAAA,OACF;AAAA,MAEA,WAAW,EAAqB,EAAA;AAC9B,QAAA,KAAA,CAAM,EAAE,CAAA,CAAA;AAAA,OACV;AAAA,MAEA,eAAe,EAAqB,EAAA;AAClC,QAAA,KAAA,CAAM,EAAE,CAAA,CAAA;AAAA,OACV;AAAA,KACF,CAAA;;;;;;;;;;;;;;;;;"}