{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/rate/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Rate from './src/rate.vue'\n\nexport const ElRate = withInstall(Rate)\nexport default ElRate\n\nexport * from './src/rate'\n"], "names": ["withInstall", "Rate"], "mappings": ";;;;;;;;;AAEY,MAAC,MAAM,GAAGA,mBAAW,CAACC,iBAAI;;;;;;;"}