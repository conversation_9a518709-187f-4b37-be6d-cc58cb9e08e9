{"name": "clipboard-polyfill", "version": "4.1.1", "description": "A polyfill for the asynchronous clipboard API", "type": "module", "devDependencies": {"@biomejs/biome": "^1.1.0", "@types/bun": "^1.1.0", "barely-a-dev-server": "^0.6.0", "esbuild": "^0.19.11", "typescript": "^5.4.5"}, "repository": {"type": "git", "url": "https://github.com/lgarron/clipboard-polyfill"}, "keywords": ["clipboard", "HTML5", "copy", "copying", "cut", "paste", "execCommand", "setData", "getData", "polyfill"], "author": "<PERSON> <<EMAIL>> (https://garron.net/)", "license": "MIT", "bugs": {"url": "https://github.com/lgarron/clipboard-polyfill/issues"}, "files": ["/dist", "/overwrite-globals", "README.md"], "main": "./dist/es6/clipboard-polyfill.es6.js", "module": "./dist/es6/clipboard-polyfill.es6.js", "types": "./dist/types/entries/es6/clipboard-polyfill.es6.d.ts", "exports": {".": {"types": "./dist/types/entries/es6/clipboard-polyfill.es6.d.ts", "import": "./dist/es6/clipboard-polyfill.es6.js", "default": "./dist/es6/clipboard-polyfill.es6.js"}, "./overwrite-globals": {"types": "./dist/types/entries/es5/overwrite-globals.d.ts", "import": "./dist/es5/overwrite-globals/clipboard-polyfill.overwrite-globals.es5.js", "default": "./dist/es5/overwrite-globals/clipboard-polyfill.overwrite-globals.es5.js"}}}