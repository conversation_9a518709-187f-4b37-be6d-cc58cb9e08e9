/*! @vuemap/vue-amap-loca v2.1.2 */import{getCurrentInstance as ke,inject as Pe,onMounted as Be,onBeforeUnmount as Ee,onBeforeUpdate as Oe,onUpdated as je,nextTick as R,isProxy as We,toRaw as ze,unref as Fe,watch as Re,defineComponent as b,openBlock as $,createElementBlock as _,provide as Te,renderSlot as De}from"vue";const Me=(o=[])=>{const c=[];return{install:d=>{c.includes(d)||(c.push(d),o.forEach(s=>d.use(s)))}}};function He(o){if(!o||o.length<4)return o;const c=o.substring(3,o.length);return o[2].toLowerCase()+c}const Ne=/^on[A-Z]+/;function Ve(o){return o&&o.charAt(0).toUpperCase()+o.slice(1)}function Ge(o,c,d){!o||!o.on||o.on(c,d)}function Ue(o,c,d){!o||!o.off||o.off(c,d)}const pe={visible:{type:Boolean,default:!0},zIndex:{type:Number},reEventWhenUpdate:{type:Boolean,default:!1},extraOptions:{type:Object}},ie=o=>Object.assign({},pe,o),ne="parentInstance",I=(o,c)=>{let d=ke(),{props:s,attrs:n}=d,r=Pe(ne,void 0);const a=c.emits;let p=!1,l;Be(()=>{r?r.$amapComponent?g():r.addChildComponent(g):c.isRoot&&g()}),Ee(()=>{l&&(E(),F(),c.destroyComponent?c.destroyComponent():v(),c.provideData&&(c.provideData.isDestroy=!0),r=void 0,s=void 0,n=void 0,d=void 0,l=void 0)}),Oe(()=>{s.reEventWhenUpdate&&p&&l&&E()}),je(()=>{s.reEventWhenUpdate&&p&&l&&L()});const g=()=>{const u=h();o(u,r==null?void 0:r.$amapComponent).then(S=>{l=S,L(),y(),W(),Object.assign(d.ctx,d.exposed),a("init",l,d.ctx),R(()=>{re()}).then(),p=!0})},y=()=>{["editable","visible","zooms"].forEach(u=>{if(s[u]!==void 0){const S=z(u);S&&S.call(l,f(C(u,s[u])))}})},m=c.propsRedirect||{},h=()=>{const u={};return s.extraOptions&&Object.assign(u,s.extraOptions),Object.keys(s).forEach(S=>{let A=S;const O=C(A,s[A]);O!==void 0&&(m&&m[S]&&(A=m[A]),u[A]=O)}),u},t=c.converts||{},C=(u,S)=>t&&t[u]?t[u].call(void 0,S):S,f=u=>We(u)?ze(u):Fe(u);let i=[],e=Object.assign({__visible:u=>{l&&l.show&&l.hide&&(u?l.show():l.hide())},__zIndex(u){l&&l.setzIndex&&l.setzIndex(u)}},c.watchRedirectFn||{});const W=()=>{Object.keys(s).forEach(u=>{let S=u;m&&m[u]&&(S=m[u]);const A=z(S);if(!A)return;const O={deep:!1},se=Object.prototype.toString.call(s[u]);(se==="[object Object]"||se==="[object Array]")&&(O.deep=!0);const we=Re(()=>s[u],xe=>{A.call(l,f(C(u,xe)))},O);i.push(we)})},F=()=>{i.forEach(u=>u()),i=[],e=void 0},z=u=>e[`__${u}`]?e[`__${u}`]:l?l[`set${Ve(u)}`]:null,j={},L=()=>{Object.keys(n).forEach(u=>{if(Ne.test(u)){const S=He(u);Ge(l,S,n[u]),j[S]=n[u]}})},E=()=>{Object.keys(j).forEach(u=>{Ue(l,u,j[u]),delete j[u]})},re=()=>{const u=c.needInitComponents||[];for(;u.length>0;)u[0](),u.splice(0,1)},v=()=>{l&&(l.setMap&&l.setMap(null),l.close&&l.close(),l.editor&&l.editor.close())};function B(){return l}return{$$getInstance:B,parentInstance:r,isMounted:p}},w=o=>Object.assign({},pe,{sourceUrl:{type:String},sourceData:{type:Object},geoBufferSource:{type:[ArrayBuffer,String],default(){return null}},layerStyle:{type:Object},defaultStyleValue:{type:Object,default(){return{}}},zooms:{type:Array},opacity:{type:Number},initEvents:{type:Boolean,default:!0},visibleDuration:{type:Number,default:0},onClick:{type:Function,default:null},onMousemove:{type:Function,default:null},onRightclick:{type:Function,default:null}},o),x=["init","mousemove","click","rightclick"];function k(o){return{__layerStyle(c){R(()=>{var d;(d=o.$amapComponent())!=null&&d.setStyle&&o.$amapComponent().setStyle(c)}).then()},__sourceUrl(){R(()=>{o.setSource()}).then()},__sourceData(){R(()=>{o.setSource()}).then()},__geoBufferSource(){R(()=>{o.setSource()}).then()},__visible(c){const d=o.$amapComponent();d!=null&&d.show&&d!=null&&d.hide&&(c?d.show(o.props.visibleDuration):d.hide(o.props.visibleDuration))}}}function P(o){let c=!1,d=!1,s;const{parentInstance:n,$amapComponent:r,emits:a,props:p,setStyle:l}=o,g=()=>{s&&(s.destroy(),s=null),p.geoBufferSource?typeof p.geoBufferSource=="string"?s=new Loca.GeoBufferSource({url:p.geoBufferSource}):s=new Loca.GeoBufferSource({data:p.geoBufferSource}):p.sourceUrl?s=new Loca.GeoJSONSource({url:p.sourceUrl}):p.sourceData?s=new Loca.GeoJSONSource({data:p.sourceData}):s=new Loca.GeoJSONSource({}),r.setSource(s)},y=()=>{p.initEvents&&m()},m=()=>{if(n){const L=n.getMap();p.onClick!==null&&L.on("click",h),p.onMousemove!==null&&(L.on("mousemove",C),L.on("dragstart",i),L.on("dragend",e),L.on("rotatestart",F),L.on("rotateend",z),L.on("mouseout",W)),p.onRightclick!==null&&L.on("rightclick",t)}},h=L=>{const E=f(L);a("click",E,L)},t=L=>{const E=f(L);a("rightclick",E,L)},C=L=>{if(c||d)return;const E=f(L);a("mousemove",E,L)},f=L=>r.queryFeature(L.pixel.toArray()),i=()=>{c=!0},e=()=>{c=!1},W=()=>{c=!1,d=!1},F=()=>{d=!0},z=()=>{d=!1},j=()=>{if(n){const L=n.getMap();L.off("click",h),L.off("rightclick",t),L.off("mousemove",C),L.off("dragstart",i),L.off("dragend",e),L.off("rotatestart",F),L.off("rotateend",z),L.off("mouseout",W)}};return g(),l(),n==null||n.$amapComponent.add(r),y(),{_destroyComponent:()=>{j(),n!=null&&n.isDestroy||(n==null||n.$amapComponent.remove(r),r.destroy()),s&&(s.destroy(),s=null)},setSource:g}}var T=b({name:"ElAmapLocaGrid",inheritAttrs:!1,__name:"GridLayer",props:w({cullface:{type:String},acceptLight:{type:Boolean,default:!0},shininess:{type:Number},hasSide:{type:Boolean,default:!0},depth:{type:Boolean,default:!0}}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.GridLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{topColor:"#fff",sideTopColor:"#fff",sideBottomColor:"#fff",altitude:0,height:0,radius:1e3,gap:0,unit:"meter"},s.defaultStyleValue),C={topColor:(i,e)=>(e.properties=e.properties||{},e.properties.topColor===void 0?t.topColor:e.properties.topColor),sideTopColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideTopColor===void 0?t.sideTopColor:e.properties.sideTopColor),sideBottomColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideBottomColor===void 0?t.sideBottomColor:e.properties.sideBottomColor),altitude:(i,e)=>(e.properties=e.properties||{},e.properties.altitude===void 0?t.altitude:e.properties.altitude),height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),radius:t.radius,gap:t.gap,unit:t.unit},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});T.__file="src/vue-amap-loca/packages/GridLayer/GridLayer.vue",T.install=o=>(o.component(T.name,T),o);const ae=T;var D=b({name:"ElAmapLocaHeatmap",inheritAttrs:!1,__name:"HeatMapLayer",props:w({depth:{type:Boolean,default:!0}}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.HeatMapLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{radius:20,value:10,gradient:{.5:"blue",.65:"rgb(117,211,248)",.7:"rgb(0, 255, 0)",.9:"#ffea00",1:"red"},opacity:[0,1],height:100,heightBezier:[.4,.2,.4,.8],max:null,min:null,unit:"px",difference:!1},s.defaultStyleValue),C={radius:(i,e)=>(e.properties=e.properties||{},e.properties.radius===void 0?t.radius:e.properties.radius),value:(i,e)=>(e.properties=e.properties||{},e.properties.value===void 0?t.value:e.properties.value),gradient:t.gradient,opacity:t.opacity,height:t.height,heightBezier:t.heightBezier,max:t.max,min:t.min,unit:t.unit,difference:t.difference},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});D.__file="src/vue-amap-loca/packages/HeatMapLayer/HeatMapLayer.vue",D.install=o=>(o.component(D.name,D),o);const le=D;var M=b({name:"ElAmapLocaHexagon",inheritAttrs:!1,__name:"HexagonLayer",props:w({cullface:{type:String},acceptLight:{type:Boolean,default:!0},shininess:{type:Number},hasSide:{type:Boolean,default:!0},depth:{type:Boolean,default:!0}}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.HexagonLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{topColor:"#fff",sideTopColor:"#fff",sideBottomColor:"#fff",altitude:0,height:0,radius:1e3,gap:0,unit:"meter"},s.defaultStyleValue),C={topColor:(i,e)=>(e.properties=e.properties||{},e.properties.topColor===void 0?t.topColor:e.properties.topColor),sideTopColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideTopColor===void 0?t.sideTopColor:e.properties.sideTopColor),sideBottomColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideBottomColor===void 0?t.sideBottomColor:e.properties.sideBottomColor),altitude:(i,e)=>(e.properties=e.properties||{},e.properties.altitude===void 0?t.altitude:e.properties.altitude),height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),radius:t.radius,gap:t.gap,unit:t.unit},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});M.__file="src/vue-amap-loca/packages/HexagonLayer/HexagonLayer.vue",M.install=o=>(o.component(M.name,M),o);const ce=M;var H=b({name:"ElAmapLocaIcon",inheritAttrs:!1,__name:"IconLayer",props:w({}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.IconLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{unit:"px",icon:"",iconSize:[20,20],rotation:0,opacity:1,offset:[0,0]},s.defaultStyleValue),C={unit:t.unit,icon:(i,e)=>(e.properties=e.properties||{},e.properties.icon===void 0?t.icon:e.properties.icon),iconSize:(i,e)=>(e.properties=e.properties||{},e.properties.iconSize===void 0?t.iconSize:e.properties.iconSize),rotation:(i,e)=>(e.properties=e.properties||{},e.properties.rotation===void 0?t.rotation:e.properties.rotation),opacity:(i,e)=>(e.properties=e.properties||{},e.properties.opacity===void 0?t.opacity:e.properties.opacity),offset:(i,e)=>(e.properties=e.properties||{},e.properties.offset===void 0?t.offset:e.properties.offset)},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});H.__file="src/vue-amap-loca/packages/IconLayer/IconLayer.vue",H.install=o=>(o.component(H.name,H),o);const de=H;var N=b({name:"ElAmapLocaLine",inheritAttrs:!1,__name:"LineLayer",props:w({}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.LineLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{color:"#fff",lineWidth:2,altitude:0,borderWidth:0,borderColor:"#fff",dashArray:[10,0,10,0]},s.defaultStyleValue),C=Object.assign({},{color:(f,i)=>(i.properties=i.properties||{},i.properties.color===void 0?t.color:i.properties.color),lineWidth:(f,i)=>(i.properties=i.properties||{},i.properties.lineWidth===void 0?t.lineWidth:i.properties.lineWidth),altitude:(f,i)=>(i.properties=i.properties||{},i.properties.altitude===void 0?t.altitude:i.properties.altitude),borderWidth:(f,i)=>(i.properties=i.properties||{},i.properties.borderWidth===void 0?t.borderWidth:i.properties.borderWidth),borderColor:(f,i)=>(i.properties=i.properties||{},i.properties.borderColor===void 0?t.borderColor:i.properties.borderColor),dashArray:(f,i)=>(i.properties=i.properties||{},i.properties.dashArray===void 0?t.dashArray:i.properties.dashArray)},s.layerStyle);r.setStyle(C)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});N.__file="src/vue-amap-loca/packages/LineLayer/LineLayer.vue",N.install=o=>(o.component(N.name,N),o);const ue=N;var V=b({name:"ElAmapLocaLink",inheritAttrs:!1,__name:"LinkLayer",props:w({}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.LinkLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{lineColors:["rgba(255,255,255,1)","rgba(255,255,255,0)"],height:100,smoothSteps:100},s.defaultStyleValue),C=Object.assign({},{lineColors:(f,i)=>(i.properties=i.properties||{},i.properties.lineColors===void 0?t.lineColors:i.properties.lineColors),height:(f,i)=>(i.properties=i.properties||{},i.properties.height===void 0?t.height:i.properties.height),smoothSteps:(f,i)=>(i.properties=i.properties||{},i.properties.smoothSteps===void 0?t.smoothSteps:i.properties.smoothSteps)},s.layerStyle);r.setStyle(C)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});V.__file="src/vue-amap-loca/packages/LinkLayer/LinkLayer.vue",V.install=o=>(o.component(V.name,V),o);const me=V;var G=b({name:"ElAmapLoca",inheritAttrs:!1,__name:"Loca",props:w({ambLight:{type:Object},dirLight:{type:Object},pointLight:{type:Object},onClick:{type:Function,default:null},onMousemove:{type:Function,default:null},onRightclick:{type:Function,default:null},eventOptions:{type:Object,default:()=>({hitFirst:!0})}}),emits:x,setup(o,{expose:c,emit:d}){const s=[],n={$amapComponent:void 0,addChildComponent(v){s.push(v)},isDestroy:!1,getMap:()=>t()};Te(ne,n);const r=o,a=d;let p,l=!1,g=!1,y=!0;const{$$getInstance:m,parentInstance:h}=I((v,B)=>new Promise(u=>{p=new Loca.Container({map:B}),n.$amapComponent=p,v.ambLight&&(p.ambLight=v.ambLight),v.dirLight&&(p.dirLight=v.dirLight),v.pointLight&&(p.pointLight=v.pointLight),y=v.eventOptions.hitFirst,C(),u(p)}),{emits:a,needInitComponents:s,provideData:n,destroyComponent(){p&&(re(),p.animate&&p.animate.stop&&p.animate.stop(),p.destroy(),p=null)}}),t=()=>h==null?void 0:h.$amapComponent,C=()=>{if(h){const v=t();r.onClick!==null&&v.on("click",f),r.onMousemove!==null&&(v.on("mousemove",e),v.on("dragstart",F),v.on("dragend",z),v.on("rotatestart",L),v.on("rotateend",E),v.on("mouseout",j)),r.onRightclick!==null&&v.on("rightclick",i)}},f=v=>{const B=W(v);a("click",B,v)},i=v=>{const B=W(v);a("rightclick",B,v)},e=v=>{if(l||g)return;const B=W(v);a("mousemove",B,v)},W=v=>{const B=[];if(p.layers){const u=[];p.layers.forEach(A=>{u.push(A)}),u.sort((A,O)=>O.zIndex-A.zIndex);const S=u.length;for(let A=0;A<S;A++){const O=u[A].queryFeature(v.pixel.toArray());if(O&&(B.push(O),y))break}}return B},F=()=>{l=!0},z=()=>{l=!1},j=()=>{l=!1,g=!1},L=()=>{g=!0},E=()=>{g=!1},re=()=>{if(h){const v=t();v.off("click",f),v.off("rightclick",i),v.off("mousemove",e),v.off("dragstart",F),v.off("dragend",z),v.off("rotatestart",L),v.off("rotateend",E),v.off("mouseout",j)}};return c({$$getInstance:m}),(v,B)=>($(),_("div",null,[De(v.$slots,"default")]))}});G.__file="src/vue-amap-loca/packages/Loca/Loca.vue",G.install=o=>(o.component(G.name,G),o);const he=G;var U=b({name:"ElAmapLocaPoint",inheritAttrs:!1,__name:"PointLayer",props:w({blend:{type:String}}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.PointLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{radius:20,color:"#fff",unit:"px",borderWidth:10,borderColor:"#fff",blurWidth:-1},s.defaultStyleValue),C={radius:(i,e)=>(e.properties=e.properties||{},e.properties.radius===void 0?t.radius:e.properties.radius),color:(i,e)=>(e.properties=e.properties||{},e.properties.color===void 0?t.color:e.properties.color),unit:t.unit,borderWidth:(i,e)=>(e.properties=e.properties||{},e.properties.borderWidth===void 0?t.borderWidth:e.properties.borderWidth),borderColor:(i,e)=>(e.properties=e.properties||{},e.properties.borderColor===void 0?t.borderColor:e.properties.borderColor),blurWidth:(i,e)=>(e.properties=e.properties||{},e.properties.blurWidth===void 0?t.blurWidth:e.properties.blurWidth)},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});U.__file="src/vue-amap-loca/packages/PointLayer/PointLayer.vue",U.install=o=>(o.component(U.name,U),o);const ye=U;var Z=b({name:"ElAmapLocaPolygon",inheritAttrs:!1,__name:"PolygonLayer",props:w({cullface:{type:String},acceptLight:{type:Boolean,default:!0},shininess:{type:Number},hasSide:{type:Boolean,default:!0},hasBottom:{type:Boolean,default:!1},blockHide:{type:Boolean,default:!0},depth:{type:Boolean,default:!0}}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.PolygonLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{topColor:"#fff",sideTopColor:"#fff",sideBottomColor:"#fff",altitude:0,height:0,texture:null,textureSize:[20,3],label:void 0,labelAltitude:0},s.defaultStyleValue),C={topColor:(i,e)=>(e.properties=e.properties||{},e.properties.topColor===void 0?t.topColor:e.properties.topColor),sideTopColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideTopColor===void 0?t.sideTopColor:e.properties.sideTopColor),sideBottomColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideBottomColor===void 0?t.sideBottomColor:e.properties.sideBottomColor),altitude:(i,e)=>(e.properties=e.properties||{},e.properties.altitude===void 0?t.altitude:e.properties.altitude),height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),texture:t.texture,textureSize:(i,e)=>(e.properties=e.properties||{},e.properties.textureSize===void 0?t.textureSize:e.properties.textureSize),label:(i,e)=>(e.properties=e.properties||{},e.properties.label===void 0?t.label:e.properties.label),labelAltitude:(i,e)=>(e.properties=e.properties||{},e.properties.labelAltitude===void 0?t.labelAltitude:e.properties.labelAltitude)},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});Z.__file="src/vue-amap-loca/packages/PolygonLayer/PolygonLayer.vue",Z.install=o=>(o.component(Z.name,Z),o);const ge=Z;var q=b({name:"ElAmapLocaPrism",inheritAttrs:!1,__name:"PrismLayer",props:w({cullface:{type:String},acceptLight:{type:Boolean,default:!0},shininess:{type:Number},hasSide:{type:Boolean,default:!0},depth:{type:Boolean,default:!0}}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.PrismLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{radius:20,unit:"px",sideNumber:3,rotation:0,altitude:0,height:100,topColor:"#fff",sideTopColor:"#fff",sideBottomColor:"#fff"},s.defaultStyleValue),C={radius:t.radius,unit:t.unit,sideNumber:t.sideNumber,rotation:(i,e)=>(e.properties=e.properties||{},e.properties.rotation===void 0?t.rotation:e.properties.rotation),altitude:t.altitude,height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),topColor:(i,e)=>(e.properties=e.properties||{},e.properties.topColor===void 0?t.topColor:e.properties.topColor),sideTopColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideTopColor===void 0?t.sideTopColor:e.properties.sideTopColor),sideBottomColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideBottomColor===void 0?t.sideBottomColor:e.properties.sideBottomColor)},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});q.__file="src/vue-amap-loca/packages/PrismLayer/PrismLayer.vue",q.install=o=>(o.component(q.name,q),o);const fe=q;var J=b({name:"ElAmapLocaPulseLine",inheritAttrs:!1,__name:"PulseLineLayer",props:w({depth:{type:Boolean,default:!0}}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.PulseLineLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{lineWidth:1,headColor:"rgba(0, 0, 0, 0.75)",trailColor:"rgba(0, 0, 0, 0.25)",altitude:0,interval:1,duration:2e3},s.defaultStyleValue),C={lineWidth:(i,e)=>(e.properties=e.properties||{},e.properties.lineWidth===void 0?t.lineWidth:e.properties.lineWidth),headColor:(i,e)=>(e.properties=e.properties||{},e.properties.headColor===void 0?t.headColor:e.properties.headColor),trailColor:(i,e)=>(e.properties=e.properties||{},e.properties.trailColor===void 0?t.trailColor:e.properties.trailColor),altitude:t.altitude,interval:t.interval,duration:t.duration},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});J.__file="src/vue-amap-loca/packages/PulseLineLayer/PulseLineLayer.vue",J.install=o=>(o.component(J.name,J),o);const ve=J;var K=b({name:"ElAmapLocaPulseLink",inheritAttrs:!1,__name:"PulseLinkLayer",props:w({depth:{type:Boolean,default:!0}}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.PulseLinkLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{lineColors:["#fff"],height:100,maxHeightScale:0,smoothSteps:50,lineWidth:[1,1],unit:"px",dash:[4e3,0,4e3,0],speed:100,headColor:"rgba(0, 0, 0, 0.75)",trailColor:"rgba(0, 0, 0, 0.25)",flowLength:100},s.defaultStyleValue),C={lineColors:(i,e)=>(e.properties=e.properties||{},e.properties.lineColors===void 0?t.lineColors:e.properties.lineColors),height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),maxHeightScale:(i,e)=>(e.properties=e.properties||{},e.properties.maxHeightScale===void 0?t.maxHeightScale:e.properties.maxHeightScale),smoothSteps:(i,e)=>(e.properties=e.properties||{},e.properties.smoothSteps===void 0?t.smoothSteps:e.properties.smoothSteps),lineWidth:(i,e)=>(e.properties=e.properties||{},e.properties.lineWidth===void 0?t.lineWidth:e.properties.lineWidth),unit:t.unit,dash:(i,e)=>(e.properties=e.properties||{},e.properties.dash===void 0?t.dash:e.properties.dash),speed:(i,e)=>(e.properties=e.properties||{},e.properties.speed===void 0?t.speed:e.properties.speed),headColor:(i,e)=>(e.properties=e.properties||{},e.properties.headColor===void 0?t.headColor:e.properties.headColor),trailColor:(i,e)=>(e.properties=e.properties||{},e.properties.trailColor===void 0?t.trailColor:e.properties.trailColor),flowLength:t.flowLength},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});K.__file="src/vue-amap-loca/packages/PulseLinkLayer/PulseLinkLayer.vue",K.install=o=>(o.component(K.name,K),o);const Ce=K;var Q=b({name:"ElAmapLocaScatter",inheritAttrs:!1,__name:"ScatterLayer",props:w({}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.ScatterLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{size:[20,20],rotation:0,color:"rgba(200,200,200,1)",altitude:0,borderWidth:0,borderColor:"rgba(250,250,250,1)",texture:null,unit:"px",animate:!1,duration:0},s.defaultStyleValue),C={size:(i,e)=>(e.properties=e.properties||{},e.properties.size===void 0?t.size:e.properties.size),rotation:(i,e)=>(e.properties=e.properties||{},e.properties.rotation===void 0?t.rotation:e.properties.rotation),color:(i,e)=>(e.properties=e.properties||{},e.properties.color===void 0?t.color:e.properties.color),altitude:(i,e)=>(e.properties=e.properties||{},e.properties.altitude===void 0?t.altitude:e.properties.altitude),borderWidth:(i,e)=>(e.properties=e.properties||{},e.properties.borderWidth===void 0?t.borderWidth:e.properties.borderWidth),borderColor:(i,e)=>(e.properties=e.properties||{},e.properties.borderColor===void 0?t.borderColor:e.properties.borderColor),texture:t.texture,unit:t.unit,animate:t.animate,duration:t.duration},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});Q.__file="src/vue-amap-loca/packages/ScatterLayer/ScatterLayer.vue",Q.install=o=>(o.component(Q.name,Q),o);const Le=Q;var X=b({name:"ElAmapLocaZMarker",inheritAttrs:!1,__name:"ZMarkerLayer",props:w({}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.ZMarkerLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{unit:"px",content:"",size:[20,20],rotation:0,alwaysFront:!1,altitude:0},s.defaultStyleValue),C={unit:t.unit,content:(i,e)=>(e.properties=e.properties||{},e.properties.content===void 0?t.content:e.properties.content),size:(i,e)=>(e.properties=e.properties||{},e.properties.size===void 0?t.size:e.properties.size),rotation:(i,e)=>(e.properties=e.properties||{},e.properties.rotation===void 0?t.rotation:e.properties.rotation),alwaysFront:t.alwaysFront,altitude:(i,e)=>(e.properties=e.properties||{},e.properties.altitude===void 0?t.altitude:e.properties.altitude)},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});X.__file="src/vue-amap-loca/packages/ZMarkerLayer/ZMarkerLayer.vue",X.install=o=>(o.component(X.name,X),o);const Se=X;var Y=b({name:"ElAmapLocaAmbientLight",inheritAttrs:!1,__name:"AmbientLight",props:ie({color:{type:String},intensity:{type:Number}}),emits:["init"],setup(o,{expose:c,emit:d}){const s=d;let n;const{$$getInstance:r,parentInstance:a}=I((p,l)=>new Promise(g=>{n=new Loca.AmbientLight(p),l.addLight(n),g(n)}),{emits:s,destroyComponent(){n&&a!=null&&a.$amapComponent&&(a.isDestroy||a.$amapComponent.removeLight(n),n=null)}});return c({$$getInstance:r}),(p,l)=>($(),_("div"))}});Y.__file="src/vue-amap-loca/packages/AmbientLight/AmbientLight.vue",Y.install=o=>(o.component(Y.name,Y),o);const be=Y;var ee=b({name:"ElAmapLocaDirectionalLight",inheritAttrs:!1,__name:"DirectionalLight",props:ie({color:{type:String},intensity:{type:Number},position:{type:Array,required:!0},target:{type:Array}}),emits:["init"],setup(o,{expose:c,emit:d}){const s=d;let n;const{$$getInstance:r,parentInstance:a}=I((p,l)=>new Promise(g=>{n=new Loca.DirectionalLight(p),l.addLight(n),g(n)}),{emits:s,destroyComponent(){n&&a!=null&&a.$amapComponent&&(a.isDestroy||a.$amapComponent.removeLight(n),n=null)}});return c({$$getInstance:r}),(p,l)=>($(),_("div"))}});ee.__file="src/vue-amap-loca/packages/DirectionalLight/DirectionalLight.vue",ee.install=o=>(o.component(ee.name,ee),o);const $e=ee;var te=b({name:"ElAmapLocaPointLight",inheritAttrs:!1,__name:"PointLight",props:ie({color:{type:String},intensity:{type:Number},position:{type:Array,required:!0},distance:{type:Number}}),emits:["init"],setup(o,{expose:c,emit:d}){const s=d;let n;const{$$getInstance:r,parentInstance:a}=I((p,l)=>new Promise(g=>{n=new Loca.PointLight(p),l.addLight(n),g(n)}),{emits:s,destroyComponent(){n&&a!=null&&a.$amapComponent&&(a.isDestroy||a.$amapComponent.removeLight(n),n=null)}});return c({$$getInstance:r}),(p,l)=>($(),_("div"))}});te.__file="src/vue-amap-loca/packages/PointLight/PointLight.vue",te.install=o=>(o.component(te.name,te),o);const _e=te;var oe=b({name:"ElAmapLocaLaser",inheritAttrs:!1,__name:"LaserLayer",props:w({depth:{type:Boolean,default:!0}}),emits:x,setup(o,{expose:c,emit:d}){const s=o,n=d;let r,a,p;const{$$getInstance:l,parentInstance:g}=I(y=>new Promise(m=>{r=new Loca.LaserLayer(y);const h=P({parentInstance:g,$amapComponent:r,emits:n,props:s,setStyle(){const t=Object.assign({},{unit:"px",height:200,color:"rgba(255,255,0,0.5)",angle:0,lineWidth:2,trailLength:30,duration:2e3,interval:0,delay:0,repeat:void 0},s.defaultStyleValue),C={unit:t.unit,height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),color:(i,e)=>(e.properties=e.properties||{},e.properties.color===void 0?t.color:e.properties.color),angle:t.angle,lineWidth:t.lineWidth,trailLength:t.trailLength,duration:(i,e)=>(e.properties=e.properties||{},e.properties.duration===void 0?t.duration:e.properties.duration),interval:(i,e)=>(e.properties=e.properties||{},e.properties.interval===void 0?t.interval:e.properties.interval),delay:(i,e)=>(e.properties=e.properties||{},e.properties.delay===void 0?t.delay:e.properties.delay),repeat:t.repeat},f=Object.assign({},C,s.layerStyle);r.setStyle(f)}});a=h._destroyComponent,p=h.setSource,m(r)}),{emits:n,watchRedirectFn:k({setSource(){p&&p()},$amapComponent:()=>r,props:s}),destroyComponent(){a&&a(),r=null}});return c({$$getInstance:l}),(y,m)=>($(),_("div"))}});oe.__file="src/vue-amap-loca/packages/LaserLayer/LaserLayer.vue",oe.install=o=>(o.component(oe.name,oe),o);const Ie=oe;var Ze=[ae,le,ce,de,ue,me,he,ye,ge,fe,ve,Ce,Le,Se,be,$e,_e,Ie],Ae=Me([...Ze]);const qe=Ae.install;export{he as ElAmapLoca,be as ElAmapLocaAmbientLight,$e as ElAmapLocaDirectionalLight,ae as ElAmapLocaGrid,le as ElAmapLocaHeatmap,ce as ElAmapLocaHexagon,de as ElAmapLocaIcon,Ie as ElAmapLocaLaser,ue as ElAmapLocaLine,me as ElAmapLocaLink,ye as ElAmapLocaPoint,_e as ElAmapLocaPointLight,ge as ElAmapLocaPolygon,fe as ElAmapLocaPrism,ve as ElAmapLocaPulseLine,Ce as ElAmapLocaPulseLink,Le as ElAmapLocaScatter,Se as ElAmapLocaZMarker,Ae as default,qe as install};
//# sourceMappingURL=index.es.min.js.map
