export interface GeolocationPromise {
    /**
     * 获取 用户的精确位置，有失败几率
     * @return {Promise<CurrentPositionResult>}
     */
    getCurrentPosition: () => Promise<AMap.CurrentPositionResult>;
    /**
     * 根据用户 IP 获取 用户所在城市信息
     * @return {Promise<CurrentPositionResult>}
     */
    getCityInfo: () => Promise<AMap.CurrentPositionResult>;
}
/**
 * Geolocation插件getCurrentPosition方法返回的正确数据
 */
/**
 * 定位插件hook，提供常用的基于浏览器定位的方法，和基于城市定位的方法
 * @param {GeolocationOptions} [options] 定位插件参数
 */
export declare function useGeolocation(options?: AMap.GeolocationOptions): Promise<GeolocationPromise>;
