import {
  buildProps,
  commonProps,
  makeInstaller,
  provide<PERSON><PERSON>,
  useRegister
} from "./chunk-EPFRRRX4.js";
import "./chunk-RHQEB4B4.js";
import {
  createElementBlock,
  defineComponent,
  nextTick,
  openBlock,
  provide,
  renderSlot
} from "./chunk-YHJVOVJ5.js";
import "./chunk-5WWUZCGV.js";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/utils/buildHelper.mjs
var buildLocaProps = (props) => {
  return Object.assign({}, commonProps, {
    sourceUrl: {
      type: String
    },
    sourceData: {
      type: Object
    },
    geoBufferSource: {
      type: [ArrayBuffer, String],
      default() {
        return null;
      }
    },
    layerStyle: {
      type: Object
    },
    defaultStyleValue: {
      type: Object,
      default() {
        return {};
      }
    },
    zooms: {
      type: Array
    },
    opacity: {
      type: Number
    },
    initEvents: {
      type: Boolean,
      default: true
    },
    visibleDuration: {
      type: Number,
      default: 0
    },
    onClick: {
      type: Function,
      default: null
    },
    onMousemove: {
      type: Function,
      default: null
    },
    onRightclick: {
      type: Function,
      default: null
    }
  }, props);
};
var commonEmitNames = ["init", "mousemove", "click", "rightclick"];

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/mixins/useLoca.mjs
function useWatchFn(options) {
  return {
    __layerStyle(style) {
      nextTick(() => {
        var _a;
        if ((_a = options.$amapComponent()) == null ? void 0 : _a.setStyle) {
          options.$amapComponent().setStyle(style);
        }
      }).then();
    },
    __sourceUrl() {
      nextTick(() => {
        options.setSource();
      }).then();
    },
    __sourceData() {
      nextTick(() => {
        options.setSource();
      }).then();
    },
    __geoBufferSource() {
      nextTick(() => {
        options.setSource();
      }).then();
    },
    __visible(flag) {
      const $amapComponent = options.$amapComponent();
      if (($amapComponent == null ? void 0 : $amapComponent.show) && ($amapComponent == null ? void 0 : $amapComponent.hide)) {
        !flag ? $amapComponent.hide(options.props.visibleDuration) : $amapComponent.show(options.props.visibleDuration);
      }
    }
  };
}
function useLocaEvents(options) {
  let isDragging = false;
  let isRotating = false;
  let source;
  const { parentInstance, $amapComponent, emits, props, setStyle } = options;
  const setSource = () => {
    if (source) {
      source.destroy();
      source = null;
    }
    if (props.geoBufferSource) {
      if (typeof props.geoBufferSource === "string") {
        source = new Loca.GeoBufferSource({
          url: props.geoBufferSource
        });
      } else {
        source = new Loca.GeoBufferSource({
          data: props.geoBufferSource
        });
      }
    } else if (props.sourceUrl) {
      source = new Loca.GeoJSONSource({
        url: props.sourceUrl
      });
    } else if (props.sourceData) {
      source = new Loca.GeoJSONSource({
        data: props.sourceData
      });
    } else {
      source = new Loca.GeoJSONSource({});
    }
    $amapComponent.setSource(source);
  };
  const initComplete = () => {
    if (props.initEvents) {
      bindEvents();
    }
  };
  const bindEvents = () => {
    if (parentInstance) {
      const map = parentInstance.getMap();
      if (props.onClick !== null) {
        map.on("click", clickMap);
      }
      if (props.onMousemove !== null) {
        map.on("mousemove", mouseMoveMap);
        map.on("dragstart", dragStart);
        map.on("dragend", dragEnd);
        map.on("rotatestart", rotateStart);
        map.on("rotateend", rotateEnd);
        map.on("mouseout", mouseoutMap);
      }
      if (props.onRightclick !== null) {
        map.on("rightclick", rightclickMap);
      }
    }
  };
  const clickMap = (e) => {
    const feature = _getFeature(e);
    emits("click", feature, e);
  };
  const rightclickMap = (e) => {
    const feature = _getFeature(e);
    emits("rightclick", feature, e);
  };
  const mouseMoveMap = (e) => {
    if (isDragging || isRotating) {
      return;
    }
    const feature = _getFeature(e);
    emits("mousemove", feature, e);
  };
  const _getFeature = (e) => {
    return $amapComponent.queryFeature(e.pixel.toArray());
  };
  const dragStart = () => {
    isDragging = true;
  };
  const dragEnd = () => {
    isDragging = false;
  };
  const mouseoutMap = () => {
    isDragging = false;
    isRotating = false;
  };
  const rotateStart = () => {
    isRotating = true;
  };
  const rotateEnd = () => {
    isRotating = false;
  };
  const unBindEvents = () => {
    if (parentInstance) {
      const map = parentInstance.getMap();
      map.off("click", clickMap);
      map.off("rightclick", rightclickMap);
      map.off("mousemove", mouseMoveMap);
      map.off("dragstart", dragStart);
      map.off("dragend", dragEnd);
      map.off("rotatestart", rotateStart);
      map.off("rotateend", rotateEnd);
      map.off("mouseout", mouseoutMap);
    }
  };
  setSource();
  setStyle();
  parentInstance == null ? void 0 : parentInstance.$amapComponent.add($amapComponent);
  initComplete();
  const _destroyComponent = () => {
    unBindEvents();
    if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
      parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
      $amapComponent.destroy();
    }
    if (source) {
      source.destroy();
      source = null;
    }
  };
  return {
    _destroyComponent,
    setSource
  };
}

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/GridLayer/GridLayer.vue2.mjs
var script = defineComponent({
  ...{
    name: "ElAmapLocaGrid",
    inheritAttrs: false
  },
  __name: "GridLayer",
  props: buildLocaProps({
    // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。
    cullface: {
      type: String
    },
    // 面是否接受光照，光照信息在 loca 对象中配置
    acceptLight: {
      type: Boolean,
      default: true
    },
    // 立体网格的粗糙度，值越高，说明表面越粗糙。
    shininess: {
      type: Number
    },
    // 当面有厚度的时候，有没有侧面和底面
    hasSide: {
      type: Boolean,
      default: true
    },
    // 是否开启深度检测，开启后可能会影响zIndex
    depth: {
      type: Boolean,
      default: true
    }
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.GridLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              topColor: "#fff",
              sideTopColor: "#fff",
              sideBottomColor: "#fff",
              altitude: 0,
              height: 0,
              radius: 1e3,
              gap: 0,
              unit: "meter"
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              topColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.topColor === void 0 ? style.topColor : feature.properties.topColor;
              },
              sideTopColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideTopColor === void 0 ? style.sideTopColor : feature.properties.sideTopColor;
              },
              sideBottomColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideBottomColor === void 0 ? style.sideBottomColor : feature.properties.sideBottomColor;
              },
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              },
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              radius: style.radius,
              gap: style.gap,
              unit: style.unit
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/GridLayer/GridLayer.vue.mjs
script.__file = "src/vue-amap-loca/packages/GridLayer/GridLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/GridLayer/index.mjs
script.install = (app) => {
  app.component(script.name, script);
  return app;
};
var ElAmapLocaGrid = script;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/HeatMapLayer/HeatMapLayer.vue2.mjs
var script2 = defineComponent({
  ...{
    name: "ElAmapLocaHeatmap",
    inheritAttrs: false
  },
  __name: "HeatMapLayer",
  props: buildLocaProps({
    depth: {
      type: Boolean,
      default: true
    }
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.HeatMapLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              radius: 20,
              value: 10,
              gradient: { 0.5: "blue", 0.65: "rgb(117,211,248)", 0.7: "rgb(0, 255, 0)", 0.9: "#ffea00", 1: "red" },
              opacity: [0, 1],
              height: 100,
              heightBezier: [0.4, 0.2, 0.4, 0.8],
              max: null,
              min: null,
              unit: "px",
              difference: false
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              radius: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.radius === void 0 ? style.radius : feature.properties.radius;
              },
              value: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.value === void 0 ? style.value : feature.properties.value;
              },
              gradient: style.gradient,
              opacity: style.opacity,
              height: style.height,
              heightBezier: style.heightBezier,
              max: style.max,
              min: style.min,
              unit: style.unit,
              difference: style.difference
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/HeatMapLayer/HeatMapLayer.vue.mjs
script2.__file = "src/vue-amap-loca/packages/HeatMapLayer/HeatMapLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/HeatMapLayer/index.mjs
script2.install = (app) => {
  app.component(script2.name, script2);
  return app;
};
var ElAmapLocaHeatmap = script2;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/HexagonLayer/HexagonLayer.vue2.mjs
var script3 = defineComponent({
  ...{
    name: "ElAmapLocaHexagon",
    inheritAttrs: false
  },
  __name: "HexagonLayer",
  props: buildLocaProps({
    cullface: {
      type: String
    },
    // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。
    acceptLight: {
      type: Boolean,
      default: true
    },
    // 面是否接受光照，光照信息在 loca 对象中配置
    shininess: {
      type: Number
    },
    // 立体网格的粗糙度，值越高，说明表面越粗糙。
    hasSide: {
      type: Boolean,
      default: true
    },
    // 当面有厚度的时候，有没有侧面和底面
    depth: {
      type: Boolean,
      default: true
    }
    // 是否开启深度检测，开启后可能会影响zIndex
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.HexagonLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              topColor: "#fff",
              sideTopColor: "#fff",
              sideBottomColor: "#fff",
              altitude: 0,
              height: 0,
              radius: 1e3,
              gap: 0,
              unit: "meter"
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              topColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.topColor === void 0 ? style.topColor : feature.properties.topColor;
              },
              sideTopColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideTopColor === void 0 ? style.sideTopColor : feature.properties.sideTopColor;
              },
              sideBottomColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideBottomColor === void 0 ? style.sideBottomColor : feature.properties.sideBottomColor;
              },
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              },
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              radius: style.radius,
              gap: style.gap,
              unit: style.unit
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/HexagonLayer/HexagonLayer.vue.mjs
script3.__file = "src/vue-amap-loca/packages/HexagonLayer/HexagonLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/HexagonLayer/index.mjs
script3.install = (app) => {
  app.component(script3.name, script3);
  return app;
};
var ElAmapLocaHexagon = script3;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/IconLayer/IconLayer.vue2.mjs
var script4 = defineComponent({
  ...{
    name: "ElAmapLocaIcon",
    inheritAttrs: false
  },
  __name: "IconLayer",
  props: buildLocaProps({}),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.IconLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              unit: "px",
              icon: "",
              iconSize: [20, 20],
              rotation: 0,
              opacity: 1,
              offset: [0, 0]
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              unit: style.unit,
              icon: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.icon === void 0 ? style.icon : feature.properties.icon;
              },
              iconSize: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.iconSize === void 0 ? style.iconSize : feature.properties.iconSize;
              },
              rotation: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.rotation === void 0 ? style.rotation : feature.properties.rotation;
              },
              opacity: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.opacity === void 0 ? style.opacity : feature.properties.opacity;
              },
              offset: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.offset === void 0 ? style.offset : feature.properties.offset;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/IconLayer/IconLayer.vue.mjs
script4.__file = "src/vue-amap-loca/packages/IconLayer/IconLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/IconLayer/index.mjs
script4.install = (app) => {
  app.component(script4.name, script4);
  return app;
};
var ElAmapLocaIcon = script4;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LineLayer/LineLayer.vue2.mjs
var script5 = defineComponent({
  ...{
    name: "ElAmapLocaLine",
    inheritAttrs: false
  },
  __name: "LineLayer",
  props: buildLocaProps({}),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.LineLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              color: "#fff",
              lineWidth: 2,
              altitude: 0,
              borderWidth: 0,
              borderColor: "#fff",
              dashArray: [10, 0, 10, 0]
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              color: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.color === void 0 ? style.color : feature.properties.color;
              },
              lineWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.lineWidth === void 0 ? style.lineWidth : feature.properties.lineWidth;
              },
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              },
              borderWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderWidth === void 0 ? style.borderWidth : feature.properties.borderWidth;
              },
              borderColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderColor === void 0 ? style.borderColor : feature.properties.borderColor;
              },
              dashArray: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.dashArray === void 0 ? style.dashArray : feature.properties.dashArray;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LineLayer/LineLayer.vue.mjs
script5.__file = "src/vue-amap-loca/packages/LineLayer/LineLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LineLayer/index.mjs
script5.install = (app) => {
  app.component(script5.name, script5);
  return app;
};
var ElAmapLocaLine = script5;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LinkLayer/LinkLayer.vue2.mjs
var script6 = defineComponent({
  ...{
    name: "ElAmapLocaLink",
    inheritAttrs: false
  },
  __name: "LinkLayer",
  props: buildLocaProps({}),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.LinkLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              lineColors: ["rgba(255,255,255,1)", "rgba(255,255,255,0)"],
              height: 100,
              smoothSteps: 100
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              lineColors: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.lineColors === void 0 ? style.lineColors : feature.properties.lineColors;
              },
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              smoothSteps: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.smoothSteps === void 0 ? style.smoothSteps : feature.properties.smoothSteps;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LinkLayer/LinkLayer.vue.mjs
script6.__file = "src/vue-amap-loca/packages/LinkLayer/LinkLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LinkLayer/index.mjs
script6.install = (app) => {
  app.component(script6.name, script6);
  return app;
};
var ElAmapLocaLink = script6;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/Loca/Loca.vue2.mjs
var script7 = defineComponent({
  ...{
    name: "ElAmapLoca",
    inheritAttrs: false
  },
  __name: "Loca",
  props: buildLocaProps({
    ambLight: {
      type: Object
    },
    // 环境光
    dirLight: {
      type: Object
    },
    // 平行光
    pointLight: {
      type: Object
    },
    // 点光
    onClick: {
      type: Function,
      default: null
    },
    onMousemove: {
      type: Function,
      default: null
    },
    onRightclick: {
      type: Function,
      default: null
    },
    eventOptions: {
      type: Object,
      default: () => ({
        hitFirst: true
      })
    }
    // 对于事件是否只触发第一个选中的数据
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const needInitComponents = [];
    const provideData = {
      $amapComponent: void 0,
      addChildComponent(cb) {
        needInitComponents.push(cb);
      },
      isDestroy: false,
      getMap: () => {
        return getMap();
      }
    };
    provide(provideKey, provideData);
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let isDragging = false;
    let isRotating = false;
    let hitFirst = true;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.Container({
          map: parentComponent
        });
        provideData.$amapComponent = $amapComponent;
        if (options.ambLight) {
          $amapComponent.ambLight = options.ambLight;
        }
        if (options.dirLight) {
          $amapComponent.dirLight = options.dirLight;
        }
        if (options.pointLight) {
          $amapComponent.pointLight = options.pointLight;
        }
        hitFirst = options.eventOptions.hitFirst;
        bindEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      needInitComponents,
      provideData,
      destroyComponent() {
        if ($amapComponent) {
          unBindEvents();
          if ($amapComponent.animate && $amapComponent.animate.stop) {
            $amapComponent.animate.stop();
          }
          $amapComponent.destroy();
          $amapComponent = null;
        }
      }
    });
    const getMap = () => {
      return parentInstance == null ? void 0 : parentInstance.$amapComponent;
    };
    const bindEvents = () => {
      if (parentInstance) {
        const map = getMap();
        if (props.onClick !== null) {
          map.on("click", clickMap);
        }
        if (props.onMousemove !== null) {
          map.on("mousemove", mouseMoveMap);
          map.on("dragstart", dragStart);
          map.on("dragend", dragEnd);
          map.on("rotatestart", rotateStart);
          map.on("rotateend", rotateEnd);
          map.on("mouseout", mouseoutMap);
        }
        if (props.onRightclick !== null) {
          map.on("rightclick", rightclickMap);
        }
      }
    };
    const clickMap = (e) => {
      const features = _getFeature(e);
      emits("click", features, e);
    };
    const rightclickMap = (e) => {
      const features = _getFeature(e);
      emits("rightclick", features, e);
    };
    const mouseMoveMap = (e) => {
      if (isDragging || isRotating) {
        return;
      }
      const features = _getFeature(e);
      emits("mousemove", features, e);
    };
    const _getFeature = (e) => {
      const features = [];
      if ($amapComponent.layers) {
        const layers = [];
        $amapComponent.layers.forEach((v) => {
          layers.push(v);
        });
        layers.sort((a, b) => b.zIndex - a.zIndex);
        const layerLen = layers.length;
        for (let i = 0; i < layerLen; i++) {
          const temp = layers[i].queryFeature(e.pixel.toArray());
          if (temp) {
            features.push(temp);
            if (hitFirst) {
              break;
            }
          }
        }
      }
      return features;
    };
    const dragStart = () => {
      isDragging = true;
    };
    const dragEnd = () => {
      isDragging = false;
    };
    const mouseoutMap = () => {
      isDragging = false;
      isRotating = false;
    };
    const rotateStart = () => {
      isRotating = true;
    };
    const rotateEnd = () => {
      isRotating = false;
    };
    const unBindEvents = () => {
      if (parentInstance) {
        const map = getMap();
        map.off("click", clickMap);
        map.off("rightclick", rightclickMap);
        map.off("mousemove", mouseMoveMap);
        map.off("dragstart", dragStart);
        map.off("dragend", dragEnd);
        map.off("rotatestart", rotateStart);
        map.off("rotateend", rotateEnd);
        map.off("mouseout", mouseoutMap);
      }
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", null, [
        renderSlot(_ctx.$slots, "default")
      ]);
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/Loca/Loca.vue.mjs
script7.__file = "src/vue-amap-loca/packages/Loca/Loca.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/Loca/index.mjs
script7.install = (app) => {
  app.component(script7.name, script7);
  return app;
};
var ElAmapLoca = script7;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PointLayer/PointLayer.vue2.mjs
var script8 = defineComponent({
  ...{
    name: "ElAmapLocaPoint",
    inheritAttrs: false
  },
  __name: "PointLayer",
  props: buildLocaProps({
    // 图层里面元素的叠加效果，normal：正常透明度叠加，lighter：叠加后可能更加明亮
    blend: {
      type: String
    }
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PointLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              radius: 20,
              color: "#fff",
              unit: "px",
              borderWidth: 10,
              borderColor: "#fff",
              blurWidth: -1
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              radius: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.radius === void 0 ? style.radius : feature.properties.radius;
              },
              color: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.color === void 0 ? style.color : feature.properties.color;
              },
              unit: style.unit,
              borderWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderWidth === void 0 ? style.borderWidth : feature.properties.borderWidth;
              },
              borderColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderColor === void 0 ? style.borderColor : feature.properties.borderColor;
              },
              blurWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.blurWidth === void 0 ? style.blurWidth : feature.properties.blurWidth;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PointLayer/PointLayer.vue.mjs
script8.__file = "src/vue-amap-loca/packages/PointLayer/PointLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PointLayer/index.mjs
script8.install = (app) => {
  app.component(script8.name, script8);
  return app;
};
var ElAmapLocaPoint = script8;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PolygonLayer/PolygonLayer.vue2.mjs
var script9 = defineComponent({
  ...{
    name: "ElAmapLocaPolygon",
    inheritAttrs: false
  },
  __name: "PolygonLayer",
  props: buildLocaProps({
    cullface: {
      type: String
    },
    // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。
    acceptLight: {
      type: Boolean,
      default: true
    },
    // 面是否接受光照，光照信息在 loca 对象中配置
    shininess: {
      type: Number
    },
    // 立体网格的粗糙度，值越高，说明表面越粗糙。
    hasSide: {
      type: Boolean,
      default: true
    },
    // 当面有厚度的时候，有没有侧面
    hasBottom: {
      type: Boolean,
      default: false
    },
    //当面有厚度的时候，有没有底面。
    blockHide: {
      type: Boolean,
      default: true
    },
    //是否开启被遮挡的面隐藏，默认开启，如果关闭，在有透明度的时候，会显示出被遮挡的面。
    depth: {
      type: Boolean,
      default: true
    }
    // 是否开启深度检测，开启后可能会影响zIndex
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PolygonLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              topColor: "#fff",
              sideTopColor: "#fff",
              sideBottomColor: "#fff",
              altitude: 0,
              height: 0,
              texture: null,
              textureSize: [20, 3],
              label: void 0,
              labelAltitude: 0
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              topColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.topColor === void 0 ? style.topColor : feature.properties.topColor;
              },
              sideTopColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideTopColor === void 0 ? style.sideTopColor : feature.properties.sideTopColor;
              },
              sideBottomColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideBottomColor === void 0 ? style.sideBottomColor : feature.properties.sideBottomColor;
              },
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              },
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              texture: style.texture,
              textureSize: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.textureSize === void 0 ? style.textureSize : feature.properties.textureSize;
              },
              label: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.label === void 0 ? style.label : feature.properties.label;
              },
              labelAltitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.labelAltitude === void 0 ? style.labelAltitude : feature.properties.labelAltitude;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PolygonLayer/PolygonLayer.vue.mjs
script9.__file = "src/vue-amap-loca/packages/PolygonLayer/PolygonLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PolygonLayer/index.mjs
script9.install = (app) => {
  app.component(script9.name, script9);
  return app;
};
var ElAmapLocaPolygon = script9;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PrismLayer/PrismLayer.vue2.mjs
var script10 = defineComponent({
  ...{
    name: "ElAmapLocaPrism",
    inheritAttrs: false
  },
  __name: "PrismLayer",
  props: buildLocaProps({
    cullface: {
      type: String
    },
    // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。
    acceptLight: {
      type: Boolean,
      default: true
    },
    // 面是否接受光照，光照信息在 loca 对象中配置
    shininess: {
      type: Number
    },
    // 立体网格的粗糙度，值越高，说明表面越粗糙。
    hasSide: {
      type: Boolean,
      default: true
    },
    // 当面有厚度的时候，有没有侧面和底面
    depth: {
      type: Boolean,
      default: true
    }
    // 是否开启深度检测，开启后可能会影响zIndex
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PrismLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              radius: 20,
              unit: "px",
              sideNumber: 3,
              rotation: 0,
              altitude: 0,
              height: 100,
              topColor: "#fff",
              sideTopColor: "#fff",
              sideBottomColor: "#fff"
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              radius: style.radius,
              unit: style.unit,
              sideNumber: style.sideNumber,
              rotation: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.rotation === void 0 ? style.rotation : feature.properties.rotation;
              },
              altitude: style.altitude,
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              topColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.topColor === void 0 ? style.topColor : feature.properties.topColor;
              },
              sideTopColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideTopColor === void 0 ? style.sideTopColor : feature.properties.sideTopColor;
              },
              sideBottomColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideBottomColor === void 0 ? style.sideBottomColor : feature.properties.sideBottomColor;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PrismLayer/PrismLayer.vue.mjs
script10.__file = "src/vue-amap-loca/packages/PrismLayer/PrismLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PrismLayer/index.mjs
script10.install = (app) => {
  app.component(script10.name, script10);
  return app;
};
var ElAmapLocaPrism = script10;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PulseLineLayer/PulseLineLayer.vue2.mjs
var script11 = defineComponent({
  ...{
    name: "ElAmapLocaPulseLine",
    inheritAttrs: false
  },
  __name: "PulseLineLayer",
  props: buildLocaProps({
    depth: {
      type: Boolean,
      default: true
    }
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PulseLineLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              lineWidth: 1,
              headColor: "rgba(0, 0, 0, 0.75)",
              trailColor: "rgba(0, 0, 0, 0.25)",
              altitude: 0,
              interval: 1,
              duration: 2e3
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              lineWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.lineWidth === void 0 ? style.lineWidth : feature.properties.lineWidth;
              },
              headColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.headColor === void 0 ? style.headColor : feature.properties.headColor;
              },
              trailColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.trailColor === void 0 ? style.trailColor : feature.properties.trailColor;
              },
              altitude: style.altitude,
              interval: style.interval,
              duration: style.duration
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PulseLineLayer/PulseLineLayer.vue.mjs
script11.__file = "src/vue-amap-loca/packages/PulseLineLayer/PulseLineLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PulseLineLayer/index.mjs
script11.install = (app) => {
  app.component(script11.name, script11);
  return app;
};
var ElAmapLocaPulseLine = script11;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PulseLinkLayer/PulseLinkLayer.vue2.mjs
var script12 = defineComponent({
  ...{
    name: "ElAmapLocaPulseLink",
    inheritAttrs: false
  },
  __name: "PulseLinkLayer",
  props: buildLocaProps({
    depth: {
      type: Boolean,
      default: true
    }
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PulseLinkLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              lineColors: ["#fff"],
              height: 100,
              maxHeightScale: 0,
              smoothSteps: 50,
              lineWidth: [1, 1],
              unit: "px",
              dash: [4e3, 0, 4e3, 0],
              speed: 100,
              headColor: "rgba(0, 0, 0, 0.75)",
              trailColor: "rgba(0, 0, 0, 0.25)",
              flowLength: 100
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              lineColors: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.lineColors === void 0 ? style.lineColors : feature.properties.lineColors;
              },
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              maxHeightScale: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.maxHeightScale === void 0 ? style.maxHeightScale : feature.properties.maxHeightScale;
              },
              smoothSteps: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.smoothSteps === void 0 ? style.smoothSteps : feature.properties.smoothSteps;
              },
              lineWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.lineWidth === void 0 ? style.lineWidth : feature.properties.lineWidth;
              },
              unit: style.unit,
              dash: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.dash === void 0 ? style.dash : feature.properties.dash;
              },
              speed: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.speed === void 0 ? style.speed : feature.properties.speed;
              },
              headColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.headColor === void 0 ? style.headColor : feature.properties.headColor;
              },
              trailColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.trailColor === void 0 ? style.trailColor : feature.properties.trailColor;
              },
              flowLength: style.flowLength
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PulseLinkLayer/PulseLinkLayer.vue.mjs
script12.__file = "src/vue-amap-loca/packages/PulseLinkLayer/PulseLinkLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PulseLinkLayer/index.mjs
script12.install = (app) => {
  app.component(script12.name, script12);
  return app;
};
var ElAmapLocaPulseLink = script12;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/ScatterLayer/ScatterLayer.vue2.mjs
var script13 = defineComponent({
  ...{
    name: "ElAmapLocaScatter",
    inheritAttrs: false
  },
  __name: "ScatterLayer",
  props: buildLocaProps({}),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.ScatterLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              size: [20, 20],
              rotation: 0,
              color: "rgba(200,200,200,1)",
              altitude: 0,
              borderWidth: 0,
              borderColor: "rgba(250,250,250,1)",
              texture: null,
              unit: "px",
              animate: false,
              duration: 0
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              size: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.size === void 0 ? style.size : feature.properties.size;
              },
              rotation: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.rotation === void 0 ? style.rotation : feature.properties.rotation;
              },
              color: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.color === void 0 ? style.color : feature.properties.color;
              },
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              },
              borderWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderWidth === void 0 ? style.borderWidth : feature.properties.borderWidth;
              },
              borderColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderColor === void 0 ? style.borderColor : feature.properties.borderColor;
              },
              texture: style.texture,
              unit: style.unit,
              animate: style.animate,
              duration: style.duration
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/ScatterLayer/ScatterLayer.vue.mjs
script13.__file = "src/vue-amap-loca/packages/ScatterLayer/ScatterLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/ScatterLayer/index.mjs
script13.install = (app) => {
  app.component(script13.name, script13);
  return app;
};
var ElAmapLocaScatter = script13;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/ZMarkerLayer/ZMarkerLayer.vue2.mjs
var script14 = defineComponent({
  ...{
    name: "ElAmapLocaZMarker",
    inheritAttrs: false
  },
  __name: "ZMarkerLayer",
  props: buildLocaProps({}),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.ZMarkerLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              unit: "px",
              content: "",
              size: [20, 20],
              rotation: 0,
              alwaysFront: false,
              altitude: 0
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              unit: style.unit,
              content: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.content === void 0 ? style.content : feature.properties.content;
              },
              size: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.size === void 0 ? style.size : feature.properties.size;
              },
              rotation: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.rotation === void 0 ? style.rotation : feature.properties.rotation;
              },
              alwaysFront: style.alwaysFront,
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/ZMarkerLayer/ZMarkerLayer.vue.mjs
script14.__file = "src/vue-amap-loca/packages/ZMarkerLayer/ZMarkerLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/ZMarkerLayer/index.mjs
script14.install = (app) => {
  app.component(script14.name, script14);
  return app;
};
var ElAmapLocaZMarker = script14;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/AmbientLight/AmbientLight.vue2.mjs
var script15 = defineComponent({
  ...{
    name: "ElAmapLocaAmbientLight",
    inheritAttrs: false
  },
  __name: "AmbientLight",
  props: buildProps({
    // 环境光颜色。
    color: {
      type: String
    },
    // 环境光强度。
    intensity: {
      type: Number
    }
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.AmbientLight(options);
        parentComponent.addLight($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!parentInstance.isDestroy) {
            parentInstance.$amapComponent.removeLight($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/AmbientLight/AmbientLight.vue.mjs
script15.__file = "src/vue-amap-loca/packages/AmbientLight/AmbientLight.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/AmbientLight/index.mjs
script15.install = (app) => {
  app.component(script15.name, script15);
  return app;
};
var ElAmapLocaAmbientLight = script15;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/DirectionalLight/DirectionalLight.vue2.mjs
var script16 = defineComponent({
  ...{
    name: "ElAmapLocaDirectionalLight",
    inheritAttrs: false
  },
  __name: "DirectionalLight",
  props: buildProps({
    color: {
      type: String
    },
    // 环境光颜色。
    intensity: {
      type: Number
    },
    // 环境光强度。
    position: {
      type: Array,
      required: true
    },
    // 坐标位置
    target: {
      type: Array
    }
    // 光射向的目标位置
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.DirectionalLight(options);
        parentComponent.addLight($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!parentInstance.isDestroy) {
            parentInstance.$amapComponent.removeLight($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/DirectionalLight/DirectionalLight.vue.mjs
script16.__file = "src/vue-amap-loca/packages/DirectionalLight/DirectionalLight.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/DirectionalLight/index.mjs
script16.install = (app) => {
  app.component(script16.name, script16);
  return app;
};
var ElAmapLocaDirectionalLight = script16;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PointLight/PointLight.vue2.mjs
var script17 = defineComponent({
  ...{
    name: "ElAmapLocaPointLight",
    inheritAttrs: false
  },
  __name: "PointLight",
  props: buildProps({
    color: {
      type: String
    },
    // 点光颜色。
    intensity: {
      type: Number
    },
    // 光照强度。
    position: {
      type: Array,
      required: true
    },
    // 点光位置
    distance: {
      type: Number
    }
    // 距离表示从光源到光照强度为 0 的位置，0 就是光不会消失
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PointLight(options);
        parentComponent.addLight($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!parentInstance.isDestroy) {
            parentInstance.$amapComponent.removeLight($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PointLight/PointLight.vue.mjs
script17.__file = "src/vue-amap-loca/packages/PointLight/PointLight.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/PointLight/index.mjs
script17.install = (app) => {
  app.component(script17.name, script17);
  return app;
};
var ElAmapLocaPointLight = script17;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LaserLayer/LaserLayer.vue2.mjs
var script18 = defineComponent({
  ...{
    name: "ElAmapLocaLaser",
    inheritAttrs: false
  },
  __name: "LaserLayer",
  props: buildLocaProps({
    depth: {
      type: Boolean,
      default: true
    }
    // 图层中的要素是否具有前后遮盖关系，默认开启
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.LaserLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              unit: "px",
              height: 200,
              color: "rgba(255,255,0,0.5)",
              angle: 0,
              lineWidth: 2,
              trailLength: 30,
              duration: 2e3,
              interval: 0,
              delay: 0,
              repeat: void 0
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              unit: style.unit,
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              color: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.color === void 0 ? style.color : feature.properties.color;
              },
              angle: style.angle,
              lineWidth: style.lineWidth,
              trailLength: style.trailLength,
              duration: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.duration === void 0 ? style.duration : feature.properties.duration;
              },
              interval: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.interval === void 0 ? style.interval : feature.properties.interval;
              },
              delay: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.delay === void 0 ? style.delay : feature.properties.delay;
              },
              repeat: style.repeat
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LaserLayer/LaserLayer.vue.mjs
script18.__file = "src/vue-amap-loca/packages/LaserLayer/LaserLayer.vue";

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/packages/LaserLayer/index.mjs
script18.install = (app) => {
  app.component(script18.name, script18);
  return app;
};
var ElAmapLocaLaser = script18;

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/component.mjs
var Components = [
  ElAmapLocaGrid,
  ElAmapLocaHeatmap,
  ElAmapLocaHexagon,
  ElAmapLocaIcon,
  ElAmapLocaLine,
  ElAmapLocaLink,
  ElAmapLoca,
  ElAmapLocaPoint,
  ElAmapLocaPolygon,
  ElAmapLocaPrism,
  ElAmapLocaPulseLine,
  ElAmapLocaPulseLink,
  ElAmapLocaScatter,
  ElAmapLocaZMarker,
  ElAmapLocaAmbientLight,
  ElAmapLocaDirectionalLight,
  ElAmapLocaPointLight,
  ElAmapLocaLaser
];

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/defaults.mjs
var installer = makeInstaller([...Components]);

// node_modules/.pnpm/@vuemap+vue-amap-loca@2.1.2_67485a2480ba3c5cf749d77a200aed55/node_modules/@vuemap/vue-amap-loca/es/index.mjs
var install = installer.install;
export {
  ElAmapLoca,
  ElAmapLocaAmbientLight,
  ElAmapLocaDirectionalLight,
  ElAmapLocaGrid,
  ElAmapLocaHeatmap,
  ElAmapLocaHexagon,
  ElAmapLocaIcon,
  ElAmapLocaLaser,
  ElAmapLocaLine,
  ElAmapLocaLink,
  ElAmapLocaPoint,
  ElAmapLocaPointLight,
  ElAmapLocaPolygon,
  ElAmapLocaPrism,
  ElAmapLocaPulseLine,
  ElAmapLocaPulseLink,
  ElAmapLocaScatter,
  ElAmapLocaZMarker,
  installer as default,
  install
};
//# sourceMappingURL=@vuemap_vue-amap-loca.js.map
