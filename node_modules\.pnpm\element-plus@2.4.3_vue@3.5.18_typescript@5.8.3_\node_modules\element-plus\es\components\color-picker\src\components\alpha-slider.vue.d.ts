declare const _default: import("vue").DefineComponent<{
    readonly color: {
        readonly type: import("vue").PropType<import("../utils/color").default>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly vertical: import("../../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
}, {
    COMPONENT_NAME: string;
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly color: {
            readonly type: import("vue").PropType<import("../utils/color").default>;
            readonly required: true;
            readonly validator: ((val: unknown) => boolean) | undefined;
            __epPropKey: true;
        };
        readonly vertical: import("../../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    bar: import("vue").ShallowRef<HTMLElement | undefined>;
    thumb: import("vue").ShallowRef<HTMLElement | undefined>;
    handleDrag: (event: MouseEvent | TouchEvent) => void;
    handleClick: (event: MouseEvent | TouchEvent) => void;
    rootKls: import("vue").ComputedRef<string[]>;
    barKls: import("vue").ComputedRef<string>;
    barStyle: import("vue").ComputedRef<{
        background: string | undefined;
    }>;
    thumbKls: import("vue").ComputedRef<string>;
    thumbStyle: import("vue").ComputedRef<{
        left: string | undefined;
        top: string | undefined;
    }>;
    update: () => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly color: {
        readonly type: import("vue").PropType<import("../utils/color").default>;
        readonly required: true;
        readonly validator: ((val: unknown) => boolean) | undefined;
        __epPropKey: true;
    };
    readonly vertical: import("../../../../utils").EpPropFinalized<BooleanConstructor, unknown, unknown, false, boolean>;
}>>, {
    readonly vertical: import("../../../../utils").EpPropMergeType<BooleanConstructor, unknown, unknown>;
}>;
export default _default;
