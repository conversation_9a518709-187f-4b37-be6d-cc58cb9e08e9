{"version": 3, "file": "amap.vue2.js", "sources": ["../../../../packages/amap/amap.vue"], "sourcesContent": ["<template>\r\n  <div class=\"el-vue-amap-container\">\r\n    <div\r\n      :id=\"mapDomId\"\r\n      class=\"el-vue-amap\"\r\n    />\r\n    <slot />\r\n  </div>\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions, ref, nextTick, provide, onBeforeUnmount} from 'vue';\r\nimport {useRegister, provideKey} from \"../../mixins\";\r\nimport guid from \"../../utils/guid\";\r\nimport {lazyAMapApiLoaderInstance} from \"../../services\";\r\nimport {propsType} from \"./props\";\r\nimport type {IProvideType, TRegisterFn} from \"../../mixins\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmap',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst needInitComponents: TRegisterFn[] = [];\r\nconst provideData:IProvideType = {\r\n  $amapComponent: undefined,\r\n  addChildComponent (cb){\r\n    needInitComponents.push(cb);\r\n  },\r\n  isDestroy: false\r\n};\r\n\r\nprovide(provideKey, provideData);\r\n\r\nconst props = defineProps(propsType);\r\n\r\nconst emits = defineEmits(['init','update:zoom', 'update:center', 'update:rotation', 'update:pitch']);\r\n\r\nconst mapDomId = ref<string>(props.vid as string || guid());\r\nlet $amapComponent: AMap.Map;\r\n\r\nconst {$$getInstance} = useRegister((options) => {\r\n  return new Promise<AMap.Map>((resolve, reject) => {\r\n    if(!lazyAMapApiLoaderInstance){\r\n     reject(new Error('请初始化initAMapApiLoader'));\r\n     return;\r\n    }\r\n    lazyAMapApiLoaderInstance.then(() => {\r\n      nextTick(() => {\r\n        $amapComponent = new AMap.Map(mapDomId.value, options);\r\n        provideData.$amapComponent = $amapComponent;\r\n        bindModelEvents();\r\n        resolve($amapComponent);\r\n      });\r\n    }).catch((e: any) => {\r\n      reject(e);\r\n    });\r\n  });\r\n  \r\n}, {\r\n  isRoot: true,\r\n  emits,\r\n  needInitComponents,\r\n  provideData,\r\n  watchRedirectFn: {\r\n    __dragEnable (flag: boolean){\r\n      if($amapComponent){\r\n        $amapComponent.setStatus({dragEnable: flag});\r\n      }\r\n    },\r\n    __zoomEnable (flag: boolean){\r\n      if($amapComponent){\r\n        $amapComponent.setStatus({zoomEnable: flag});\r\n      }\r\n    },\r\n    __jogEnable (flag: boolean){\r\n      if($amapComponent){\r\n        $amapComponent.setStatus({jogEnable: flag});\r\n      }\r\n    },\r\n    __keyboardEnable (flag: boolean){\r\n      if($amapComponent){\r\n        $amapComponent.setStatus({keyboardEnable: flag});\r\n      }\r\n    },\r\n    __doubleClickZoom (flag: boolean){\r\n      if($amapComponent){\r\n        $amapComponent.setStatus({doubleClickZoom: flag});\r\n      }\r\n    },\r\n    __scrollWheel (flag: boolean){\r\n      if($amapComponent){\r\n        $amapComponent.setStatus({scrollWheel: flag});\r\n      }\r\n    },\r\n    __rotateEnable (flag: boolean){\r\n      if($amapComponent){\r\n        $amapComponent.setStatus({rotateEnable: flag});\r\n      }\r\n    },\r\n    __pitchEnable (flag: boolean){\r\n      if($amapComponent){\r\n        $amapComponent.setStatus({pitchEnable: flag});\r\n      }\r\n    },\r\n    __resizeEnable (flag: boolean){\r\n      if($amapComponent){\r\n        $amapComponent.setStatus({resizeEnable: flag});\r\n      }\r\n    },\r\n    __showIndoorMap (flag: boolean){\r\n      if($amapComponent){\r\n        $amapComponent.setStatus({showIndoorMap: flag});\r\n      }\r\n    }\r\n  }\r\n});\r\nconst bindModelEvents = () => {\r\n  $amapComponent.on('zoomchange',() => {\r\n    emits('update:zoom', $amapComponent.getZoom());\r\n  });\r\n  $amapComponent.on('rotateend',() => {\r\n    emits('update:rotation', $amapComponent.getRotation());\r\n    // emits('update:pitch', $amapComponent.getPitch());\r\n  });\r\n  $amapComponent.on('dragging',() => {\r\n    emits('update:center', getCenter());\r\n    // emits('update:pitch', $amapComponent.getPitch());\r\n  });\r\n  $amapComponent.on('dragend',() => {\r\n    emits('update:pitch', $amapComponent.getPitch());\r\n  });\r\n  $amapComponent.on('touchmove',() => {\r\n    emits('update:center', getCenter());\r\n  });\r\n};\r\nconst getCenter = (): [number, number] => {\r\n  const center = $amapComponent.getCenter();\r\n  return [center.lng, center.lat];\r\n};\r\n\r\nonBeforeUnmount(() => {\r\n  if ($amapComponent) {\r\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\r\n    // @ts-ignore\r\n    $amapComponent.clearEvents();\r\n    $amapComponent.destroy();\r\n    $amapComponent = null as any;\r\n  }\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance\r\n});\r\n\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.el-vue-amap-container {\r\n  height: 100%;\r\n  position: relative;\r\n\r\n  .el-vue-amap {\r\n    height: 100%;\r\n  }\r\n}\r\n</style>\r\n"], "names": ["provide", "<PERSON><PERSON><PERSON>", "ref", "guid", "useRegister", "lazyAMapApiLoaderInstance", "nextTick", "onBeforeUnmount"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAsBA,IAAA,MAAM,qBAAoC,EAAC,CAAA;AAC3C,IAAA,MAAM,WAA2B,GAAA;AAAA,MAC/B,cAAgB,EAAA,KAAA,CAAA;AAAA,MAChB,kBAAmB,EAAG,EAAA;AACpB,QAAA,kBAAA,CAAmB,KAAK,EAAE,CAAA,CAAA;AAAA,OAC5B;AAAA,MACA,SAAW,EAAA,KAAA;AAAA,KACb,CAAA;AAEA,IAAAA,WAAA,CAAQC,wBAAY,WAAW,CAAA,CAAA;AAE/B,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AAEd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAA,MAAM,QAAW,GAAAC,OAAA,CAAY,KAAM,CAAA,GAAA,IAAiBC,cAAM,CAAA,CAAA;AAC1D,IAAI,IAAA,cAAA,CAAA;AAEJ,IAAA,MAAM,EAAC,aAAA,EAAiB,GAAAC,uBAAA,CAAY,CAAC,OAAY,KAAA;AAC/C,MAAA,OAAO,IAAI,OAAA,CAAkB,CAAC,OAAA,EAAS,MAAW,KAAA;AAChD,QAAA,IAAG,CAACC,iDAA0B,EAAA;AAC7B,UAAO,MAAA,CAAA,IAAI,KAAM,CAAA,2CAAuB,CAAC,CAAA,CAAA;AACzC,UAAA,OAAA;AAAA,SACD;AACA,QAAAA,iDAAA,CAA0B,KAAK,MAAM;AACnC,UAAAC,YAAA,CAAS,MAAM;AACb,YAAA,cAAA,GAAiB,IAAI,IAAA,CAAK,GAAI,CAAA,QAAA,CAAS,OAAO,OAAO,CAAA,CAAA;AACrD,YAAA,WAAA,CAAY,cAAiB,GAAA,cAAA,CAAA;AAC7B,YAAgB,eAAA,EAAA,CAAA;AAChB,YAAA,OAAA,CAAQ,cAAc,CAAA,CAAA;AAAA,WACvB,CAAA,CAAA;AAAA,SACF,CAAA,CAAE,KAAM,CAAA,CAAC,CAAW,KAAA;AACnB,UAAA,MAAA,CAAO,CAAC,CAAA,CAAA;AAAA,SACT,CAAA,CAAA;AAAA,OACF,CAAA,CAAA;AAAA,KAEA,EAAA;AAAA,MACD,MAAQ,EAAA,IAAA;AAAA,MACR,KAAA;AAAA,MACA,kBAAA;AAAA,MACA,WAAA;AAAA,MACA,eAAiB,EAAA;AAAA,QACf,aAAc,IAAc,EAAA;AAC1B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,SAAU,CAAA,EAAC,UAAY,EAAA,IAAA,EAAK,CAAA,CAAA;AAAA,WAC7C;AAAA,SACF;AAAA,QACA,aAAc,IAAc,EAAA;AAC1B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,SAAU,CAAA,EAAC,UAAY,EAAA,IAAA,EAAK,CAAA,CAAA;AAAA,WAC7C;AAAA,SACF;AAAA,QACA,YAAa,IAAc,EAAA;AACzB,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,SAAU,CAAA,EAAC,SAAW,EAAA,IAAA,EAAK,CAAA,CAAA;AAAA,WAC5C;AAAA,SACF;AAAA,QACA,iBAAkB,IAAc,EAAA;AAC9B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,SAAU,CAAA,EAAC,cAAgB,EAAA,IAAA,EAAK,CAAA,CAAA;AAAA,WACjD;AAAA,SACF;AAAA,QACA,kBAAmB,IAAc,EAAA;AAC/B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,SAAU,CAAA,EAAC,eAAiB,EAAA,IAAA,EAAK,CAAA,CAAA;AAAA,WAClD;AAAA,SACF;AAAA,QACA,cAAe,IAAc,EAAA;AAC3B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,SAAU,CAAA,EAAC,WAAa,EAAA,IAAA,EAAK,CAAA,CAAA;AAAA,WAC9C;AAAA,SACF;AAAA,QACA,eAAgB,IAAc,EAAA;AAC5B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,SAAU,CAAA,EAAC,YAAc,EAAA,IAAA,EAAK,CAAA,CAAA;AAAA,WAC/C;AAAA,SACF;AAAA,QACA,cAAe,IAAc,EAAA;AAC3B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,SAAU,CAAA,EAAC,WAAa,EAAA,IAAA,EAAK,CAAA,CAAA;AAAA,WAC9C;AAAA,SACF;AAAA,QACA,eAAgB,IAAc,EAAA;AAC5B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,SAAU,CAAA,EAAC,YAAc,EAAA,IAAA,EAAK,CAAA,CAAA;AAAA,WAC/C;AAAA,SACF;AAAA,QACA,gBAAiB,IAAc,EAAA;AAC7B,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,SAAU,CAAA,EAAC,aAAe,EAAA,IAAA,EAAK,CAAA,CAAA;AAAA,WAChD;AAAA,SACF;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AACD,IAAA,MAAM,kBAAkB,MAAM;AAC5B,MAAe,cAAA,CAAA,EAAA,CAAG,cAAa,MAAM;AACnC,QAAM,KAAA,CAAA,aAAA,EAAe,cAAe,CAAA,OAAA,EAAS,CAAA,CAAA;AAAA,OAC9C,CAAA,CAAA;AACD,MAAe,cAAA,CAAA,EAAA,CAAG,aAAY,MAAM;AAClC,QAAM,KAAA,CAAA,iBAAA,EAAmB,cAAe,CAAA,WAAA,EAAa,CAAA,CAAA;AAAA,OAEtD,CAAA,CAAA;AACD,MAAe,cAAA,CAAA,EAAA,CAAG,YAAW,MAAM;AACjC,QAAM,KAAA,CAAA,eAAA,EAAiB,WAAW,CAAA,CAAA;AAAA,OAEnC,CAAA,CAAA;AACD,MAAe,cAAA,CAAA,EAAA,CAAG,WAAU,MAAM;AAChC,QAAM,KAAA,CAAA,cAAA,EAAgB,cAAe,CAAA,QAAA,EAAU,CAAA,CAAA;AAAA,OAChD,CAAA,CAAA;AACD,MAAe,cAAA,CAAA,EAAA,CAAG,aAAY,MAAM;AAClC,QAAM,KAAA,CAAA,eAAA,EAAiB,WAAW,CAAA,CAAA;AAAA,OACnC,CAAA,CAAA;AAAA,KACH,CAAA;AACA,IAAA,MAAM,YAAY,MAAwB;AACxC,MAAM,MAAA,MAAA,GAAS,eAAe,SAAU,EAAA,CAAA;AACxC,MAAA,OAAO,CAAC,MAAA,CAAO,GAAK,EAAA,MAAA,CAAO,GAAG,CAAA,CAAA;AAAA,KAChC,CAAA;AAEA,IAAAC,mBAAA,CAAgB,MAAM;AACpB,MAAA,IAAI,cAAgB,EAAA;AAGlB,QAAA,cAAA,CAAe,WAAY,EAAA,CAAA;AAC3B,QAAA,cAAA,CAAe,OAAQ,EAAA,CAAA;AACvB,QAAiB,cAAA,GAAA,IAAA,CAAA;AAAA,OACnB;AAAA,KACD,CAAA,CAAA;AAED,IAAa,QAAA,CAAA;AAAA,MACX,aAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;"}