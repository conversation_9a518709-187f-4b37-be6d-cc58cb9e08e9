{"version": 3, "file": "useEditor.js", "sources": ["../../../mixins/useEditor.ts"], "sourcesContent": ["import {useAttrs} from \"vue\";\r\nimport {bindInstanceEvent, eventReg, convertEventToLowerCase} from \"../utils\";\r\n\r\nexport function useEditor (editor: any, attrs: any){\r\n  if (!editor) return;\r\n  const filters = ['addnode', 'adjust', 'removenode', 'end', 'move', 'add'];\r\n  const filterSet: any = {};\r\n  Object.keys(attrs).forEach(key => {\r\n    if(eventReg.test(key)){\r\n      const eventKey = convertEventToLowerCase(key);\r\n      if (filters.indexOf(eventKey) !== -1) filterSet[eventKey] = attrs[key];\r\n    }\r\n\r\n  });\r\n  Object.keys(filterSet).forEach(key => {\r\n    bindInstanceEvent(editor, key, filterSet[key]);\r\n  });\r\n}"], "names": ["eventReg", "convertEventToLowerCase", "bindInstanceEvent"], "mappings": ";;;;;;AAGgB,SAAA,SAAA,CAAW,QAAa,KAAW,EAAA;AACjD,EAAA,IAAI,CAAC,MAAA;AAAQ,IAAA,OAAA;AACb,EAAA,MAAM,UAAU,CAAC,SAAA,EAAW,UAAU,YAAc,EAAA,KAAA,EAAO,QAAQ,KAAK,CAAA,CAAA;AACxE,EAAA,MAAM,YAAiB,EAAC,CAAA;AACxB,EAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAE,CAAA,OAAA,CAAQ,CAAO,GAAA,KAAA;AAChC,IAAG,IAAAA,aAAA,CAAS,IAAK,CAAA,GAAG,CAAE,EAAA;AACpB,MAAM,MAAA,QAAA,GAAWC,6BAAwB,GAAG,CAAA,CAAA;AAC5C,MAAI,IAAA,OAAA,CAAQ,OAAQ,CAAA,QAAQ,CAAM,KAAA,CAAA,CAAA;AAAI,QAAU,SAAA,CAAA,QAAQ,CAAI,GAAA,KAAA,CAAM,GAAG,CAAA,CAAA;AAAA,KACvE;AAAA,GAED,CAAA,CAAA;AACD,EAAA,MAAA,CAAO,IAAK,CAAA,SAAS,CAAE,CAAA,OAAA,CAAQ,CAAO,GAAA,KAAA;AACpC,IAAAC,6BAAA,CAAkB,MAAQ,EAAA,GAAA,EAAK,SAAU,CAAA,GAAG,CAAC,CAAA,CAAA;AAAA,GAC9C,CAAA,CAAA;AACH;;;;"}