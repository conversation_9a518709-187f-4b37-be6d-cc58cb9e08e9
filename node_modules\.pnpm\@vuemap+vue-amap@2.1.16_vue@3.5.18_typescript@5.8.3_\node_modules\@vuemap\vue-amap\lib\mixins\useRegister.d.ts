export declare type TRegisterFn = () => void;
export interface IProvideType {
    $amapComponent: any;
    addChildComponent: (cb: TRegisterFn) => void;
    isDestroy: boolean;
    [key: string]: any;
}
interface TInitComponentProps {
    propsRedirect?: Record<string, string>;
    emits: (event: any, ...args: any[]) => void;
    converts?: Record<string, (val: any) => any>;
    isRoot?: boolean;
    watchRedirectFn?: Record<string, (source: any) => any>;
    needInitComponents?: TRegisterFn[];
    provideData?: IProvideType;
    destroyComponent?: () => void;
}
export declare const provideKey = "parentInstance";
export declare const useRegister: <T, D = any>(_init: (options: any, parentComponent: D) => Promise<T>, params: TInitComponentProps) => {
    $$getInstance: () => T;
    parentInstance: IProvideType | undefined;
    isMounted: boolean;
};
export {};
