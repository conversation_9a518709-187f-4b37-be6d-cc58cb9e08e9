/// <reference types="@vuemap/amap-jsapi-types" />
import ControlBar from './ControlBar.vue';
import type { Plugin } from "vue";
export declare const ElAmapControlControlBar: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        position: {
            type: (StringConstructor | ObjectConstructor)[];
        };
        offset: {
            type: ArrayConstructor;
        };
        showControlButton: {
            type: BooleanConstructor;
            default: boolean;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {
        emits: (event: "init", ...args: any[]) => void;
        $amapComponent: AMap.ControlBar;
        $$getInstance: () => AMap.ControlBar;
        parentInstance: import("../../..").IProvideType | undefined;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        position: {
            type: (StringConstructor | ObjectConstructor)[];
        };
        offset: {
            type: ArrayConstructor;
        };
        showControlButton: {
            type: BooleanConstructor;
            default: boolean;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {
        showControlButton: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        position: {
            type: (StringConstructor | ObjectConstructor)[];
        };
        offset: {
            type: ArrayConstructor;
        };
        showControlButton: {
            type: BooleanConstructor;
            default: boolean;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {
        emits: (event: "init", ...args: any[]) => void;
        $amapComponent: AMap.ControlBar;
        $$getInstance: () => AMap.ControlBar;
        parentInstance: import("../../..").IProvideType | undefined;
    }, {}, {}, {}, {
        showControlButton: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    position: {
        type: (StringConstructor | ObjectConstructor)[];
    };
    offset: {
        type: ArrayConstructor;
    };
    showControlButton: {
        type: BooleanConstructor;
        default: boolean;
    };
} & {
    visible: import("../../..").IPropOptions<boolean>;
    zIndex: import("../../..").IPropOptions<number>;
    reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
    extraOptions: import("../../..").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
}, {
    emits: (event: "init", ...args: any[]) => void;
    $amapComponent: AMap.ControlBar;
    $$getInstance: () => AMap.ControlBar;
    parentInstance: import("../../..").IProvideType | undefined;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], "init", {
    showControlButton: boolean;
}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Plugin<any[]>;
export default ElAmapControlControlBar;
export declare type ElAmapControlControlBarInstance = InstanceType<typeof ControlBar>;
