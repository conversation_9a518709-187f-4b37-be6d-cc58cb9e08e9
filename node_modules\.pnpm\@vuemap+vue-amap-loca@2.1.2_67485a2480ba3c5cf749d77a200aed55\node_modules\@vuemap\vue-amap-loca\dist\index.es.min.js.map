{"version": 3, "file": "index.es.min.js", "sources": ["../../../vue-amap/utils/make-installer.ts", "../../../vue-amap/utils/util.js", "../../../vue-amap/utils/eventHelper.ts", "../../../vue-amap/utils/buildHelper.ts", "../../../vue-amap/mixins/useRegister.ts", "../../utils/buildHelper.ts", "../../mixins/useLoca.ts", "../../packages/GridLayer/GridLayer.vue", "../../packages/GridLayer/index.ts", "../../packages/HeatMapLayer/HeatMapLayer.vue", "../../packages/HeatMapLayer/index.ts", "../../packages/HexagonLayer/HexagonLayer.vue", "../../packages/HexagonLayer/index.ts", "../../packages/IconLayer/IconLayer.vue", "../../packages/IconLayer/index.ts", "../../packages/LineLayer/LineLayer.vue", "../../packages/LineLayer/index.ts", "../../packages/LinkLayer/LinkLayer.vue", "../../packages/LinkLayer/index.ts", "../../packages/Loca/Loca.vue", "../../packages/Loca/index.ts", "../../packages/PointLayer/PointLayer.vue", "../../packages/PointLayer/index.ts", "../../packages/PolygonLayer/PolygonLayer.vue", "../../packages/PolygonLayer/index.ts", "../../packages/PrismLayer/PrismLayer.vue", "../../packages/PrismLayer/index.ts", "../../packages/PulseLineLayer/PulseLineLayer.vue", "../../packages/PulseLineLayer/index.ts", "../../packages/PulseLinkLayer/PulseLinkLayer.vue", "../../packages/PulseLinkLayer/index.ts", "../../packages/ScatterLayer/ScatterLayer.vue", "../../packages/ScatterLayer/index.ts", "../../packages/ZMarkerLayer/ZMarkerLayer.vue", "../../packages/ZMarkerLayer/index.ts", "../../packages/AmbientLight/AmbientLight.vue", "../../packages/AmbientLight/index.ts", "../../packages/DirectionalLight/DirectionalLight.vue", "../../packages/DirectionalLight/index.ts", "../../packages/PointLight/PointLight.vue", "../../packages/PointLight/index.ts", "../../packages/LaserLayer/LaserLayer.vue", "../../packages/LaserLayer/index.ts", "../../component.ts", "../../defaults.ts", "../../index.ts"], "sourcesContent": ["import type { App, Plugin } from 'vue';\r\n\r\nexport const makeInstaller = (components: Plugin[] = []) => {\r\n  const apps: App[] = [];\r\n\r\n  const install = (app: App) => {\r\n\r\n    if (apps.includes(app)) return;\r\n    apps.push(app);\r\n\r\n    components.forEach((c) => app.use(c));\r\n  };\r\n\r\n  return {\r\n    install,\r\n  };\r\n};\r\n", "/**\r\n * 判断对象是不是map实例对象\r\n * @param instance\r\n * @returns {string|boolean}\r\n */\r\nexport function isMapInstance(instance) {\r\n  if (!instance) {\r\n    return false;\r\n  }\r\n  return instance instanceof AMap.Map;\r\n}\r\n\r\n/**\r\n * 判断对象是不是OverlayGroup实例对象\r\n * @param instance\r\n * @returns {string|boolean}\r\n */\r\nexport function isOverlayGroupInstance(instance) {\r\n  if (!instance) {\r\n    return false;\r\n  }\r\n  return instance instanceof AMap.OverlayGroup;\r\n}\r\n\r\n/**\r\n * 判断对象是不是IndoorMap实例对象\r\n * @param instance\r\n * @returns {string|boolean}\r\n */\r\nexport function isIndoorMapInstance(instance) {\r\n  if (!instance) {\r\n    return false;\r\n  }\r\n  return instance instanceof AMap.IndoorMap;\r\n}\r\n\r\n/**\r\n * 判断对象是不是LabelsLayer实例对象\r\n * @param instance\r\n * @returns {string|boolean}\r\n */\r\nexport function isLabelsLayerInstance(instance) {\r\n  if (!instance) {\r\n    return false;\r\n  }\r\n  return instance instanceof AMap.LabelsLayer;\r\n}\r\n\r\n/**\r\n * 判断对象是不是VectorLayer实例对象\r\n * @param instance\r\n * @returns {string|boolean}\r\n */\r\nexport function isVectorLayerInstance(instance) {\r\n  if (!instance) {\r\n    return false;\r\n  }\r\n  return instance instanceof AMap.VectorLayer;\r\n}\r\n\r\n/**\r\n * 将$props中的事件名称转换为地图组件需要的事件名\r\n * @param functionName\r\n * @returns {string|*}\r\n */\r\nexport function convertEventToLowerCase(functionName){\r\n  if(!functionName || functionName.length < 4){\r\n    return functionName;\r\n  }\r\n  const func = functionName.substring(3, functionName.length);\r\n  const firstLetter = functionName[2].toLowerCase();\r\n  return firstLetter + func;\r\n}\r\n\r\nexport const eventReg = /^on[A-Z]+/;\r\n\r\n/**\r\n * 加载JS文件\r\n * @param url\r\n * @param callback\r\n */\r\nexport function loadScript(url, callback){\r\n  if(!url){\r\n    throw new Error('请传入url');\r\n  }\r\n  const script = document.createElement('script');\r\n  script.type = 'text/javascript';\r\n  script.async = true;\r\n  script.defer = true;\r\n  script.src = url;\r\n  document.body.appendChild(script);\r\n  if(callback){\r\n    script.addEventListener('load',() => {\r\n      callback();\r\n    });\r\n  }\r\n}\r\n\r\nexport function convertLnglat(lnglat){\r\n  if(Array.isArray(lnglat)){\r\n    return lnglat.map(convertLnglat);\r\n  }\r\n  return lnglat.toArray();\r\n}\r\n\r\n/**\r\n * 将字符串的第一个字符调整为大写\r\n * @param prop\r\n */\r\nexport function upperCamelCase(prop){\r\n  if(!prop){\r\n    return prop;\r\n  }\r\n  return prop.charAt(0).toUpperCase() + prop.slice(1);\r\n}\r\n", "/**\r\n * 给地图实例绑定事件\r\n * @param instance\r\n * @param eventName\r\n * @param handler\r\n */\r\nexport function bindInstanceEvent (instance: any, eventName: string, handler: any){\r\n  if(!instance || !instance.on){\r\n    return;\r\n  }\r\n  instance.on(eventName, handler);\r\n}\r\n\r\n/**\r\n * 从地图实例上移除事件\r\n * @param instance\r\n * @param eventName\r\n * @param handler\r\n */\r\nexport function removeInstanceEvent (instance: any, eventName: string, handler: any){\r\n  if(!instance || !instance.off){\r\n    return;\r\n  }\r\n  instance.off(eventName, handler);\r\n}", "import type {ComponentObjectPropsOptions, PropType} from \"vue\";\r\n\r\nexport type IPropData = Record<string, unknown>\r\nexport interface IPropOptions<T = any> {\r\n  type?: PropType<T> | true | null;\r\n  required?: boolean;\r\n  default?: any;\r\n  validator?(value: unknown, props: IPropData): boolean;\r\n}\r\nexport interface ICommonProps{\r\n  // 是否显隐\r\n  visible: IPropOptions<boolean>\r\n  // 层级\r\n  zIndex: IPropOptions<number>\r\n  // 是否在更新组件后重新注册事件\r\n  reEventWhenUpdate: IPropOptions<boolean>\r\n  // 额外参数，用于在初始化组件时提供prop中未定义的属性\r\n  extraOptions: IPropOptions,\r\n}\r\n\r\nexport const commonProps: ICommonProps = {\r\n  visible: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  zIndex: {\r\n    type: Number,\r\n  },\r\n  reEventWhenUpdate: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  extraOptions: {\r\n    type: Object\r\n  },\r\n};\r\n\r\n/**\r\n * 合并生成基础的属性\r\n * @param props\r\n */\r\nexport const buildProps = <Props extends ComponentObjectPropsOptions >(props: Props): Props & {\r\n  [K  in keyof ICommonProps]: ICommonProps[K]\r\n} => {\r\n  return Object.assign({}, commonProps, props);\r\n};\r\n", "import {\r\n  inject,\r\n  onMounted,\r\n  onBeforeUnmount,\r\n  onBeforeUpdate,\r\n  onUpdated,\r\n  getCurrentInstance,\r\n  isProxy, toRaw, unref,\r\n  watch,\r\n  nextTick\r\n} from 'vue';\r\nimport {convertEventToLowerCase, eventReg, upperCamelCase, bindInstanceEvent, removeInstanceEvent} from \"../utils\";\r\nimport type {ComponentInternalInstance, WatchStopHandle} from 'vue';\r\n\r\nexport type TRegisterFn = () => void\r\nexport interface IProvideType{\r\n  // 父组件的地图相关实例\r\n  $amapComponent: any\r\n  // // 父组件用来缓存需要执行注册的回调\r\n  // needInitComponents: TRegisterFn[]\r\n  // 当组件初始化的时候如果父组件还未初始化成功，那么需要调用该方法将自身的初始化方法注册到父组件中\r\n  addChildComponent: (cb: TRegisterFn) => void\r\n  // 父组件是否已经销毁\r\n  isDestroy: boolean\r\n  [key : string]: any\r\n}\r\n\r\n\r\ninterface TInitComponentProps {\r\n  // 属性名重定向\r\n  propsRedirect?: Record<string, string>;\r\n  emits: (event: any, ...args: any[]) => void;\r\n  // 转化数据使用\r\n  converts?: Record<string, (val: any) => any>\r\n  // 是否是根节点，只用于map组件初始化使用\r\n  isRoot?: boolean\r\n  // 监听事件使用的方法，默认是读取地图实例的属性的set方法\r\n  watchRedirectFn?: Record<string, (source:  any) => any>\r\n  // 需要初始化的子组件\r\n  needInitComponents?: TRegisterFn[]\r\n  provideData?: IProvideType\r\n  destroyComponent?: () => void\r\n}\r\n\r\nexport const provideKey = 'parentInstance';\r\n\r\nexport const useRegister = <T, D = any>(_init: (options: any, parentComponent: D) => Promise<T>, params: TInitComponentProps) => {\r\n  let componentInstance = getCurrentInstance() as ComponentInternalInstance;\r\n  let {props, attrs} = componentInstance;\r\n  let parentInstance = inject<IProvideType | undefined>(provideKey, undefined);\r\n  const emits = params.emits;\r\n\r\n  let isMounted = false;\r\n  let $amapComponent: T;\r\n  \r\n  onMounted(() => {\r\n    if(parentInstance){\r\n      if(parentInstance.$amapComponent){\r\n        register();\r\n      }else{\r\n        parentInstance.addChildComponent(register);\r\n      }\r\n    }else if(params.isRoot){\r\n      register();\r\n    }\r\n  });\r\n  onBeforeUnmount(() => {\r\n    if(!$amapComponent){\r\n      return;\r\n    }\r\n    unregisterEvents();\r\n    stopWatchers();\r\n    if(params.destroyComponent){\r\n      params.destroyComponent();\r\n    }else{\r\n      destroyComponent();\r\n    }\r\n    if(params.provideData){\r\n      params.provideData.isDestroy = true;\r\n    }\r\n    parentInstance = undefined;\r\n    props = undefined as any;\r\n    attrs = undefined as any;\r\n    componentInstance = undefined as any;\r\n    $amapComponent = undefined as any;\r\n  });\r\n  \r\n  onBeforeUpdate(() => {\r\n    if(props.reEventWhenUpdate && isMounted && $amapComponent){\r\n      unregisterEvents();\r\n    }\r\n  });\r\n  \r\n  onUpdated(() => {\r\n    if(props.reEventWhenUpdate && isMounted && $amapComponent){\r\n      registerEvents();\r\n    }\r\n  });\r\n  \r\n  const register = () => {\r\n    const options = convertProps();\r\n    _init(options, parentInstance?.$amapComponent).then(mapInstance => {\r\n      $amapComponent = mapInstance;\r\n      registerEvents();\r\n      initProps();\r\n      setPropWatchers();\r\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\r\n      // @ts-ignore\r\n      Object.assign(componentInstance.ctx, componentInstance.exposed);\r\n      // eslint-disable-next-line @typescript-eslint/ban-ts-comment\r\n      // @ts-ignore\r\n      emits('init', $amapComponent, componentInstance.ctx);\r\n      nextTick(() => {\r\n        createChildren();\r\n      }).then();\r\n      isMounted = true;\r\n    });\r\n  };\r\n  \r\n  // 初始化属性\r\n  const initProps = () => {\r\n    const propsList = ['editable', 'visible', 'zooms'];\r\n\r\n    propsList.forEach(propStr => {\r\n      if (props[propStr] !== undefined) {\r\n        const handleFun = getHandlerFun(propStr);\r\n        handleFun && handleFun.call($amapComponent, convertProxyToRaw(convertSignalProp(propStr, props[propStr])));\r\n      }\r\n    });\r\n  };\r\n  \r\n  // 开始处理props数据\r\n  const propsRedirect = params.propsRedirect || {};\r\n  const convertProps = () => {\r\n    const propsCache: Record<string, any> = {};\r\n    if(props.extraOptions){\r\n      Object.assign(propsCache, props.extraOptions);\r\n    }\r\n    Object.keys(props).forEach(_key => {\r\n      let key = _key;\r\n      const propsValue = convertSignalProp(key, props[key]);\r\n      if (propsValue !== undefined){\r\n        if (propsRedirect && propsRedirect[_key]){\r\n          key = propsRedirect[key];\r\n        }\r\n        propsCache[key] = propsValue;\r\n      }\r\n    });\r\n    return propsCache;\r\n  };\r\n  \r\n  const converters = params.converts || {};\r\n  const convertSignalProp = (key: string, sourceData: any) => {\r\n    if (converters && converters[key]) {\r\n      return converters[key].call(this, sourceData);\r\n    }\r\n    return sourceData;\r\n  };\r\n\r\n  const convertProxyToRaw = (value: any) => {\r\n    if(isProxy(value)){\r\n      return toRaw(value);\r\n    }\r\n    return unref(value);\r\n  };\r\n  \r\n  // 结束处理props数据\r\n  \r\n  // 开始监控数据变化\r\n  let unwatchFns: WatchStopHandle[] = [];\r\n  let watchRedirectFn: Record<string, (source:  any) => any> = Object.assign({\r\n    __visible: (flag: boolean) => {\r\n      if(!!$amapComponent && !!$amapComponent['show'] && !!$amapComponent['hide']){\r\n        !flag ? $amapComponent['hide']() : $amapComponent['show']();\r\n      }\r\n    },\r\n    __zIndex (value: number){\r\n      if ($amapComponent && $amapComponent['setzIndex']) {\r\n        $amapComponent['setzIndex'](value);\r\n      }\r\n    }\r\n  }, params.watchRedirectFn || {});\r\n  const setPropWatchers = () => {\r\n\r\n    Object.keys(props).forEach(prop => {\r\n      let handleProp = prop;\r\n      if (propsRedirect && propsRedirect[prop]) handleProp = propsRedirect[prop];\r\n      const handleFun = getHandlerFun(handleProp);\r\n      if (!handleFun) return;\r\n      const watchOptions = {\r\n        deep: false\r\n      };\r\n      const propValueType = Object.prototype.toString.call(props[prop]);\r\n      if ( propValueType === '[object Object]' || propValueType === '[object Array]') {\r\n        watchOptions.deep = true;\r\n      }\r\n      // watch props\r\n      const unwatch = watch(() => props[prop], nv => {\r\n        handleFun.call($amapComponent, convertProxyToRaw(convertSignalProp(prop, nv)));\r\n      }, watchOptions);\r\n\r\n      // collect watchers for destroyed\r\n      unwatchFns.push(unwatch);\r\n    });\r\n  };\r\n  \r\n  const stopWatchers = () => {\r\n    unwatchFns.forEach(fn => fn());\r\n    unwatchFns = [];\r\n    watchRedirectFn = undefined as any;\r\n  };\r\n\r\n  const getHandlerFun = (prop: string) => {\r\n    if (watchRedirectFn[`__${prop}`]) {\r\n      return watchRedirectFn[`__${prop}`];\r\n    }\r\n    if(!$amapComponent){\r\n      return null;\r\n    }\r\n    return $amapComponent[`set${upperCamelCase(prop)}`];\r\n  };\r\n  // 监控数据变化\r\n  \r\n  \r\n  // 开始为地图实例注册事件\r\n  const cacheEvents: Record<string, any> = {};\r\n  const registerEvents = () => {\r\n    Object.keys(attrs).forEach(key => {\r\n      if(eventReg.test(key)){\r\n        const eventKey = convertEventToLowerCase(key);\r\n        bindInstanceEvent($amapComponent, eventKey, attrs[key]);\r\n        cacheEvents[eventKey] = attrs[key];\r\n      }\r\n    });\r\n  };\r\n  const unregisterEvents = () => {\r\n    Object.keys(cacheEvents).forEach(eventKey => {\r\n      removeInstanceEvent($amapComponent, eventKey, cacheEvents[eventKey]);\r\n      delete cacheEvents[eventKey];\r\n    });\r\n  };\r\n  \r\n  // 处理事件\r\n  \r\n  const createChildren = () => {\r\n    const needInitComponents = params.needInitComponents || [];\r\n    while (needInitComponents.length > 0){\r\n      needInitComponents[0]();\r\n      needInitComponents.splice(0, 1);\r\n    }\r\n  };\r\n  \r\n  // 销毁组件时的回调\r\n  const destroyComponent = () => {\r\n    if(!$amapComponent){\r\n      return;\r\n    }\r\n    ($amapComponent as any).setMap && ($amapComponent as any).setMap(null);\r\n    ($amapComponent as any).close && ($amapComponent as any).close();\r\n    ($amapComponent as any).editor && ($amapComponent as any).editor.close();\r\n  };\r\n\r\n  function $$getInstance (): T{\r\n    return $amapComponent;\r\n  }\r\n  return {\r\n    $$getInstance,\r\n    parentInstance,\r\n    isMounted\r\n  };\r\n};\r\n", "import {commonProps} from '@vuemap/vue-amap';\r\nimport type {ICommonProps, IPropOptions} from '@vuemap/vue-amap';\r\nimport type {ComponentObjectPropsOptions, PropType} from \"vue\";\r\n\r\nexport interface ILocaProps{\r\n  // \r\n  sourceUrl: IPropOptions<string>\r\n  // \r\n  sourceData: IPropOptions<object>\r\n  // \r\n  geoBufferSource: IPropOptions\r\n  // \r\n  layerStyle: IPropOptions<object>\r\n  defaultStyleValue: IPropOptions<object>\r\n  zooms: IPropOptions<object>\r\n  opacity: IPropOptions<number>\r\n  initEvents: IPropOptions<boolean>\r\n  visibleDuration: IPropOptions<number>\r\n  onClick:  IPropOptions<(e: any) => void>\r\n  onMousemove:  IPropOptions<(e: any) => void>\r\n  onRightclick:  IPropOptions<(e: any) => void>\r\n}\r\n\r\n/**\r\n * 绑定Loca的属性\r\n * @param props\r\n */\r\nexport const buildLocaProps = <Props extends ComponentObjectPropsOptions>(props: Props): Props & {\r\n  [K  in keyof ICommonProps]: ICommonProps[K]\r\n} & {\r\n  [T  in keyof ILocaProps]: ILocaProps[T]\r\n} => {\r\n  return Object.assign({}, commonProps, {\r\n    sourceUrl: {\r\n      type: String\r\n    },\r\n    sourceData: {\r\n      type: Object\r\n    },\r\n    geoBufferSource: {\r\n      type: [ArrayBuffer, String],\r\n      default () {\r\n        return null;\r\n      }\r\n    },\r\n    layerStyle: {\r\n      type: Object\r\n    },\r\n    defaultStyleValue: {\r\n      type: Object,\r\n      default () {\r\n        return {};\r\n      }\r\n    },\r\n    zooms: {\r\n      type: Array\r\n    },\r\n    opacity: {\r\n      type: Number\r\n    },\r\n    initEvents: {\r\n      type: Boolean,\r\n      default: true\r\n    },\r\n    visibleDuration: {\r\n      type: Number,\r\n      default: 0\r\n    },\r\n    onClick: {\r\n      type: Function as PropType<(e: any) => void>,\r\n      default: null\r\n    },\r\n    onMousemove: {\r\n      type: Function as PropType<(e: any) => void>,\r\n      default: null\r\n    },\r\n    onRightclick: {\r\n      type: Function as PropType<(e: any) => void>,\r\n      default: null\r\n    },\r\n  } as ILocaProps, props);\r\n};\r\n\r\nexport const commonEmitNames = ['init', 'mousemove', 'click', 'rightclick' ];", "import {nextTick} from \"vue\";\r\nimport type {IProvideType} from '@vuemap/vue-amap';\r\n\r\n\r\ninterface IUseWatchFnType {\r\n  setSource: () => void\r\n  $amapComponent: () => any\r\n  props: any\r\n}\r\n\r\nexport interface IUseLocaTypes {\r\n  $$getInstance: () => any\r\n  parentInstance: IProvideType\r\n}\r\n\r\nexport function useWatchFn (options: IUseWatchFnType){\r\n  return {\r\n    __layerStyle (style: any) {\r\n      nextTick(() => {\r\n        if (options.$amapComponent()?.setStyle) {\r\n          options.$amapComponent().setStyle(style);\r\n        }\r\n      }).then();\r\n    },\r\n    __sourceUrl (){\r\n      nextTick(() => {\r\n        options.setSource();\r\n      }).then();\r\n    },\r\n    __sourceData (){\r\n      nextTick(() => {\r\n        options.setSource();\r\n      }).then();\r\n    },\r\n    __geoBufferSource (){\r\n      nextTick(() => {\r\n        options.setSource();\r\n      }).then();\r\n    },\r\n    __visible (flag: boolean) {\r\n      const $amapComponent = options.$amapComponent();\r\n      if ($amapComponent?.show && $amapComponent?.hide) {\r\n        !flag ? $amapComponent.hide(options.props.visibleDuration) : $amapComponent.show(options.props.visibleDuration);\r\n      }\r\n    }\r\n  };\r\n}\r\n\r\nexport function useLocaEvents (options: {\r\n  parentInstance?: IProvideType\r\n  $amapComponent: any\r\n  emits: any,\r\n  props: any\r\n  setStyle: () => void\r\n}){\r\n  let isDragging = false;\r\n  let isRotating = false;\r\n  let source: any;\r\n  const {parentInstance, $amapComponent, emits, props, setStyle}  = options;\r\n  \r\n  const setSource = () => {\r\n    if (source) {\r\n      source.destroy();\r\n      source = null;\r\n    }\r\n    if (props.geoBufferSource) {\r\n      if(typeof props.geoBufferSource === 'string'){\r\n        source = new Loca.GeoBufferSource({\r\n          url: props.geoBufferSource\r\n        });\r\n      }else{\r\n        source = new Loca.GeoBufferSource({\r\n          data: props.geoBufferSource\r\n        });\r\n      }\r\n    }else if (props.sourceUrl) {\r\n      source = new Loca.GeoJSONSource({\r\n        url: props.sourceUrl\r\n      });\r\n    } else if (props.sourceData) {\r\n      source = new Loca.GeoJSONSource({\r\n        data: props.sourceData\r\n      });\r\n    } else {\r\n      source = new Loca.GeoJSONSource({\r\n      });\r\n    }\r\n    $amapComponent.setSource(source);\r\n  };\r\n  \r\n  const initComplete = () => {\r\n    if (props.initEvents) {\r\n      bindEvents();\r\n    }\r\n  };\r\n  const bindEvents = () => {\r\n    if(parentInstance){\r\n      const map = parentInstance.getMap();\r\n      if(props.onClick !== null){\r\n        map.on('click', clickMap);\r\n      }\r\n      if(props.onMousemove !== null){\r\n        map.on('mousemove', mouseMoveMap);\r\n        map.on('dragstart', dragStart);\r\n        map.on('dragend', dragEnd);\r\n        map.on('rotatestart', rotateStart);\r\n        map.on('rotateend', rotateEnd);\r\n        map.on('mouseout', mouseoutMap);\r\n      }\r\n      if(props.onRightclick !== null){\r\n        map.on('rightclick', rightclickMap);\r\n      }\r\n    }\r\n  };\r\n  const clickMap = (e: any) => {\r\n    const feature = _getFeature(e);\r\n    emits('click', feature, e);\r\n  };\r\n  const rightclickMap = (e: any) => {\r\n    const feature = _getFeature(e);\r\n    emits('rightclick', feature, e);\r\n  };\r\n  const mouseMoveMap = (e:any) => {\r\n    if(isDragging || isRotating){\r\n      return;\r\n    }\r\n    const feature = _getFeature(e);\r\n    emits('mousemove', feature, e);\r\n  };\r\n  const _getFeature = (e:any) => {\r\n    return $amapComponent.queryFeature(e.pixel.toArray());\r\n  };\r\n  const dragStart =  () => {\r\n    isDragging = true;\r\n  };\r\n  const dragEnd  = () => {\r\n    isDragging = false;\r\n  };\r\n  const mouseoutMap =  () => {\r\n    isDragging = false;\r\n    isRotating = false;\r\n  };\r\n  const rotateStart  = () => {\r\n    isRotating = true;\r\n  };\r\n  const rotateEnd  = () => {\r\n    isRotating = false;\r\n  };\r\n  const unBindEvents =  ()  => {\r\n    if(parentInstance){\r\n      const map = parentInstance.getMap();\r\n      map.off('click', clickMap);\r\n      map.off('rightclick', rightclickMap);\r\n      map.off('mousemove', mouseMoveMap);\r\n      map.off('dragstart', dragStart);\r\n      map.off('dragend', dragEnd);\r\n      map.off('rotatestart', rotateStart);\r\n      map.off('rotateend', rotateEnd);\r\n      map.off('mouseout', mouseoutMap);\r\n    }\r\n  };\r\n  \r\n  setSource();\r\n  setStyle();\r\n  parentInstance?.$amapComponent.add($amapComponent);\r\n  initComplete();\r\n  \r\n  const _destroyComponent = () => {\r\n    unBindEvents();\r\n    if(!parentInstance?.isDestroy){\r\n      parentInstance?.$amapComponent.remove($amapComponent);\r\n      $amapComponent.destroy();\r\n    }\r\n    if (source) {\r\n      source.destroy();\r\n      source = null;\r\n    }\r\n  };\r\n  \r\n  return {\r\n    _destroyComponent,\r\n    setSource\r\n  };\r\n}", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaGrid',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。\r\n  cullface: {\r\n    type: String\r\n  },\r\n  // 面是否接受光照，光照信息在 loca 对象中配置\r\n  acceptLight: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  // 立体网格的粗糙度，值越高，说明表面越粗糙。\r\n  shininess: {\r\n    type: Number\r\n  },\r\n  // 当面有厚度的时候，有没有侧面和底面\r\n  hasSide: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  // 是否开启深度检测，开启后可能会影响zIndex\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.GridLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.GridLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          topColor: '#fff',\r\n          sideTopColor: '#fff',\r\n          sideBottomColor: '#fff',\r\n          altitude: 0,\r\n          height: 0,\r\n          radius: 1000,\r\n          gap: 0,\r\n          unit: 'meter'\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          topColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.topColor === undefined ? style.topColor : feature.properties.topColor;\r\n          },\r\n          sideTopColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideTopColor === undefined ? style.sideTopColor : feature.properties.sideTopColor;\r\n          },\r\n          sideBottomColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideBottomColor === undefined ? style.sideBottomColor : feature.properties.sideBottomColor;\r\n          },\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude;\r\n          },\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          radius: style.radius,\r\n          gap: style.gap,\r\n          unit: style.unit\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle as any);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null as any;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import GridLayer from './GridLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nGridLayer.install = (app: App) => {\r\n  app.component(GridLayer.name, GridLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaGrid = GridLayer as typeof GridLayer & Plugin;\r\nexport default ElAmapLocaGrid;\r\n\r\nexport type ElAmapLocaGridInstance = InstanceType<typeof GridLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaHeatmap',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.HeatMapLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.HeatMapLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          radius: 20,\r\n          value: 10,\r\n          gradient: {0.5: 'blue', 0.65: 'rgb(117,211,248)', 0.7: 'rgb(0, 255, 0)', 0.9: '#ffea00', 1.0: 'red'},\r\n          opacity: [0, 1],\r\n          height: 100,\r\n          heightBezier: [0.4, 0.2, 0.4, 0.8],\r\n          max: null,\r\n          min: null,\r\n          unit: 'px',\r\n          difference: false\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          radius: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.radius === undefined ? style.radius : feature.properties.radius;\r\n          },\r\n          value: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.value === undefined ? style.value : feature.properties.value;\r\n          },\r\n          gradient: style.gradient,\r\n          opacity: style.opacity,\r\n          height: style.height,\r\n          heightBezier: style.heightBezier,\r\n          max: style.max,\r\n          min: style.min,\r\n          unit: style.unit,\r\n          difference: style.difference\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle as any);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null as any;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import HeatMapLayer from './HeatMapLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nHeatMapLayer.install = (app: App) => {\r\n  app.component(HeatMapLayer.name, HeatMapLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaHeatmap = HeatMapLayer as typeof HeatMapLayer & Plugin;\r\nexport default ElAmapLocaHeatmap;\r\n\r\nexport type ElAmapLocaHeatmapInstance = InstanceType<typeof HeatMapLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaHexagon',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  cullface: {\r\n    type: String\r\n  }, // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。\r\n  acceptLight: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 面是否接受光照，光照信息在 loca 对象中配置\r\n  shininess: {\r\n    type: Number\r\n  }, // 立体网格的粗糙度，值越高，说明表面越粗糙。\r\n  hasSide: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 当面有厚度的时候，有没有侧面和底面\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }// 是否开启深度检测，开启后可能会影响zIndex\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.HexagonLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.HexagonLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          topColor: '#fff',\r\n          sideTopColor: '#fff',\r\n          sideBottomColor: '#fff',\r\n          altitude: 0,\r\n          height: 0,\r\n          radius: 1000,\r\n          gap: 0,\r\n          unit: 'meter'\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          topColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.topColor === undefined ? style.topColor : feature.properties.topColor;\r\n          },\r\n          sideTopColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideTopColor === undefined ? style.sideTopColor : feature.properties.sideTopColor;\r\n          },\r\n          sideBottomColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideBottomColor === undefined ? style.sideBottomColor : feature.properties.sideBottomColor;\r\n          },\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude;\r\n          },\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          radius: style.radius,\r\n          gap: style.gap,\r\n          unit: style.unit\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import HexagonLayer from './HexagonLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nHexagonLayer.install = (app: App) => {\r\n  app.component(HexagonLayer.name, HexagonLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaHexagon = HexagonLayer as typeof HexagonLayer & Plugin;\r\nexport default ElAmapLocaHexagon;\r\n\r\nexport type ElAmapLocaHexagonInstance = InstanceType<typeof HexagonLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaIcon',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.IconLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.IconLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          unit: 'px',\r\n          icon: '',\r\n          iconSize: [20, 20],\r\n          rotation: 0,\r\n          opacity: 1,\r\n          offset: [0, 0]\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          unit: style.unit,\r\n          icon: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.icon === undefined ? style.icon : feature.properties.icon;\r\n          },\r\n          iconSize: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.iconSize === undefined ? style.iconSize : feature.properties.iconSize;\r\n          },\r\n          rotation: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.rotation === undefined ? style.rotation : feature.properties.rotation;\r\n          },\r\n          opacity: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.opacity === undefined ? style.opacity : feature.properties.opacity;\r\n          },\r\n          offset: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.offset === undefined ? style.offset : feature.properties.offset;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import IconLayer from './IconLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nIconLayer.install = (app: App) => {\r\n  app.component(IconLayer.name, IconLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaIcon = IconLayer as typeof IconLayer & Plugin;\r\nexport default ElAmapLocaIcon;\r\n\r\nexport type ElAmapLocaIconInstance = InstanceType<typeof IconLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaLine',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.LineLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.LineLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          color: '#fff',\r\n          lineWidth: 2,\r\n          altitude: 0,\r\n          borderWidth: 0,\r\n          borderColor: '#fff',\r\n          dashArray: [10, 0, 10, 0]\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          color: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.color === undefined ? style.color : feature.properties.color;\r\n          },\r\n          lineWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.lineWidth === undefined ? style.lineWidth : feature.properties.lineWidth;\r\n          },\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude;\r\n          },\r\n          borderWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderWidth === undefined ? style.borderWidth : feature.properties.borderWidth;\r\n          },\r\n          borderColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderColor === undefined ? style.borderColor : feature.properties.borderColor;\r\n          },\r\n          dashArray: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.dashArray === undefined ? style.dashArray : feature.properties.dashArray;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import LineLayer from './LineLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nLineLayer.install = (app: App) => {\r\n  app.component(LineLayer.name, LineLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaLine = LineLayer as typeof LineLayer & Plugin;\r\nexport default ElAmapLocaLine;\r\n\r\nexport type ElAmapLocaLineInstance = InstanceType<typeof LineLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaLink',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.LinkLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.LinkLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          lineColors: ['rgba(255,255,255,1)', 'rgba(255,255,255,0)'],\r\n          height: 100,\r\n          smoothSteps: 100\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          lineColors: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.lineColors === undefined ? style.lineColors : feature.properties.lineColors;\r\n          },\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          smoothSteps: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.smoothSteps === undefined ? style.smoothSteps : feature.properties.smoothSteps;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import LinkLayer from './LinkLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nLinkLayer.install = (app: App) => {\r\n  app.component(LinkLayer.name, LinkLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaLink = LinkLayer as typeof LinkLayer & Plugin;\r\nexport default ElAmapLocaLink;\r\n\r\nexport type ElAmapLocaLinkInstance = InstanceType<typeof LinkLayer>\r\n", "<template>\r\n  <div><slot /></div>\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions, provide} from 'vue';\r\nimport {provideKey, useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport type { TRegisterFn, IProvideType} from \"@vuemap/vue-amap\";\r\nimport type {PropType} from 'vue';\r\nimport type {EventOptions} from \"./Type\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLoca',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst needInitComponents: TRegisterFn[] = [];\r\nconst provideData:IProvideType = {\r\n  $amapComponent: undefined,\r\n  addChildComponent (cb){\r\n    needInitComponents.push(cb);\r\n  },\r\n  isDestroy: false,\r\n  getMap: () => {\r\n    return getMap();\r\n  }\r\n};\r\n\r\nprovide(provideKey, provideData);\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  ambLight: {\r\n    type: Object\r\n  }, // 环境光\r\n  dirLight: {\r\n    type: Object\r\n  }, // 平行光\r\n  pointLight: {\r\n    type: Object\r\n  }, // 点光\r\n  onClick: {\r\n    type: Function,\r\n    default: null\r\n  },\r\n  onMousemove: {\r\n    type: Function,\r\n    default: null\r\n  },\r\n  onRightclick: {\r\n    type: Function,\r\n    default: null\r\n  },\r\n  eventOptions: {\r\n    type: Object as PropType<EventOptions>,\r\n    default: () => ({\r\n      hitFirst: true\r\n    })\r\n  } // 对于事件是否只触发第一个选中的数据\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\nlet isDragging = false;\r\nlet isRotating = false;\r\nlet hitFirst = true;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.Container, AMap.Map>((options, parentComponent) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.Container({\r\n      map: parentComponent\r\n    });\r\n    provideData.$amapComponent = $amapComponent;\r\n    if (options.ambLight) {\r\n      $amapComponent.ambLight = options.ambLight;\r\n    }\r\n    if (options.dirLight) {\r\n      $amapComponent.dirLight = options.dirLight;\r\n    }\r\n    if (options.pointLight) {\r\n      $amapComponent.pointLight = options.pointLight;\r\n    }\r\n    hitFirst = options.eventOptions.hitFirst;\r\n    bindEvents();\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  needInitComponents,\r\n  provideData,\r\n  destroyComponent () {\r\n    if ($amapComponent) {\r\n      unBindEvents();\r\n      if($amapComponent.animate && $amapComponent.animate.stop){\r\n        $amapComponent.animate.stop();\r\n      }\r\n      $amapComponent.destroy();\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\nconst getMap = () => {\r\n  return parentInstance?.$amapComponent;\r\n};\r\n\r\nconst bindEvents = () => {\r\n  if(parentInstance){\r\n    const map = getMap();\r\n    if(props.onClick !== null){\r\n      map.on('click', clickMap);\r\n    }\r\n    if(props.onMousemove !== null){\r\n      map.on('mousemove', mouseMoveMap);\r\n      map.on('dragstart', dragStart);\r\n      map.on('dragend', dragEnd);\r\n      map.on('rotatestart', rotateStart);\r\n      map.on('rotateend', rotateEnd);\r\n      map.on('mouseout', mouseoutMap);\r\n    }\r\n    if(props.onRightclick !== null){\r\n      map.on('rightclick', rightclickMap);\r\n    }\r\n  }\r\n};\r\nconst clickMap = (e) => {\r\n  const features = _getFeature(e);\r\n  emits('click', features, e);\r\n};\r\nconst rightclickMap = (e) => {\r\n  const features = _getFeature(e);\r\n  emits('rightclick', features, e);\r\n};\r\nconst mouseMoveMap = (e) => {\r\n  if(isDragging || isRotating){\r\n    return;\r\n  }\r\n  const features = _getFeature(e);\r\n  emits('mousemove', features, e);\r\n};\r\nconst _getFeature = (e) => {\r\n  const features: any[] = [];\r\n  if($amapComponent.layers){\r\n    const layers: any[] = [];\r\n    $amapComponent.layers.forEach(( v => {\r\n      layers.push(v);\r\n    }));\r\n    layers.sort((a,b) => b.zIndex - a.zIndex);\r\n    const layerLen = layers.length;\r\n    for(let i=0;i<layerLen;i++){\r\n      const temp = layers[i].queryFeature(e.pixel.toArray());\r\n      if(temp){\r\n        features.push(temp);\r\n        if(hitFirst){\r\n          break;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  return features;\r\n};\r\nconst dragStart = () => {\r\n  isDragging = true;\r\n};\r\nconst dragEnd = () => {\r\n  isDragging = false;\r\n};\r\nconst mouseoutMap = () => {\r\n  isDragging = false;\r\n  isRotating = false;\r\n};\r\nconst rotateStart = () => {\r\n  isRotating = true;\r\n};\r\nconst rotateEnd = () => {\r\n  isRotating = false;\r\n};\r\nconst unBindEvents = () => {\r\n  if(parentInstance){\r\n    const map = getMap();\r\n    map.off('click', clickMap);\r\n    map.off('rightclick', rightclickMap);\r\n    map.off('mousemove', mouseMoveMap);\r\n    map.off('dragstart', dragStart);\r\n    map.off('dragend', dragEnd);\r\n    map.off('rotatestart', rotateStart);\r\n    map.off('rotateend', rotateEnd);\r\n    map.off('mouseout', mouseoutMap);\r\n  }\r\n};\r\n\r\ndefineExpose({\r\n  $$getInstance\r\n});\r\n\r\n</script>\r\n", "import Loca from './Loca.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nLoca.install = (app: App) => {\r\n  app.component(Loca.name, Loca);\r\n  return app;\r\n};\r\nexport const ElAmapLoca = Loca as typeof Loca & Plugin;\r\nexport default ElAmapLoca;\r\n\r\nexport type ElAmapLocaInstance = InstanceType<typeof Loca>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPoint',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  // 图层里面元素的叠加效果，normal：正常透明度叠加，lighter：叠加后可能更加明亮\r\n  blend: {\r\n    type: String\r\n  } \r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PointLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PointLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          radius: 20,\r\n          color: '#fff',\r\n          unit: 'px',\r\n          borderWidth: 10,\r\n          borderColor: '#fff',\r\n          blurWidth: -1\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          radius: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.radius === undefined ? style.radius : feature.properties.radius;\r\n          },\r\n          color: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.color === undefined ? style.color : feature.properties.color;\r\n          },\r\n          unit: style.unit,\r\n          borderWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderWidth === undefined ? style.borderWidth : feature.properties.borderWidth;\r\n          },\r\n          borderColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderColor === undefined ? style.borderColor : feature.properties.borderColor;\r\n          },\r\n          blurWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.blurWidth === undefined ? style.blurWidth : feature.properties.blurWidth;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import PointLayer from './PointLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPointLayer.install = (app: App) => {\r\n  app.component(PointLayer.name, PointLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPoint = PointLayer as typeof PointLayer & Plugin;\r\nexport default ElAmapLocaPoint;\r\n\r\nexport type ElAmapLocaPointInstance = InstanceType<typeof PointLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPolygon',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  cullface: {\r\n    type: String\r\n  }, // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。\r\n  acceptLight: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 面是否接受光照，光照信息在 loca 对象中配置\r\n  shininess: {\r\n    type: Number\r\n  }, // 立体网格的粗糙度，值越高，说明表面越粗糙。\r\n  hasSide: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 当面有厚度的时候，有没有侧面\r\n  hasBottom: {\r\n    type: Boolean,\r\n    default: false\r\n  },//当面有厚度的时候，有没有底面。\r\n  blockHide: {\r\n    type: Boolean,\r\n    default: true\r\n  },//是否开启被遮挡的面隐藏，默认开启，如果关闭，在有透明度的时候，会显示出被遮挡的面。\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }// 是否开启深度检测，开启后可能会影响zIndex\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PolygonLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PolygonLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          topColor: '#fff',\r\n          sideTopColor: '#fff',\r\n          sideBottomColor: '#fff',\r\n          altitude: 0,\r\n          height: 0,\r\n          texture: null,\r\n          textureSize: [20, 3],\r\n          label: undefined,\r\n          labelAltitude: 0\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          topColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.topColor === undefined ? style.topColor : feature.properties.topColor;\r\n          },\r\n          sideTopColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideTopColor === undefined ? style.sideTopColor : feature.properties.sideTopColor;\r\n          },\r\n          sideBottomColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideBottomColor === undefined ? style.sideBottomColor : feature.properties.sideBottomColor;\r\n          },\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude;\r\n          },\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          texture: style.texture,\r\n          textureSize: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.textureSize === undefined ? style.textureSize : feature.properties.textureSize;\r\n          },\r\n          label: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.label === undefined ? style.label : feature.properties.label;\r\n          },\r\n          labelAltitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.labelAltitude === undefined ? style.labelAltitude : feature.properties.labelAltitude;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import PolygonLayer from './PolygonLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPolygonLayer.install = (app: App) => {\r\n  app.component(PolygonLayer.name, PolygonLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPolygon = PolygonLayer as typeof PolygonLayer & Plugin;\r\nexport default ElAmapLocaPolygon;\r\n\r\nexport type ElAmapLocaPolygonInstance = InstanceType<typeof PolygonLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPrism',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  cullface: {\r\n    type: String\r\n  }, // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。\r\n  acceptLight: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 面是否接受光照，光照信息在 loca 对象中配置\r\n  shininess: {\r\n    type: Number\r\n  }, // 立体网格的粗糙度，值越高，说明表面越粗糙。\r\n  hasSide: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 当面有厚度的时候，有没有侧面和底面\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }// 是否开启深度检测，开启后可能会影响zIndex\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PrismLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PrismLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          radius: 20,\r\n          unit: 'px',\r\n          sideNumber: 3,\r\n          rotation: 0,\r\n          altitude: 0,\r\n          height: 100,\r\n          topColor: '#fff',\r\n          sideTopColor: '#fff',\r\n          sideBottomColor: '#fff'\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          radius: style.radius,\r\n          unit: style.unit,\r\n          sideNumber: style.sideNumber,\r\n          rotation: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.rotation === undefined ? style.rotation : feature.properties.rotation;\r\n          },\r\n          altitude: style.altitude,\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          topColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.topColor === undefined ? style.topColor : feature.properties.topColor;\r\n          },\r\n          sideTopColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideTopColor === undefined ? style.sideTopColor : feature.properties.sideTopColor;\r\n          },\r\n          sideBottomColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.sideBottomColor === undefined ? style.sideBottomColor : feature.properties.sideBottomColor;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import PrismLayer from './PrismLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPrismLayer.install = (app: App) => {\r\n  app.component(PrismLayer.name, PrismLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPrism = PrismLayer as typeof PrismLayer & Plugin;\r\nexport default ElAmapLocaPrism;\r\n\r\nexport type ElAmapLocaPrismInstance = InstanceType<typeof PrismLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPulseLine',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PulseLineLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PulseLineLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          lineWidth: 1,\r\n          headColor: 'rgba(0, 0, 0, 0.75)',\r\n          trailColor: 'rgba(0, 0, 0, 0.25)',\r\n          altitude: 0,\r\n          interval: 1,\r\n          duration: 2000\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          lineWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.lineWidth === undefined ? style.lineWidth : feature.properties.lineWidth;\r\n          },\r\n          headColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.headColor === undefined ? style.headColor : feature.properties.headColor;\r\n          },\r\n          trailColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.trailColor === undefined ? style.trailColor : feature.properties.trailColor;\r\n          },\r\n          altitude: style.altitude,\r\n          interval: style.interval,\r\n          duration: style.duration\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import PulseLineLayer from './PulseLineLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPulseLineLayer.install = (app: App) => {\r\n  app.component(PulseLineLayer.name, PulseLineLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPulseLine = PulseLineLayer as typeof PulseLineLayer & Plugin;\r\nexport default ElAmapLocaPulseLine;\r\n\r\nexport type ElAmapLocaPulseLineInstance = InstanceType<typeof PulseLineLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPulseLink',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PulseLinkLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PulseLinkLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          lineColors: ['#fff'],\r\n          height: 100,\r\n          maxHeightScale: 0,\r\n          smoothSteps: 50,\r\n          lineWidth: [1, 1],\r\n          unit: 'px',\r\n          dash: [4000, 0, 4000, 0],\r\n          speed: 100,\r\n          headColor: 'rgba(0, 0, 0, 0.75)',\r\n          trailColor: 'rgba(0, 0, 0, 0.25)',\r\n          flowLength: 100\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          lineColors: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.lineColors === undefined ? style.lineColors : feature.properties.lineColors;\r\n          },\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          maxHeightScale: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.maxHeightScale === undefined ? style.maxHeightScale : feature.properties.maxHeightScale;\r\n          },\r\n          smoothSteps: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.smoothSteps === undefined ? style.smoothSteps : feature.properties.smoothSteps;\r\n          },\r\n          lineWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.lineWidth === undefined ? style.lineWidth : feature.properties.lineWidth;\r\n          },\r\n          unit: style.unit,\r\n          dash: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.dash === undefined ? style.dash : feature.properties.dash;\r\n          },\r\n          speed: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.speed === undefined ? style.speed : feature.properties.speed;\r\n          },\r\n          headColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.headColor === undefined ? style.headColor : feature.properties.headColor;\r\n          },\r\n          trailColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.trailColor === undefined ? style.trailColor : feature.properties.trailColor;\r\n          },\r\n          flowLength: style.flowLength\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import PulseLinkLayer from './PulseLinkLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPulseLinkLayer.install = (app: App) => {\r\n  app.component(PulseLinkLayer.name, PulseLinkLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPulseLink = PulseLinkLayer as typeof PulseLinkLayer & Plugin;\r\nexport default ElAmapLocaPulseLink;\r\n\r\nexport type ElAmapLocaPulseLinkInstance = InstanceType<typeof PulseLinkLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaScatter',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.ScatterLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.ScatterLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          size: [20, 20],\r\n          rotation: 0,\r\n          color: 'rgba(200,200,200,1)',\r\n          altitude: 0,\r\n          borderWidth: 0,\r\n          borderColor: 'rgba(250,250,250,1)',\r\n          texture: null,\r\n          unit: 'px',\r\n          animate: false,\r\n          duration: 0\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          size: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.size === undefined ? style.size : feature.properties.size;\r\n          },\r\n          rotation: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.rotation === undefined ? style.rotation : feature.properties.rotation;\r\n          },\r\n          color: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.color === undefined ? style.color : feature.properties.color;\r\n          },\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude;\r\n          },\r\n          borderWidth: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderWidth === undefined ? style.borderWidth : feature.properties.borderWidth;\r\n          },\r\n          borderColor: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.borderColor === undefined ? style.borderColor : feature.properties.borderColor;\r\n          },\r\n          texture: style.texture,\r\n          unit: style.unit,\r\n          animate: style.animate,\r\n          duration: style.duration\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import ScatterLayer from './ScatterLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nScatterLayer.install = (app: App) => {\r\n  app.component(ScatterLayer.name, ScatterLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaScatter = ScatterLayer as typeof ScatterLayer & Plugin;\r\nexport default ElAmapLocaScatter;\r\n\r\nexport type ElAmapLocaScatterInstance = InstanceType<typeof ScatterLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaZMarker',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.ZMarkerLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.ZMarkerLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          unit: 'px',\r\n          content: '',\r\n          size: [20, 20],\r\n          rotation: 0,\r\n          alwaysFront: false,\r\n          altitude: 0\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          unit: style.unit,\r\n          content: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.content === undefined ? style.content : feature.properties.content;\r\n          },\r\n          size: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.size === undefined ? style.size : feature.properties.size;\r\n          },\r\n          rotation: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.rotation === undefined ? style.rotation : feature.properties.rotation;\r\n          },\r\n          alwaysFront: style.alwaysFront,\r\n          altitude: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.altitude === undefined ? style.altitude : feature.properties.altitude ;\r\n          }\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import ZMarkerLayer from './ZMarkerLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nZMarkerLayer.install = (app: App) => {\r\n  app.component(ZMarkerLayer.name, ZMarkerLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaZMarker = ZMarkerLayer as typeof ZMarkerLayer & Plugin;\r\nexport default ElAmapLocaZMarker;\r\n\r\nexport type ElAmapLocaZMarkerInstance = InstanceType<typeof ZMarkerLayer>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister, buildProps} from \"@vuemap/vue-amap\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaAmbientLight',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(buildProps({\r\n  // 环境光颜色。\r\n  color: {\r\n    type: String\r\n  },\r\n  // 环境光强度。\r\n  intensity: {\r\n    type: Number\r\n  }, \r\n}));\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: any;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.AmbientLight, Loca.Container>((options, parentComponent) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.AmbientLight(options);\r\n    parentComponent.addLight($amapComponent);\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance.isDestroy){\r\n        parentInstance.$amapComponent.removeLight($amapComponent);\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import AmbientLight from './AmbientLight.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nAmbientLight.install = (app: App) => {\r\n  app.component(AmbientLight.name, AmbientLight);\r\n  return app;\r\n};\r\nexport const ElAmapLocaAmbientLight = AmbientLight as typeof AmbientLight & Plugin;\r\nexport default ElAmapLocaAmbientLight;\r\n\r\nexport type ElAmapLocaAmbientLightInstance = InstanceType<typeof AmbientLight>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister, buildProps} from \"@vuemap/vue-amap\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaDirectionalLight',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(buildProps({\r\n  color: {\r\n    type: String\r\n  }, // 环境光颜色。\r\n  intensity: {\r\n    type: Number\r\n  }, // 环境光强度。\r\n  position: {\r\n    type: Array,\r\n    required: true\r\n  }, // 坐标位置\r\n  target: {\r\n    type: Array\r\n  }, // 光射向的目标位置\r\n}));\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: any;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.DirectionalLight, Loca.Container>((options, parentComponent) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.DirectionalLight(options);\r\n    parentComponent.addLight($amapComponent);\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance.isDestroy){\r\n        parentInstance.$amapComponent.removeLight($amapComponent);\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import DirectionalLight from './DirectionalLight.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nDirectionalLight.install = (app: App) => {\r\n  app.component(DirectionalLight.name, DirectionalLight);\r\n  return app;\r\n};\r\nexport const ElAmapLocaDirectionalLight = DirectionalLight as typeof DirectionalLight & Plugin;\r\nexport default ElAmapLocaDirectionalLight;\r\n\r\nexport type ElAmapLocaDirectionalLightInstance = InstanceType<typeof DirectionalLight>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister, buildProps} from \"@vuemap/vue-amap\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaPointLight',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(buildProps({\r\n  color: {\r\n    type: String\r\n  }, // 点光颜色。\r\n  intensity: {\r\n    type: Number\r\n  }, // 光照强度。\r\n  position: {\r\n    type: Array,\r\n    required: true\r\n  }, // 点光位置\r\n  distance: {\r\n    type: Number\r\n  }, // 距离表示从光源到光照强度为 0 的位置，0 就是光不会消失\r\n}));\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: any;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.PointLight, Loca.Container>((options, parentComponent) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.PointLight(options);\r\n    parentComponent.addLight($amapComponent);\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance.isDestroy){\r\n        parentInstance.$amapComponent.removeLight($amapComponent);\r\n      }\r\n      $amapComponent = null;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import PointLight from './PointLight.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPointLight.install = (app: App) => {\r\n  app.component(PointLight.name, PointLight);\r\n  return app;\r\n};\r\nexport const ElAmapLocaPointLight = PointLight as typeof PointLight & Plugin;\r\nexport default ElAmapLocaPointLight;\r\n\r\nexport type ElAmapLocaPointLightInstance = InstanceType<typeof PointLight>\r\n", "<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"@vuemap/vue-amap\";\r\nimport {buildLocaProps, commonEmitNames} from \"../../utils/buildHelper\";\r\nimport {useWatchFn, useLocaEvents} from '../../mixins/useLoca';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaLaser',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildLocaProps({\r\n  depth: {\r\n    type: Boolean,\r\n    default: true\r\n  }// 图层中的要素是否具有前后遮盖关系，默认开启\r\n}));\r\nconst emits = defineEmits(commonEmitNames);\r\n\r\nlet $amapComponent: any;\r\n\r\nlet _destroyComponent: () => void;\r\nlet _setSource: () => void;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.LaserLayer, Loca.Container>((options) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.LaserLayer(options);\r\n    const useResult = useLocaEvents({\r\n      parentInstance,\r\n      $amapComponent,\r\n      emits,\r\n      props,\r\n      setStyle () {\r\n        const defaultStyleValue = {\r\n          unit: 'px',\r\n          height: 200,\r\n          color: 'rgba(255,255,0,0.5)',\r\n          angle: 0,\r\n          lineWidth: 2,\r\n          trailLength: 30,\r\n          duration: 2000,\r\n          interval: 0,\r\n          delay: 0,\r\n          repeat: undefined\r\n        };\r\n        const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);\r\n        const defaultLayerStyle = {\r\n          unit: style.unit,\r\n          height: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.height === undefined ? style.height : feature.properties.height;\r\n          },\r\n          color: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.color === undefined ? style.color : feature.properties.color;\r\n          },\r\n          angle: style.angle,\r\n          lineWidth: style.lineWidth,\r\n          trailLength: style.trailLength,\r\n          duration: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.duration === undefined ? style.duration : feature.properties.duration;\r\n          },\r\n          interval: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.interval === undefined ? style.interval : feature.properties.interval;\r\n          },\r\n          delay: (index, feature) => {\r\n            feature.properties = feature.properties || {};\r\n            return feature.properties.delay === undefined ? style.delay : feature.properties.delay;\r\n          },\r\n          repeat: style.repeat\r\n        };\r\n        const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);\r\n        $amapComponent.setStyle(layerStyle);\r\n      },\r\n    });\r\n    _destroyComponent = useResult._destroyComponent;\r\n    _setSource = useResult.setSource;\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: useWatchFn({\r\n    setSource (){\r\n      if(_setSource){\r\n        _setSource();\r\n      }\r\n    },\r\n    $amapComponent: () => $amapComponent,\r\n    props\r\n  }),\r\n  destroyComponent () {\r\n    if(_destroyComponent){\r\n      _destroyComponent();\r\n    }\r\n    $amapComponent = null;\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n", "import LaserLayer from './LaserLayer.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nLaserLayer.install = (app: App) => {\r\n  app.component(LaserLayer.name, LaserLayer);\r\n  return app;\r\n};\r\nexport const ElAmapLocaLaser = LaserLayer as typeof LaserLayer & Plugin;\r\nexport default ElAmapLocaLaser;\r\n\r\nexport type ElAmapLocaLaserInstance = InstanceType<typeof LaserLayer>\r\n", "import {ElAmapLocaGrid} from './packages/GridLayer'\r\nimport {ElAmapLocaHeatmap} from './packages/HeatMapLayer'\r\nimport {ElAmapLocaHexagon} from './packages/HexagonLayer'\r\nimport {ElAmapLocaIcon} from './packages/IconLayer'\r\nimport {ElAmapLocaLine} from './packages/LineLayer'\r\nimport {ElAmapLocaLink} from './packages/LinkLayer'\r\nimport {ElAmapLoca} from './packages/Loca'\r\nimport {ElAmapLocaPoint} from './packages/PointLayer'\r\nimport {ElAmapLocaPolygon} from './packages/PolygonLayer'\r\nimport {ElAmapLocaPrism} from './packages/PrismLayer'\r\nimport {ElAmapLocaPulseLine} from './packages/PulseLineLayer'\r\nimport {ElAmapLocaPulseLink} from './packages/PulseLinkLayer'\r\nimport {ElAmapLocaScatter} from './packages/ScatterLayer'\r\nimport {ElAmapLocaZMarker} from './packages/ZMarkerLayer'\r\nimport {ElAmapLocaAmbientLight} from './packages/AmbientLight'\r\nimport {ElAmapLocaDirectionalLight} from './packages/DirectionalLight'\r\nimport {ElAmapLocaPointLight} from './packages/PointLight'\r\nimport {ElAmapLocaLaser} from \"./packages/LaserLayer\"\r\n\r\nimport type { Plugin } from 'vue'\r\n\r\nexport default [\r\n  ElAmapLocaGrid,\r\n  ElAmapLocaHeatmap,\r\n  ElAmapLocaHexagon,\r\n  ElAmapLocaIcon,\r\n  ElAmapLocaLine,\r\n  ElAmapLocaLink,\r\n  ElAmapLoca,\r\n  ElAmapLocaPoint,\r\n  ElAmapLocaPolygon,\r\n  ElAmapLocaPrism,\r\n  ElAmapLocaPulseLine,\r\n  ElAmapLocaPulseLink,\r\n  ElAmapLocaScatter,\r\n  ElAmapLocaZMarker,\r\n  ElAmapLocaAmbientLight,\r\n  ElAmapLocaDirectionalLight,\r\n  ElAmapLocaPointLight,\r\n  ElAmapLocaLaser,\r\n] as Plugin[]\r\n", "import { makeInstaller } from '@vuemap/vue-amap';\r\nimport Components from './component';\r\n\r\nexport default makeInstaller([...Components]);\r\n", "import installer from './defaults';\r\nexport * from './packages';\r\nexport { default } from './defaults';\r\nexport const install = installer.install;\r\n"], "names": ["makeInstaller", "components", "apps", "app", "c", "convertEventToLowerCase", "functionName", "func", "eventReg", "upperCamelCase", "prop", "bindInstanceEvent", "instance", "eventName", "handler", "removeInstanceEvent", "commonProps", "buildProps", "props", "<PERSON><PERSON><PERSON>", "useRegister", "_init", "params", "componentInstance", "getCurrentInstance", "attrs", "parentInstance", "inject", "emits", "isMounted", "$amapComponent", "onMounted", "register", "onBeforeUnmount", "unregisterEvents", "stopWatchers", "destroyComponent", "onBeforeUpdate", "onUpdated", "registerEvents", "options", "convertProps", "mapInstance", "initProps", "setPropWatchers", "nextTick", "createChildren", "propStr", "handleFun", "getHandlerFun", "convertProxyToRaw", "convertSignalProp", "propsRedirect", "propsCache", "_key", "key", "props<PERSON><PERSON><PERSON>", "converters", "sourceData", "value", "isProxy", "toRaw", "unref", "unwatchFns", "watchRedirectFn", "flag", "handleProp", "watchOptions", "propValueType", "unwatch", "watch", "nv", "fn", "cacheEvents", "eventKey", "needInitComponents", "$$getInstance", "buildLocaProps", "commonEmitNames", "useWatchFn", "style", "_a", "useLocaEvents", "isDragging", "isRotating", "source", "setStyle", "setSource", "initComplete", "bindEvents", "map", "clickMap", "mouseMoveMap", "dragStart", "dragEnd", "rotateStart", "rotateEnd", "mouseoutMap", "rightclickMap", "e", "feature", "_getFeature", "unBindEvents", "__props", "__emit", "_destroyComponent", "_setSource", "resolve", "useResult", "defaultLayerStyle", "index", "layerStyle", "__expose", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElAmapLocaGrid", "HeatMapLayer", "ElAmapLocaHeatmap", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ElAmapLocaHexagon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElAmapLocaIcon", "LineLayer", "ElAmapLocaLine", "<PERSON><PERSON><PERSON><PERSON>", "ElAmapLocaLink", "provideData", "cb", "getMap", "provide", "hitFirst", "parentComponent", "features", "layers", "v", "a", "b", "layerLen", "i", "temp", "Loca", "ElAmapLoca", "<PERSON><PERSON><PERSON><PERSON>", "ElAmapLocaPoint", "PolygonLayer", "ElAmapLocaPolygon", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElAmapLocaPrism", "PulseLineLayer", "ElAmapLocaPulseLine", "PulseLinkLayer", "ElAmapLocaPulseLink", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ElAmapLocaScatter", "ZMarker<PERSON><PERSON><PERSON>", "ElAmapLocaZMarker", "AmbientLight", "ElAmapLocaAmbientLight", "DirectionalLight", "ElAmapLocaDirectionalLight", "PointLight", "ElAmapLocaPointLight", "Laser<PERSON><PERSON>er", "ElAmapLocaLaser", "m", "t", "Components", "install", "installer"], "mappings": "gUAEO,MAAMA,GAAgB,CAACC,EAAuB,KAAO,CAC1D,MAAMC,EAAc,GAUpB,MAAO,CACL,QATeC,GAAa,CAExBD,EAAK,SAASC,CAAG,IACrBD,EAAK,KAAKC,CAAG,EAEbF,EAAW,QAASG,GAAMD,EAAI,IAAIC,CAAC,CAAC,EACtC,CAIA,CACF,ECiDO,SAASC,GAAwBC,EAAa,CACnD,GAAG,CAACA,GAAgBA,EAAa,OAAS,EACxC,OAAOA,EAET,MAAMC,EAAOD,EAAa,UAAU,EAAGA,EAAa,MAAM,EAE1D,OADoBA,EAAa,CAAC,EAAE,YAAA,EACfC,CACvB,CAEa,MAAAC,GAAW,YAmCjB,SAASC,GAAeC,EAAK,CAClC,OAAIA,GAGGA,EAAK,OAAO,CAAC,EAAE,YAAA,EAAgBA,EAAK,MAAM,CAAC,CACpD,CC5GO,SAASC,GAAmBC,EAAeC,EAAmBC,EAAa,CAC7E,CAACF,GAAY,CAACA,EAAS,IAG1BA,EAAS,GAAGC,EAAWC,CAAO,CAChC,CAQO,SAASC,GAAqBH,EAAeC,EAAmBC,EAAa,CAC/E,CAACF,GAAY,CAACA,EAAS,KAG1BA,EAAS,IAAIC,EAAWC,CAAO,CACjC,CCJO,MAAME,GAA4B,CACvC,QAAS,CACP,KAAM,QACN,QAAS,EACX,EACA,OAAQ,CACN,KAAM,MACR,EACA,kBAAmB,CACjB,KAAM,QACN,QAAS,EACX,EACA,aAAc,CACZ,KAAM,MACR,CACF,EAMaC,GAA0DC,GAG9D,OAAO,OAAO,CAAA,EAAIF,GAAaE,CAAK,ECAhCC,GAAa,iBAEbC,EAAc,CAAaC,EAAyDC,IAAgC,CAC/H,IAAIC,EAAoBC,GAAmB,EACvC,CAAC,MAAAN,EAAO,MAAAO,CAAK,EAAIF,EACjBG,EAAiBC,GAAiCR,GAAY,MAAS,EAC3E,MAAMS,EAAQN,EAAO,MAErB,IAAIO,EAAY,GACZC,EAEJC,GAAU,IAAM,CACXL,EACEA,EAAe,eAChBM,EAEAN,EAAAA,EAAe,kBAAkBM,CAAQ,EAEpCV,EAAO,QACdU,EAAS,CAEb,CAAC,EACDC,GAAgB,IAAM,CAChBH,IAGJI,EAAAA,EACAC,EAAa,EACVb,EAAO,iBACRA,EAAO,iBAEPc,EAAAA,EAAAA,EAECd,EAAO,cACRA,EAAO,YAAY,UAAY,IAEjCI,EAAiB,OACjBR,EAAQ,OACRO,EAAQ,OACRF,EAAoB,OACpBO,EAAiB,OACnB,CAAC,EAEDO,GAAe,IAAM,CAChBnB,EAAM,mBAAqBW,GAAaC,GACzCI,EAEJ,CAAA,CAAC,EAEDI,GAAU,IAAM,CACXpB,EAAM,mBAAqBW,GAAaC,GACzCS,EAEJ,CAAA,CAAC,EAED,MAAMP,EAAW,IAAM,CACrB,MAAMQ,EAAUC,IAChBpB,EAAMmB,EAASd,GAAA,KAAA,OAAAA,EAAgB,cAAc,EAAE,KAAKgB,GAAe,CACjEZ,EAAiBY,EACjBH,EAAAA,EACAI,EAAU,EACVC,EAAgB,EAGhB,OAAO,OAAOrB,EAAkB,IAAKA,EAAkB,OAAO,EAG9DK,EAAM,OAAQE,EAAgBP,EAAkB,GAAG,EACnDsB,EAAS,IAAM,CACbC,GACF,CAAA,CAAC,EAAE,KAAK,EACRjB,EAAY,EACd,CAAC,CACH,EAGMc,EAAY,IAAM,CACJ,CAAC,WAAY,UAAW,OAAO,EAEvC,QAAQI,GAAW,CAC3B,GAAI7B,EAAM6B,CAAO,IAAM,OAAW,CAChC,MAAMC,EAAYC,EAAcF,CAAO,EACvCC,GAAaA,EAAU,KAAKlB,EAAgBoB,EAAkBC,EAAkBJ,EAAS7B,EAAM6B,CAAO,CAAC,CAAC,CAAC,CAC3G,CACF,CAAC,CACH,EAGMK,EAAgB9B,EAAO,eAAiB,CAAA,EACxCmB,EAAe,IAAM,CACzB,MAAMY,EAAkC,CAAA,EACxC,OAAGnC,EAAM,cACP,OAAO,OAAOmC,EAAYnC,EAAM,YAAY,EAE9C,OAAO,KAAKA,CAAK,EAAE,QAAQoC,GAAQ,CACjC,IAAIC,EAAMD,EACV,MAAME,EAAaL,EAAkBI,EAAKrC,EAAMqC,CAAG,CAAC,EAChDC,IAAe,SACbJ,GAAiBA,EAAcE,CAAI,IACrCC,EAAMH,EAAcG,CAAG,GAEzBF,EAAWE,CAAG,EAAIC,EAEtB,CAAC,EACMH,CACT,EAEMI,EAAanC,EAAO,UAAY,CAAC,EACjC6B,EAAoB,CAACI,EAAaG,IAClCD,GAAcA,EAAWF,CAAG,EACvBE,EAAWF,CAAG,EAAE,KAAK,OAAMG,CAAU,EAEvCA,EAGHR,EAAqBS,GACtBC,GAAQD,CAAK,EACPE,GAAMF,CAAK,EAEbG,GAAMH,CAAK,EAMpB,IAAII,EAAgC,CAAA,EAChCC,EAAyD,OAAO,OAAO,CACzE,UAAYC,GAAkB,CACvBnC,GAAoBA,EAAe,MAAaA,EAAe,OACjEmC,EAAkCnC,EAAe,KAA1CA,EAAAA,EAAe,KAAQ,EAEnC,EACA,SAAU6B,EAAc,CAClB7B,GAAkBA,EAAe,WACnCA,EAAe,UAAa6B,CAAK,CAErC,CACF,EAAGrC,EAAO,iBAAmB,CAAE,CAAA,EAC/B,MAAMsB,EAAkB,IAAM,CAE5B,OAAO,KAAK1B,CAAK,EAAE,QAAQR,GAAQ,CACjC,IAAIwD,EAAaxD,EACb0C,GAAiBA,EAAc1C,CAAI,IAAGwD,EAAad,EAAc1C,CAAI,GACzE,MAAMsC,EAAYC,EAAciB,CAAU,EAC1C,GAAI,CAAClB,EAAW,OAChB,MAAMmB,EAAe,CACnB,KAAM,EACR,EACMC,GAAgB,OAAO,UAAU,SAAS,KAAKlD,EAAMR,CAAI,CAAC,GAC3D0D,KAAkB,mBAAqBA,KAAkB,oBAC5DD,EAAa,KAAO,IAGtB,MAAME,GAAUC,GAAM,IAAMpD,EAAMR,CAAI,EAAG6D,IAAM,CAC7CvB,EAAU,KAAKlB,EAAgBoB,EAAkBC,EAAkBzC,EAAM6D,EAAE,CAAC,CAAC,CAC/E,EAAGJ,CAAY,EAGfJ,EAAW,KAAKM,EAAO,CACzB,CAAC,CACH,EAEMlC,EAAe,IAAM,CACzB4B,EAAW,QAAQS,GAAMA,EAAI,CAAA,EAC7BT,EAAa,CAAA,EACbC,EAAkB,MACpB,EAEMf,EAAiBvC,GACjBsD,EAAgB,KAAKtD,CAAI,EAAE,EACtBsD,EAAgB,KAAKtD,CAAI,EAAE,EAEhCoB,EAGGA,EAAe,MAAMrB,GAAeC,CAAI,CAAC,EAAE,EAFzC,KAQL+D,EAAmC,CAAA,EACnClC,EAAiB,IAAM,CAC3B,OAAO,KAAKd,CAAK,EAAE,QAAQ8B,GAAO,CAChC,GAAG/C,GAAS,KAAK+C,CAAG,EAAE,CACpB,MAAMmB,EAAWrE,GAAwBkD,CAAG,EAC5C5C,GAAkBmB,EAAgB4C,EAAUjD,EAAM8B,CAAG,CAAC,EACtDkB,EAAYC,CAAQ,EAAIjD,EAAM8B,CAAG,CACnC,CACF,CAAC,CACH,EACMrB,EAAmB,IAAM,CAC7B,OAAO,KAAKuC,CAAW,EAAE,QAAQC,GAAY,CAC3C3D,GAAoBe,EAAgB4C,EAAUD,EAAYC,CAAQ,CAAC,EACnE,OAAOD,EAAYC,CAAQ,CAC7B,CAAC,CACH,EAIM5B,GAAiB,IAAM,CAC3B,MAAM6B,EAAqBrD,EAAO,oBAAsB,CACxD,EAAA,KAAOqD,EAAmB,OAAS,GACjCA,EAAmB,CAAC,EACpBA,EAAAA,EAAmB,OAAO,EAAG,CAAC,CAElC,EAGMvC,EAAmB,IAAM,CACzBN,IAGHA,EAAuB,QAAWA,EAAuB,OAAO,IAAI,EACpEA,EAAuB,OAAUA,EAAuB,MACxDA,EAAAA,EAAuB,QAAWA,EAAuB,OAAO,MAAM,EACzE,EAEA,SAAS8C,GAAmB,CAC1B,OAAO9C,CACT,CACA,MAAO,CACL,cAAA8C,EACA,eAAAlD,EACA,UAAAG,CACF,CACF,ECnPagD,EAA6D3D,GAKjE,OAAO,OAAO,CAAIF,EAAAA,GAAa,CACpC,UAAW,CACT,KAAM,MACR,EACA,WAAY,CACV,KAAM,MACR,EACA,gBAAiB,CACf,KAAM,CAAC,YAAa,MAAM,EAC1B,SAAW,CACT,OAAO,IACT,CACF,EACA,WAAY,CACV,KAAM,MACR,EACA,kBAAmB,CACjB,KAAM,OACN,SAAW,CACT,MAAO,EACT,CACF,EACA,MAAO,CACL,KAAM,KACR,EACA,QAAS,CACP,KAAM,MACR,EACA,WAAY,CACV,KAAM,QACN,QAAS,EACX,EACA,gBAAiB,CACf,KAAM,OACN,QAAS,CACX,EACA,QAAS,CACP,KAAM,SACN,QAAS,IACX,EACA,YAAa,CACX,KAAM,SACN,QAAS,IACX,EACA,aAAc,CACZ,KAAM,SACN,QAAS,IACX,CACF,EAAiBE,CAAK,EAGX4D,EAAkB,CAAC,OAAQ,YAAa,QAAS,YAAa,ECpE3D,SAAAC,EAAYvC,EAAyB,CACnD,MAAO,CACL,aAAcwC,EAAY,CACxBnC,EAAS,IAAM,CAlBrB,IAAAoC,GAmBYA,EAAAzC,EAAQ,eAAR,IAAA,MAAAyC,EAA0B,UAC5BzC,EAAQ,iBAAiB,SAASwC,CAAK,CAE3C,CAAC,EAAE,KAAA,CACL,EACA,aAAc,CACZnC,EAAS,IAAM,CACbL,EAAQ,WACV,CAAC,EAAE,KAAK,CACV,EACA,cAAe,CACbK,EAAS,IAAM,CACbL,EAAQ,WACV,CAAC,EAAE,KAAK,CACV,EACA,mBAAoB,CAClBK,EAAS,IAAM,CACbL,EAAQ,UACV,CAAA,CAAC,EAAE,MACL,EACA,UAAWyB,EAAe,CACxB,MAAMnC,EAAiBU,EAAQ,eAAA,EAC3BV,GAAA,MAAAA,EAAgB,MAAQA,GAAA,MAAAA,EAAgB,OACzCmC,EAA4DnC,EAAe,KAAKU,EAAQ,MAAM,eAAe,EAAtGV,EAAe,KAAKU,EAAQ,MAAM,eAAe,EAE7D,CACF,CACF,CAEgB,SAAA0C,EAAe1C,EAM7B,CACA,IAAI2C,EAAa,GACbC,EAAa,GACbC,EACJ,KAAM,CAAC,eAAA3D,EAAgB,eAAAI,EAAgB,MAAAF,EAAO,MAAAV,EAAO,SAAAoE,CAAQ,EAAK9C,EAE5D+C,EAAY,IAAM,CAClBF,IACFA,EAAO,UACPA,EAAS,MAEPnE,EAAM,gBACL,OAAOA,EAAM,iBAAoB,SAClCmE,EAAS,IAAI,KAAK,gBAAgB,CAChC,IAAKnE,EAAM,eACb,CAAC,EAEDmE,EAAS,IAAI,KAAK,gBAAgB,CAChC,KAAMnE,EAAM,eACd,CAAC,EAEKA,EAAM,UACdmE,EAAS,IAAI,KAAK,cAAc,CAC9B,IAAKnE,EAAM,SACb,CAAC,EACQA,EAAM,WACfmE,EAAS,IAAI,KAAK,cAAc,CAC9B,KAAMnE,EAAM,UACd,CAAC,EAEDmE,EAAS,IAAI,KAAK,cAAc,EAC/B,EAEHvD,EAAe,UAAUuD,CAAM,CACjC,EAEMG,EAAe,IAAM,CACrBtE,EAAM,YACRuE,EAAAA,CAEJ,EACMA,EAAa,IAAM,CACvB,GAAG/D,EAAe,CAChB,MAAMgE,EAAMhE,EAAe,OAAO,EAC/BR,EAAM,UAAY,MACnBwE,EAAI,GAAG,QAASC,CAAQ,EAEvBzE,EAAM,cAAgB,OACvBwE,EAAI,GAAG,YAAaE,CAAY,EAChCF,EAAI,GAAG,YAAaG,CAAS,EAC7BH,EAAI,GAAG,UAAWI,CAAO,EACzBJ,EAAI,GAAG,cAAeK,CAAW,EACjCL,EAAI,GAAG,YAAaM,CAAS,EAC7BN,EAAI,GAAG,WAAYO,CAAW,GAE7B/E,EAAM,eAAiB,MACxBwE,EAAI,GAAG,aAAcQ,CAAa,CAEtC,CACF,EACMP,EAAYQ,GAAW,CAC3B,MAAMC,EAAUC,EAAYF,CAAC,EAC7BvE,EAAM,QAASwE,EAASD,CAAC,CAC3B,EACMD,EAAiBC,GAAW,CAChC,MAAMC,EAAUC,EAAYF,CAAC,EAC7BvE,EAAM,aAAcwE,EAASD,CAAC,CAChC,EACMP,EAAgBO,GAAU,CAC9B,GAAGhB,GAAcC,EACf,OAEF,MAAMgB,EAAUC,EAAYF,CAAC,EAC7BvE,EAAM,YAAawE,EAASD,CAAC,CAC/B,EACME,EAAeF,GACZrE,EAAe,aAAaqE,EAAE,MAAM,QAAS,CAAA,EAEhDN,EAAa,IAAM,CACvBV,EAAa,EACf,EACMW,EAAW,IAAM,CACrBX,EAAa,EACf,EACMc,EAAe,IAAM,CACzBd,EAAa,GACbC,EAAa,EACf,EACMW,EAAe,IAAM,CACzBX,EAAa,EACf,EACMY,EAAa,IAAM,CACvBZ,EAAa,EACf,EACMkB,EAAgB,IAAO,CAC3B,GAAG5E,EAAe,CAChB,MAAMgE,EAAMhE,EAAe,OAAA,EAC3BgE,EAAI,IAAI,QAASC,CAAQ,EACzBD,EAAI,IAAI,aAAcQ,CAAa,EACnCR,EAAI,IAAI,YAAaE,CAAY,EACjCF,EAAI,IAAI,YAAaG,CAAS,EAC9BH,EAAI,IAAI,UAAWI,CAAO,EAC1BJ,EAAI,IAAI,cAAeK,CAAW,EAClCL,EAAI,IAAI,YAAaM,CAAS,EAC9BN,EAAI,IAAI,WAAYO,CAAW,CACjC,CACF,EAEA,OAAAV,EAAU,EACVD,EAAS,EACT5D,GAAA,MAAAA,EAAgB,eAAe,IAAII,CACnC0D,EAAAA,EAAAA,EAcO,CACL,kBAbwB,IAAM,CAC9Bc,EAAa,EACT5E,GAAA,MAAAA,EAAgB,YAClBA,GAAA,MAAAA,EAAgB,eAAe,OAAOI,GACtCA,EAAe,WAEbuD,IACFA,EAAO,QAAA,EACPA,EAAS,KAEb,EAIE,UAAAE,CACF,CACF,wQCzKA,MAAMrE,EAAQqF,EAyBR3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAA6CoB,GAC5E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,UAAUU,CAAO,EAC3C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CAWV,MAAM8D,EAAQ,OAAO,OAAO,CAAA,EAVF,CACxB,SAAU,OACV,aAAc,OACd,gBAAiB,OACjB,SAAU,EACV,OAAQ,EACR,OAAQ,IACR,IAAK,EACL,KAAM,OACR,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,SAAU,CAACC,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,aAAc,CAACU,EAAOV,KACpBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,eAAiB,OAAYpB,EAAM,aAAeoB,EAAQ,WAAW,cAEjG,gBAAiB,CAACU,EAAOV,KACvBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,kBAAoB,OAAYpB,EAAM,gBAAkBoB,EAAQ,WAAW,iBAEvG,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,OAAQ,CAACU,EAAOV,KACdA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,SAAW,OAAYpB,EAAM,OAASoB,EAAQ,WAAW,QAErF,OAAQpB,EAAM,OACd,IAAKA,EAAM,IACX,KAAMA,EAAM,IACd,EACM+B,EAAa,OAAO,OAAO,CAAA,EAAIF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAiB,CAC3C,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAEJ,CAAA,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,IAEF3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,yFCvHDqC,EAAU,QAAW9G,IACnBA,EAAI,UAAU8G,EAAU,KAAMA,CAAS,EAChC9G,GAEI,MAAA+G,GAAiBD,wJCQ9B,MAAM/F,EAAQqF,EAMR3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAAgDoB,GAC/E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,aAAaU,CAAO,EAC9C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CAaV,MAAM8D,EAAQ,OAAO,OAAO,GAZF,CACxB,OAAQ,GACR,MAAO,GACP,SAAU,CAAC,GAAK,OAAQ,IAAM,mBAAoB,GAAK,iBAAkB,GAAK,UAAW,EAAK,KAAK,EACnG,QAAS,CAAC,EAAG,CAAC,EACd,OAAQ,IACR,aAAc,CAAC,GAAK,GAAK,GAAK,EAAG,EACjC,IAAK,KACL,IAAK,KACL,KAAM,KACN,WAAY,EACd,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,OAAQ,CAACC,EAAOV,KACdA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,SAAW,OAAYpB,EAAM,OAASoB,EAAQ,WAAW,QAErF,MAAO,CAACU,EAAOV,KACbA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,QAAU,OAAYpB,EAAM,MAAQoB,EAAQ,WAAW,OAEnF,SAAUpB,EAAM,SAChB,QAASA,EAAM,QACf,OAAQA,EAAM,OACd,aAAcA,EAAM,aACpB,IAAKA,EAAM,IACX,IAAKA,EAAM,IACX,KAAMA,EAAM,KACZ,WAAYA,EAAM,UACpB,EACM+B,EAAa,OAAO,OAAO,CAAC,EAAGF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAiB,CAC3C,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAAAA,CAEJ,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,EAAAA,EAEF3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,+FC/FDuC,EAAa,QAAWhH,IACtBA,EAAI,UAAUgH,EAAa,KAAMA,CAAY,EACtChH,GAEI,MAAAiH,GAAoBD,+QCQjC,MAAMjG,EAAQqF,EAoBR3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAAgDoB,GAC/E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,aAAaU,CAAO,EAC9C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CAWV,MAAM8D,EAAQ,OAAO,OAAO,CAAA,EAVF,CACxB,SAAU,OACV,aAAc,OACd,gBAAiB,OACjB,SAAU,EACV,OAAQ,EACR,OAAQ,IACR,IAAK,EACL,KAAM,OACR,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,SAAU,CAACC,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,aAAc,CAACU,EAAOV,KACpBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,eAAiB,OAAYpB,EAAM,aAAeoB,EAAQ,WAAW,cAEjG,gBAAiB,CAACU,EAAOV,KACvBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,kBAAoB,OAAYpB,EAAM,gBAAkBoB,EAAQ,WAAW,iBAEvG,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,OAAQ,CAACU,EAAOV,KACdA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,SAAW,OAAYpB,EAAM,OAASoB,EAAQ,WAAW,QAErF,OAAQpB,EAAM,OACd,IAAKA,EAAM,IACX,KAAMA,EAAM,IACd,EACM+B,EAAa,OAAO,OAAO,CAAA,EAAIF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAEJ,CAAA,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,IAEF3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,+FClHDyC,EAAa,QAAWlH,IACtBA,EAAI,UAAUkH,EAAa,KAAMA,CAAY,EACtClH,GAEI,MAAAmH,GAAoBD,mHCQjC,MAAMnG,EAAQqF,EAER3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAA6CoB,GAC5E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,UAAUU,CAAO,EAC3C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CASV,MAAM8D,EAAQ,OAAO,OAAO,CARF,EAAA,CACxB,KAAM,KACN,KAAM,GACN,SAAU,CAAC,GAAI,EAAE,EACjB,SAAU,EACV,QAAS,EACT,OAAQ,CAAC,EAAG,CAAC,CACf,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,KAAM7B,EAAM,KACZ,KAAM,CAAC8B,EAAOV,KACZA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,OAAS,OAAYpB,EAAM,KAAOoB,EAAQ,WAAW,MAEjF,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,QAAS,CAACU,EAAOV,KACfA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,UAAY,OAAYpB,EAAM,QAAUoB,EAAQ,WAAW,SAEvF,OAAQ,CAACU,EAAOV,KACdA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,SAAW,OAAYpB,EAAM,OAASoB,EAAQ,WAAW,OAEvF,EACMW,EAAa,OAAO,OAAO,CAAA,EAAIF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAAW,CAEf,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,EAEF3E,EAAAA,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,yFC5FD2C,EAAU,QAAWpH,IACnBA,EAAI,UAAUoH,EAAU,KAAMA,CAAS,EAChCpH,GAEI,MAAAqH,GAAiBD,mHCQ9B,MAAMrG,EAAQqF,EAER3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAA6CoB,GAC5E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,UAAUU,CAAO,EAC3C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CASV,MAAM8D,EAAQ,OAAO,OAAO,CARF,EAAA,CACxB,MAAO,OACP,UAAW,EACX,SAAU,EACV,YAAa,EACb,YAAa,OACb,UAAW,CAAC,GAAI,EAAG,GAAI,CAAC,CAC1B,EACmD9D,EAAM,iBAAiB,EA2BpE6F,EAAa,OAAO,OAAO,CAAC,EA1BR,CACxB,MAAO,CAACD,EAAOV,KACbA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,QAAU,OAAYpB,EAAM,MAAQoB,EAAQ,WAAW,OAEnF,UAAW,CAACU,EAAOV,KACjBA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,YAAc,OAAYpB,EAAM,UAAYoB,EAAQ,WAAW,WAE3F,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,YAAa,CAACU,EAAOV,KACnBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,cAAgB,OAAYpB,EAAM,YAAcoB,EAAQ,WAAW,aAE/F,YAAa,CAACU,EAAOV,KACnBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,cAAgB,OAAYpB,EAAM,YAAcoB,EAAQ,WAAW,aAE/F,UAAW,CAACU,EAAOV,KACjBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,YAAc,OAAYpB,EAAM,UAAYoB,EAAQ,WAAW,UAE7F,EACwDlF,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAEJ,CAAA,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,EAAkB,EAEpB3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,yFC/FD6C,EAAU,QAAWtH,IACnBA,EAAI,UAAUsH,EAAU,KAAMA,CAAS,EAChCtH,GAEI,MAAAuH,GAAiBD,mHCQ9B,MAAMvG,EAAQqF,EAER3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAA6CoB,GAC5E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,UAAUU,CAAO,EAC3C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CAMV,MAAM8D,EAAQ,OAAO,OAAO,CALF,EAAA,CACxB,WAAY,CAAC,sBAAuB,qBAAqB,EACzD,OAAQ,IACR,YAAa,GACf,EACmD9D,EAAM,iBAAiB,EAepE6F,EAAa,OAAO,OAAO,GAdP,CACxB,WAAY,CAACD,EAAOV,KAClBA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,aAAe,OAAYpB,EAAM,WAAaoB,EAAQ,WAAW,YAE7F,OAAQ,CAACU,EAAOV,KACdA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,SAAW,OAAYpB,EAAM,OAASoB,EAAQ,WAAW,QAErF,YAAa,CAACU,EAAOV,KACnBA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,cAAgB,OAAYpB,EAAM,YAAcoB,EAAQ,WAAW,YAEjG,EACwDlF,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,GAEJ,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,IAEF3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,yFChFD+C,EAAU,QAAWxH,IACnBA,EAAI,UAAUwH,EAAU,KAAMA,CAAS,EAChCxH,GAEI,MAAAyH,GAAiBD,+VCU9B,MAAMhD,EAAoC,CAAA,EACpCkD,EAA2B,CAC/B,eAAgB,OAChB,kBAAmBC,EAAG,CACpBnD,EAAmB,KAAKmD,CAAE,CAC5B,EACA,UAAW,GACX,OAAQ,IACCC,EAEX,CAAA,EAEAC,GAAQ7G,GAAY0G,CAAW,EAE/B,MAAM3G,EAAQqF,EA6BR3E,EAAQ4E,EAEd,IAAI1E,EACAqD,EAAa,GACbC,EAAa,GACb6C,EAAW,GAEf,KAAM,CAAC,cAAArD,EAAe,eAAAlD,CAAc,EAAIN,EAAsC,CAACoB,EAAS0F,IAC/E,IAAI,QAAcvB,GAAY,CACnC7E,EAAiB,IAAI,KAAK,UAAU,CAClC,IAAKoG,CACP,CAAC,EACDL,EAAY,eAAiB/F,EACzBU,EAAQ,WACVV,EAAe,SAAWU,EAAQ,UAEhCA,EAAQ,WACVV,EAAe,SAAWU,EAAQ,UAEhCA,EAAQ,aACVV,EAAe,WAAaU,EAAQ,YAEtCyF,EAAWzF,EAAQ,aAAa,SAChCiD,EAAW,EACXkB,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,mBAAA+C,EACA,YAAAkD,EACA,kBAAoB,CACd/F,IACFwE,KACGxE,EAAe,SAAWA,EAAe,QAAQ,MAClDA,EAAe,QAAQ,KAEzBA,EAAAA,EAAe,QAAQ,EACvBA,EAAiB,KAErB,CACF,CAAC,EAEKiG,EAAS,IACNrG,GAAA,KAAAA,OAAAA,EAAgB,eAGnB+D,EAAa,IAAM,CACvB,GAAG/D,EAAe,CAChB,MAAMgE,EAAMqC,EAAAA,EACT7G,EAAM,UAAY,MACnBwE,EAAI,GAAG,QAASC,CAAQ,EAEvBzE,EAAM,cAAgB,OACvBwE,EAAI,GAAG,YAAaE,CAAY,EAChCF,EAAI,GAAG,YAAaG,CAAS,EAC7BH,EAAI,GAAG,UAAWI,CAAO,EACzBJ,EAAI,GAAG,cAAeK,CAAW,EACjCL,EAAI,GAAG,YAAaM,CAAS,EAC7BN,EAAI,GAAG,WAAYO,CAAW,GAE7B/E,EAAM,eAAiB,MACxBwE,EAAI,GAAG,aAAcQ,CAAa,CAEtC,CACF,EACMP,EAAYQ,GAAM,CACtB,MAAMgC,EAAW9B,EAAYF,CAAC,EAC9BvE,EAAM,QAASuG,EAAUhC,CAAC,CAC5B,EACMD,EAAiBC,GAAM,CAC3B,MAAMgC,EAAW9B,EAAYF,CAAC,EAC9BvE,EAAM,aAAcuG,EAAUhC,CAAC,CACjC,EACMP,EAAgBO,GAAM,CAC1B,GAAGhB,GAAcC,EACf,OAEF,MAAM+C,EAAW9B,EAAYF,CAAC,EAC9BvE,EAAM,YAAauG,EAAUhC,CAAC,CAChC,EACME,EAAeF,GAAM,CACzB,MAAMgC,EAAkB,CAAA,EACxB,GAAGrG,EAAe,OAAO,CACvB,MAAMsG,EAAgB,CAAA,EACtBtG,EAAe,OAAO,QAAUuG,GAAK,CACnCD,EAAO,KAAKC,CAAC,CACf,CAAE,EACFD,EAAO,KAAK,CAACE,EAAEC,IAAMA,EAAE,OAASD,EAAE,MAAM,EACxC,MAAME,EAAWJ,EAAO,OACxB,QAAQK,EAAE,EAAEA,EAAED,EAASC,IAAI,CACzB,MAAMC,EAAON,EAAOK,CAAC,EAAE,aAAatC,EAAE,MAAM,QAAQ,CAAC,EACrD,GAAGuC,IACDP,EAAS,KAAKO,CAAI,EACfT,GACD,KAGN,CACF,CAEA,OAAOE,CACT,EACMtC,EAAY,IAAM,CACtBV,EAAa,EACf,EACMW,EAAU,IAAM,CACpBX,EAAa,EACf,EACMc,EAAc,IAAM,CACxBd,EAAa,GACbC,EAAa,EACf,EACMW,EAAc,IAAM,CACxBX,EAAa,EACf,EACMY,EAAY,IAAM,CACtBZ,EAAa,EACf,EACMkB,GAAe,IAAM,CACzB,GAAG5E,EAAe,CAChB,MAAMgE,EAAMqC,EAAO,EACnBrC,EAAI,IAAI,QAASC,CAAQ,EACzBD,EAAI,IAAI,aAAcQ,CAAa,EACnCR,EAAI,IAAI,YAAaE,CAAY,EACjCF,EAAI,IAAI,YAAaG,CAAS,EAC9BH,EAAI,IAAI,UAAWI,CAAO,EAC1BJ,EAAI,IAAI,cAAeK,CAAW,EAClCL,EAAI,IAAI,YAAaM,CAAS,EAC9BN,EAAI,IAAI,WAAYO,CAAW,CACjC,CACF,EAEA,OAAAe,EAAa,CACX,cAAApC,CACF,CAAC,6GChMD+D,EAAK,QAAWxI,IACdA,EAAI,UAAUwI,EAAK,KAAMA,CAAI,EACtBxI,GAEI,MAAAyI,GAAaD,wICQ1B,MAAMzH,EAAQqF,EAMR3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAA8CoB,GAC7E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,WAAWU,CAAO,EAC5C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CASV,MAAM8D,EAAQ,OAAO,OAAO,CARF,EAAA,CACxB,OAAQ,GACR,MAAO,OACP,KAAM,KACN,YAAa,GACb,YAAa,OACb,UAAW,EACb,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,OAAQ,CAACC,EAAOV,KACdA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,SAAW,OAAYpB,EAAM,OAASoB,EAAQ,WAAW,QAErF,MAAO,CAACU,EAAOV,KACbA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,QAAU,OAAYpB,EAAM,MAAQoB,EAAQ,WAAW,OAEnF,KAAMpB,EAAM,KACZ,YAAa,CAAC8B,EAAOV,KACnBA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,cAAgB,OAAYpB,EAAM,YAAcoB,EAAQ,WAAW,aAE/F,YAAa,CAACU,EAAOV,KACnBA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,cAAgB,OAAYpB,EAAM,YAAcoB,EAAQ,WAAW,aAE/F,UAAW,CAACU,EAAOV,KACjBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,YAAc,OAAYpB,EAAM,UAAYoB,EAAQ,WAAW,UAE7F,EACMW,EAAa,OAAO,OAAO,CAAA,EAAIF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAAW,CAEf,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,EAEF3E,EAAAA,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,2FChGDiE,EAAW,QAAW1I,IACpBA,EAAI,UAAU0I,EAAW,KAAMA,CAAU,EAClC1I,GAEI,MAAA2I,GAAkBD,uVCQ/B,MAAM3H,EAAQqF,EA4BR3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAAgDoB,GAC/E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,aAAaU,CAAO,EAC9C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CAYV,MAAM8D,EAAQ,OAAO,OAAO,CAAA,EAXF,CACxB,SAAU,OACV,aAAc,OACd,gBAAiB,OACjB,SAAU,EACV,OAAQ,EACR,QAAS,KACT,YAAa,CAAC,GAAI,CAAC,EACnB,MAAO,OACP,cAAe,CACjB,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,SAAU,CAACC,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,aAAc,CAACU,EAAOV,KACpBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,eAAiB,OAAYpB,EAAM,aAAeoB,EAAQ,WAAW,cAEjG,gBAAiB,CAACU,EAAOV,KACvBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,kBAAoB,OAAYpB,EAAM,gBAAkBoB,EAAQ,WAAW,iBAEvG,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,OAAQ,CAACU,EAAOV,KACdA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,SAAW,OAAYpB,EAAM,OAASoB,EAAQ,WAAW,QAErF,QAASpB,EAAM,QACf,YAAa,CAAC8B,EAAOV,KACnBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,cAAgB,OAAYpB,EAAM,YAAcoB,EAAQ,WAAW,aAE/F,MAAO,CAACU,EAAOV,KACbA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,QAAU,OAAYpB,EAAM,MAAQoB,EAAQ,WAAW,OAEnF,cAAe,CAACU,EAAOV,KACrBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,gBAAkB,OAAYpB,EAAM,cAAgBoB,EAAQ,WAAW,cAErG,EACMW,EAAa,OAAO,OAAO,CAAA,EAAIF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAEJ,CAAA,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,IAEF3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,+FCrIDmE,EAAa,QAAW5I,IACtBA,EAAI,UAAU4I,EAAa,KAAMA,CAAY,EACtC5I,GAEI,MAAA6I,GAAoBD,2QCQjC,MAAM7H,EAAQqF,EAoBR3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAA8CoB,GAC7E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,WAAWU,CAAO,EAC5C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CAYV,MAAM8D,EAAQ,OAAO,OAAO,CAAC,EAXH,CACxB,OAAQ,GACR,KAAM,KACN,WAAY,EACZ,SAAU,EACV,SAAU,EACV,OAAQ,IACR,SAAU,OACV,aAAc,OACd,gBAAiB,MACnB,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,OAAQ7B,EAAM,OACd,KAAMA,EAAM,KACZ,WAAYA,EAAM,WAClB,SAAU,CAAC8B,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,SAAUpB,EAAM,SAChB,OAAQ,CAAC8B,EAAOV,KACdA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,SAAW,OAAYpB,EAAM,OAASoB,EAAQ,WAAW,QAErF,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,aAAc,CAACU,EAAOV,KACpBA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,eAAiB,OAAYpB,EAAM,aAAeoB,EAAQ,WAAW,cAEjG,gBAAiB,CAACU,EAAOV,KACvBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,kBAAoB,OAAYpB,EAAM,gBAAkBoB,EAAQ,WAAW,gBAEzG,EACMW,EAAa,OAAO,OAAO,CAAA,EAAIF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAAW,CAEf,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,IAEF3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,2FCpHDqE,EAAW,QAAW9I,IACpBA,EAAI,UAAU8I,EAAW,KAAMA,CAAU,EAClC9I,GAEI,MAAA+I,GAAkBD,4JCQ/B,MAAM/H,EAAQqF,EAMR3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAAkDoB,GACjF,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,eAAeU,CAAO,EAChD,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CASV,MAAM8D,EAAQ,OAAO,OAAO,GARF,CACxB,UAAW,EACX,UAAW,sBACX,WAAY,sBACZ,SAAU,EACV,SAAU,EACV,SAAU,GACZ,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,UAAW,CAACC,EAAOV,KACjBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,YAAc,OAAYpB,EAAM,UAAYoB,EAAQ,WAAW,WAE3F,UAAW,CAACU,EAAOV,KACjBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,YAAc,OAAYpB,EAAM,UAAYoB,EAAQ,WAAW,WAE3F,WAAY,CAACU,EAAOV,KAClBA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,aAAe,OAAYpB,EAAM,WAAaoB,EAAQ,WAAW,YAE7F,SAAUpB,EAAM,SAChB,SAAUA,EAAM,SAChB,SAAUA,EAAM,QAClB,EACM+B,EAAa,OAAO,OAAO,CAAA,EAAIF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAAAA,CAEJ,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,EAEF3E,EAAAA,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,mGC1FDuE,EAAe,QAAWhJ,IACxBA,EAAI,UAAUgJ,EAAe,KAAMA,CAAc,EAC1ChJ,GAEI,MAAAiJ,GAAsBD,4JCQnC,MAAMjI,EAAQqF,EAMR3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAAkDoB,GACjF,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,eAAeU,CAAO,EAChD,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CAcV,MAAM8D,EAAQ,OAAO,OAAO,CAAA,EAbF,CACxB,WAAY,CAAC,MAAM,EACnB,OAAQ,IACR,eAAgB,EAChB,YAAa,GACb,UAAW,CAAC,EAAG,CAAC,EAChB,KAAM,KACN,KAAM,CAAC,IAAM,EAAG,IAAM,CAAC,EACvB,MAAO,IACP,UAAW,sBACX,WAAY,sBACZ,WAAY,GACd,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,WAAY,CAACC,EAAOV,KAClBA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,aAAe,OAAYpB,EAAM,WAAaoB,EAAQ,WAAW,YAE7F,OAAQ,CAACU,EAAOV,KACdA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,SAAW,OAAYpB,EAAM,OAASoB,EAAQ,WAAW,QAErF,eAAgB,CAACU,EAAOV,KACtBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,iBAAmB,OAAYpB,EAAM,eAAiBoB,EAAQ,WAAW,gBAErG,YAAa,CAACU,EAAOV,KACnBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,cAAgB,OAAYpB,EAAM,YAAcoB,EAAQ,WAAW,aAE/F,UAAW,CAACU,EAAOV,KACjBA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,YAAc,OAAYpB,EAAM,UAAYoB,EAAQ,WAAW,WAE3F,KAAMpB,EAAM,KACZ,KAAM,CAAC8B,EAAOV,KACZA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,OAAS,OAAYpB,EAAM,KAAOoB,EAAQ,WAAW,MAEjF,MAAO,CAACU,EAAOV,KACbA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,QAAU,OAAYpB,EAAM,MAAQoB,EAAQ,WAAW,OAEnF,UAAW,CAACU,EAAOV,KACjBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,YAAc,OAAYpB,EAAM,UAAYoB,EAAQ,WAAW,WAE3F,WAAY,CAACU,EAAOV,KAClBA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,aAAe,OAAYpB,EAAM,WAAaoB,EAAQ,WAAW,YAE7F,WAAYpB,EAAM,UACpB,EACM+B,EAAa,OAAO,OAAO,CAAC,EAAGF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,GAEJ,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,IAEF3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,mGCtHDyE,EAAe,QAAWlJ,IACxBA,EAAI,UAAUkJ,EAAe,KAAMA,CAAc,EAC1ClJ,GAEI,MAAAmJ,GAAsBD,yHCQnC,MAAMnI,EAAQqF,EAER3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAAgDoB,GAC/E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,aAAaU,CAAO,EAC9C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CAaV,MAAM8D,EAAQ,OAAO,OAAO,CAAA,EAZF,CACxB,KAAM,CAAC,GAAI,EAAE,EACb,SAAU,EACV,MAAO,sBACP,SAAU,EACV,YAAa,EACb,YAAa,sBACb,QAAS,KACT,KAAM,KACN,QAAS,GACT,SAAU,CACZ,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,KAAM,CAACC,EAAOV,KACZA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,OAAS,OAAYpB,EAAM,KAAOoB,EAAQ,WAAW,MAEjF,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,MAAO,CAACU,EAAOV,KACbA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,QAAU,OAAYpB,EAAM,MAAQoB,EAAQ,WAAW,OAEnF,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,YAAa,CAACU,EAAOV,KACnBA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,cAAgB,OAAYpB,EAAM,YAAcoB,EAAQ,WAAW,aAE/F,YAAa,CAACU,EAAOV,KACnBA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,cAAgB,OAAYpB,EAAM,YAAcoB,EAAQ,WAAW,aAE/F,QAASpB,EAAM,QACf,KAAMA,EAAM,KACZ,QAASA,EAAM,QACf,SAAUA,EAAM,QAClB,EACM+B,EAAa,OAAO,OAAO,CAAA,EAAIF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAEJ,CAAA,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,IAEF3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,+FCvGD2E,EAAa,QAAWpJ,IACtBA,EAAI,UAAUoJ,EAAa,KAAMA,CAAY,EACtCpJ,GAEI,MAAAqJ,GAAoBD,yHCQjC,MAAMrI,EAAQqF,EAER3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAAgDoB,GAC/E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,aAAaU,CAAO,EAC9C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CASV,MAAM8D,EAAQ,OAAO,OAAO,GARF,CACxB,KAAM,KACN,QAAS,GACT,KAAM,CAAC,GAAI,EAAE,EACb,SAAU,EACV,YAAa,GACb,SAAU,CACZ,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,KAAM7B,EAAM,KACZ,QAAS,CAAC8B,EAAOV,KACfA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,UAAY,OAAYpB,EAAM,QAAUoB,EAAQ,WAAW,SAEvF,KAAM,CAACU,EAAOV,KACZA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,OAAS,OAAYpB,EAAM,KAAOoB,EAAQ,WAAW,MAEjF,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,YAAapB,EAAM,YACnB,SAAU,CAAC8B,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,GACpCA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,SAE3F,EACMW,EAAa,OAAO,OAAO,CAAIF,EAAAA,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,GAEJ,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,EAAkB,EAEpB3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,+FCzFD6E,EAAa,QAAWtJ,IACtBA,EAAI,UAAUsJ,EAAa,KAAMA,CAAY,EACtCtJ,GAEI,MAAAuJ,GAAoBD,iLCgBjC,MAAM7H,EAAQ4E,EAEd,IAAI1E,EAEJ,KAAM,CAAC,cAAA8C,EAAe,eAAAlD,CAAc,EAAIN,EAA+C,CAACoB,EAAS0F,IACxF,IAAI,QAAcvB,GAAY,CACnC7E,EAAiB,IAAI,KAAK,aAAaU,CAAO,EAC9C0F,EAAgB,SAASpG,CAAc,EACvC6E,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,kBAAoB,CACdE,GAAkBJ,GAAA,MAAAA,EAAgB,iBAChCA,EAAe,WACjBA,EAAe,eAAe,YAAYI,CAAc,EAE1DA,EAAiB,KAErB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,+FC7CD+E,EAAa,QAAWxJ,IACtBA,EAAI,UAAUwJ,EAAa,KAAMA,CAAY,EACtCxJ,GAEI,MAAAyJ,GAAyBD,gPCqBtC,MAAM/H,EAAQ4E,EAEd,IAAI1E,EAEJ,KAAM,CAAC,cAAA8C,EAAe,eAAAlD,CAAc,EAAIN,EAAmD,CAACoB,EAAS0F,IAC5F,IAAI,QAAcvB,GAAY,CACnC7E,EAAiB,IAAI,KAAK,iBAAiBU,CAAO,EAClD0F,EAAgB,SAASpG,CAAc,EACvC6E,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,kBAAoB,CACdE,GAAkBJ,GAAA,MAAAA,EAAgB,iBAChCA,EAAe,WACjBA,EAAe,eAAe,YAAYI,CAAc,EAE1DA,EAAiB,KAErB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,wGClDDiF,GAAiB,QAAW1J,IAC1BA,EAAI,UAAU0J,GAAiB,KAAMA,EAAgB,EAC9C1J,GAEI,MAAA2J,GAA6BD,wOCqB1C,MAAMjI,EAAQ4E,EAEd,IAAI1E,EAEJ,KAAM,CAAC,cAAA8C,EAAe,eAAAlD,CAAc,EAAIN,EAA6C,CAACoB,EAAS0F,IACtF,IAAI,QAAcvB,GAAY,CACnC7E,EAAiB,IAAI,KAAK,WAAWU,CAAO,EAC5C0F,EAAgB,SAASpG,CAAc,EACvC6E,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,kBAAoB,CACdE,GAAkBJ,GAAA,MAAAA,EAAgB,iBAChCA,EAAe,WACjBA,EAAe,eAAe,YAAYI,CAAc,EAE1DA,EAAiB,KAErB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,4FClDDmF,GAAW,QAAW5J,IACpBA,EAAI,UAAU4J,GAAW,KAAMA,EAAU,EAClC5J,GAEI,MAAA6J,GAAuBD,sJCQpC,MAAM7I,EAAQqF,EAMR3E,EAAQ4E,EAEd,IAAI1E,EAEA2E,EACAC,EAEJ,KAAM,CAAC,cAAA9B,EAAe,eAAAlD,CAAc,EAAIN,EAA8CoB,GAC7E,IAAI,QAAcmE,GAAY,CACnC7E,EAAiB,IAAI,KAAK,WAAWU,CAAO,EAC5C,MAAMoE,EAAY1B,EAAc,CAC9B,eAAAxD,EACA,eAAAI,EACA,MAAAF,EACA,MAAAV,EACA,UAAY,CAaV,MAAM8D,EAAQ,OAAO,OAAO,GAZF,CACxB,KAAM,KACN,OAAQ,IACR,MAAO,sBACP,MAAO,EACP,UAAW,EACX,YAAa,GACb,SAAU,IACV,SAAU,EACV,MAAO,EACP,OAAQ,MACV,EACmD9D,EAAM,iBAAiB,EACpE2F,EAAoB,CACxB,KAAM7B,EAAM,KACZ,OAAQ,CAAC8B,EAAOV,KACdA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,SAAW,OAAYpB,EAAM,OAASoB,EAAQ,WAAW,QAErF,MAAO,CAACU,EAAOV,KACbA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,QAAU,OAAYpB,EAAM,MAAQoB,EAAQ,WAAW,OAEnF,MAAOpB,EAAM,MACb,UAAWA,EAAM,UACjB,YAAaA,EAAM,YACnB,SAAU,CAAC8B,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CAAC,EACrCA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,SAAU,CAACU,EAAOV,KAChBA,EAAQ,WAAaA,EAAQ,YAAc,CACpCA,EAAAA,EAAQ,WAAW,WAAa,OAAYpB,EAAM,SAAWoB,EAAQ,WAAW,UAEzF,MAAO,CAACU,EAAOV,KACbA,EAAQ,WAAaA,EAAQ,YAAc,CAAA,EACpCA,EAAQ,WAAW,QAAU,OAAYpB,EAAM,MAAQoB,EAAQ,WAAW,OAEnF,OAAQpB,EAAM,MAChB,EACM+B,EAAa,OAAO,OAAO,CAAA,EAAIF,EAAmB3F,EAAM,UAAU,EACxEY,EAAe,SAASiF,CAAU,CACpC,CACF,CAAC,EACDN,EAAoBG,EAAU,kBAC9BF,EAAaE,EAAU,UACvBD,EAAQ7E,CAAc,CACxB,CAAC,EAEA,CACD,MAAAF,EACA,gBAAiBmD,EAAW,CAC1B,WAAY,CACP2B,GACDA,EAEJ,CAAA,EACA,eAAgB,IAAM5E,EACtB,MAAAZ,CACF,CAAC,EACD,kBAAoB,CACfuF,GACDA,IAEF3E,EAAiB,IACnB,CACF,CAAC,EAED,OAAAkF,EAAa,CACX,cAAApC,CACF,CAAC,4FCxGDqF,GAAW,QAAW9J,IACpBA,EAAI,UAAU8J,GAAW,KAAMA,EAAU,EAClC9J,GAEI,MAAA+J,GAAkBD,GCe/B,IAAAE,GAAe,CACbjD,GACAE,GACAE,GACAE,GACAE,GACAE,GACAgB,GACAE,GACAE,GACAE,GACAE,GACAE,GACAE,GACAE,GACAE,GACAE,GACAE,GACAE,EACF,ECrCAE,GAAepK,GAAc,CAAC,GAAGqK,EAAU,CAAC,ECA/B,MAAAC,GAAUC,GAAU"}