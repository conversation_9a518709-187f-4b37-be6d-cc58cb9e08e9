{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/vector/Rectangle/index.ts"], "sourcesContent": ["import Rectangle from './Rectangle.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nRectangle.install = (app: App) => {\r\n  app.component(Rectangle.name, Rectangle);\r\n  return app;\r\n};\r\nexport const ElAmapRectangle = Rectangle as typeof Rectangle & Plugin;\r\nexport default ElAmapRectangle;\r\n\r\nexport type ElAmapRectangleInstance = InstanceType<typeof Rectangle>\r\n"], "names": ["Rectangle"], "mappings": ";;;AAEAA,MAAU,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AAChC,EAAI,GAAA,CAAA,SAAA,CAAUA,MAAU,CAAA,IAAA,EAAMA,MAAS,CAAA,CAAA;AACvC,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AACO,MAAM,eAAkB,GAAAA;;;;"}