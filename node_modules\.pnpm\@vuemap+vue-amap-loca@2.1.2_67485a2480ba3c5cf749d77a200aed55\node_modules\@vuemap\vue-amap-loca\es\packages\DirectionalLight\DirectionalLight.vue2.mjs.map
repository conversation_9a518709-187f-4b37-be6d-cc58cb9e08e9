{"version": 3, "file": "DirectionalLight.vue2.mjs", "sources": ["../../../../packages/DirectionalLight/DirectionalLight.vue"], "sourcesContent": ["<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister, buildProps} from \"@vuemap/vue-amap\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapLocaDirectionalLight',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(buildProps({\r\n  color: {\r\n    type: String\r\n  }, // 环境光颜色。\r\n  intensity: {\r\n    type: Number\r\n  }, // 环境光强度。\r\n  position: {\r\n    type: Array,\r\n    required: true\r\n  }, // 坐标位置\r\n  target: {\r\n    type: Array\r\n  }, // 光射向的目标位置\r\n}));\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: any;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<Loca.DirectionalLight, Loca.Container>((options, parentComponent) => {\r\n  return new Promise<any>((resolve) => {\r\n    $amapComponent = new Loca.DirectionalLight(options);\r\n    parentComponent.addLight($amapComponent);\r\n    resolve($amapComponent);\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance.isDestroy){\r\n        parentInstance.$amapComponent.removeLight($amapComponent);\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance,\r\n});\r\n\r\n</script>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAI,IAAA,cAAA,CAAA;AAEJ,IAAA,MAAM,EAAC,aAAe,EAAA,cAAA,KAAkB,WAAmD,CAAA,CAAC,SAAS,eAAoB,KAAA;AACvH,MAAO,OAAA,IAAI,OAAa,CAAA,CAAC,OAAY,KAAA;AACnC,QAAiB,cAAA,GAAA,IAAI,IAAK,CAAA,gBAAA,CAAiB,OAAO,CAAA,CAAA;AAClD,QAAA,eAAA,CAAgB,SAAS,cAAc,CAAA,CAAA;AACvC,QAAA,OAAA,CAAQ,cAAc,CAAA,CAAA;AAAA,OACvB,CAAA,CAAA;AAAA,KAEA,EAAA;AAAA,MACD,KAAA;AAAA,MACA,gBAAoB,GAAA;AAClB,QAAI,IAAA,cAAA,KAAkB,iDAAgB,cAAgB,CAAA,EAAA;AACpD,UAAG,IAAA,CAAC,eAAe,SAAU,EAAA;AAC3B,YAAe,cAAA,CAAA,cAAA,CAAe,YAAY,cAAc,CAAA,CAAA;AAAA,WAC1D;AACA,UAAiB,cAAA,GAAA,IAAA,CAAA;AAAA,SACnB;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AAED,IAAa,QAAA,CAAA;AAAA,MACX,aAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;"}