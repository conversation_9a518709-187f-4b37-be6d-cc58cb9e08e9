{"version": 3, "sources": ["../../src/clipboard-polyfill/builtins/window-globalThis.ts", "../../src/clipboard-polyfill/builtins/promise-constructor.ts", "../../src/clipboard-polyfill/promise/polyfill.ts", "../../src/clipboard-polyfill/promise/set-promise-polyfill-if-needed.ts", "../../src/clipboard-polyfill/builtins/builtin-globals.ts", "../../src/clipboard-polyfill/ClipboardItem/data-types.ts", "../../src/clipboard-polyfill/promise/promise-compat.ts", "../../src/clipboard-polyfill/ClipboardItem/convert.ts", "../../src/clipboard-polyfill/ClipboardItem/ClipboardItemPolyfill.ts", "../../src/clipboard-polyfill/ClipboardItem/check.ts", "../../src/clipboard-polyfill/debug.ts", "../../src/clipboard-polyfill/strategies/internet-explorer.ts", "../../src/clipboard-polyfill/strategies/dom.ts", "../../src/clipboard-polyfill/implementations/write-fallback.ts", "../../src/clipboard-polyfill/implementations/text.ts", "../../src/clipboard-polyfill/implementations/blob.ts", "../../src/clipboard-polyfill/entries/es5/window-var.ts", "../../src/clipboard-polyfill/entries/es5/window-var.promise.ts"], "sourcesContent": ["export var originalWindow = typeof window === \"undefined\" ? undefined : window;\nexport var originalGlobalThis =\n  typeof globalThis === \"undefined\" ? undefined : globalThis;\n", "import { PromiseConstructor } from \"../promise/es6-promise\";\nimport { originalGlobalThis, originalWindow } from \"./window-globalThis\";\n\nvar promiseConstructorImpl: PromiseConstructor | undefined =\n  (originalWindow as { Promise?: PromiseConstructor } | undefined)?.Promise ??\n  originalGlobalThis?.Promise;\n\n// This must be called *before* `builtin-globals.ts` is imported, or it has no effect.\nexport function setPromiseConstructor(\n  newPromiseConstructorImpl: PromiseConstructor,\n) {\n  promiseConstructorImpl = newPromiseConstructorImpl;\n}\n\nexport function getPromiseConstructor(): PromiseConstructor {\n  if (!promiseConstructorImpl) {\n    throw new Error(\n      \"No `Promise` implementation available for `clipboard-polyfill`. Consider using: https://github.com/lgarron/clipboard-polyfill#flat-file-version-with-promise-included\",\n    );\n  }\n  return promiseConstructorImpl;\n}\n", "import type { PromiseConstructor } from \"./es6-promise\";\n\n/**\n * @this {PromisePolyfill}\n */\nfunction finallyConstructor(callback) {\n  var thisConstructor = this.constructor;\n  return this.then(\n    function (value) {\n      return thisConstructor.resolve(callback()).then(() => {\n        return value;\n      });\n    },\n    function (reason) {\n      return thisConstructor.resolve(callback()).then(() => {\n        return thisConstructor.reject(reason);\n      });\n    },\n  );\n}\n\nfunction allSettled(arr) {\n  var P = this;\n  return new P(function (resolve, reject) {\n    if (!(arr && typeof arr.length !== \"undefined\")) {\n      return reject(\n        new TypeError(\n          // biome-ignore lint/style/useTemplate: Vendored code.\n          typeof arr +\n            \" \" +\n            arr +\n            \" is not iterable(cannot read property Symbol(Symbol.iterator))\",\n        ),\n      );\n    }\n    var args = Array.prototype.slice.call(arr);\n    if (args.length === 0) return resolve([]);\n    var remaining = args.length;\n\n    function res(i, val) {\n      if (val && (typeof val === \"object\" || typeof val === \"function\")) {\n        var then = val.then;\n        if (typeof then === \"function\") {\n          then.call(\n            val,\n            function (val) {\n              res(i, val);\n            },\n            function (e) {\n              args[i] = { status: \"rejected\", reason: e };\n              if (--remaining === 0) {\n                resolve(args);\n              }\n            },\n          );\n          return;\n        }\n      }\n      args[i] = { status: \"fulfilled\", value: val };\n      if (--remaining === 0) {\n        resolve(args);\n      }\n    }\n\n    for (var i = 0; i < args.length; i++) {\n      res(i, args[i]);\n    }\n  });\n}\n\n// Store setTimeout reference so promise-polyfill will be unaffected by\n// other code modifying setTimeout (like sinon.useFakeTimers())\nvar setTimeoutFunc = setTimeout;\n\nfunction isArray(x) {\n  return Boolean(x && typeof x.length !== \"undefined\");\n}\n\nfunction noop() {}\n\n// Polyfill for Function.prototype.bind\nfunction bind(fn, thisArg) {\n  return function () {\n    // biome-ignore lint/style/noArguments: Vendored code.\n    fn.apply(thisArg, arguments);\n  };\n}\n\n/**\n * @constructor\n * @param {Function} fn\n */\n\nexport function PromisePolyfill(fn) {\n  if (!(this instanceof PromisePolyfill))\n    throw new TypeError(\"Promises must be constructed via new\");\n  if (typeof fn !== \"function\") throw new TypeError(\"not a function\");\n  /** @type {!number} */\n  this._state = 0;\n  /** @type {!boolean} */\n  this._handled = false;\n  /** @type {PromisePolyfill|undefined} */\n  this._value = undefined;\n  /** @type {!Array<!Function>} */\n  this._deferreds = [];\n\n  doResolve(fn, this);\n}\n\nfunction handle(self, deferred) {\n  while (self._state === 3) {\n    // biome-ignore lint/style/noParameterAssign: Inherited from library code.\n    self = self._value;\n  }\n  if (self._state === 0) {\n    self._deferreds.push(deferred);\n    return;\n  }\n  self._handled = true;\n  PromisePolyfill._immediateFn(function () {\n    var cb = self._state === 1 ? deferred.onFulfilled : deferred.onRejected;\n    if (cb === null) {\n      (self._state === 1 ? resolve : reject)(deferred.promise, self._value);\n      return;\n    }\n    var ret;\n    try {\n      ret = cb(self._value);\n    } catch (e) {\n      reject(deferred.promise, e);\n      return;\n    }\n    resolve(deferred.promise, ret);\n  });\n}\n\nfunction resolve(self, newValue) {\n  try {\n    // Promise Resolution Procedure: https://github.com/promises-aplus/promises-spec#the-promise-resolution-procedure\n    if (newValue === self)\n      throw new TypeError(\"A promise cannot be resolved with itself.\");\n    if (\n      newValue &&\n      (typeof newValue === \"object\" || typeof newValue === \"function\")\n    ) {\n      var then = newValue.then;\n      if (newValue instanceof PromisePolyfill) {\n        self._state = 3;\n        self._value = newValue;\n        finale(self);\n        return;\n      } else if (typeof then === \"function\") {\n        doResolve(bind(then, newValue), self);\n        return;\n      }\n    }\n    self._state = 1;\n    self._value = newValue;\n    finale(self);\n  } catch (e) {\n    reject(self, e);\n  }\n}\n\nfunction reject(self, newValue) {\n  self._state = 2;\n  self._value = newValue;\n  finale(self);\n}\n\nfunction finale(self) {\n  if (self._state === 2 && self._deferreds.length === 0) {\n    PromisePolyfill._immediateFn(function () {\n      if (!self._handled) {\n        PromisePolyfill._unhandledRejectionFn(self._value);\n      }\n    });\n  }\n\n  for (var i = 0, len = self._deferreds.length; i < len; i++) {\n    handle(self, self._deferreds[i]);\n  }\n  self._deferreds = null;\n}\n\n/**\n * @constructor\n */\nfunction Handler(onFulfilled, onRejected, promise) {\n  this.onFulfilled = typeof onFulfilled === \"function\" ? onFulfilled : null;\n  this.onRejected = typeof onRejected === \"function\" ? onRejected : null;\n  this.promise = promise;\n}\n\n/**\n * Take a potentially misbehaving resolver function and make sure\n * onFulfilled and onRejected are only called once.\n *\n * Makes no guarantees about asynchrony.\n */\nfunction doResolve(fn, self) {\n  var done = false;\n  try {\n    fn(\n      function (value) {\n        if (done) return;\n        done = true;\n        resolve(self, value);\n      },\n      function (reason) {\n        if (done) return;\n        done = true;\n        reject(self, reason);\n      },\n    );\n  } catch (ex) {\n    if (done) return;\n    done = true;\n    reject(self, ex);\n  }\n}\n\nPromisePolyfill.prototype[\"catch\"] = function (onRejected) {\n  return this.then(null, onRejected);\n};\n\nPromisePolyfill.prototype.then = function (onFulfilled, onRejected) {\n  // @ts-ignore\n  var prom = new this.constructor(noop);\n\n  handle(this, new Handler(onFulfilled, onRejected, prom));\n  return prom;\n};\n\nPromisePolyfill.prototype[\"finally\"] = finallyConstructor;\n\nPromisePolyfill.all = function (arr) {\n  return new PromisePolyfill(function (resolve, reject) {\n    if (!isArray(arr)) {\n      return reject(new TypeError(\"Promise.all accepts an array\"));\n    }\n\n    var args = Array.prototype.slice.call(arr);\n    if (args.length === 0) return resolve([]);\n    var remaining = args.length;\n\n    function res(i, val) {\n      try {\n        if (val && (typeof val === \"object\" || typeof val === \"function\")) {\n          var then = val.then;\n          if (typeof then === \"function\") {\n            then.call(\n              val,\n              function (val) {\n                res(i, val);\n              },\n              reject,\n            );\n            return;\n          }\n        }\n        args[i] = val;\n        if (--remaining === 0) {\n          resolve(args);\n        }\n      } catch (ex) {\n        reject(ex);\n      }\n    }\n\n    for (var i = 0; i < args.length; i++) {\n      res(i, args[i]);\n    }\n  });\n};\n\nPromisePolyfill.allSettled = allSettled;\n\nPromisePolyfill.resolve = function (value) {\n  if (\n    value &&\n    typeof value === \"object\" &&\n    value.constructor === PromisePolyfill\n  ) {\n    return value;\n  }\n\n  return new PromisePolyfill(function (resolve) {\n    resolve(value);\n  });\n};\n\nPromisePolyfill.reject = function (value) {\n  return new PromisePolyfill(function (resolve, reject) {\n    reject(value);\n  });\n};\n\nPromisePolyfill.race = function (arr) {\n  return new PromisePolyfill(function (resolve, reject) {\n    if (!isArray(arr)) {\n      return reject(new TypeError(\"Promise.race accepts an array\"));\n    }\n\n    for (var i = 0, len = arr.length; i < len; i++) {\n      PromisePolyfill.resolve(arr[i]).then(resolve, reject);\n    }\n  });\n};\n\n// Use polyfill for setImmediate for performance gains\nPromisePolyfill._immediateFn =\n  // @ts-ignore\n  (typeof setImmediate === \"function\" &&\n    function (fn) {\n      // @ts-ignore\n      setImmediate(fn);\n    }) ||\n  function (fn) {\n    setTimeoutFunc(fn, 0);\n  };\n\nPromisePolyfill._unhandledRejectionFn = function _unhandledRejectionFn(err) {\n  if (typeof console !== \"undefined\" && console) {\n    console.warn(\"Possible Unhandled Promise Rejection:\", err); // eslint-disable-line no-console\n  }\n};\n\nexport var PromisePolyfillConstructor: PromiseConstructor =\n  PromisePolyfill as any as PromiseConstructor;\n\n// Set the Promise polyfill before getting globals.\nimport { setPromiseConstructor } from \"../builtins/promise-constructor\";\nsetPromiseConstructor(PromisePolyfillConstructor);\n", "import { setPromiseConstructor } from \"../builtins/promise-constructor\";\nimport { originalWindow } from \"../builtins/window-globalThis\";\nimport { PromisePolyfillConstructor } from \"./polyfill\";\n\noriginalWindow?.Promise || setPromiseConstructor(PromisePolyfillConstructor);\n", "// We cache the references so that callers can do the following without causing infinite recursion/bugs:\n//\n//     import * as clipboard from \"clipboard-polyfill\";\n//     navigator.clipboard = clipboard;\n//\n//     import { ClipboardItem } from \"clipboard-polyfill\";\n//     window.ClipboardItem = clipboard;\n//\n// Note that per the spec:\n//\n// - is *not* possible to overwrite `navigator.clipboard`. https://www.w3.org/TR/clipboard-apis/#navigator-interface\n// - it *may* be possible to overwrite `window.ClipboardItem`.\n//\n// Chrome 83 and Safari 13.1 match this. We save the original\n// `navigator.clipboard` anyhow, because 1) it doesn't cost more code (in fact,\n// it probably saves code), and 2) just in case an unknown/future implementation\n// allows overwriting `navigator.clipboard` like this.\n\nimport type { PromiseConstructor } from \"../promise/es6-promise\";\nimport { originalWindow } from \"./window-globalThis\";\n\nimport {\n  ClipboardItemConstructor,\n  ClipboardEventTarget,\n  ClipboardItems,\n} from \"../ClipboardItem/spec\";\nimport { getPromiseConstructor } from \"./promise-constructor\";\n\nvar originalNavigator =\n  typeof navigator === \"undefined\" ? undefined : navigator;\nvar originalNavigatorClipboard: ClipboardEventTarget | undefined =\n  originalNavigator?.clipboard as any;\nexport var originalNavigatorClipboardRead:\n  | (() => Promise<ClipboardItems>)\n  | undefined = originalNavigatorClipboard?.read?.bind(\n  originalNavigatorClipboard,\n);\nexport var originalNavigatorClipboardReadText:\n  | (() => Promise<string>)\n  | undefined = originalNavigatorClipboard?.readText?.bind(\n  originalNavigatorClipboard,\n);\nexport var originalNavigatorClipboardWrite:\n  | ((data: ClipboardItems) => Promise<void>)\n  | undefined = originalNavigatorClipboard?.write?.bind(\n  originalNavigatorClipboard,\n);\nexport var originalNavigatorClipboardWriteText:\n  | ((data: string) => Promise<void>)\n  | undefined = originalNavigatorClipboard?.writeText?.bind(\n  originalNavigatorClipboard,\n);\n\n// The spec specifies that this goes on `window`, not e.g. `globalThis`. It's not (currently) available in workers.\nexport var originalWindowClipboardItem: ClipboardItemConstructor | undefined =\n  originalWindow?.ClipboardItem;\n\nexport var promiseConstructor: PromiseConstructor = getPromiseConstructor();\n", "export var TEXT_PLAIN = \"text/plain\";\nexport var TEXT_HTML = \"text/html\";\n", "import { promiseConstructor } from \"../builtins/builtin-globals\";\n\nexport function promiseRecordMap<T>(\n  keys: readonly string[],\n  f: (key: string) => Promise<T>,\n): Promise<Record<string, T>> {\n  var promiseList: Promise<T>[] = [];\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    promiseList.push(f(key));\n  }\n  return promiseConstructor\n    .all(promiseList)\n    .then((vList: T[]): Record<string, T> => {\n      var dataOut: Record<string, T> = {};\n      for (var i = 0; i < keys.length; i++) {\n        dataOut[keys[i]] = vList[i];\n      }\n      return dataOut;\n    });\n}\n\nexport var voidPromise: Promise<void> = promiseConstructor.resolve();\nexport var truePromiseFn: () => Promise<boolean> = () =>\n  promiseConstructor.resolve(true);\nexport var falsePromise: Promise<boolean> = promiseConstructor.resolve(false);\n\nexport function rejectThrownErrors<T>(executor: () => Promise<T>): Promise<T> {\n  return new promiseConstructor((resolve, reject) => {\n    try {\n      resolve(executor());\n    } catch (e) {\n      reject(e);\n    }\n  });\n}\n", "import { ClipboardItemPolyfill } from \"./ClipboardItemPolyfill\";\nimport { TEXT_PLAIN } from \"./data-types\";\nimport { ClipboardItemInterface, ClipboardItemOptions } from \"./spec\";\nimport {\n  promiseConstructor,\n  originalWindowClipboardItem,\n} from \"../builtins/builtin-globals\";\nimport { promiseRecordMap } from \"../promise/promise-compat\";\n\nexport function stringToBlob(type: string, str: string): Blob {\n  return new Blob([str], {\n    type,\n  });\n}\n\nexport function blobToString(blob: Blob): Promise<string> {\n  return new promiseConstructor((resolve, reject) => {\n    var fileReader = new FileReader();\n    fileReader.addEventListener(\"load\", () => {\n      var result = fileReader.result;\n      if (typeof result === \"string\") {\n        resolve(result);\n      } else {\n        reject(\"could not convert blob to string\");\n      }\n    });\n    fileReader.readAsText(blob);\n  });\n}\n\nexport function clipboardItemToGlobalClipboardItem(\n  clipboardItem: ClipboardItemInterface,\n): Promise<ClipboardItemInterface> {\n  // Note that we use `Blob` instead of `ClipboardItemDataType`. This is because\n  // Chrome 83 can only accept `Blob` (not `string`). The return value of\n  // `getType()` is already `Blob` per the spec, so this is simple for us.\n  return promiseRecordMap(clipboardItem.types, function (type: string) {\n    return clipboardItem.getType(type);\n  }).then((items: Record<string, Blob>) => {\n    return new promiseConstructor((resolve, reject) => {\n      var options: ClipboardItemOptions = {};\n      if (clipboardItem.presentationStyle) {\n        options.presentationStyle = clipboardItem.presentationStyle;\n      }\n      if (originalWindowClipboardItem) {\n        resolve(new originalWindowClipboardItem(items, options));\n      } else {\n        reject(\"window.ClipboardItem is not defined\");\n      }\n    });\n  });\n}\n\nexport function textToClipboardItem(text: string): ClipboardItemInterface {\n  var items: { [type: string]: Blob } = {};\n  items[TEXT_PLAIN] = stringToBlob(text, TEXT_PLAIN);\n  return new ClipboardItemPolyfill(items);\n}\n\nexport function getTypeAsString(\n  clipboardItem: ClipboardItemInterface,\n  type: string,\n): Promise<string> {\n  return clipboardItem.getType(type).then((text: Blob) => {\n    return blobToString(text);\n  });\n}\n\nexport interface StringItem {\n  [type: string]: string;\n}\n\nexport function toStringItem(\n  data: ClipboardItemInterface,\n): Promise<StringItem> {\n  return promiseRecordMap(data.types, function (type: string) {\n    return getTypeAsString(data, type);\n  });\n}\n", "import { promiseConstructor } from \"../builtins/builtin-globals\";\nimport { stringToBlob } from \"./convert\";\nimport {\n  ClipboardItemConstructor,\n  ClipboardItemDataType,\n  ClipboardItemInterface,\n  ClipboardItemOptions,\n} from \"./spec\";\n\nfunction ClipboardItemPolyfillImpl(\n  // TODO: The spec specifies values as `ClipboardItemData`, but\n  // implementations (e.g. Chrome 83) seem to assume `ClipboardItemDataType`\n  // values. https://github.com/w3c/clipboard-apis/pull/126\n  items: { [type: string]: ClipboardItemDataType },\n  options?: ClipboardItemOptions,\n): ClipboardItemInterface {\n  var types = Object.keys(items);\n  var _items: { [type: string]: Blob } = {};\n  // biome-ignore lint/suspicious/noRedeclare: This is a false positive from Biome. https://github.com/biomejs/biome/issues/175\n  for (var type in items) {\n    var item = items[type];\n    if (typeof item === \"string\") {\n      _items[type] = stringToBlob(type, item);\n    } else {\n      _items[type] = item;\n    }\n  }\n  // The explicit default for `presentationStyle` is \"unspecified\":\n  // https://www.w3.org/TR/clipboard-apis/#clipboard-interface\n  var presentationStyle = options?.presentationStyle ?? \"unspecified\";\n\n  function getType(type: string): Promise<Blob> {\n    return promiseConstructor.resolve(_items[type]);\n  }\n  return {\n    types: types,\n    presentationStyle: presentationStyle,\n    getType: getType,\n  };\n}\n\nexport var ClipboardItemPolyfill: ClipboardItemConstructor =\n  ClipboardItemPolyfillImpl as any as ClipboardItemConstructor;\n", "import { ClipboardItemInterface } from \"./spec\";\n\nexport function hasItemWithType(\n  clipboardItems: ClipboardItemInterface[],\n  typeName: string,\n): boolean {\n  for (var i = 0; i < clipboardItems.length; i++) {\n    var item = clipboardItems[i];\n    if (item.types.indexOf(typeName) !== -1) {\n      return true;\n    }\n  }\n  return false;\n}\n", "/******** Debug Logging ********/\n\n// tslint:disable-next-line: no-empty\nvar debugLogImpl = (s: string) => {};\n\nexport function debugLog(s: string) {\n  debugLogImpl(s);\n}\n\nexport function setDebugLog(logFn: (s: string) => void) {\n  debugLogImpl = logFn;\n}\n\n/******** Warnings ********/\n\nvar showWarnings = true;\n\nexport function suppressWarnings() {\n  showWarnings = false;\n}\n\nexport function shouldShowWarnings(): boolean {\n  return showWarnings;\n}\n\n// Workaround for:\n// - IE9 (can't bind console functions directly), and\n// - Edge Issue #14495220 (referencing `console` without F12 Developer Tools can cause an exception)\nfunction warnOrLog() {\n  // biome-ignore lint/style/noArguments: `arguments` is ES5-compatible.\n  (console.warn || console.log).apply(console, arguments);\n}\n\nexport var warn = warnOrLog.bind(\"[clipboard-polyfill]\");\n", "import { originalWindow } from \"../builtins/window-globalThis\";\nimport { debugLog } from \"../debug\";\n\ninterface IEWindow extends Window {\n  clipboardData?: {\n    setData: (key: string, value: string) => boolean;\n    // Always results in a string: https://msdn.microsoft.com/en-us/library/ms536436(v=vs.85).aspx\n    getData: (key: string) => string;\n  };\n}\n\nvar ieWindow = originalWindow as IEWindow;\n\nexport function seemToBeInIE(): boolean {\n  return (\n    typeof ClipboardEvent === \"undefined\" &&\n    typeof ieWindow?.clipboardData !== \"undefined\" &&\n    typeof ieWindow?.clipboardData.setData !== \"undefined\"\n  );\n}\n\nexport function writeTextIE(text: string): boolean {\n  if (!ieWindow.clipboardData) {\n    return false;\n  }\n  // IE supports text or URL, but not HTML: https://msdn.microsoft.com/en-us/library/ms536744(v=vs.85).aspx\n  // TODO: Write URLs to `text/uri-list`? https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types\n  var success = ieWindow.clipboardData.setData(\"Text\", text);\n  if (success) {\n    debugLog(\"writeTextIE worked\");\n  }\n  return success;\n}\n\n// Returns \"\" if the read failed, e.g. because the user rejected the permission.\nexport function readTextIE(): string {\n  if (!ieWindow.clipboardData) {\n    throw new Error(\"Cannot read IE clipboard Data \");\n  }\n  var text = ieWindow.clipboardData.getData(\"Text\");\n  if (text === \"\") {\n    throw new Error(\n      \"Empty clipboard or could not read plain text from clipboard\",\n    );\n  }\n  return text;\n}\n", "import { StringItem } from \"../ClipboardItem/convert\";\nimport { TEXT_PLAIN } from \"../ClipboardItem/data-types\";\nimport { debugLog } from \"../debug\";\n\n/******** Implementations ********/\n\ninterface FallbackTracker {\n  success: boolean;\n}\n\nfunction copyListener(\n  tracker: FallbackTracker,\n  data: StringItem,\n  e: ClipboardEvent,\n): void {\n  debugLog(\"listener called\");\n  tracker.success = true;\n  // tslint:disable-next-line: forin\n  for (var type in data) {\n    var value = data[type];\n    // biome-ignore lint/style/noNonNullAssertion: We assume the data is present if the listener was called.\n    var clipboardData = e.clipboardData!;\n    clipboardData.setData(type, value);\n    if (type === TEXT_PLAIN && clipboardData.getData(type) !== value) {\n      debugLog(\"setting text/plain failed\");\n      tracker.success = false;\n    }\n  }\n  e.preventDefault();\n}\n\nexport function execCopy(data: StringItem): boolean {\n  var tracker: FallbackTracker = { success: false };\n  var listener = copyListener.bind(this, tracker, data);\n\n  document.addEventListener(\"copy\", listener);\n  try {\n    // We ignore the return value, since FallbackTracker tells us whether the\n    // listener was called. It seems that checking the return value here gives\n    // us no extra information in any browser.\n    document.execCommand(\"copy\");\n  } finally {\n    document.removeEventListener(\"copy\", listener);\n  }\n  return tracker.success;\n}\n\n// Temporarily select a DOM element, so that `execCommand()` is not rejected.\nexport function copyUsingTempSelection(\n  e: HTMLElement,\n  data: StringItem,\n): boolean {\n  selectionSet(e);\n  var success = execCopy(data);\n  selectionClear();\n  return success;\n}\n\n// Create a temporary DOM element to select, so that `execCommand()` is not\n// rejected.\nexport function copyUsingTempElem(data: StringItem): boolean {\n  var tempElem = document.createElement(\"div\");\n  // Setting an individual property does not support `!important`, so we set the\n  // whole style instead of just the `-webkit-user-select` property.\n  tempElem.setAttribute(\"style\", \"-webkit-user-select: text !important\");\n  // Place some text in the elem so that Safari has something to select.\n  tempElem.textContent = \"temporary element\";\n  document.body.appendChild(tempElem);\n\n  var success = copyUsingTempSelection(tempElem, data);\n\n  document.body.removeChild(tempElem);\n  return success;\n}\n\n// Uses shadow DOM.\nexport function copyTextUsingDOM(str: string): boolean {\n  debugLog(\"copyTextUsingDOM\");\n\n  var tempElem = document.createElement(\"div\");\n  // Setting an individual property does not support `!important`, so we set the\n  // whole style instead of just the `-webkit-user-select` property.\n  tempElem.setAttribute(\"style\", \"-webkit-user-select: text !important\");\n  // Use shadow DOM if available.\n  var spanParent: Node = tempElem;\n  if (tempElem.attachShadow) {\n    debugLog(\"Using shadow DOM.\");\n    spanParent = tempElem.attachShadow({ mode: \"open\" });\n  }\n\n  var span = document.createElement(\"span\");\n  span.innerText = str;\n\n  spanParent.appendChild(span);\n  document.body.appendChild(tempElem);\n  selectionSet(span);\n\n  var result = document.execCommand(\"copy\");\n\n  selectionClear();\n  document.body.removeChild(tempElem);\n\n  return result;\n}\n\n/******** Selection ********/\n\nfunction selectionSet(elem: Element): void {\n  var sel = document.getSelection();\n  if (sel) {\n    var range = document.createRange();\n    range.selectNodeContents(elem);\n    sel.removeAllRanges();\n    sel.addRange(range);\n  }\n}\n\nfunction selectionClear(): void {\n  var sel = document.getSelection();\n  if (sel) {\n    sel.removeAllRanges();\n  }\n}\n", "import { StringItem } from \"../ClipboardItem/convert\";\nimport { TEXT_PLAIN } from \"../ClipboardItem/data-types\";\nimport { debugLog } from \"../debug\";\nimport {\n  copyTextUsingDOM,\n  copyUsingTempElem,\n  copyUsingTempSelection,\n  execCopy,\n} from \"../strategies/dom\";\nimport { seemToBeInIE, writeTextIE } from \"../strategies/internet-explorer\";\n\n// Note: the fallback order is carefully tuned for compatibility. It might seem\n// safe to move some of them around, but do not do so without testing all browsers.\nexport function writeFallback(stringItem: StringItem): boolean {\n  var hasTextPlain = TEXT_PLAIN in stringItem;\n\n  // Internet Explorer\n  if (seemToBeInIE()) {\n    if (!hasTextPlain) {\n      throw new Error(\"No `text/plain` value was specified.\");\n    }\n    if (writeTextIE(stringItem[TEXT_PLAIN])) {\n      return true;\n    } else {\n      throw new Error(\"Copying failed, possibly because the user rejected it.\");\n    }\n  }\n\n  if (execCopy(stringItem)) {\n    debugLog(\"regular execCopy worked\");\n    return true;\n  }\n\n  // Success detection on Edge is not possible, due to bugs in all 4\n  // detection mechanisms we could try to use. Assume success.\n  if (navigator.userAgent.indexOf(\"Edge\") > -1) {\n    debugLog('UA \"Edge\" => assuming success');\n    return true;\n  }\n\n  // Fallback 1 for desktop Safari.\n  if (copyUsingTempSelection(document.body, stringItem)) {\n    debugLog(\"copyUsingTempSelection worked\");\n    return true;\n  }\n\n  // Fallback 2 for desktop Safari.\n  if (copyUsingTempElem(stringItem)) {\n    debugLog(\"copyUsingTempElem worked\");\n    return true;\n  }\n\n  // Fallback for iOS Safari.\n  if (copyTextUsingDOM(stringItem[TEXT_PLAIN])) {\n    debugLog(\"copyTextUsingDOM worked\");\n    return true;\n  }\n\n  return false;\n}\n", "import { StringItem } from \"../ClipboardItem/convert\";\nimport { TEXT_PLAIN } from \"../ClipboardItem/data-types\";\nimport { debugLog } from \"../debug\";\nimport {\n  originalNavigatorClipboardReadText,\n  originalNavigatorClipboardWriteText,\n  promiseConstructor,\n} from \"../builtins/builtin-globals\";\nimport { readTextIE, seemToBeInIE } from \"../strategies/internet-explorer\";\nimport { writeFallback } from \"./write-fallback\";\nimport { rejectThrownErrors } from \"../promise/promise-compat\";\n\nfunction stringToStringItem(s: string): StringItem {\n  var stringItem: StringItem = {};\n  stringItem[TEXT_PLAIN] = s;\n  return stringItem;\n}\n\nexport function writeText(s: string): Promise<void> {\n  // Use the browser implementation if it exists.\n  if (originalNavigatorClipboardWriteText) {\n    debugLog(\"Using `navigator.clipboard.writeText()`.\");\n    return originalNavigatorClipboardWriteText(s).catch(() =>\n      writeTextStringFallbackPromise(s),\n    );\n  }\n  return writeTextStringFallbackPromise(s);\n}\n\nfunction writeTextStringFallbackPromise(s: string): Promise<void> {\n  return rejectThrownErrors(() =>\n    promiseConstructor.resolve(writeTextStringFallback(s)),\n  );\n}\n\nfunction writeTextStringFallback(s: string): void {\n  if (!writeFallback(stringToStringItem(s))) {\n    throw new Error(\"writeText() failed\");\n  }\n}\n\nexport function readText(): Promise<string> {\n  return rejectThrownErrors(() => {\n    // Use the browser implementation if it exists.\n    if (originalNavigatorClipboardReadText) {\n      debugLog(\"Using `navigator.clipboard.readText()`.\");\n      return originalNavigatorClipboardReadText();\n    }\n\n    // Fallback for IE.\n    if (seemToBeInIE()) {\n      var result = readTextIE();\n      return promiseConstructor.resolve(result);\n    }\n\n    throw new Error(\"Read is not supported in your browser.\");\n  });\n}\n", "import { hasItemWithType } from \"../ClipboardItem/check\";\nimport {\n  clipboardItemToGlobalClipboardItem,\n  toStringItem,\n  textToClipboardItem,\n  StringItem,\n} from \"../ClipboardItem/convert\";\nimport { TEXT_HTML, TEXT_PLAIN } from \"../ClipboardItem/data-types\";\nimport { ClipboardItemInterface, ClipboardItems } from \"../ClipboardItem/spec\";\nimport { debugLog, shouldShowWarnings } from \"../debug\";\nimport {\n  promiseConstructor,\n  originalNavigatorClipboardRead,\n  originalNavigatorClipboardWrite,\n  originalWindowClipboardItem,\n} from \"../builtins/builtin-globals\";\nimport {\n  falsePromise,\n  rejectThrownErrors,\n  truePromiseFn,\n  voidPromise,\n} from \"../promise/promise-compat\";\nimport { readText } from \"./text\";\nimport { writeFallback } from \"./write-fallback\";\n\nexport function write(data: ClipboardItemInterface[]): Promise<void> {\n  // Use the browser implementation if it exists.\n  // TODO: detect `text/html`.\n  return rejectThrownErrors((): Promise<boolean> => {\n    if (originalNavigatorClipboardWrite && originalWindowClipboardItem) {\n      // TODO: This reference is a workaround for TypeScript inference.\n      var originalNavigatorClipboardWriteCached =\n        originalNavigatorClipboardWrite;\n      debugLog(\"Using `navigator.clipboard.write()`.\");\n      return promiseConstructor\n        .all(data.map(clipboardItemToGlobalClipboardItem))\n        .then(\n          (\n            globalClipboardItems: ClipboardItemInterface[],\n          ): Promise<boolean> => {\n            return originalNavigatorClipboardWriteCached(globalClipboardItems)\n              .then(truePromiseFn)\n              .catch((e: Error): Promise<boolean> => {\n                // Chrome 83 will throw a DOMException or NotAllowedError because it doesn't support e.g. `text/html`.\n                // We want to fall back to the other strategies in a situation like this.\n                // See https://github.com/w3c/clipboard-apis/issues/128 and https://github.com/w3c/clipboard-apis/issues/67\n                if (\n                  !hasItemWithType(data, TEXT_PLAIN) &&\n                  !hasItemWithType(data, TEXT_HTML)\n                ) {\n                  throw e;\n                }\n                return falsePromise;\n              });\n          },\n        );\n    }\n    return falsePromise;\n  }).then((success: boolean) => {\n    if (success) {\n      return voidPromise;\n    }\n\n    var hasTextPlain = hasItemWithType(data, TEXT_PLAIN);\n    if (shouldShowWarnings() && !hasTextPlain) {\n      debugLog(\n        \"clipboard.write() was called without a \" +\n          \"`text/plain` data type. On some platforms, this may result in an \" +\n          \"empty clipboard. Call suppressWarnings() \" +\n          \"to suppress this warning.\",\n      );\n    }\n\n    return toStringItem(data[0]).then((stringItem: StringItem) => {\n      if (!writeFallback(stringItem)) {\n        throw new Error(\"write() failed\");\n      }\n    });\n  });\n}\n\nexport function read(): Promise<ClipboardItems> {\n  return rejectThrownErrors(() => {\n    // Use the browser implementation if it exists.\n    if (originalNavigatorClipboardRead) {\n      debugLog(\"Using `navigator.clipboard.read()`.\");\n      return originalNavigatorClipboardRead();\n    }\n\n    // Fallback to reading text only.\n    return readText().then((text: string) => {\n      return [textToClipboardItem(text)];\n    });\n  });\n}\n", "import { ClipboardItemPolyfill } from \"../../ClipboardItem/ClipboardItemPolyfill\";\nimport {\n  ClipboardItemConstructor,\n  ClipboardWithoutEventTarget,\n} from \"../../ClipboardItem/spec\";\nimport { read, write } from \"../../implementations/blob\";\nimport { readText, writeText } from \"../../implementations/text\";\nimport { setDebugLog, suppressWarnings } from \"../../debug\";\n\ndeclare global {\n  var clipboard: ClipboardWithoutEventTarget & {\n    ClipboardItem: ClipboardItemConstructor;\n    setDebugLog: typeof setDebugLog;\n    suppressWarnings: typeof suppressWarnings;\n  };\n}\n\nwindow.clipboard = {\n  read: read,\n  readText: readText,\n  write: write,\n  writeText: writeText,\n  ClipboardItem: ClipboardItemPolyfill,\n  setDebugLog: setDebugLog,\n  suppressWarnings: suppressWarnings,\n};\n", "// Set the Promise polyfill before globals.\nimport \"../../promise/set-promise-polyfill-if-needed\";\n\nimport { PromiseConstructor } from \"../../promise/es6-promise\";\nimport { PromisePolyfillConstructor } from \"../../promise/polyfill\";\n\nimport \"./window-var\";\n\ndeclare global {\n  var PromisePolyfill: PromiseConstructor;\n}\n\nwindow.PromisePolyfill = PromisePolyfillConstructor;\n"], "mappings": ";AAAO,IAAI,iBAAiB,OAAO,WAAW,cAAc,SAAY;AACjE,IAAI,qBACT,OAAO,eAAe,cAAc,SAAY;;;ACFlD;AAGA,IAAI,0BACD,iDAAiE,YAAjE,aACD,+CAAoB;AAGf,SAAS,sBACd,2BACA;AACA,2BAAyB;AAC3B;AAEO,SAAS,wBAA4C;AAC1D,MAAI,CAAC,wBAAwB;AAC3B,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;AChBA,SAAS,mBAAmB,UAAU;AACpC,MAAI,kBAAkB,KAAK;AAC3B,SAAO,KAAK;AAAA,IACV,SAAU,OAAO;AACf,aAAO,gBAAgB,QAAQ,SAAS,CAAC,EAAE,KAAK,WAAM;AACpD,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAAA,IACA,SAAU,QAAQ;AAChB,aAAO,gBAAgB,QAAQ,SAAS,CAAC,EAAE,KAAK,WAAM;AACpD,eAAO,gBAAgB,OAAO,MAAM;AAAA,MACtC,CAAC;AAAA,IACH;AAAA,EACF;AACF;AAEA,SAAS,WAAW,KAAK;AACvB,MAAI,IAAI;AACR,SAAO,IAAI,EAAE,SAAUA,UAASC,SAAQ;AACtC,QAAI,EAAE,OAAO,OAAO,IAAI,WAAW,cAAc;AAC/C,aAAOA;AAAA,QACL,IAAI;AAAA;AAAA,UAEF,OAAO,MACL,MACA,MACA;AAAA,QACJ;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,MAAM,UAAU,MAAM,KAAK,GAAG;AACzC,QAAI,KAAK,WAAW;AAAG,aAAOD,SAAQ,CAAC,CAAC;AACxC,QAAI,YAAY,KAAK;AAErB,aAAS,IAAIE,IAAG,KAAK;AACnB,UAAI,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa;AACjE,YAAI,OAAO,IAAI;AACf,YAAI,OAAO,SAAS,YAAY;AAC9B,eAAK;AAAA,YACH;AAAA,YACA,SAAUC,MAAK;AACb,kBAAID,IAAGC,IAAG;AAAA,YACZ;AAAA,YACA,SAAU,GAAG;AACX,mBAAKD,EAAC,IAAI,EAAE,QAAQ,YAAY,QAAQ,EAAE;AAC1C,kBAAI,EAAE,cAAc,GAAG;AACrB,gBAAAF,SAAQ,IAAI;AAAA,cACd;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAAA,MACF;AACA,WAAKE,EAAC,IAAI,EAAE,QAAQ,aAAa,OAAO,IAAI;AAC5C,UAAI,EAAE,cAAc,GAAG;AACrB,QAAAF,SAAQ,IAAI;AAAA,MACd;AAAA,IACF;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAI,GAAG,KAAK,CAAC,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AAIA,IAAI,iBAAiB;AAErB,SAAS,QAAQ,GAAG;AAClB,SAAO,QAAQ,KAAK,OAAO,EAAE,WAAW,WAAW;AACrD;AAEA,SAAS,OAAO;AAAC;AAGjB,SAAS,KAAK,IAAI,SAAS;AACzB,SAAO,WAAY;AAEjB,OAAG,MAAM,SAAS,SAAS;AAAA,EAC7B;AACF;AAOO,SAAS,gBAAgB,IAAI;AAClC,MAAI,EAAE,gBAAgB;AACpB,UAAM,IAAI,UAAU,sCAAsC;AAC5D,MAAI,OAAO,OAAO;AAAY,UAAM,IAAI,UAAU,gBAAgB;AAElE,OAAK,SAAS;AAEd,OAAK,WAAW;AAEhB,OAAK,SAAS;AAEd,OAAK,aAAa,CAAC;AAEnB,YAAU,IAAI,IAAI;AACpB;AAEA,SAAS,OAAO,MAAM,UAAU;AAC9B,SAAO,KAAK,WAAW,GAAG;AAExB,WAAO,KAAK;AAAA,EACd;AACA,MAAI,KAAK,WAAW,GAAG;AACrB,SAAK,WAAW,KAAK,QAAQ;AAC7B;AAAA,EACF;AACA,OAAK,WAAW;AAChB,kBAAgB,aAAa,WAAY;AACvC,QAAI,KAAK,KAAK,WAAW,IAAI,SAAS,cAAc,SAAS;AAC7D,QAAI,OAAO,MAAM;AACf,OAAC,KAAK,WAAW,IAAI,UAAU,QAAQ,SAAS,SAAS,KAAK,MAAM;AACpE;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AACF,YAAM,GAAG,KAAK,MAAM;AAAA,IACtB,SAAS,GAAG;AACV,aAAO,SAAS,SAAS,CAAC;AAC1B;AAAA,IACF;AACA,YAAQ,SAAS,SAAS,GAAG;AAAA,EAC/B,CAAC;AACH;AAEA,SAAS,QAAQ,MAAM,UAAU;AAC/B,MAAI;AAEF,QAAI,aAAa;AACf,YAAM,IAAI,UAAU,2CAA2C;AACjE,QACE,aACC,OAAO,aAAa,YAAY,OAAO,aAAa,aACrD;AACA,UAAI,OAAO,SAAS;AACpB,UAAI,oBAAoB,iBAAiB;AACvC,aAAK,SAAS;AACd,aAAK,SAAS;AACd,eAAO,IAAI;AACX;AAAA,MACF,WAAW,OAAO,SAAS,YAAY;AACrC,kBAAU,KAAK,MAAM,QAAQ,GAAG,IAAI;AACpC;AAAA,MACF;AAAA,IACF;AACA,SAAK,SAAS;AACd,SAAK,SAAS;AACd,WAAO,IAAI;AAAA,EACb,SAAS,GAAG;AACV,WAAO,MAAM,CAAC;AAAA,EAChB;AACF;AAEA,SAAS,OAAO,MAAM,UAAU;AAC9B,OAAK,SAAS;AACd,OAAK,SAAS;AACd,SAAO,IAAI;AACb;AAEA,SAAS,OAAO,MAAM;AACpB,MAAI,KAAK,WAAW,KAAK,KAAK,WAAW,WAAW,GAAG;AACrD,oBAAgB,aAAa,WAAY;AACvC,UAAI,CAAC,KAAK,UAAU;AAClB,wBAAgB,sBAAsB,KAAK,MAAM;AAAA,MACnD;AAAA,IACF,CAAC;AAAA,EACH;AAEA,WAAS,IAAI,GAAG,MAAM,KAAK,WAAW,QAAQ,IAAI,KAAK,KAAK;AAC1D,WAAO,MAAM,KAAK,WAAW,CAAC,CAAC;AAAA,EACjC;AACA,OAAK,aAAa;AACpB;AAKA,SAAS,QAAQ,aAAa,YAAY,SAAS;AACjD,OAAK,cAAc,OAAO,gBAAgB,aAAa,cAAc;AACrE,OAAK,aAAa,OAAO,eAAe,aAAa,aAAa;AAClE,OAAK,UAAU;AACjB;AAQA,SAAS,UAAU,IAAI,MAAM;AAC3B,MAAI,OAAO;AACX,MAAI;AACF;AAAA,MACE,SAAU,OAAO;AACf,YAAI;AAAM;AACV,eAAO;AACP,gBAAQ,MAAM,KAAK;AAAA,MACrB;AAAA,MACA,SAAU,QAAQ;AAChB,YAAI;AAAM;AACV,eAAO;AACP,eAAO,MAAM,MAAM;AAAA,MACrB;AAAA,IACF;AAAA,EACF,SAAS,IAAI;AACX,QAAI;AAAM;AACV,WAAO;AACP,WAAO,MAAM,EAAE;AAAA,EACjB;AACF;AAEA,gBAAgB,UAAU,OAAO,IAAI,SAAU,YAAY;AACzD,SAAO,KAAK,KAAK,MAAM,UAAU;AACnC;AAEA,gBAAgB,UAAU,OAAO,SAAU,aAAa,YAAY;AAElE,MAAI,OAAO,IAAI,KAAK,YAAY,IAAI;AAEpC,SAAO,MAAM,IAAI,QAAQ,aAAa,YAAY,IAAI,CAAC;AACvD,SAAO;AACT;AAEA,gBAAgB,UAAU,SAAS,IAAI;AAEvC,gBAAgB,MAAM,SAAU,KAAK;AACnC,SAAO,IAAI,gBAAgB,SAAUA,UAASC,SAAQ;AACpD,QAAI,CAAC,QAAQ,GAAG,GAAG;AACjB,aAAOA,QAAO,IAAI,UAAU,8BAA8B,CAAC;AAAA,IAC7D;AAEA,QAAI,OAAO,MAAM,UAAU,MAAM,KAAK,GAAG;AACzC,QAAI,KAAK,WAAW;AAAG,aAAOD,SAAQ,CAAC,CAAC;AACxC,QAAI,YAAY,KAAK;AAErB,aAAS,IAAIE,IAAG,KAAK;AACnB,UAAI;AACF,YAAI,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,aAAa;AACjE,cAAI,OAAO,IAAI;AACf,cAAI,OAAO,SAAS,YAAY;AAC9B,iBAAK;AAAA,cACH;AAAA,cACA,SAAUC,MAAK;AACb,oBAAID,IAAGC,IAAG;AAAA,cACZ;AAAA,cACAF;AAAA,YACF;AACA;AAAA,UACF;AAAA,QACF;AACA,aAAKC,EAAC,IAAI;AACV,YAAI,EAAE,cAAc,GAAG;AACrB,UAAAF,SAAQ,IAAI;AAAA,QACd;AAAA,MACF,SAAS,IAAI;AACX,QAAAC,QAAO,EAAE;AAAA,MACX;AAAA,IACF;AAEA,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAI,GAAG,KAAK,CAAC,CAAC;AAAA,IAChB;AAAA,EACF,CAAC;AACH;AAEA,gBAAgB,aAAa;AAE7B,gBAAgB,UAAU,SAAU,OAAO;AACzC,MACE,SACA,OAAO,UAAU,YACjB,MAAM,gBAAgB,iBACtB;AACA,WAAO;AAAA,EACT;AAEA,SAAO,IAAI,gBAAgB,SAAUD,UAAS;AAC5C,IAAAA,SAAQ,KAAK;AAAA,EACf,CAAC;AACH;AAEA,gBAAgB,SAAS,SAAU,OAAO;AACxC,SAAO,IAAI,gBAAgB,SAAUA,UAASC,SAAQ;AACpD,IAAAA,QAAO,KAAK;AAAA,EACd,CAAC;AACH;AAEA,gBAAgB,OAAO,SAAU,KAAK;AACpC,SAAO,IAAI,gBAAgB,SAAUD,UAASC,SAAQ;AACpD,QAAI,CAAC,QAAQ,GAAG,GAAG;AACjB,aAAOA,QAAO,IAAI,UAAU,+BAA+B,CAAC;AAAA,IAC9D;AAEA,aAAS,IAAI,GAAG,MAAM,IAAI,QAAQ,IAAI,KAAK,KAAK;AAC9C,sBAAgB,QAAQ,IAAI,CAAC,CAAC,EAAE,KAAKD,UAASC,OAAM;AAAA,IACtD;AAAA,EACF,CAAC;AACH;AAGA,gBAAgB;AAEb,OAAO,iBAAiB,cACvB,SAAU,IAAI;AAEZ,eAAa,EAAE;AACjB,KACF,SAAU,IAAI;AACZ,iBAAe,IAAI,CAAC;AACtB;AAEF,gBAAgB,wBAAwB,SAAS,sBAAsB,KAAK;AAC1E,MAAI,OAAO,YAAY,eAAe,SAAS;AAC7C,YAAQ,KAAK,yCAAyC,GAAG;AAAA,EAC3D;AACF;AAEO,IAAI,6BACT;AAIF,sBAAsB,0BAA0B;;;AC7UhD,IAAAG;AAAA,EAIAA,MAAA,mCAAAA,IAAgB,YAAW,sBAAsB,0BAA0B;;;ACwB3E,IAAI,oBACF,OAAO,cAAc,cAAc,SAAY;AACjD,IAAI,6BACF,uDAAmB;AA/BrB,IAAAC;AAgCO,IAAI,kCAEKA,MAAA,yEAA4B,SAA5B,gBAAAA,IAAkC;AAAA,EAChD;AAAA;AAnCF,IAAAA;AAqCO,IAAI,sCAEKA,MAAA,yEAA4B,aAA5B,gBAAAA,IAAsC;AAAA,EACpD;AAAA;AAxCF,IAAAA;AA0CO,IAAI,mCAEKA,MAAA,yEAA4B,UAA5B,gBAAAA,IAAmC;AAAA,EACjD;AAAA;AA7CF,IAAAA;AA+CO,IAAI,uCAEKA,MAAA,yEAA4B,cAA5B,gBAAAA,IAAuC;AAAA,EACrD;AAAA;AAlDF,IAAAA;AAsDO,IAAI,+BACTA,MAAA,mCAAAA,IAAgB;AAEX,IAAI,qBAAyC,sBAAsB;;;ACzDnE,IAAI,aAAa;AACjB,IAAI,YAAY;;;ACChB,SAAS,iBACd,MACA,GAC4B;AAC5B,MAAI,cAA4B,CAAC;AACjC,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,QAAI,MAAM,KAAK,CAAC;AAChB,gBAAY,KAAK,EAAE,GAAG,CAAC;AAAA,EACzB;AACA,SAAO,mBACJ,IAAI,WAAW,EACf,KAAK,SAAC,OAAkC;AACvC,QAAI,UAA6B,CAAC;AAClC,aAASC,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AACpC,cAAQ,KAAKA,EAAC,CAAC,IAAI,MAAMA,EAAC;AAAA,IAC5B;AACA,WAAO;AAAA,EACT,CAAC;AACL;AAEO,IAAI,cAA6B,mBAAmB,QAAQ;AAC5D,IAAI,gBAAwC,WAAG;AACpD,4BAAmB,QAAQ,IAAI;AAAA;AAC1B,IAAI,eAAiC,mBAAmB,QAAQ,KAAK;AAErE,SAAS,mBAAsB,UAAwC;AAC5E,SAAO,IAAI,mBAAmB,SAACC,UAASC,SAAW;AACjD,QAAI;AACF,MAAAD,SAAQ,SAAS,CAAC;AAAA,IACpB,SAAS,GAAG;AACV,MAAAC,QAAO,CAAC;AAAA,IACV;AAAA,EACF,CAAC;AACH;;;AC1BO,SAAS,aAAa,MAAc,KAAmB;AAC5D,SAAO,IAAI,KAAK,CAAC,GAAG,GAAG;AAAA,IACrB;AAAA,EACF,CAAC;AACH;AAEO,SAAS,aAAa,MAA6B;AACxD,SAAO,IAAI,mBAAmB,SAACC,UAASC,SAAW;AACjD,QAAI,aAAa,IAAI,WAAW;AAChC,eAAW,iBAAiB,QAAQ,WAAM;AACxC,UAAI,SAAS,WAAW;AACxB,UAAI,OAAO,WAAW,UAAU;AAC9B,QAAAD,SAAQ,MAAM;AAAA,MAChB,OAAO;AACL,QAAAC,QAAO,kCAAkC;AAAA,MAC3C;AAAA,IACF,CAAC;AACD,eAAW,WAAW,IAAI;AAAA,EAC5B,CAAC;AACH;AAEO,SAAS,mCACd,eACiC;AAIjC,SAAO,iBAAiB,cAAc,OAAO,SAAU,MAAc;AACnE,WAAO,cAAc,QAAQ,IAAI;AAAA,EACnC,CAAC,EAAE,KAAK,SAAC,OAAgC;AACvC,WAAO,IAAI,mBAAmB,SAACD,UAASC,SAAW;AACjD,UAAI,UAAgC,CAAC;AACrC,UAAI,cAAc,mBAAmB;AACnC,gBAAQ,oBAAoB,cAAc;AAAA,MAC5C;AACA,UAAI,6BAA6B;AAC/B,QAAAD,SAAQ,IAAI,4BAA4B,OAAO,OAAO,CAAC;AAAA,MACzD,OAAO;AACL,QAAAC,QAAO,qCAAqC;AAAA,MAC9C;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEO,SAAS,oBAAoB,MAAsC;AACxE,MAAI,QAAkC,CAAC;AACvC,QAAM,UAAU,IAAI,aAAa,MAAM,UAAU;AACjD,SAAO,IAAI,sBAAsB,KAAK;AACxC;AAEO,SAAS,gBACd,eACA,MACiB;AACjB,SAAO,cAAc,QAAQ,IAAI,EAAE,KAAK,SAAC,MAAe;AACtD,WAAO,aAAa,IAAI;AAAA,EAC1B,CAAC;AACH;AAMO,SAAS,aACd,MACqB;AACrB,SAAO,iBAAiB,KAAK,OAAO,SAAU,MAAc;AAC1D,WAAO,gBAAgB,MAAM,IAAI;AAAA,EACnC,CAAC;AACH;;;ACrEA,SAAS,0BAIP,OACA,SACwB;AAf1B,MAAAC;AAgBE,MAAI,QAAQ,OAAO,KAAK,KAAK;AAC7B,MAAI,SAAmC,CAAC;AAExC,WAAS,QAAQ,OAAO;AACtB,QAAI,OAAO,MAAM,IAAI;AACrB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,IAAI,IAAI,aAAa,MAAM,IAAI;AAAA,IACxC,OAAO;AACL,aAAO,IAAI,IAAI;AAAA,IACjB;AAAA,EACF;AAGA,MAAI,qBAAoBA,MAAA,mCAAS,sBAAT,OAAAA,MAA8B;AAEtD,WAAS,QAAQC,OAA6B;AAC5C,WAAO,mBAAmB,QAAQ,OAAOA,KAAI,CAAC;AAAA,EAChD;AACA,SAAO;AAAA,IACL,OAAO;AAAA,IACP,mBAAmB;AAAA,IACnB,SAAS;AAAA,EACX;AACF;AAEO,IAAI,wBACT;;;ACxCK,SAAS,gBACd,gBACA,UACS;AACT,WAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,QAAI,OAAO,eAAe,CAAC;AAC3B,QAAI,KAAK,MAAM,QAAQ,QAAQ,MAAM,IAAI;AACvC,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;;;ACVA,IAAI,eAAe,SAAC,GAAc;AAAC;AAE5B,SAAS,SAAS,GAAW;AAClC,eAAa,CAAC;AAChB;AAEO,SAAS,YAAY,OAA4B;AACtD,iBAAe;AACjB;AAIA,IAAI,eAAe;AAEZ,SAAS,mBAAmB;AACjC,iBAAe;AACjB;AAEO,SAAS,qBAA8B;AAC5C,SAAO;AACT;AAKA,SAAS,YAAY;AAEnB,GAAC,QAAQ,QAAQ,QAAQ,KAAK,MAAM,SAAS,SAAS;AACxD;AAEO,IAAI,OAAO,UAAU,KAAK,sBAAsB;;;ACtBvD,IAAI,WAAW;AAER,SAAS,eAAwB;AACtC,SACE,OAAO,mBAAmB,eAC1B,QAAO,qCAAU,mBAAkB,eACnC,QAAO,qCAAU,cAAc,aAAY;AAE/C;AAEO,SAAS,YAAY,MAAuB;AACjD,MAAI,CAAC,SAAS,eAAe;AAC3B,WAAO;AAAA,EACT;AAGA,MAAI,UAAU,SAAS,cAAc,QAAQ,QAAQ,IAAI;AACzD,MAAI,SAAS;AACX,aAAS,oBAAoB;AAAA,EAC/B;AACA,SAAO;AACT;AAGO,SAAS,aAAqB;AACnC,MAAI,CAAC,SAAS,eAAe;AAC3B,UAAM,IAAI,MAAM,gCAAgC;AAAA,EAClD;AACA,MAAI,OAAO,SAAS,cAAc,QAAQ,MAAM;AAChD,MAAI,SAAS,IAAI;AACf,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;;;ACpCA,SAAS,aACP,SACA,MACA,GACM;AACN,WAAS,iBAAiB;AAC1B,UAAQ,UAAU;AAElB,WAAS,QAAQ,MAAM;AACrB,QAAI,QAAQ,KAAK,IAAI;AAErB,QAAI,gBAAgB,EAAE;AACtB,kBAAc,QAAQ,MAAM,KAAK;AACjC,QAAI,SAAS,cAAc,cAAc,QAAQ,IAAI,MAAM,OAAO;AAChE,eAAS,2BAA2B;AACpC,cAAQ,UAAU;AAAA,IACpB;AAAA,EACF;AACA,IAAE,eAAe;AACnB;AAEO,SAAS,SAAS,MAA2B;AAClD,MAAI,UAA2B,EAAE,SAAS,MAAM;AAChD,MAAI,WAAW,aAAa,KAAK,MAAM,SAAS,IAAI;AAEpD,WAAS,iBAAiB,QAAQ,QAAQ;AAC1C,MAAI;AAIF,aAAS,YAAY,MAAM;AAAA,EAC7B,UAAE;AACA,aAAS,oBAAoB,QAAQ,QAAQ;AAAA,EAC/C;AACA,SAAO,QAAQ;AACjB;AAGO,SAAS,uBACd,GACA,MACS;AACT,eAAa,CAAC;AACd,MAAI,UAAU,SAAS,IAAI;AAC3B,iBAAe;AACf,SAAO;AACT;AAIO,SAAS,kBAAkB,MAA2B;AAC3D,MAAI,WAAW,SAAS,cAAc,KAAK;AAG3C,WAAS,aAAa,SAAS,sCAAsC;AAErE,WAAS,cAAc;AACvB,WAAS,KAAK,YAAY,QAAQ;AAElC,MAAI,UAAU,uBAAuB,UAAU,IAAI;AAEnD,WAAS,KAAK,YAAY,QAAQ;AAClC,SAAO;AACT;AAGO,SAAS,iBAAiB,KAAsB;AACrD,WAAS,kBAAkB;AAE3B,MAAI,WAAW,SAAS,cAAc,KAAK;AAG3C,WAAS,aAAa,SAAS,sCAAsC;AAErE,MAAI,aAAmB;AACvB,MAAI,SAAS,cAAc;AACzB,aAAS,mBAAmB;AAC5B,iBAAa,SAAS,aAAa,EAAE,MAAM,OAAO,CAAC;AAAA,EACrD;AAEA,MAAI,OAAO,SAAS,cAAc,MAAM;AACxC,OAAK,YAAY;AAEjB,aAAW,YAAY,IAAI;AAC3B,WAAS,KAAK,YAAY,QAAQ;AAClC,eAAa,IAAI;AAEjB,MAAI,SAAS,SAAS,YAAY,MAAM;AAExC,iBAAe;AACf,WAAS,KAAK,YAAY,QAAQ;AAElC,SAAO;AACT;AAIA,SAAS,aAAa,MAAqB;AACzC,MAAI,MAAM,SAAS,aAAa;AAChC,MAAI,KAAK;AACP,QAAI,QAAQ,SAAS,YAAY;AACjC,UAAM,mBAAmB,IAAI;AAC7B,QAAI,gBAAgB;AACpB,QAAI,SAAS,KAAK;AAAA,EACpB;AACF;AAEA,SAAS,iBAAuB;AAC9B,MAAI,MAAM,SAAS,aAAa;AAChC,MAAI,KAAK;AACP,QAAI,gBAAgB;AAAA,EACtB;AACF;;;AC7GO,SAAS,cAAc,YAAiC;AAC7D,MAAI,eAAe,cAAc;AAGjC,MAAI,aAAa,GAAG;AAClB,QAAI,CAAC,cAAc;AACjB,YAAM,IAAI,MAAM,sCAAsC;AAAA,IACxD;AACA,QAAI,YAAY,WAAW,UAAU,CAAC,GAAG;AACvC,aAAO;AAAA,IACT,OAAO;AACL,YAAM,IAAI,MAAM,wDAAwD;AAAA,IAC1E;AAAA,EACF;AAEA,MAAI,SAAS,UAAU,GAAG;AACxB,aAAS,yBAAyB;AAClC,WAAO;AAAA,EACT;AAIA,MAAI,UAAU,UAAU,QAAQ,MAAM,IAAI,IAAI;AAC5C,aAAS,+BAA+B;AACxC,WAAO;AAAA,EACT;AAGA,MAAI,uBAAuB,SAAS,MAAM,UAAU,GAAG;AACrD,aAAS,+BAA+B;AACxC,WAAO;AAAA,EACT;AAGA,MAAI,kBAAkB,UAAU,GAAG;AACjC,aAAS,0BAA0B;AACnC,WAAO;AAAA,EACT;AAGA,MAAI,iBAAiB,WAAW,UAAU,CAAC,GAAG;AAC5C,aAAS,yBAAyB;AAClC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AC/CA,SAAS,mBAAmB,GAAuB;AACjD,MAAI,aAAyB,CAAC;AAC9B,aAAW,UAAU,IAAI;AACzB,SAAO;AACT;AAEO,SAAS,UAAU,GAA0B;AAElD,MAAI,qCAAqC;AACvC,aAAS,0CAA0C;AACnD,WAAO,oCAAoC,CAAC,EAAE;AAAA,MAAM,WAAG;AACrD,8CAA+B,CAAC;AAAA;AAAA,IAClC;AAAA,EACF;AACA,SAAO,+BAA+B,CAAC;AACzC;AAEA,SAAS,+BAA+B,GAA0B;AAChE,SAAO;AAAA,IAAmB,WAAG;AAC3B,gCAAmB,QAAQ,wBAAwB,CAAC,CAAC;AAAA;AAAA,EACvD;AACF;AAEA,SAAS,wBAAwB,GAAiB;AAChD,MAAI,CAAC,cAAc,mBAAmB,CAAC,CAAC,GAAG;AACzC,UAAM,IAAI,MAAM,oBAAoB;AAAA,EACtC;AACF;AAEO,SAAS,WAA4B;AAC1C,SAAO,mBAAmB,WAAM;AAE9B,QAAI,oCAAoC;AACtC,eAAS,yCAAyC;AAClD,aAAO,mCAAmC;AAAA,IAC5C;AAGA,QAAI,aAAa,GAAG;AAClB,UAAI,SAAS,WAAW;AACxB,aAAO,mBAAmB,QAAQ,MAAM;AAAA,IAC1C;AAEA,UAAM,IAAI,MAAM,wCAAwC;AAAA,EAC1D,CAAC;AACH;;;AChCO,SAAS,MAAM,MAA+C;AAGnE,SAAO,mBAAmB,WAAwB;AAChD,QAAI,mCAAmC,6BAA6B;AAElE,UAAI,wCACF;AACF,eAAS,sCAAsC;AAC/C,aAAO,mBACJ,IAAI,KAAK,IAAI,kCAAkC,CAAC,EAChD;AAAA,QACC,SACE,sBACqB;AACrB,iBAAO,sCAAsC,oBAAoB,EAC9D,KAAK,aAAa,EAClB,MAAM,SAAC,GAA+B;AAIrC,gBACE,CAAC,gBAAgB,MAAM,UAAU,KACjC,CAAC,gBAAgB,MAAM,SAAS,GAChC;AACA,oBAAM;AAAA,YACR;AACA,mBAAO;AAAA,UACT,CAAC;AAAA,QACL;AAAA,MACF;AAAA,IACJ;AACA,WAAO;AAAA,EACT,CAAC,EAAE,KAAK,SAAC,SAAqB;AAC5B,QAAI,SAAS;AACX,aAAO;AAAA,IACT;AAEA,QAAI,eAAe,gBAAgB,MAAM,UAAU;AACnD,QAAI,mBAAmB,KAAK,CAAC,cAAc;AACzC;AAAA,QACE;AAAA,MAIF;AAAA,IACF;AAEA,WAAO,aAAa,KAAK,CAAC,CAAC,EAAE,KAAK,SAAC,YAA2B;AAC5D,UAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,cAAM,IAAI,MAAM,gBAAgB;AAAA,MAClC;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACH;AAEO,SAAS,OAAgC;AAC9C,SAAO,mBAAmB,WAAM;AAE9B,QAAI,gCAAgC;AAClC,eAAS,qCAAqC;AAC9C,aAAO,+BAA+B;AAAA,IACxC;AAGA,WAAO,SAAS,EAAE,KAAK,SAAC,MAAiB;AACvC,aAAO,CAAC,oBAAoB,IAAI,CAAC;AAAA,IACnC,CAAC;AAAA,EACH,CAAC;AACH;;;AC7EA,OAAO,YAAY;AAAA,EACjB,MAAM;AAAA,EACN,UAAU;AAAA,EACV,OAAO;AAAA,EACP,WAAW;AAAA,EACX,eAAe;AAAA,EACf,aAAa;AAAA,EACb,kBAAkB;AACpB;;;ACbA,OAAO,kBAAkB;", "names": ["resolve", "reject", "i", "val", "_a", "_a", "i", "resolve", "reject", "resolve", "reject", "_a", "type"]}