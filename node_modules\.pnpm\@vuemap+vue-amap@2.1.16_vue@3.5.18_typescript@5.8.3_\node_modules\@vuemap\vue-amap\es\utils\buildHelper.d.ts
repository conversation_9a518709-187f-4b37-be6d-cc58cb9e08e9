import type { ComponentObjectPropsOptions, PropType } from "vue";
export declare type IPropData = Record<string, unknown>;
export interface IPropOptions<T = any> {
    type?: PropType<T> | true | null;
    required?: boolean;
    default?: any;
    validator?(value: unknown, props: IPropData): boolean;
}
export interface ICommonProps {
    visible: IPropOptions<boolean>;
    zIndex: IPropOptions<number>;
    reEventWhenUpdate: IPropOptions<boolean>;
    extraOptions: IPropOptions;
}
export declare const commonProps: ICommonProps;
/**
 * 合并生成基础的属性
 * @param props
 */
export declare const buildProps: <Props extends ComponentObjectPropsOptions<{
    [x: string]: unknown;
}>>(props: Props) => Props & {
    visible: IPropOptions<boolean>;
    zIndex: IPropOptions<number>;
    reEventWhenUpdate: IPropOptions<boolean>;
    extraOptions: IPropOptions;
};
