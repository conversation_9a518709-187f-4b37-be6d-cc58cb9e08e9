/// <reference types="@vuemap/amap-jsapi-types" />
declare const _default: import("vue").DefineComponent<{
    autoMove: {
        type: BooleanConstructor;
        default: boolean;
    };
    showRectangle: {
        type: BooleanConstructor;
        default: boolean;
    };
    showButton: {
        type: BooleanConstructor;
        default: boolean;
    };
    isOpen: {
        type: BooleanConstructor;
        default: boolean;
    };
    mapStyle: {
        type: StringConstructor;
    };
    layers: {
        type: ArrayConstructor;
    };
    width: {
        type: StringConstructor;
    };
    height: {
        type: StringConstructor;
    };
    offset: {
        type: ArrayConstructor;
    };
    borderStyle: {
        type: StringConstructor;
    };
    borderColor: {
        type: StringConstructor;
    };
    borderRadius: {
        type: StringConstructor;
    };
    borderWidth: {
        type: StringConstructor;
    };
    buttonSize: {
        type: StringConstructor;
    };
} & {
    visible: import("../../..").IPropOptions<boolean>;
    zIndex: import("../../..").IPropOptions<number>;
    reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
    extraOptions: import("../../..").IPropOptions<any>;
}, {
    emits: (event: "init", ...args: any[]) => void;
    $amapComponent: AMap.HawkEye;
    $$getInstance: () => AMap.HawkEye;
    parentInstance: import("../../../mixins").IProvideType | undefined;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], "init", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    autoMove: {
        type: BooleanConstructor;
        default: boolean;
    };
    showRectangle: {
        type: BooleanConstructor;
        default: boolean;
    };
    showButton: {
        type: BooleanConstructor;
        default: boolean;
    };
    isOpen: {
        type: BooleanConstructor;
        default: boolean;
    };
    mapStyle: {
        type: StringConstructor;
    };
    layers: {
        type: ArrayConstructor;
    };
    width: {
        type: StringConstructor;
    };
    height: {
        type: StringConstructor;
    };
    offset: {
        type: ArrayConstructor;
    };
    borderStyle: {
        type: StringConstructor;
    };
    borderColor: {
        type: StringConstructor;
    };
    borderRadius: {
        type: StringConstructor;
    };
    borderWidth: {
        type: StringConstructor;
    };
    buttonSize: {
        type: StringConstructor;
    };
} & {
    visible: import("../../..").IPropOptions<boolean>;
    zIndex: import("../../..").IPropOptions<number>;
    reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
    extraOptions: import("../../..").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
}, {
    autoMove: boolean;
    showRectangle: boolean;
    showButton: boolean;
    isOpen: boolean;
}, {}>;
export default _default;
