import type { PropType } from "vue";
export declare const propsType: {
    position: {
        type: PropType<"LT" | "RT" | "LB" | "RB">;
    };
    offset: {
        type: ArrayConstructor;
    };
    borderColor: {
        type: StringConstructor;
    };
    borderRadius: {
        type: StringConstructor;
    };
    buttonSize: {
        type: StringConstructor;
    };
    convert: {
        type: BooleanConstructor;
        default: boolean;
    };
    enableHighAccuracy: {
        type: BooleanConstructor;
        default: boolean;
    };
    timeout: {
        type: NumberConstructor;
    };
    maximumAge: {
        type: NumberConstructor;
    };
    showButton: {
        type: BooleanConstructor;
        default: boolean;
    };
    showCircle: {
        type: BooleanConstructor;
        default: boolean;
    };
    showMarker: {
        type: BooleanConstructor;
        default: boolean;
    };
    markerOptions: {
        type: ObjectConstructor;
    };
    circleOptions: {
        type: ObjectConstructor;
    };
    panToLocation: {
        type: BooleanConstructor;
        default: boolean;
    };
    zoomToAccuracy: {
        type: BooleanConstructor;
        default: boolean;
    };
    GeoLocationFirst: {
        type: BooleanConstructor;
        default: boolean;
    };
    noIpLocate: {
        type: NumberConstructor;
    };
    noGeoLocation: {
        type: NumberConstructor;
    };
    useNative: {
        type: BooleanConstructor;
        default: boolean;
    };
    getCityWhenFail: {
        type: BooleanConstructor;
        default: boolean;
    };
    needAddress: {
        type: BooleanConstructor;
        default: boolean;
    };
    extensions: {
        type: PropType<"base" | "all">;
        validator: (value: string) => boolean;
    };
} & {
    visible: import("../../../utils/buildHelper").IPropOptions<boolean>;
    zIndex: import("../../../utils/buildHelper").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils/buildHelper").IPropOptions<boolean>;
    extraOptions: import("../../../utils/buildHelper").IPropOptions<any>;
};
