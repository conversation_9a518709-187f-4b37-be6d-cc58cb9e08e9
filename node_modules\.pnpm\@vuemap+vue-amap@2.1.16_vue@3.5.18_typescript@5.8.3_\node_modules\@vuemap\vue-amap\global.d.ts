// GlobalComponents for Volar

declare module '@vue/runtime-core' {
  export interface GlobalComponents {
    ElAmap: typeof import('@vuemap/vue-amap')['ElAmap'],
    ElAmapControlControlBar: typeof import('@vuemap/vue-amap')['ElAmapControlControlBar'],
    ElAmapControlHawkEye: typeof import('@vuemap/vue-amap')['ElAmapControlHawkEye'],
    ElAmapControlMapType: typeof import('@vuemap/vue-amap')['ElAmapControlMapType'],
    ElAmapControlScale: typeof import('@vuemap/vue-amap')['ElAmapControlScale'],
    ElAmapControlToolBar: typeof import('@vuemap/vue-amap')['ElAmapControlToolBar'],
    ElAmapSearchBox: typeof import('@vuemap/vue-amap')['ElAmapSearchBox'],
    ElAmapInfoWindow: typeof import('@vuemap/vue-amap')['ElAmapInfoWindow'],
    ElAmapLayerCanvas: typeof import('@vuemap/vue-amap')['ElAmapLayerCanvas'],
    ElAmapLayerCustom: typeof import('@vuemap/vue-amap')['ElAmapLayerCustom'],
    ElAmapLayerFlexible: typeof import('@vuemap/vue-amap')['ElAmapLayerFlexible'],
    ElAmapLayerGlCustom: typeof import('@vuemap/vue-amap')['ElAmapLayerGlCustom'],
    ElAmapLayerHeatMap: typeof import('@vuemap/vue-amap')['ElAmapLayerHeatMap'],
    ElAmapLayerImage: typeof import('@vuemap/vue-amap')['ElAmapLayerImage'],
    ElAmapLayerLabels: typeof import('@vuemap/vue-amap')['ElAmapLayerLabels'],
    ElAmapLayerVector: typeof import('@vuemap/vue-amap')['ElAmapLayerVector'],
    ElAmapLayerBuildings: typeof import('@vuemap/vue-amap')['ElAmapLayerBuildings'],
    ElAmapLayerDefault: typeof import('@vuemap/vue-amap')['ElAmapLayerDefault'],
    ElAmapLayerDistrict: typeof import('@vuemap/vue-amap')['ElAmapLayerDistrict'],
    ElAmapLayerIndoorMap: typeof import('@vuemap/vue-amap')['ElAmapLayerIndoorMap'],
    ElAmapLayerRoadNet: typeof import('@vuemap/vue-amap')['ElAmapLayerRoadNet'],
    ElAmapLayerSatellite: typeof import('@vuemap/vue-amap')['ElAmapLayerSatellite'],
    ElAmapLayerTile: typeof import('@vuemap/vue-amap')['ElAmapLayerTile'],
    ElAmapLayerTraffic: typeof import('@vuemap/vue-amap')['ElAmapLayerTraffic'],
    ElAmapLayerMapboxVectorTile: typeof import('@vuemap/vue-amap')['ElAmapLayerMapboxVectorTile'],
    ElAmapLayerWms: typeof import('@vuemap/vue-amap')['ElAmapLayerWms'],
    ElAmapLayerWmts: typeof import('@vuemap/vue-amap')['ElAmapLayerWmts'],
    ElAmapElasticMarker: typeof import('@vuemap/vue-amap')['ElAmapElasticMarker'],
    ElAmapLabelMarker: typeof import('@vuemap/vue-amap')['ElAmapLabelMarker'],
    ElAmapMarker: typeof import('@vuemap/vue-amap')['ElAmapMarker'],
    ElAmapMarkerCluster: typeof import('@vuemap/vue-amap')['ElAmapMarkerCluster'],
    ElAmapMassMarks: typeof import('@vuemap/vue-amap')['ElAmapMassMarks'],
    ElAmapText: typeof import('@vuemap/vue-amap')['ElAmapText'],
    ElAmapBezierCurve: typeof import('@vuemap/vue-amap')['ElAmapBezierCurve'],
    ElAmapCircle: typeof import('@vuemap/vue-amap')['ElAmapCircle'],
    ElAmapEllipse: typeof import('@vuemap/vue-amap')['ElAmapEllipse'],
    ElAmapGeojson: typeof import('@vuemap/vue-amap')['ElAmapGeojson'],
    ElAmapPolygon: typeof import('@vuemap/vue-amap')['ElAmapPolygon'],
    ElAmapPolyline: typeof import('@vuemap/vue-amap')['ElAmapPolyline'],
    ElAmapRectangle: typeof import('@vuemap/vue-amap')['ElAmapRectangle'],
    ElAmapLayerTiles3d: typeof import('@vuemap/vue-amap')['ElAmapLayerTiles3d'],
    ElAmapLayerVideo: typeof import('@vuemap/vue-amap')['ElAmapLayerVideo'],
    ElAmapMouseTool: typeof import('@vuemap/vue-amap')['ElAmapMouseTool'],
    ElAmapLayerDistrictCluster: typeof import('@vuemap/vue-amap')['ElAmapLayerDistrictCluster'],
    ElAmapLayerCustomXyz: typeof import('@vuemap/vue-amap')['ElAmapLayerCustomXyz']
  }
}

export {};
