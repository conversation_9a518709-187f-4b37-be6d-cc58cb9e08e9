{"version": 3, "file": "index.js", "sources": ["../../../../../packages/control/Geolocation/index.ts"], "sourcesContent": ["import Geolocation from './Geolocation.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nGeolocation.install = (app: App) => {\r\n  app.component(Geolocation.name, Geolocation);\r\n  return app;\r\n};\r\nexport const ElAmapControlGeolocation = Geolocation as typeof Geolocation & Plugin;\r\nexport default ElAmapControlGeolocation;\r\n\r\nexport type ElAmapControlGeolocationInstance = InstanceType<typeof ElAmapControlGeolocation>\r\n"], "names": ["Geolocation"], "mappings": ";;;;;;;AAEAA,uDAAY,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AAClC,EAAI,GAAA,CAAA,SAAA,CAAUA,uDAAY,CAAA,IAAA,EAAMA,uDAAW,CAAA,CAAA;AAC3C,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AACO,MAAM,wBAA2B,GAAAA;;;;;"}