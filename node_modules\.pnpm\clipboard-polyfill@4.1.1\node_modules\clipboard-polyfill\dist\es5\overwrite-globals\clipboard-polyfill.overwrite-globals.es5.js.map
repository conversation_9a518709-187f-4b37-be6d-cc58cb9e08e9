{"version": 3, "sources": ["../../../src/clipboard-polyfill/ClipboardItem/data-types.ts", "../../../src/clipboard-polyfill/debug.ts", "../../../src/clipboard-polyfill/builtins/window-globalThis.ts", "../../../src/clipboard-polyfill/builtins/promise-constructor.ts", "../../../src/clipboard-polyfill/builtins/builtin-globals.ts", "../../../src/clipboard-polyfill/strategies/internet-explorer.ts", "../../../src/clipboard-polyfill/strategies/dom.ts", "../../../src/clipboard-polyfill/implementations/write-fallback.ts", "../../../src/clipboard-polyfill/promise/promise-compat.ts", "../../../src/clipboard-polyfill/implementations/text.ts", "../../../src/clipboard-polyfill/ClipboardItem/check.ts", "../../../src/clipboard-polyfill/ClipboardItem/ClipboardItemPolyfill.ts", "../../../src/clipboard-polyfill/ClipboardItem/convert.ts", "../../../src/clipboard-polyfill/implementations/blob.ts", "../../../src/clipboard-polyfill/entries/es5/overwrite-globals.ts"], "sourcesContent": ["export var TEXT_PLAIN = \"text/plain\";\nexport var TEXT_HTML = \"text/html\";\n", "/******** Debug Logging ********/\n\n// tslint:disable-next-line: no-empty\nvar debugLogImpl = (s: string) => {};\n\nexport function debugLog(s: string) {\n  debugLogImpl(s);\n}\n\nexport function setDebugLog(logFn: (s: string) => void) {\n  debugLogImpl = logFn;\n}\n\n/******** Warnings ********/\n\nvar showWarnings = true;\n\nexport function suppressWarnings() {\n  showWarnings = false;\n}\n\nexport function shouldShowWarnings(): boolean {\n  return showWarnings;\n}\n\n// Workaround for:\n// - IE9 (can't bind console functions directly), and\n// - Edge Issue #14495220 (referencing `console` without F12 Developer Tools can cause an exception)\nfunction warnOrLog() {\n  // biome-ignore lint/style/noArguments: `arguments` is ES5-compatible.\n  (console.warn || console.log).apply(console, arguments);\n}\n\nexport var warn = warnOrLog.bind(\"[clipboard-polyfill]\");\n", "export var originalWindow = typeof window === \"undefined\" ? undefined : window;\nexport var originalGlobalThis =\n  typeof globalThis === \"undefined\" ? undefined : globalThis;\n", "import { PromiseConstructor } from \"../promise/es6-promise\";\nimport { originalGlobalThis, originalWindow } from \"./window-globalThis\";\n\nvar promiseConstructorImpl: PromiseConstructor | undefined =\n  (originalWindow as { Promise?: PromiseConstructor } | undefined)?.Promise ??\n  originalGlobalThis?.Promise;\n\n// This must be called *before* `builtin-globals.ts` is imported, or it has no effect.\nexport function setPromiseConstructor(\n  newPromiseConstructorImpl: PromiseConstructor,\n) {\n  promiseConstructorImpl = newPromiseConstructorImpl;\n}\n\nexport function getPromiseConstructor(): PromiseConstructor {\n  if (!promiseConstructorImpl) {\n    throw new Error(\n      \"No `Promise` implementation available for `clipboard-polyfill`. Consider using: https://github.com/lgarron/clipboard-polyfill#flat-file-version-with-promise-included\",\n    );\n  }\n  return promiseConstructorImpl;\n}\n", "// We cache the references so that callers can do the following without causing infinite recursion/bugs:\n//\n//     import * as clipboard from \"clipboard-polyfill\";\n//     navigator.clipboard = clipboard;\n//\n//     import { ClipboardItem } from \"clipboard-polyfill\";\n//     window.ClipboardItem = clipboard;\n//\n// Note that per the spec:\n//\n// - is *not* possible to overwrite `navigator.clipboard`. https://www.w3.org/TR/clipboard-apis/#navigator-interface\n// - it *may* be possible to overwrite `window.ClipboardItem`.\n//\n// Chrome 83 and Safari 13.1 match this. We save the original\n// `navigator.clipboard` anyhow, because 1) it doesn't cost more code (in fact,\n// it probably saves code), and 2) just in case an unknown/future implementation\n// allows overwriting `navigator.clipboard` like this.\n\nimport type { PromiseConstructor } from \"../promise/es6-promise\";\nimport { originalWindow } from \"./window-globalThis\";\n\nimport {\n  ClipboardItemConstructor,\n  ClipboardEventTarget,\n  ClipboardItems,\n} from \"../ClipboardItem/spec\";\nimport { getPromiseConstructor } from \"./promise-constructor\";\n\nvar originalNavigator =\n  typeof navigator === \"undefined\" ? undefined : navigator;\nvar originalNavigatorClipboard: ClipboardEventTarget | undefined =\n  originalNavigator?.clipboard as any;\nexport var originalNavigatorClipboardRead:\n  | (() => Promise<ClipboardItems>)\n  | undefined = originalNavigatorClipboard?.read?.bind(\n  originalNavigatorClipboard,\n);\nexport var originalNavigatorClipboardReadText:\n  | (() => Promise<string>)\n  | undefined = originalNavigatorClipboard?.readText?.bind(\n  originalNavigatorClipboard,\n);\nexport var originalNavigatorClipboardWrite:\n  | ((data: ClipboardItems) => Promise<void>)\n  | undefined = originalNavigatorClipboard?.write?.bind(\n  originalNavigatorClipboard,\n);\nexport var originalNavigatorClipboardWriteText:\n  | ((data: string) => Promise<void>)\n  | undefined = originalNavigatorClipboard?.writeText?.bind(\n  originalNavigatorClipboard,\n);\n\n// The spec specifies that this goes on `window`, not e.g. `globalThis`. It's not (currently) available in workers.\nexport var originalWindowClipboardItem: ClipboardItemConstructor | undefined =\n  originalWindow?.ClipboardItem;\n\nexport var promiseConstructor: PromiseConstructor = getPromiseConstructor();\n", "import { originalWindow } from \"../builtins/window-globalThis\";\nimport { debugLog } from \"../debug\";\n\ninterface IEWindow extends Window {\n  clipboardData?: {\n    setData: (key: string, value: string) => boolean;\n    // Always results in a string: https://msdn.microsoft.com/en-us/library/ms536436(v=vs.85).aspx\n    getData: (key: string) => string;\n  };\n}\n\nvar ieWindow = originalWindow as IEWindow;\n\nexport function seemToBeInIE(): boolean {\n  return (\n    typeof ClipboardEvent === \"undefined\" &&\n    typeof ieWindow?.clipboardData !== \"undefined\" &&\n    typeof ieWindow?.clipboardData.setData !== \"undefined\"\n  );\n}\n\nexport function writeTextIE(text: string): boolean {\n  if (!ieWindow.clipboardData) {\n    return false;\n  }\n  // IE supports text or URL, but not HTML: https://msdn.microsoft.com/en-us/library/ms536744(v=vs.85).aspx\n  // TODO: Write URLs to `text/uri-list`? https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types\n  var success = ieWindow.clipboardData.setData(\"Text\", text);\n  if (success) {\n    debugLog(\"writeTextIE worked\");\n  }\n  return success;\n}\n\n// Returns \"\" if the read failed, e.g. because the user rejected the permission.\nexport function readTextIE(): string {\n  if (!ieWindow.clipboardData) {\n    throw new Error(\"Cannot read IE clipboard Data \");\n  }\n  var text = ieWindow.clipboardData.getData(\"Text\");\n  if (text === \"\") {\n    throw new Error(\n      \"Empty clipboard or could not read plain text from clipboard\",\n    );\n  }\n  return text;\n}\n", "import { StringItem } from \"../ClipboardItem/convert\";\nimport { TEXT_PLAIN } from \"../ClipboardItem/data-types\";\nimport { debugLog } from \"../debug\";\n\n/******** Implementations ********/\n\ninterface FallbackTracker {\n  success: boolean;\n}\n\nfunction copyListener(\n  tracker: FallbackTracker,\n  data: StringItem,\n  e: ClipboardEvent,\n): void {\n  debugLog(\"listener called\");\n  tracker.success = true;\n  // tslint:disable-next-line: forin\n  for (var type in data) {\n    var value = data[type];\n    // biome-ignore lint/style/noNonNullAssertion: We assume the data is present if the listener was called.\n    var clipboardData = e.clipboardData!;\n    clipboardData.setData(type, value);\n    if (type === TEXT_PLAIN && clipboardData.getData(type) !== value) {\n      debugLog(\"setting text/plain failed\");\n      tracker.success = false;\n    }\n  }\n  e.preventDefault();\n}\n\nexport function execCopy(data: StringItem): boolean {\n  var tracker: FallbackTracker = { success: false };\n  var listener = copyListener.bind(this, tracker, data);\n\n  document.addEventListener(\"copy\", listener);\n  try {\n    // We ignore the return value, since FallbackTracker tells us whether the\n    // listener was called. It seems that checking the return value here gives\n    // us no extra information in any browser.\n    document.execCommand(\"copy\");\n  } finally {\n    document.removeEventListener(\"copy\", listener);\n  }\n  return tracker.success;\n}\n\n// Temporarily select a DOM element, so that `execCommand()` is not rejected.\nexport function copyUsingTempSelection(\n  e: HTMLElement,\n  data: StringItem,\n): boolean {\n  selectionSet(e);\n  var success = execCopy(data);\n  selectionClear();\n  return success;\n}\n\n// Create a temporary DOM element to select, so that `execCommand()` is not\n// rejected.\nexport function copyUsingTempElem(data: StringItem): boolean {\n  var tempElem = document.createElement(\"div\");\n  // Setting an individual property does not support `!important`, so we set the\n  // whole style instead of just the `-webkit-user-select` property.\n  tempElem.setAttribute(\"style\", \"-webkit-user-select: text !important\");\n  // Place some text in the elem so that Safari has something to select.\n  tempElem.textContent = \"temporary element\";\n  document.body.appendChild(tempElem);\n\n  var success = copyUsingTempSelection(tempElem, data);\n\n  document.body.removeChild(tempElem);\n  return success;\n}\n\n// Uses shadow DOM.\nexport function copyTextUsingDOM(str: string): boolean {\n  debugLog(\"copyTextUsingDOM\");\n\n  var tempElem = document.createElement(\"div\");\n  // Setting an individual property does not support `!important`, so we set the\n  // whole style instead of just the `-webkit-user-select` property.\n  tempElem.setAttribute(\"style\", \"-webkit-user-select: text !important\");\n  // Use shadow DOM if available.\n  var spanParent: Node = tempElem;\n  if (tempElem.attachShadow) {\n    debugLog(\"Using shadow DOM.\");\n    spanParent = tempElem.attachShadow({ mode: \"open\" });\n  }\n\n  var span = document.createElement(\"span\");\n  span.innerText = str;\n\n  spanParent.appendChild(span);\n  document.body.appendChild(tempElem);\n  selectionSet(span);\n\n  var result = document.execCommand(\"copy\");\n\n  selectionClear();\n  document.body.removeChild(tempElem);\n\n  return result;\n}\n\n/******** Selection ********/\n\nfunction selectionSet(elem: Element): void {\n  var sel = document.getSelection();\n  if (sel) {\n    var range = document.createRange();\n    range.selectNodeContents(elem);\n    sel.removeAllRanges();\n    sel.addRange(range);\n  }\n}\n\nfunction selectionClear(): void {\n  var sel = document.getSelection();\n  if (sel) {\n    sel.removeAllRanges();\n  }\n}\n", "import { StringItem } from \"../ClipboardItem/convert\";\nimport { TEXT_PLAIN } from \"../ClipboardItem/data-types\";\nimport { debugLog } from \"../debug\";\nimport {\n  copyTextUsingDOM,\n  copyUsingTempElem,\n  copyUsingTempSelection,\n  execCopy,\n} from \"../strategies/dom\";\nimport { seemToBeInIE, writeTextIE } from \"../strategies/internet-explorer\";\n\n// Note: the fallback order is carefully tuned for compatibility. It might seem\n// safe to move some of them around, but do not do so without testing all browsers.\nexport function writeFallback(stringItem: StringItem): boolean {\n  var hasTextPlain = TEXT_PLAIN in stringItem;\n\n  // Internet Explorer\n  if (seemToBeInIE()) {\n    if (!hasTextPlain) {\n      throw new Error(\"No `text/plain` value was specified.\");\n    }\n    if (writeTextIE(stringItem[TEXT_PLAIN])) {\n      return true;\n    } else {\n      throw new Error(\"Copying failed, possibly because the user rejected it.\");\n    }\n  }\n\n  if (execCopy(stringItem)) {\n    debugLog(\"regular execCopy worked\");\n    return true;\n  }\n\n  // Success detection on Edge is not possible, due to bugs in all 4\n  // detection mechanisms we could try to use. Assume success.\n  if (navigator.userAgent.indexOf(\"Edge\") > -1) {\n    debugLog('UA \"Edge\" => assuming success');\n    return true;\n  }\n\n  // Fallback 1 for desktop Safari.\n  if (copyUsingTempSelection(document.body, stringItem)) {\n    debugLog(\"copyUsingTempSelection worked\");\n    return true;\n  }\n\n  // Fallback 2 for desktop Safari.\n  if (copyUsingTempElem(stringItem)) {\n    debugLog(\"copyUsingTempElem worked\");\n    return true;\n  }\n\n  // Fallback for iOS Safari.\n  if (copyTextUsingDOM(stringItem[TEXT_PLAIN])) {\n    debugLog(\"copyTextUsingDOM worked\");\n    return true;\n  }\n\n  return false;\n}\n", "import { promiseConstructor } from \"../builtins/builtin-globals\";\n\nexport function promiseRecordMap<T>(\n  keys: readonly string[],\n  f: (key: string) => Promise<T>,\n): Promise<Record<string, T>> {\n  var promiseList: Promise<T>[] = [];\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i];\n    promiseList.push(f(key));\n  }\n  return promiseConstructor\n    .all(promiseList)\n    .then((vList: T[]): Record<string, T> => {\n      var dataOut: Record<string, T> = {};\n      for (var i = 0; i < keys.length; i++) {\n        dataOut[keys[i]] = vList[i];\n      }\n      return dataOut;\n    });\n}\n\nexport var voidPromise: Promise<void> = promiseConstructor.resolve();\nexport var truePromiseFn: () => Promise<boolean> = () =>\n  promiseConstructor.resolve(true);\nexport var falsePromise: Promise<boolean> = promiseConstructor.resolve(false);\n\nexport function rejectThrownErrors<T>(executor: () => Promise<T>): Promise<T> {\n  return new promiseConstructor((resolve, reject) => {\n    try {\n      resolve(executor());\n    } catch (e) {\n      reject(e);\n    }\n  });\n}\n", "import { StringItem } from \"../ClipboardItem/convert\";\nimport { TEXT_PLAIN } from \"../ClipboardItem/data-types\";\nimport { debugLog } from \"../debug\";\nimport {\n  originalNavigatorClipboardReadText,\n  originalNavigatorClipboardWriteText,\n  promiseConstructor,\n} from \"../builtins/builtin-globals\";\nimport { readTextIE, seemToBeInIE } from \"../strategies/internet-explorer\";\nimport { writeFallback } from \"./write-fallback\";\nimport { rejectThrownErrors } from \"../promise/promise-compat\";\n\nfunction stringToStringItem(s: string): StringItem {\n  var stringItem: StringItem = {};\n  stringItem[TEXT_PLAIN] = s;\n  return stringItem;\n}\n\nexport function writeText(s: string): Promise<void> {\n  // Use the browser implementation if it exists.\n  if (originalNavigatorClipboardWriteText) {\n    debugLog(\"Using `navigator.clipboard.writeText()`.\");\n    return originalNavigatorClipboardWriteText(s).catch(() =>\n      writeTextStringFallbackPromise(s),\n    );\n  }\n  return writeTextStringFallbackPromise(s);\n}\n\nfunction writeTextStringFallbackPromise(s: string): Promise<void> {\n  return rejectThrownErrors(() =>\n    promiseConstructor.resolve(writeTextStringFallback(s)),\n  );\n}\n\nfunction writeTextStringFallback(s: string): void {\n  if (!writeFallback(stringToStringItem(s))) {\n    throw new Error(\"writeText() failed\");\n  }\n}\n\nexport function readText(): Promise<string> {\n  return rejectThrownErrors(() => {\n    // Use the browser implementation if it exists.\n    if (originalNavigatorClipboardReadText) {\n      debugLog(\"Using `navigator.clipboard.readText()`.\");\n      return originalNavigatorClipboardReadText();\n    }\n\n    // Fallback for IE.\n    if (seemToBeInIE()) {\n      var result = readTextIE();\n      return promiseConstructor.resolve(result);\n    }\n\n    throw new Error(\"Read is not supported in your browser.\");\n  });\n}\n", "import { ClipboardItemInterface } from \"./spec\";\n\nexport function hasItemWithType(\n  clipboardItems: ClipboardItemInterface[],\n  typeName: string,\n): boolean {\n  for (var i = 0; i < clipboardItems.length; i++) {\n    var item = clipboardItems[i];\n    if (item.types.indexOf(typeName) !== -1) {\n      return true;\n    }\n  }\n  return false;\n}\n", "import { promiseConstructor } from \"../builtins/builtin-globals\";\nimport { stringToBlob } from \"./convert\";\nimport {\n  ClipboardItemConstructor,\n  ClipboardItemDataType,\n  ClipboardItemInterface,\n  ClipboardItemOptions,\n} from \"./spec\";\n\nfunction ClipboardItemPolyfillImpl(\n  // TODO: The spec specifies values as `ClipboardItemData`, but\n  // implementations (e.g. Chrome 83) seem to assume `ClipboardItemDataType`\n  // values. https://github.com/w3c/clipboard-apis/pull/126\n  items: { [type: string]: ClipboardItemDataType },\n  options?: ClipboardItemOptions,\n): ClipboardItemInterface {\n  var types = Object.keys(items);\n  var _items: { [type: string]: Blob } = {};\n  // biome-ignore lint/suspicious/noRedeclare: This is a false positive from Biome. https://github.com/biomejs/biome/issues/175\n  for (var type in items) {\n    var item = items[type];\n    if (typeof item === \"string\") {\n      _items[type] = stringToBlob(type, item);\n    } else {\n      _items[type] = item;\n    }\n  }\n  // The explicit default for `presentationStyle` is \"unspecified\":\n  // https://www.w3.org/TR/clipboard-apis/#clipboard-interface\n  var presentationStyle = options?.presentationStyle ?? \"unspecified\";\n\n  function getType(type: string): Promise<Blob> {\n    return promiseConstructor.resolve(_items[type]);\n  }\n  return {\n    types: types,\n    presentationStyle: presentationStyle,\n    getType: getType,\n  };\n}\n\nexport var ClipboardItemPolyfill: ClipboardItemConstructor =\n  ClipboardItemPolyfillImpl as any as ClipboardItemConstructor;\n", "import { ClipboardItemPolyfill } from \"./ClipboardItemPolyfill\";\nimport { TEXT_PLAIN } from \"./data-types\";\nimport { ClipboardItemInterface, ClipboardItemOptions } from \"./spec\";\nimport {\n  promiseConstructor,\n  originalWindowClipboardItem,\n} from \"../builtins/builtin-globals\";\nimport { promiseRecordMap } from \"../promise/promise-compat\";\n\nexport function stringToBlob(type: string, str: string): Blob {\n  return new Blob([str], {\n    type,\n  });\n}\n\nexport function blobToString(blob: Blob): Promise<string> {\n  return new promiseConstructor((resolve, reject) => {\n    var fileReader = new FileReader();\n    fileReader.addEventListener(\"load\", () => {\n      var result = fileReader.result;\n      if (typeof result === \"string\") {\n        resolve(result);\n      } else {\n        reject(\"could not convert blob to string\");\n      }\n    });\n    fileReader.readAsText(blob);\n  });\n}\n\nexport function clipboardItemToGlobalClipboardItem(\n  clipboardItem: ClipboardItemInterface,\n): Promise<ClipboardItemInterface> {\n  // Note that we use `Blob` instead of `ClipboardItemDataType`. This is because\n  // Chrome 83 can only accept `Blob` (not `string`). The return value of\n  // `getType()` is already `Blob` per the spec, so this is simple for us.\n  return promiseRecordMap(clipboardItem.types, function (type: string) {\n    return clipboardItem.getType(type);\n  }).then((items: Record<string, Blob>) => {\n    return new promiseConstructor((resolve, reject) => {\n      var options: ClipboardItemOptions = {};\n      if (clipboardItem.presentationStyle) {\n        options.presentationStyle = clipboardItem.presentationStyle;\n      }\n      if (originalWindowClipboardItem) {\n        resolve(new originalWindowClipboardItem(items, options));\n      } else {\n        reject(\"window.ClipboardItem is not defined\");\n      }\n    });\n  });\n}\n\nexport function textToClipboardItem(text: string): ClipboardItemInterface {\n  var items: { [type: string]: Blob } = {};\n  items[TEXT_PLAIN] = stringToBlob(text, TEXT_PLAIN);\n  return new ClipboardItemPolyfill(items);\n}\n\nexport function getTypeAsString(\n  clipboardItem: ClipboardItemInterface,\n  type: string,\n): Promise<string> {\n  return clipboardItem.getType(type).then((text: Blob) => {\n    return blobToString(text);\n  });\n}\n\nexport interface StringItem {\n  [type: string]: string;\n}\n\nexport function toStringItem(\n  data: ClipboardItemInterface,\n): Promise<StringItem> {\n  return promiseRecordMap(data.types, function (type: string) {\n    return getTypeAsString(data, type);\n  });\n}\n", "import { hasItemWithType } from \"../ClipboardItem/check\";\nimport {\n  clipboardItemToGlobalClipboardItem,\n  toStringItem,\n  textToClipboardItem,\n  StringItem,\n} from \"../ClipboardItem/convert\";\nimport { TEXT_HTML, TEXT_PLAIN } from \"../ClipboardItem/data-types\";\nimport { ClipboardItemInterface, ClipboardItems } from \"../ClipboardItem/spec\";\nimport { debugLog, shouldShowWarnings } from \"../debug\";\nimport {\n  promiseConstructor,\n  originalNavigatorClipboardRead,\n  originalNavigatorClipboardWrite,\n  originalWindowClipboardItem,\n} from \"../builtins/builtin-globals\";\nimport {\n  falsePromise,\n  rejectThrownErrors,\n  truePromiseFn,\n  voidPromise,\n} from \"../promise/promise-compat\";\nimport { readText } from \"./text\";\nimport { writeFallback } from \"./write-fallback\";\n\nexport function write(data: ClipboardItemInterface[]): Promise<void> {\n  // Use the browser implementation if it exists.\n  // TODO: detect `text/html`.\n  return rejectThrownErrors((): Promise<boolean> => {\n    if (originalNavigatorClipboardWrite && originalWindowClipboardItem) {\n      // TODO: This reference is a workaround for TypeScript inference.\n      var originalNavigatorClipboardWriteCached =\n        originalNavigatorClipboardWrite;\n      debugLog(\"Using `navigator.clipboard.write()`.\");\n      return promiseConstructor\n        .all(data.map(clipboardItemToGlobalClipboardItem))\n        .then(\n          (\n            globalClipboardItems: ClipboardItemInterface[],\n          ): Promise<boolean> => {\n            return originalNavigatorClipboardWriteCached(globalClipboardItems)\n              .then(truePromiseFn)\n              .catch((e: Error): Promise<boolean> => {\n                // Chrome 83 will throw a DOMException or NotAllowedError because it doesn't support e.g. `text/html`.\n                // We want to fall back to the other strategies in a situation like this.\n                // See https://github.com/w3c/clipboard-apis/issues/128 and https://github.com/w3c/clipboard-apis/issues/67\n                if (\n                  !hasItemWithType(data, TEXT_PLAIN) &&\n                  !hasItemWithType(data, TEXT_HTML)\n                ) {\n                  throw e;\n                }\n                return falsePromise;\n              });\n          },\n        );\n    }\n    return falsePromise;\n  }).then((success: boolean) => {\n    if (success) {\n      return voidPromise;\n    }\n\n    var hasTextPlain = hasItemWithType(data, TEXT_PLAIN);\n    if (shouldShowWarnings() && !hasTextPlain) {\n      debugLog(\n        \"clipboard.write() was called without a \" +\n          \"`text/plain` data type. On some platforms, this may result in an \" +\n          \"empty clipboard. Call suppressWarnings() \" +\n          \"to suppress this warning.\",\n      );\n    }\n\n    return toStringItem(data[0]).then((stringItem: StringItem) => {\n      if (!writeFallback(stringItem)) {\n        throw new Error(\"write() failed\");\n      }\n    });\n  });\n}\n\nexport function read(): Promise<ClipboardItems> {\n  return rejectThrownErrors(() => {\n    // Use the browser implementation if it exists.\n    if (originalNavigatorClipboardRead) {\n      debugLog(\"Using `navigator.clipboard.read()`.\");\n      return originalNavigatorClipboardRead();\n    }\n\n    // Fallback to reading text only.\n    return readText().then((text: string) => {\n      return [textToClipboardItem(text)];\n    });\n  });\n}\n", "import { readText, writeText } from \"../../implementations/text\";\nimport { read, write } from \"../../implementations/blob\";\nimport { ClipboardItemPolyfill } from \"../../ClipboardItem/ClipboardItemPolyfill\";\n\n// Create the `navigator.clipboard` object if it doesn't exist.\nif (!navigator.clipboard) {\n  (navigator as any).clipboard = {};\n}\n\n// Set/replace the implementations.\nnavigator.clipboard.read = read;\nnavigator.clipboard.readText = readText;\nnavigator.clipboard.write = write;\nnavigator.clipboard.writeText = writeText;\n\nwindow.ClipboardItem = ClipboardItemPolyfill;\n"], "mappings": ";;;AAAO,MAAI,aAAa;AACjB,MAAI,YAAY;;;ACEvB,MAAI,eAAe,SAAC,GAAc;AAAA,EAAC;AAE5B,WAAS,SAAS,GAAW;AAClC,iBAAa,CAAC;AAAA,EAChB;AAQA,MAAI,eAAe;AAMZ,WAAS,qBAA8B;AAC5C,WAAO;AAAA,EACT;AAKA,WAAS,YAAY;AAEnB,KAAC,QAAQ,QAAQ,QAAQ,KAAK,MAAM,SAAS,SAAS;AAAA,EACxD;AAEO,MAAI,OAAO,UAAU,KAAK,sBAAsB;;;ACjChD,MAAI,iBAAiB,OAAO,WAAW,cAAc,SAAY;AACjE,MAAI,qBACT,OAAO,eAAe,cAAc,SAAY;;;ACFlD;AAGA,MAAI,0BACD,iDAAiE,YAAjE,aACD,+CAAoB;AASf,WAAS,wBAA4C;AAC1D,QAAI,CAAC,wBAAwB;AAC3B,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;;;ACOA,MAAI,oBACF,OAAO,cAAc,cAAc,SAAY;AACjD,MAAI,6BACF,uDAAmB;AA/BrB,MAAAA;AAgCO,MAAI,kCAEKA,MAAA,yEAA4B,SAA5B,gBAAAA,IAAkC;AAAA,IAChD;AAAA;AAnCF,MAAAA;AAqCO,MAAI,sCAEKA,MAAA,yEAA4B,aAA5B,gBAAAA,IAAsC;AAAA,IACpD;AAAA;AAxCF,MAAAA;AA0CO,MAAI,mCAEKA,MAAA,yEAA4B,UAA5B,gBAAAA,IAAmC;AAAA,IACjD;AAAA;AA7CF,MAAAA;AA+CO,MAAI,uCAEKA,MAAA,yEAA4B,cAA5B,gBAAAA,IAAuC;AAAA,IACrD;AAAA;AAlDF,MAAAA;AAsDO,MAAI,+BACTA,MAAA,mCAAAA,IAAgB;AAEX,MAAI,qBAAyC,sBAAsB;;;AC9C1E,MAAI,WAAW;AAER,WAAS,eAAwB;AACtC,WACE,OAAO,mBAAmB,eAC1B,QAAO,qCAAU,mBAAkB,eACnC,QAAO,qCAAU,cAAc,aAAY;AAAA,EAE/C;AAEO,WAAS,YAAY,MAAuB;AACjD,QAAI,CAAC,SAAS,eAAe;AAC3B,aAAO;AAAA,IACT;AAGA,QAAI,UAAU,SAAS,cAAc,QAAQ,QAAQ,IAAI;AACzD,QAAI,SAAS;AACX,eAAS,oBAAoB;AAAA,IAC/B;AACA,WAAO;AAAA,EACT;AAGO,WAAS,aAAqB;AACnC,QAAI,CAAC,SAAS,eAAe;AAC3B,YAAM,IAAI,MAAM,gCAAgC;AAAA,IAClD;AACA,QAAI,OAAO,SAAS,cAAc,QAAQ,MAAM;AAChD,QAAI,SAAS,IAAI;AACf,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;;;ACpCA,WAAS,aACP,SACA,MACA,GACM;AACN,aAAS,iBAAiB;AAC1B,YAAQ,UAAU;AAElB,aAAS,QAAQ,MAAM;AACrB,UAAI,QAAQ,KAAK,IAAI;AAErB,UAAI,gBAAgB,EAAE;AACtB,oBAAc,QAAQ,MAAM,KAAK;AACjC,UAAI,SAAS,cAAc,cAAc,QAAQ,IAAI,MAAM,OAAO;AAChE,iBAAS,2BAA2B;AACpC,gBAAQ,UAAU;AAAA,MACpB;AAAA,IACF;AACA,MAAE,eAAe;AAAA,EACnB;AAEO,WAAS,SAAS,MAA2B;AAClD,QAAI,UAA2B,EAAE,SAAS,MAAM;AAChD,QAAI,WAAW,aAAa,KAAK,MAAM,SAAS,IAAI;AAEpD,aAAS,iBAAiB,QAAQ,QAAQ;AAC1C,QAAI;AAIF,eAAS,YAAY,MAAM;AAAA,IAC7B,UAAE;AACA,eAAS,oBAAoB,QAAQ,QAAQ;AAAA,IAC/C;AACA,WAAO,QAAQ;AAAA,EACjB;AAGO,WAAS,uBACd,GACA,MACS;AACT,iBAAa,CAAC;AACd,QAAI,UAAU,SAAS,IAAI;AAC3B,mBAAe;AACf,WAAO;AAAA,EACT;AAIO,WAAS,kBAAkB,MAA2B;AAC3D,QAAI,WAAW,SAAS,cAAc,KAAK;AAG3C,aAAS,aAAa,SAAS,sCAAsC;AAErE,aAAS,cAAc;AACvB,aAAS,KAAK,YAAY,QAAQ;AAElC,QAAI,UAAU,uBAAuB,UAAU,IAAI;AAEnD,aAAS,KAAK,YAAY,QAAQ;AAClC,WAAO;AAAA,EACT;AAGO,WAAS,iBAAiB,KAAsB;AACrD,aAAS,kBAAkB;AAE3B,QAAI,WAAW,SAAS,cAAc,KAAK;AAG3C,aAAS,aAAa,SAAS,sCAAsC;AAErE,QAAI,aAAmB;AACvB,QAAI,SAAS,cAAc;AACzB,eAAS,mBAAmB;AAC5B,mBAAa,SAAS,aAAa,EAAE,MAAM,OAAO,CAAC;AAAA,IACrD;AAEA,QAAI,OAAO,SAAS,cAAc,MAAM;AACxC,SAAK,YAAY;AAEjB,eAAW,YAAY,IAAI;AAC3B,aAAS,KAAK,YAAY,QAAQ;AAClC,iBAAa,IAAI;AAEjB,QAAI,SAAS,SAAS,YAAY,MAAM;AAExC,mBAAe;AACf,aAAS,KAAK,YAAY,QAAQ;AAElC,WAAO;AAAA,EACT;AAIA,WAAS,aAAa,MAAqB;AACzC,QAAI,MAAM,SAAS,aAAa;AAChC,QAAI,KAAK;AACP,UAAI,QAAQ,SAAS,YAAY;AACjC,YAAM,mBAAmB,IAAI;AAC7B,UAAI,gBAAgB;AACpB,UAAI,SAAS,KAAK;AAAA,IACpB;AAAA,EACF;AAEA,WAAS,iBAAuB;AAC9B,QAAI,MAAM,SAAS,aAAa;AAChC,QAAI,KAAK;AACP,UAAI,gBAAgB;AAAA,IACtB;AAAA,EACF;;;AC7GO,WAAS,cAAc,YAAiC;AAC7D,QAAI,eAAe,cAAc;AAGjC,QAAI,aAAa,GAAG;AAClB,UAAI,CAAC,cAAc;AACjB,cAAM,IAAI,MAAM,sCAAsC;AAAA,MACxD;AACA,UAAI,YAAY,WAAW,UAAU,CAAC,GAAG;AACvC,eAAO;AAAA,MACT,OAAO;AACL,cAAM,IAAI,MAAM,wDAAwD;AAAA,MAC1E;AAAA,IACF;AAEA,QAAI,SAAS,UAAU,GAAG;AACxB,eAAS,yBAAyB;AAClC,aAAO;AAAA,IACT;AAIA,QAAI,UAAU,UAAU,QAAQ,MAAM,IAAI,IAAI;AAC5C,eAAS,+BAA+B;AACxC,aAAO;AAAA,IACT;AAGA,QAAI,uBAAuB,SAAS,MAAM,UAAU,GAAG;AACrD,eAAS,+BAA+B;AACxC,aAAO;AAAA,IACT;AAGA,QAAI,kBAAkB,UAAU,GAAG;AACjC,eAAS,0BAA0B;AACnC,aAAO;AAAA,IACT;AAGA,QAAI,iBAAiB,WAAW,UAAU,CAAC,GAAG;AAC5C,eAAS,yBAAyB;AAClC,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,EACT;;;ACzDO,WAAS,iBACd,MACA,GAC4B;AAC5B,QAAI,cAA4B,CAAC;AACjC,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,UAAI,MAAM,KAAK,CAAC;AAChB,kBAAY,KAAK,EAAE,GAAG,CAAC;AAAA,IACzB;AACA,WAAO,mBACJ,IAAI,WAAW,EACf,KAAK,SAAC,OAAkC;AACvC,UAAI,UAA6B,CAAC;AAClC,eAASC,KAAI,GAAGA,KAAI,KAAK,QAAQA,MAAK;AACpC,gBAAQ,KAAKA,EAAC,CAAC,IAAI,MAAMA,EAAC;AAAA,MAC5B;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACL;AAEO,MAAI,cAA6B,mBAAmB,QAAQ;AAC5D,MAAI,gBAAwC,WAAG;AACpD,8BAAmB,QAAQ,IAAI;AAAA;AAC1B,MAAI,eAAiC,mBAAmB,QAAQ,KAAK;AAErE,WAAS,mBAAsB,UAAwC;AAC5E,WAAO,IAAI,mBAAmB,SAAC,SAAS,QAAW;AACjD,UAAI;AACF,gBAAQ,SAAS,CAAC;AAAA,MACpB,SAAS,GAAG;AACV,eAAO,CAAC;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;;;ACvBA,WAAS,mBAAmB,GAAuB;AACjD,QAAI,aAAyB,CAAC;AAC9B,eAAW,UAAU,IAAI;AACzB,WAAO;AAAA,EACT;AAEO,WAAS,UAAU,GAA0B;AAElD,QAAI,qCAAqC;AACvC,eAAS,0CAA0C;AACnD,aAAO,oCAAoC,CAAC,EAAE;AAAA,QAAM,WAAG;AACrD,gDAA+B,CAAC;AAAA;AAAA,MAClC;AAAA,IACF;AACA,WAAO,+BAA+B,CAAC;AAAA,EACzC;AAEA,WAAS,+BAA+B,GAA0B;AAChE,WAAO;AAAA,MAAmB,WAAG;AAC3B,kCAAmB,QAAQ,wBAAwB,CAAC,CAAC;AAAA;AAAA,IACvD;AAAA,EACF;AAEA,WAAS,wBAAwB,GAAiB;AAChD,QAAI,CAAC,cAAc,mBAAmB,CAAC,CAAC,GAAG;AACzC,YAAM,IAAI,MAAM,oBAAoB;AAAA,IACtC;AAAA,EACF;AAEO,WAAS,WAA4B;AAC1C,WAAO,mBAAmB,WAAM;AAE9B,UAAI,oCAAoC;AACtC,iBAAS,yCAAyC;AAClD,eAAO,mCAAmC;AAAA,MAC5C;AAGA,UAAI,aAAa,GAAG;AAClB,YAAI,SAAS,WAAW;AACxB,eAAO,mBAAmB,QAAQ,MAAM;AAAA,MAC1C;AAEA,YAAM,IAAI,MAAM,wCAAwC;AAAA,IAC1D,CAAC;AAAA,EACH;;;ACvDO,WAAS,gBACd,gBACA,UACS;AACT,aAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,UAAI,OAAO,eAAe,CAAC;AAC3B,UAAI,KAAK,MAAM,QAAQ,QAAQ,MAAM,IAAI;AACvC,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;;;ACJA,WAAS,0BAIP,OACA,SACwB;AAf1B,QAAAC;AAgBE,QAAI,QAAQ,OAAO,KAAK,KAAK;AAC7B,QAAI,SAAmC,CAAC;AAExC,aAAS,QAAQ,OAAO;AACtB,UAAI,OAAO,MAAM,IAAI;AACrB,UAAI,OAAO,SAAS,UAAU;AAC5B,eAAO,IAAI,IAAI,aAAa,MAAM,IAAI;AAAA,MACxC,OAAO;AACL,eAAO,IAAI,IAAI;AAAA,MACjB;AAAA,IACF;AAGA,QAAI,qBAAoBA,MAAA,mCAAS,sBAAT,OAAAA,MAA8B;AAEtD,aAAS,QAAQC,OAA6B;AAC5C,aAAO,mBAAmB,QAAQ,OAAOA,KAAI,CAAC;AAAA,IAChD;AACA,WAAO;AAAA,MACL,OAAO;AAAA,MACP,mBAAmB;AAAA,MACnB,SAAS;AAAA,IACX;AAAA,EACF;AAEO,MAAI,wBACT;;;ACjCK,WAAS,aAAa,MAAc,KAAmB;AAC5D,WAAO,IAAI,KAAK,CAAC,GAAG,GAAG;AAAA,MACrB;AAAA,IACF,CAAC;AAAA,EACH;AAEO,WAAS,aAAa,MAA6B;AACxD,WAAO,IAAI,mBAAmB,SAAC,SAAS,QAAW;AACjD,UAAI,aAAa,IAAI,WAAW;AAChC,iBAAW,iBAAiB,QAAQ,WAAM;AACxC,YAAI,SAAS,WAAW;AACxB,YAAI,OAAO,WAAW,UAAU;AAC9B,kBAAQ,MAAM;AAAA,QAChB,OAAO;AACL,iBAAO,kCAAkC;AAAA,QAC3C;AAAA,MACF,CAAC;AACD,iBAAW,WAAW,IAAI;AAAA,IAC5B,CAAC;AAAA,EACH;AAEO,WAAS,mCACd,eACiC;AAIjC,WAAO,iBAAiB,cAAc,OAAO,SAAU,MAAc;AACnE,aAAO,cAAc,QAAQ,IAAI;AAAA,IACnC,CAAC,EAAE,KAAK,SAAC,OAAgC;AACvC,aAAO,IAAI,mBAAmB,SAAC,SAAS,QAAW;AACjD,YAAI,UAAgC,CAAC;AACrC,YAAI,cAAc,mBAAmB;AACnC,kBAAQ,oBAAoB,cAAc;AAAA,QAC5C;AACA,YAAI,6BAA6B;AAC/B,kBAAQ,IAAI,4BAA4B,OAAO,OAAO,CAAC;AAAA,QACzD,OAAO;AACL,iBAAO,qCAAqC;AAAA,QAC9C;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEO,WAAS,oBAAoB,MAAsC;AACxE,QAAI,QAAkC,CAAC;AACvC,UAAM,UAAU,IAAI,aAAa,MAAM,UAAU;AACjD,WAAO,IAAI,sBAAsB,KAAK;AAAA,EACxC;AAEO,WAAS,gBACd,eACA,MACiB;AACjB,WAAO,cAAc,QAAQ,IAAI,EAAE,KAAK,SAAC,MAAe;AACtD,aAAO,aAAa,IAAI;AAAA,IAC1B,CAAC;AAAA,EACH;AAMO,WAAS,aACd,MACqB;AACrB,WAAO,iBAAiB,KAAK,OAAO,SAAU,MAAc;AAC1D,aAAO,gBAAgB,MAAM,IAAI;AAAA,IACnC,CAAC;AAAA,EACH;;;ACrDO,WAAS,MAAM,MAA+C;AAGnE,WAAO,mBAAmB,WAAwB;AAChD,UAAI,mCAAmC,6BAA6B;AAElE,YAAI,wCACF;AACF,iBAAS,sCAAsC;AAC/C,eAAO,mBACJ,IAAI,KAAK,IAAI,kCAAkC,CAAC,EAChD;AAAA,UACC,SACE,sBACqB;AACrB,mBAAO,sCAAsC,oBAAoB,EAC9D,KAAK,aAAa,EAClB,MAAM,SAAC,GAA+B;AAIrC,kBACE,CAAC,gBAAgB,MAAM,UAAU,KACjC,CAAC,gBAAgB,MAAM,SAAS,GAChC;AACA,sBAAM;AAAA,cACR;AACA,qBAAO;AAAA,YACT,CAAC;AAAA,UACL;AAAA,QACF;AAAA,MACJ;AACA,aAAO;AAAA,IACT,CAAC,EAAE,KAAK,SAAC,SAAqB;AAC5B,UAAI,SAAS;AACX,eAAO;AAAA,MACT;AAEA,UAAI,eAAe,gBAAgB,MAAM,UAAU;AACnD,UAAI,mBAAmB,KAAK,CAAC,cAAc;AACzC;AAAA,UACE;AAAA,QAIF;AAAA,MACF;AAEA,aAAO,aAAa,KAAK,CAAC,CAAC,EAAE,KAAK,SAAC,YAA2B;AAC5D,YAAI,CAAC,cAAc,UAAU,GAAG;AAC9B,gBAAM,IAAI,MAAM,gBAAgB;AAAA,QAClC;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAAA,EACH;AAEO,WAAS,OAAgC;AAC9C,WAAO,mBAAmB,WAAM;AAE9B,UAAI,gCAAgC;AAClC,iBAAS,qCAAqC;AAC9C,eAAO,+BAA+B;AAAA,MACxC;AAGA,aAAO,SAAS,EAAE,KAAK,SAAC,MAAiB;AACvC,eAAO,CAAC,oBAAoB,IAAI,CAAC;AAAA,MACnC,CAAC;AAAA,IACH,CAAC;AAAA,EACH;;;ACzFA,MAAI,CAAC,UAAU,WAAW;AACxB,IAAC,UAAkB,YAAY,CAAC;AAAA,EAClC;AAGA,YAAU,UAAU,OAAO;AAC3B,YAAU,UAAU,WAAW;AAC/B,YAAU,UAAU,QAAQ;AAC5B,YAAU,UAAU,YAAY;AAEhC,SAAO,gBAAgB;", "names": ["_a", "i", "_a", "type"]}