'use strict';

var vue = require('vue');
require('../utils/index.js');
var util = require('../utils/util.js');
var eventHelper = require('../utils/eventHelper.js');

const provideKey = "parentInstance";
const useRegister = (_init, params) => {
  let componentInstance = vue.getCurrentInstance();
  let { props, attrs } = componentInstance;
  let parentInstance = vue.inject(provideKey, void 0);
  const emits = params.emits;
  let isMounted = false;
  let $amapComponent;
  vue.onMounted(() => {
    if (parentInstance) {
      if (parentInstance.$amapComponent) {
        register();
      } else {
        parentInstance.addChildComponent(register);
      }
    } else if (params.isRoot) {
      register();
    }
  });
  vue.onBeforeUnmount(() => {
    if (!$amapComponent) {
      return;
    }
    unregisterEvents();
    stopWatchers();
    if (params.destroyComponent) {
      params.destroyComponent();
    } else {
      destroyComponent();
    }
    if (params.provideData) {
      params.provideData.isDestroy = true;
    }
    parentInstance = void 0;
    props = void 0;
    attrs = void 0;
    componentInstance = void 0;
    $amapComponent = void 0;
  });
  vue.onBeforeUpdate(() => {
    if (props.reEventWhenUpdate && isMounted && $amapComponent) {
      unregisterEvents();
    }
  });
  vue.onUpdated(() => {
    if (props.reEventWhenUpdate && isMounted && $amapComponent) {
      registerEvents();
    }
  });
  const register = () => {
    const options = convertProps();
    _init(options, parentInstance == null ? void 0 : parentInstance.$amapComponent).then((mapInstance) => {
      $amapComponent = mapInstance;
      registerEvents();
      initProps();
      setPropWatchers();
      Object.assign(componentInstance.ctx, componentInstance.exposed);
      emits("init", $amapComponent, componentInstance.ctx);
      vue.nextTick(() => {
        createChildren();
      }).then();
      isMounted = true;
    });
  };
  const initProps = () => {
    const propsList = ["editable", "visible", "zooms"];
    propsList.forEach((propStr) => {
      if (props[propStr] !== void 0) {
        const handleFun = getHandlerFun(propStr);
        handleFun && handleFun.call($amapComponent, convertProxyToRaw(convertSignalProp(propStr, props[propStr])));
      }
    });
  };
  const propsRedirect = params.propsRedirect || {};
  const convertProps = () => {
    const propsCache = {};
    if (props.extraOptions) {
      Object.assign(propsCache, props.extraOptions);
    }
    Object.keys(props).forEach((_key) => {
      let key = _key;
      const propsValue = convertSignalProp(key, props[key]);
      if (propsValue !== void 0) {
        if (propsRedirect && propsRedirect[_key]) {
          key = propsRedirect[key];
        }
        propsCache[key] = propsValue;
      }
    });
    return propsCache;
  };
  const converters = params.converts || {};
  const convertSignalProp = (key, sourceData) => {
    if (converters && converters[key]) {
      return converters[key].call(void 0, sourceData);
    }
    return sourceData;
  };
  const convertProxyToRaw = (value) => {
    if (vue.isProxy(value)) {
      return vue.toRaw(value);
    }
    return vue.unref(value);
  };
  let unwatchFns = [];
  let watchRedirectFn = Object.assign({
    __visible: (flag) => {
      if (!!$amapComponent && !!$amapComponent["show"] && !!$amapComponent["hide"]) {
        !flag ? $amapComponent["hide"]() : $amapComponent["show"]();
      }
    },
    __zIndex(value) {
      if ($amapComponent && $amapComponent["setzIndex"]) {
        $amapComponent["setzIndex"](value);
      }
    }
  }, params.watchRedirectFn || {});
  const setPropWatchers = () => {
    Object.keys(props).forEach((prop) => {
      let handleProp = prop;
      if (propsRedirect && propsRedirect[prop])
        handleProp = propsRedirect[prop];
      const handleFun = getHandlerFun(handleProp);
      if (!handleFun)
        return;
      const watchOptions = {
        deep: false
      };
      const propValueType = Object.prototype.toString.call(props[prop]);
      if (propValueType === "[object Object]" || propValueType === "[object Array]") {
        watchOptions.deep = true;
      }
      const unwatch = vue.watch(() => props[prop], (nv) => {
        handleFun.call($amapComponent, convertProxyToRaw(convertSignalProp(prop, nv)));
      }, watchOptions);
      unwatchFns.push(unwatch);
    });
  };
  const stopWatchers = () => {
    unwatchFns.forEach((fn) => fn());
    unwatchFns = [];
    watchRedirectFn = void 0;
  };
  const getHandlerFun = (prop) => {
    if (watchRedirectFn[`__${prop}`]) {
      return watchRedirectFn[`__${prop}`];
    }
    if (!$amapComponent) {
      return null;
    }
    return $amapComponent[`set${util.upperCamelCase(prop)}`];
  };
  const cacheEvents = {};
  const registerEvents = () => {
    Object.keys(attrs).forEach((key) => {
      if (util.eventReg.test(key)) {
        const eventKey = util.convertEventToLowerCase(key);
        eventHelper.bindInstanceEvent($amapComponent, eventKey, attrs[key]);
        cacheEvents[eventKey] = attrs[key];
      }
    });
  };
  const unregisterEvents = () => {
    Object.keys(cacheEvents).forEach((eventKey) => {
      eventHelper.removeInstanceEvent($amapComponent, eventKey, cacheEvents[eventKey]);
      delete cacheEvents[eventKey];
    });
  };
  const createChildren = () => {
    const needInitComponents = params.needInitComponents || [];
    while (needInitComponents.length > 0) {
      needInitComponents[0]();
      needInitComponents.splice(0, 1);
    }
  };
  const destroyComponent = () => {
    if (!$amapComponent) {
      return;
    }
    $amapComponent.setMap && $amapComponent.setMap(null);
    $amapComponent.close && $amapComponent.close();
    $amapComponent.editor && $amapComponent.editor.close();
  };
  function $$getInstance() {
    return $amapComponent;
  }
  return {
    $$getInstance,
    parentInstance,
    isMounted
  };
};

exports.provideKey = provideKey;
exports.useRegister = useRegister;
//# sourceMappingURL=useRegister.js.map
