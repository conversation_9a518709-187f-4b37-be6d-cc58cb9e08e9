{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/collapse/index.ts"], "sourcesContent": ["import { withInstall, with<PERSON><PERSON>Install } from '@element-plus/utils'\n\nimport Collapse from './src/collapse.vue'\nimport CollapseItem from './src/collapse-item.vue'\n\nexport const ElCollapse = withInstall(Collapse, {\n  CollapseItem,\n})\nexport default ElCollapse\nexport const ElCollapseItem = withNoopInstall(CollapseItem)\n\nexport * from './src/collapse'\nexport * from './src/collapse-item'\nexport * from './src/constants'\nexport type { CollapseInstance, CollapseItemInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;;;;;AAGY,MAAC,UAAU,GAAG,WAAW,CAAC,QAAQ,EAAE;AAChD,EAAE,YAAY;AACd,CAAC,EAAE;AAES,MAAC,cAAc,GAAG,eAAe,CAAC,YAAY;;;;"}