/// <reference types="@vuemap/amap-jsapi-types" />
import HawkEye from './HawkEye.vue';
import type { Plugin } from "vue";
export declare const ElAmapControlHawkEye: {
    new (...args: any[]): import("vue").CreateComponentPublicInstance<Readonly<import("vue").ExtractPropTypes<{
        autoMove: {
            type: BooleanConstructor;
            default: boolean;
        };
        showRectangle: {
            type: BooleanConstructor;
            default: boolean;
        };
        showButton: {
            type: BooleanConstructor;
            default: boolean;
        };
        isOpen: {
            type: BooleanConstructor;
            default: boolean;
        };
        mapStyle: {
            type: StringConstructor;
        };
        layers: {
            type: ArrayConstructor;
        };
        width: {
            type: StringConstructor;
        };
        height: {
            type: StringConstructor;
        };
        offset: {
            type: ArrayConstructor;
        };
        borderStyle: {
            type: StringConstructor;
        };
        borderColor: {
            type: StringConstructor;
        };
        borderRadius: {
            type: StringConstructor;
        };
        borderWidth: {
            type: StringConstructor;
        };
        buttonSize: {
            type: StringConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {
        emits: (event: "init", ...args: any[]) => void;
        $amapComponent: AMap.HawkEye;
        $$getInstance: () => AMap.HawkEye;
        parentInstance: import("../../..").IProvideType | undefined;
    }, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Readonly<import("vue").ExtractPropTypes<{
        autoMove: {
            type: BooleanConstructor;
            default: boolean;
        };
        showRectangle: {
            type: BooleanConstructor;
            default: boolean;
        };
        showButton: {
            type: BooleanConstructor;
            default: boolean;
        };
        isOpen: {
            type: BooleanConstructor;
            default: boolean;
        };
        mapStyle: {
            type: StringConstructor;
        };
        layers: {
            type: ArrayConstructor;
        };
        width: {
            type: StringConstructor;
        };
        height: {
            type: StringConstructor;
        };
        offset: {
            type: ArrayConstructor;
        };
        borderStyle: {
            type: StringConstructor;
        };
        borderColor: {
            type: StringConstructor;
        };
        borderRadius: {
            type: StringConstructor;
        };
        borderWidth: {
            type: StringConstructor;
        };
        buttonSize: {
            type: StringConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {
        autoMove: boolean;
        showRectangle: boolean;
        showButton: boolean;
        isOpen: boolean;
    }, true, {}, {}, {
        P: {};
        B: {};
        D: {};
        C: {};
        M: {};
        Defaults: {};
    }, Readonly<import("vue").ExtractPropTypes<{
        autoMove: {
            type: BooleanConstructor;
            default: boolean;
        };
        showRectangle: {
            type: BooleanConstructor;
            default: boolean;
        };
        showButton: {
            type: BooleanConstructor;
            default: boolean;
        };
        isOpen: {
            type: BooleanConstructor;
            default: boolean;
        };
        mapStyle: {
            type: StringConstructor;
        };
        layers: {
            type: ArrayConstructor;
        };
        width: {
            type: StringConstructor;
        };
        height: {
            type: StringConstructor;
        };
        offset: {
            type: ArrayConstructor;
        };
        borderStyle: {
            type: StringConstructor;
        };
        borderColor: {
            type: StringConstructor;
        };
        borderRadius: {
            type: StringConstructor;
        };
        borderWidth: {
            type: StringConstructor;
        };
        buttonSize: {
            type: StringConstructor;
        };
    } & {
        visible: import("../../..").IPropOptions<boolean>;
        zIndex: import("../../..").IPropOptions<number>;
        reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
        extraOptions: import("../../..").IPropOptions<any>;
    }>> & {
        onInit?: ((...args: any[]) => any) | undefined;
    }, {
        emits: (event: "init", ...args: any[]) => void;
        $amapComponent: AMap.HawkEye;
        $$getInstance: () => AMap.HawkEye;
        parentInstance: import("../../..").IProvideType | undefined;
    }, {}, {}, {}, {
        autoMove: boolean;
        showRectangle: boolean;
        showButton: boolean;
        isOpen: boolean;
    }>;
    __isFragment?: undefined;
    __isTeleport?: undefined;
    __isSuspense?: undefined;
} & import("vue").ComponentOptionsBase<Readonly<import("vue").ExtractPropTypes<{
    autoMove: {
        type: BooleanConstructor;
        default: boolean;
    };
    showRectangle: {
        type: BooleanConstructor;
        default: boolean;
    };
    showButton: {
        type: BooleanConstructor;
        default: boolean;
    };
    isOpen: {
        type: BooleanConstructor;
        default: boolean;
    };
    mapStyle: {
        type: StringConstructor;
    };
    layers: {
        type: ArrayConstructor;
    };
    width: {
        type: StringConstructor;
    };
    height: {
        type: StringConstructor;
    };
    offset: {
        type: ArrayConstructor;
    };
    borderStyle: {
        type: StringConstructor;
    };
    borderColor: {
        type: StringConstructor;
    };
    borderRadius: {
        type: StringConstructor;
    };
    borderWidth: {
        type: StringConstructor;
    };
    buttonSize: {
        type: StringConstructor;
    };
} & {
    visible: import("../../..").IPropOptions<boolean>;
    zIndex: import("../../..").IPropOptions<number>;
    reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
    extraOptions: import("../../..").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
}, {
    emits: (event: "init", ...args: any[]) => void;
    $amapComponent: AMap.HawkEye;
    $$getInstance: () => AMap.HawkEye;
    parentInstance: import("../../..").IProvideType | undefined;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], "init", {
    autoMove: boolean;
    showRectangle: boolean;
    showButton: boolean;
    isOpen: boolean;
}, {}, string, {}> & import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps & Plugin<any[]>;
export default ElAmapControlHawkEye;
export declare type ElAmapControlHawkEyeInstance = InstanceType<typeof HawkEye>;
