{"version": 3, "file": "collapse-item.mjs", "sources": ["../../../../../../packages/components/collapse/src/collapse-item.ts"], "sourcesContent": ["import { buildProps, definePropType, generateId } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type { CollapseActiveName } from './collapse'\n\nexport const collapseItemProps = buildProps({\n  title: {\n    type: String,\n    default: '',\n  },\n  name: {\n    type: definePropType<CollapseActiveName>([String, Number]),\n    default: () => generateId(),\n  },\n  disabled: Boolean,\n} as const)\nexport type CollapseItemProps = ExtractPropTypes<typeof collapseItemProps>\n"], "names": [], "mappings": ";;;;AACY,MAAC,iBAAiB,GAAG,UAAU,CAAC;AAC5C,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,EAAE;AACf,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;AAC1C,IAAI,OAAO,EAAE,MAAM,UAAU,EAAE;AAC/B,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,CAAC;;;;"}