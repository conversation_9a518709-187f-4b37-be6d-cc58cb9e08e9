'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('../../../utils/index.js');
var runtime = require('../../../utils/vue/props/runtime.js');

const descriptionsRowProps = runtime.buildProps({
  row: {
    type: runtime.definePropType(Array),
    default: () => []
  }
});

exports.descriptionsRowProps = descriptionsRowProps;
//# sourceMappingURL=descriptions-row.js.map
