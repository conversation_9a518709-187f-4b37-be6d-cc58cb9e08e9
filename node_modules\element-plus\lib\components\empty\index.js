'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('../../utils/index.js');
var empty$1 = require('./src/empty2.js');
var empty = require('./src/empty.js');
var install = require('../../utils/vue/install.js');

const ElEmpty = install.withInstall(empty$1["default"]);

exports.emptyProps = empty.emptyProps;
exports.ElEmpty = ElEmpty;
exports["default"] = ElEmpty;
//# sourceMappingURL=index.js.map
