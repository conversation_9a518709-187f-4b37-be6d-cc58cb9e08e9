{"el-amap": {"attributes": ["view-mode", "show-label", "default-cursor", "is-hotspot", "wall-color", "roof-color", "show-building-block", "sky-color", "web-g-l-params", "touch-zoom", "touch-zoom-center", "show-label", "center", "zoom", "zooms", "rotation", "pitch", "features", "layers", "resize-enable", "drag-enable", "zoom-enable", "jog-enable", "pitch-enable", "rotate-enable", "animate-enable", "keyboard-enable", "double-click-zoom", "scroll-wheel", "show-indoor-map", "map-style", "label-reject-mask", "mask", "terrain", "extra-options", "resize", "complete", "click", "dblclick", "mapmove", "hotspotclick", "hotspotover", "hotspotout", "movestart", "moveend", "zoomchange", "zoomstart", "zoomend", "mousemove", "mousewheel", "mouseover", "mouseout", "mouseup", "mousedown", "rightclick", "dragstart", "dragging", "dragend", "touchstart", "touchmove", "touchend"], "description": "地图容器\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#amap)"}, "el-amap-control-control-bar": {"attributes": ["position", "offset", "show-control-button", "visible", "re-event-when-update", "extra-options", "show", "hide"], "description": "组合了旋转、倾斜、复位在内的地图控件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#amapcontrolcontrolbar)"}, "el-amap-control-geolocation": {"attributes": ["position", "offset", "border-color", "border-radius", "button-size", "convert", "enable-high-accuracy", "timeout", "maximum-age", "show-button", "show-circle", "show-marker", "marker-options", "circle-options", "pan-to-location", "zoom-to-accuracy", "geo-location-first", "no-ip-locate", "no-geo-location", "use-native", "get-city-when-fail", "need-address", "extensions", "re-event-when-update", "extra-options", "complete"], "description": "AMap.Geolocation 定位服务插件。融合了浏览器定位、高精度IP定位、安卓定位sdk辅助定位等多种手段，提供了获取当前准确位置、获取当前城市信息、持续定位\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#amapcontrolgeolocation)"}, "el-amap-control-hawk-eye": {"attributes": ["auto-move", "show-rectangle", "show-button", "map-style", "layers", "width", "height", "offset", "border-style", "border-color", "border-radius", "border-width", "button-size", "visible", "is-open", "re-event-when-update", "extra-options", "show", "hide"], "description": "鹰眼控件，用于显示缩略地图，显示于地图右下角，可以随主图的视口变化而变化，也可以配置成固定位置实现类似于南海附图的效果。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#amapcontrolhawkeye)"}, "el-amap-control-map-type": {"attributes": ["default-type", "show-traffic", "show-road", "visible", "re-event-when-update", "extra-options", "show", "hide"], "description": "地图类型切换插件。用户通过该插件进行地图切换。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#amapcontrolmaptype)"}, "el-amap-control-scale": {"attributes": ["position", "offset", "animate-enable", "visible", "re-event-when-update", "extra-options", "show", "hide"], "description": "位于地图右下角，用户可控制其显示与隐藏。继承自 AMap.Control\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#amapcontrolscale)"}, "el-amap-search-box": {"attributes": ["datatype", "input", "output", "out-put-dir-auto", "close-result-on-scroll", "lang", "visible", "type", "city", "citylimit", "input-custom", "re-event-when-update", "placeholder", "debounce", "extra-options", "select", "choose"], "description": "用于进行 POI 搜索联想与数据查询的相关类型\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#amapsearchbox)"}, "el-amap-control-tool-bar": {"attributes": ["position", "offset", "visible", "re-event-when-update", "extra-options", "show", "hide"], "description": "地图操作工具条插件。可支持方向导航、位置定位、视野级别缩放、视野级别选择等操作。继承自 AMap.Control\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#amapcontroltoolbar)"}, "el-amap-info-window": {"attributes": ["is-custom", "auto-move", "avoid", "close-when-click-map", "offset", "visible", "content", "size", "anchor", "position", "re-event-when-update", "extra-options", "init", "open", "close"], "description": "用于在地图上展示复杂的说明性信息的类型。<br/>信息窗体，地图仅可同时展示一个信息窗体，推荐为信息窗体通过样式显示设置尺寸\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#amapinfowindow)"}, "el-amap-circle-marker": {"attributes": ["bubble", "center", "radius", "cursor", "visible", "z-index", "stroke-color", "stroke-opacity", "stroke-weight", "fill-color", "fill-opacity", "draggable", "ext-data", "re-event-when-update", "extra-options", "init", "click", "dblclick", "rightclick", "hide", "show", "mousedown", "mouseup", "mouseover", "mouseout", "touchstart", "touchmove", "touchend"], "description": "构造圆形对象，通过CircleOptions指定多边形样式\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#amapcirclemarker)"}, "el-amap-elastic-marker": {"attributes": ["top-when-click", "bubble", "zooms", "visible", "z-index", "position", "offset", "draggable", "cursor", "title", "clickable", "zoom-style-mapping", "styles", "ext-data", "re-event-when-update", "extra-options", "init", "click", "dblclick", "rightclick", "mousemove", "mouseover", "mouseout", "mousedown", "mouseup", "dragstart", "dragging", "dragend", "touchstart", "touchmove", "touchend"], "description": "灵活点标记\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#amapelasticmarker)"}, "el-amap-label-marker": {"attributes": ["name", "rank", "visible", "z-index", "position", "zooms", "icon", "text", "ext-data", "re-event-when-update", "extra-options", "rotation", "init", "click", "mousemove", "mouseover", "mouseout", "mousedown", "mouseup", "touchstart", "touchmove", "touchend"], "description": "标注类\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#amaplabelmarker)"}, "el-amap-marker-cluster": {"attributes": ["render-cluster-marker", "render-marker", "cluster-by-zoom-change", "points", "grid-size", "max-zoom", "average-center", "styles", "re-event-when-update", "extra-options", "init", "click"], "description": "用于展示大量点标记，将点标记按照距离进行聚合，以提高绘制性能。点聚合支持用户自定义样式，以插件形式调用。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#amapmarkercluster)"}, "el-amap-marker": {"attributes": ["top-when-click", "bubble", "zooms", "anchor", "visible", "z-index", "position", "offset", "icon", "content", "draggable", "cursor", "angle", "title", "clickable", "label", "ext-data", "re-event-when-update", "extra-options", "move-options", "init", "click", "dblclick", "rightclick", "mousemove", "mouseover", "mouseout", "mousedown", "mouseup", "dragstart", "dragging", "dragend", "moving", "moveend", "movealong", "touchstart", "touchmove", "touchend"], "description": "[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#amapmarker)"}, "el-amap-mass-marks": {"attributes": ["data", "visible", "z-index", "zooms", "cursor", "styles", "re-event-when-update", "extra-options", "init", "complete", "click", "dblclick", "mousemove", "mouseover", "mouseout", "mousedown", "mouseup", "touchstart", "touchend"], "description": "海量点类\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#amapmassmarks)"}, "el-amap-text": {"attributes": ["top-when-click", "bubble", "zooms", "visible", "z-index", "position", "offset", "text", "draggable", "cursor", "angle", "title", "clickable", "text-style", "ext-data", "re-event-when-update", "extra-options", "init", "click", "dblclick", "rightclick", "mousemove", "mouseover", "mouseout", "mousedown", "mouseup", "dragstart", "dragging", "dragend", "moving", "moveend", "movealong", "touchstart", "touchmove", "touchend"], "description": "文本标记\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#amaptext)"}, "el-amap-mouse-tool": {"attributes": ["type", "draw-options", "auto-clear", "show-tooltip", "tooltip-text-map", "text-options", "extra-options", "init", "draw"], "description": "鼠标工具插件。通过该插件，可进行鼠标画标记点、线、多边形、矩形、圆、距离量测、面积量测、拉框放大、拉框缩小等功能\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#amapmousetool)"}, "el-amap-bezier-curve": {"attributes": ["bubble", "edit-options", "visible", "editable", "path", "z-index", "stroke-color", "stroke-opacity", "stroke-weight", "border-weight", "is-outline", "outline-color", "draggable", "stroke-style", "stroke-dasharray", "line-join", "line-cap", "geodesic", "show-dir", "ext-data", "re-event-when-update", "extra-options", "init", "click", "dblclick", "rightclick", "hide", "show", "mousedown", "mouseup", "mouseover", "mouseout", "touchstart", "touchmove", "touchend", "addnode", "removenode", "adjust", "add", "end"], "description": "贝塞尔曲线\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#amapbeziercurve)"}, "el-amap-circle": {"attributes": ["bubble", "edit-options", "center", "radius", "visible", "editable", "z-index", "stroke-color", "stroke-opacity", "stroke-weight", "fill-color", "fill-opacity", "ext-data", "stroke-style", "stroke-dasharray", "draggable", "re-event-when-update", "extra-options", "init", "click", "dblclick", "rightclick", "hide", "show", "mousedown", "mouseup", "mouseover", "mouseout", "touchstart", "touchmove", "touchend", "addnode", "removenode", "move", "adjust", "add", "end"], "description": "构造圆形对象，通过CircleOptions指定多边形样式\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#amapcircle)"}, "el-amap-ellipse": {"attributes": ["bubble", "edit-options", "center", "radius", "visible", "editable", "z-index", "stroke-color", "stroke-opacity", "stroke-weight", "fill-color", "fill-opacity", "ext-data", "stroke-style", "stroke-dasharray", "draggable", "re-event-when-update", "extra-options", "init", "click", "dblclick", "rightclick", "hide", "show", "mousedown", "mouseup", "mouseover", "mouseout", "touchstart", "touchmove", "touchend", "addnode", "removenode", "move", "adjust", "add", "end"], "description": "构造椭圆对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#amapellipse)"}, "el-amap-geojson": {"attributes": ["marker-options", "get-marker", "polyline-options", "get-polyline", "polygon-options", "get-polygon", "geo", "visible", "re-event-when-update", "extra-options", "init", "click", "dblclick", "rightclick", "mousedown", "mouseup", "mouseover", "mouseout", "touchstart", "touchmove", "touchend"], "description": "GeoJSON类，继承自OverLayGroup，可实现GeoJSON对象与OverlayGroup的相互转换\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#amapgeojson)"}, "el-amap-polygon": {"attributes": ["bubble", "edit-options", "path", "visible", "editable", "z-index", "stroke-color", "stroke-opacity", "stroke-weight", "fill-color", "fill-opacity", "ext-data", "stroke-style", "stroke-dasharray", "draggable", "re-event-when-update", "extra-options", "init", "click", "dblclick", "rightclick", "hide", "show", "mousedown", "mouseup", "mouseover", "mouseout", "touchstart", "touchmove", "touchend", "addnode", "removenode", "move", "adjust", "add", "end"], "description": "构造多边形对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#amappolygon)"}, "el-amap-polyline": {"attributes": ["bubble", "edit-options", "visible", "editable", "path", "z-index", "stroke-color", "stroke-opacity", "stroke-weight", "border-weight", "is-outline", "outline-color", "draggable", "stroke-style", "stroke-dasharray", "line-join", "line-cap", "geodesic", "show-dir", "ext-data", "re-event-when-update", "extra-options", "init", "click", "dblclick", "rightclick", "hide", "show", "mousedown", "mouseup", "mouseover", "mouseout", "touchstart", "touchmove", "touchend", "addnode", "removenode", "adjust", "add", "end"], "description": "构造折线对象，支持 lineString 和 MultiLineString\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#amappolyline)"}, "el-amap-rectangle": {"attributes": ["bubble", "edit-options", "bounds", "visible", "editable", "z-index", "stroke-color", "stroke-opacity", "stroke-weight", "fill-color", "fill-opacity", "ext-data", "stroke-style", "stroke-dasharray", "draggable", "re-event-when-update", "extra-options", "init", "click", "dblclick", "rightclick", "hide", "show", "mousedown", "mouseup", "mouseover", "mouseout", "touchstart", "touchmove", "touchend", "addnode", "removenode", "move", "adjust", "add", "end"], "description": "构造矩形对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#amaprectangle)"}, "el-amap-layer-canvas": {"attributes": ["canvas", "zooms", "bounds", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init", "complete"], "description": "Canvas图层类，用户可以将一个 Canvas 作为图层添加在地图上，Canvas图层会随缩放级别而自适应缩放。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#amaplayercanvas)"}, "el-amap-layer-custom-xyz": {"attributes": ["url", "subdomains", "tile-type", "proj", "zooms", "opacity", "visible", "z-index", "debug", "mask", "cache-size", "re-event-when-update", "extra-options", "tile-max-zoom", "altitude", "init"], "description": "自定义瓦片纠偏图层是基于GLCustomLayer实现的瓦片加载图层，支持瓦片坐标系纠偏，支持`wgs84` `gcj02` `bd09`三种坐标系。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#amaplayercustomxyz)"}, "el-amap-layer-custom": {"attributes": ["canvas", "render", "always-render", "zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init"], "description": "自定义图层是一种完全由开发者来指定绘制方法的图层\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#amaplayercustom)"}, "el-amap-layer-district-cluster": {"attributes": ["z-index", "visible", "data", "get-position", "auto-set-fit-view", "top-adcodes", "excluded-adcodes", "render-options", "extra-options", "init", "featureClick", "featureMouseover", "featureMouseout", "clusterMarkerClick"], "description": "高德地图的区划聚合图层，图层基于AMapUI的区划插件改造实现\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#amaplayerdistrictcluster)"}, "el-amap-layer-flexible": {"attributes": ["cache-size", "create-tile", "tile-size", "zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init", "complete"], "description": "灵活切片图层，继承自AMap.TileLayer，开发者可通过构造时传入给其传入createTile字段来指定每一个切片的内容<br/>\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#amaplayerflexible)"}, "el-amap-layer-gl-custom": {"attributes": ["init", "render", "zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init"], "description": "3d 自定义图层\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#amaplayerglcustom)"}, "el-amap-layer-heat-map": {"attributes": ["radius", "gradient", "config", "zooms", "visible", "z-index", "opacity", "data-set", "re-event-when-update", "extra-options", "init"], "description": "热力图，基于第三方heatmap.js实现，以特殊高亮的形式显示数据密集程度。根据密集程度的不同，图上会呈现不同的颜色，以直观的形式展现数据密度。API引用了heatmap.js最新版本v2.0，v2.0基于新的渲染模型，具有更高的渲染效率和更强的性能。支持chrome、firefox、safari、ie9及以上浏览器。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#amaplayerheatmap)"}, "el-amap-layer-image": {"attributes": ["url", "zooms", "bounds", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init", "complete"], "description": "图片图层类，用户可以将一张静态图片作为图层添加在地图上，图片图层会随缩放级别而自适应缩放。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#amaplayerimage)"}, "el-amap-layer-labels": {"attributes": ["zooms", "visible", "z-index", "opacity", "collision", "allow-collision", "re-event-when-update", "extra-options", "init"], "description": "标注层。<br/>\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#amaplayerlabels)"}, "el-amap-layer-tiles3d": {"attributes": ["url", "three-script-url", "three-gltf-loader", "layer-style", "re-event-when-update", "extra-options", "init"], "description": "使用 AMap.3DTilesLayer 图层加载渲染标准 3D Tiles 数据，可支持 i3dm、b3dm、pnts 格式\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#amaplayertiles3d)"}, "el-amap-layer-vector": {"attributes": ["visible", "z-index", "re-event-when-update", "extra-options", "init"], "description": "矢量标记图层。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#amaplayervector)"}, "el-amap-layer-video": {"attributes": ["url", "zooms", "bounds", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init"], "description": "Video图层类，用户可以将一个 Video 作为图层添加在地图上，Video图层会随缩放级别而自适应缩放，Video图层基于Canvas实现。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#amaplayervideo)"}, "el-amap-layer-buildings": {"attributes": ["wall-color", "roof-color", "height-factor", "style-opts", "zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init", "complete"], "description": "建筑楼块 3D 图层。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#amaplayerbuildings)"}, "el-amap-layer-default": {"attributes": ["zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init"], "description": "[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#amaplayerdefault)"}, "el-amap-layer-district": {"attributes": ["type", "depth", "adcode", "s-o-c", "zooms", "visible", "z-index", "opacity", "styles", "re-event-when-update", "extra-options", "init"], "description": "简易行政区划图。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#amaplayerdistrict)"}, "el-amap-layer-indoor-map": {"attributes": ["cursor", "visible", "z-index", "opacity", "hide-floor-bar", "re-event-when-update", "extra-options", "init"], "description": "室内图层，用于在适当级别展示室内地图，并提供显示商铺tip、切换楼层等功能。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#amaplayerindoormap)"}, "el-amap-layer-road-net": {"attributes": ["tile-size", "zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init", "complete"], "description": "路网图层，展示道路信息。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#amaplayerroadnet)"}, "el-amap-layer-satellite": {"attributes": ["tile-size", "zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init", "complete"], "description": "卫星图层类，继承自TileLayer。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#amaplayersatellite)"}, "el-amap-layer-tile": {"attributes": ["data-zooms", "tile-size", "tile-url", "zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init", "complete"], "description": "切片图层类，该类为基础类。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#amaplayertile)"}, "el-amap-layer-traffic": {"attributes": ["auto-refresh", "tile-size", "interval", "zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init", "complete"], "description": "实时交通图层类，继承自TileLayer。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#amaplayertraffic)"}, "el-amap-layer-mapbox-vector-tile": {"attributes": ["url", "data-zooms", "zooms", "visible", "z-index", "opacity", "styles", "re-event-when-update", "extra-options", "init"], "description": "为了满足基于矢量瓦片块的数据可视化、矢量瓦片边界展示等开发需求，通过 AMap.MapboxVectorTileLayer 插件提供了简易矢量瓦片图层\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#amaplayermapboxvectortile)"}, "el-amap-layer-wms": {"attributes": ["blend", "url", "params", "zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init", "complete"], "description": "用于加载OGC标准的WMS地图服务的一种图层类，仅支持EPSG3857坐标系统的WMS图层。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#amaplayerwms)"}, "el-amap-layer-wmts": {"attributes": ["blend", "url", "params", "zooms", "visible", "z-index", "opacity", "re-event-when-update", "extra-options", "init"], "description": "用于加载OGC标准的WMS地图服务的一种图层类，仅支持EPSG3857坐标系统的WMTS图层。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#amaplayerwmts)"}}