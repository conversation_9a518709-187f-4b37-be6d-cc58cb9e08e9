{"version": 3, "sources": ["../../.pnpm/custom-vue-scrollbar@0.0.8__198506a13cb6d6b89d6c0501db2feac6/node_modules/custom-vue-scrollbar/dist/index.js"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nimport { defineComponent, useCssVars, unref, computed, shallowRef, onUnmounted, watch, openBlock, createElementBlock, Fragment, withDirectives, createElementVNode, normalizeClass, normalizeStyle, withModifiers, setBlockTracking, vShow, createCommentVNode, reactive, watchEffect, onMounted, mergeProps, renderSlot, renderList, createVNode } from \"vue\";\nimport { throttle, debounce, clamp } from \"lodash-es\";\nvar thumb_vue_vue_type_style_index_0_lang = \"\";\nconst _hoisted_1 = [\"onPointerdown\"];\nconst __default__$1 = {\n  name: \"ScrollbarThumb\"\n};\nfunction setup$1(__props, { expose }) {\n  const props = __props;\n  useCssVars((_ctx) => ({\n    \"1fb98f78\": unref(computedThumbWidth)\n  }));\n  const computedThumbWidth = computed(() => {\n    var _a;\n    return `${(_a = props.thumbWidth) != null ? _a : 12}px`;\n  });\n  let autoHideTimer = null;\n  let thumbEl = shallowRef(null);\n  const pointerDownInfo = { pageX: 0, pageY: 0, scrollPos: 0 };\n  let pointerId = null;\n  const startAutoHideTimer = () => {\n    autoHideTimer = setTimeout(() => {\n      if (thumbEl.value)\n        thumbEl.value.classList.remove(\"scrollbar__thumbPlaceholder--scrolling\");\n      autoHideTimer = null;\n    }, props.autoHideDelay);\n  };\n  const clearAutoHideTimer = () => {\n    if (autoHideTimer !== null)\n      clearTimeout(autoHideTimer);\n  };\n  onUnmounted(clearAutoHideTimer);\n  const handlePointerMove = throttle((evt) => {\n    evt.stopPropagation();\n    evt.preventDefault();\n    const moveDirection = props.type === \"horizontal\" ? \"pageX\" : \"pageY\";\n    const moveDistance = (evt[moveDirection] - pointerDownInfo[moveDirection]) / props.scrollInfo.wrapperMainSize * props.scrollInfo.contentMainSize;\n    props.wrapperEl.scrollTo({ [props.type === \"horizontal\" ? \"left\" : \"top\"]: pointerDownInfo.scrollPos + moveDistance, behavior: \"auto\" });\n  }, 16);\n  const handlePointerEnd = () => {\n    startAutoHideTimer();\n    thumbEl.value.removeEventListener(\"pointermove\", handlePointerMove);\n    thumbEl.value.removeEventListener(\"pointerup\", handlePointerEnd);\n    thumbEl.value.removeEventListener(\"pointercancel\", handlePointerEnd);\n    thumbEl.value.removeEventListener(\"mousewheel\", handlePointerEnd);\n    document.removeEventListener(\"mousewheel\", handlePointerEnd);\n    if (typeof pointerId === \"number\")\n      thumbEl.value.releasePointerCapture(pointerId);\n    pointerId = null;\n  };\n  const handlePointerDown = (evt) => {\n    if (evt.ctrlKey || evt.button !== 0)\n      return;\n    clearAutoHideTimer();\n    pointerDownInfo.pageX = evt.pageX;\n    pointerDownInfo.pageY = evt.pageY;\n    pointerDownInfo.scrollPos = props.wrapperEl[props.type === \"horizontal\" ? \"scrollLeft\" : \"scrollTop\"];\n    pointerId = evt == null ? void 0 : evt.pointerId;\n    thumbEl.value.setPointerCapture(pointerId);\n    thumbEl.value.addEventListener(\"pointermove\", handlePointerMove);\n    thumbEl.value.addEventListener(\"pointerup\", handlePointerEnd);\n    thumbEl.value.addEventListener(\"pointercancel\", handlePointerEnd);\n    thumbEl.value.addEventListener(\"mousewheel\", handlePointerEnd, { passive: false });\n    document.addEventListener(\"mousewheel\", handlePointerEnd, { passive: false });\n    thumbEl.value.classList.add(\"scrollbar__thumbPlaceholder--scrolling\");\n  };\n  const autoHideAfterScroll = () => {\n    clearAutoHideTimer();\n    if (thumbEl.value)\n      thumbEl.value.classList.add(\"scrollbar__thumbPlaceholder--scrolling\");\n    startAutoHideTimer();\n  };\n  expose({\n    autoHideAfterScroll\n  });\n  let isWrapperIntersecting = shallowRef(false);\n  let isShepherdIntersecting = shallowRef(false);\n  let shepherdEl = shallowRef(null);\n  let shepherdIO = null;\n  let wrapperIO = null;\n  let shouldFixed = computed(() => props.fixedThumb && !isShepherdIntersecting.value);\n  const shepherdIOCallback = ([entry]) => isShepherdIntersecting.value = entry.isIntersecting;\n  const wrapperIOCallback = ([entry]) => isWrapperIntersecting.value = entry.isIntersecting;\n  const clearIO = () => {\n    if (shepherdIO) {\n      shepherdIO.disconnect();\n      shepherdIO = null;\n    }\n    if (wrapperIO) {\n      wrapperIO.disconnect();\n      wrapperIO = null;\n    }\n  };\n  watch(() => [props.fixedThumb, props.wrapperEl], () => {\n    if (!props.fixedThumb || !props.wrapperEl)\n      return clearIO();\n    clearIO();\n    wrapperIO = new IntersectionObserver(wrapperIOCallback, { threshold: [0, 0.5] });\n    wrapperIO.observe(props.wrapperEl);\n    shepherdIO = new IntersectionObserver(shepherdIOCallback);\n    shepherdIO.observe(shepherdEl.value);\n  });\n  onUnmounted(clearIO);\n  return (_ctx, _cache) => {\n    return openBlock(), createElementBlock(Fragment, null, [\n      withDirectives(createElementVNode(\"div\", {\n        class: normalizeClass([\n          \"scrollbar__thumbPlaceholder\",\n          `scrollbar__thumbPlaceholder--${__props.type}`,\n          {\n            [\"scrollbar__thumbPlaceholder--autoHide\"]: __props.autoHide,\n            [\"scrollbar__thumbPlaceholder--autoExpand\"]: __props.autoExpand\n          }\n        ]),\n        style: normalizeStyle({\n          width: __props.type === \"horizontal\" ? `${__props.scrollInfo.thumbSize}px` : \"\",\n          height: __props.type === \"vertical\" ? `${__props.scrollInfo.thumbSize}px` : \"\",\n          position: !shouldFixed.value ? \"absolute\" : \"fixed\",\n          [__props.type === \"vertical\" ? \"top\" : \"left\"]: !shouldFixed.value ? \"3px\" : `${__props.scrollInfo.boundaryDistance + 3}px`\n        }),\n        ref: (_value, _refs) => {\n          _refs[\"thumbEl\"] = _value;\n          thumbEl.value = _value;\n        },\n        onPointerdown: withModifiers(handlePointerDown, [\"stop\"])\n      }, [\n        _cache[0] || (setBlockTracking(-1), _cache[0] = createElementVNode(\"div\", {\n          class: normalizeClass([\"scrollbar__thumb\", `scrollbar__thumb--${__props.type}`])\n        }, null, 2), setBlockTracking(1), _cache[0])\n      ], 46, _hoisted_1), [\n        [vShow, Boolean(__props.scrollInfo.thumbSize) && (__props.fixedThumb ? isWrapperIntersecting.value : true)]\n      ]),\n      props.fixedThumb ? withDirectives((openBlock(), createElementBlock(\"div\", {\n        key: 0,\n        ref: (_value, _refs) => {\n          _refs[\"shepherdEl\"] = _value;\n          shepherdEl.value = _value;\n        },\n        class: normalizeClass([\"scrollbar__shepherd\", `scrollbar__shepherd--${__props.type}`])\n      }, null, 2)), [\n        [vShow, Boolean(__props.scrollInfo.thumbSize)]\n      ]) : createCommentVNode(\"\", true)\n    ], 64);\n  };\n}\nconst _sfc_main$1 = /* @__PURE__ */ defineComponent(__spreadProps(__spreadValues({}, __default__$1), {\n  props: {\n    type: null,\n    autoExpand: null,\n    autoHide: null,\n    autoHideDelay: null,\n    fixedThumb: null,\n    scrollInfo: null,\n    thumbWidth: null,\n    wrapperEl: null\n  },\n  setup: setup$1\n}));\nconst defaultOption = {\n  wait: 333,\n  type: \"debounce\"\n};\nfunction useMeasure(...args) {\n  const hasParamRef = \"value\" in (args == null ? void 0 : args[0]);\n  let option;\n  if (hasParamRef)\n    option = args == null ? void 0 : args[1];\n  else\n    option = args == null ? void 0 : args[0];\n  const { wait, type, callback } = __spreadValues(__spreadValues({}, defaultOption), option);\n  const targetRef = hasParamRef ? args[0] : shallowRef(null);\n  const rect = reactive({ left: 0, top: 0, right: 0, bottom: 0, width: 0, height: 0, x: 0, y: 0 });\n  const observerFunc = () => {\n    const domRect = targetRef.value.getBoundingClientRect();\n    rect.left = domRect.left;\n    rect.top = domRect.top;\n    rect.right = domRect.right;\n    rect.bottom = domRect.bottom;\n    rect.width = domRect.width;\n    rect.height = domRect.height;\n    rect.x = domRect.x;\n    rect.y = domRect.y;\n    callback == null ? void 0 : callback();\n  };\n  let execFunc = null;\n  let ro = null;\n  const clearRo = () => {\n    if (execFunc)\n      window.removeEventListener(\"resize\", execFunc);\n    if (!ro)\n      return;\n    ro.disconnect();\n    ro = null;\n  };\n  watchEffect(() => {\n    if (!targetRef.value)\n      return;\n    clearRo();\n    execFunc = observerFunc;\n    if (type === \"throttle\" && wait >= 4)\n      execFunc = throttle(execFunc, wait);\n    else if (type === \"debounce\" && wait >= 4)\n      execFunc = debounce(execFunc, wait);\n    window.addEventListener(\"resize\", execFunc);\n    ro = new ResizeObserver(execFunc);\n    ro.observe(targetRef.value);\n  });\n  onUnmounted(clearRo);\n  if (hasParamRef)\n    return rect;\n  return [targetRef, rect];\n}\nvar index_vue_vue_type_style_index_0_lang = \"\";\nconst __default__ = {\n  name: \"CustomScrollbar\",\n  inheritAttrs: false\n};\nfunction setup(__props, { expose, emit }) {\n  const props = __props;\n  const thumbs = {\n    horizontal: { el: null, instance: null },\n    vertical: { el: null, instance: null }\n  };\n  const setThumbsInstance = (instance, direction) => {\n    if (!thumbs[direction].instance)\n      thumbs[direction].instance = instance;\n  };\n  let wrapperEl = shallowRef(null);\n  let contentEl = shallowRef(null);\n  onMounted(() => {\n    var _a;\n    const childNodes = Array.from((_a = wrapperEl.value.parentElement) == null ? void 0 : _a.childNodes);\n    for (const thumbType in thumbs) {\n      thumbs[thumbType].el = childNodes.find((ele) => {\n        var _a2;\n        return (_a2 = ele == null ? void 0 : ele.classList) == null ? void 0 : _a2.contains(`scrollbar__thumbPlaceholder--${thumbType}`);\n      });\n    }\n  });\n  const wrapperRect = useMeasure(wrapperEl, { wait: props.throttleWait, type: props.throttleType, callback: updateMaxScrollDistance });\n  const contentRect = useMeasure(contentEl, { wait: props.throttleWait, type: props.throttleType, callback: updateMaxScrollDistance });\n  let nativeMaxScrollTop = shallowRef(0);\n  let nativeMaxScrollLeft = shallowRef(0);\n  function updateMaxScrollDistance() {\n    nativeMaxScrollTop.value = Math.max(wrapperEl.value.scrollHeight - wrapperRect.height | 0, 0);\n    nativeMaxScrollLeft.value = Math.max(wrapperEl.value.scrollWidth - wrapperRect.width | 0, 0);\n  }\n  let scrollWidthInfo = computed(() => {\n    return {\n      thumbSize: nativeMaxScrollLeft.value ? clamp(wrapperRect.width / wrapperEl.value.scrollWidth * wrapperRect.width, props.thumbMinSize > wrapperRect.width ? 48 : props.thumbMinSize, props.thumbMaxSize) : 0,\n      contentMainSize: contentRect.width,\n      wrapperMainSize: wrapperRect.width,\n      boundaryDistance: Math.abs(wrapperRect.left)\n    };\n  });\n  let scrollHeightInfo = computed(() => {\n    return {\n      thumbSize: nativeMaxScrollTop.value ? clamp(wrapperRect.height / wrapperEl.value.scrollHeight * wrapperRect.height, props.thumbMinSize > wrapperRect.height ? 48 : props.thumbMinSize, props.thumbMaxSize) : 0,\n      contentMainSize: contentRect.height,\n      wrapperMainSize: wrapperRect.height,\n      boundaryDistance: Math.abs(wrapperRect.top)\n    };\n  });\n  let maxScrollTop = computed(() => wrapperRect.height - scrollHeightInfo.value.thumbSize - 5);\n  let maxScrollLeft = computed(() => wrapperRect.width - scrollWidthInfo.value.thumbSize - 5);\n  const handleNativeScroll = () => {\n    if (nativeMaxScrollLeft.value) {\n      thumbs.horizontal.el.style.transform = `translate3d(${wrapperEl.value.scrollLeft / nativeMaxScrollLeft.value * maxScrollLeft.value}px, 0, 0)`;\n      thumbs.horizontal.instance.autoHideAfterScroll();\n    }\n    if (nativeMaxScrollTop.value) {\n      thumbs.vertical.el.style.transform = `translate3d(0, ${wrapperEl.value.scrollTop / nativeMaxScrollTop.value * maxScrollTop.value}px, 0)`;\n      thumbs.vertical.instance.autoHideAfterScroll();\n    }\n  };\n  watch(() => [nativeMaxScrollLeft.value, nativeMaxScrollTop.value], handleNativeScroll);\n  const handleSimulateScroll = (evt) => {\n    evt.stopPropagation();\n    const preScrollLeft = wrapperEl.value.scrollLeft;\n    const preScrollTop = wrapperEl.value.scrollTop;\n    const newScrollLeft = clamp(preScrollLeft + ((evt == null ? void 0 : evt.deltaX) || 0), 0, nativeMaxScrollLeft.value) | 0;\n    const newScrollTop = clamp(preScrollTop + ((evt == null ? void 0 : evt.deltaY) || 0), 0, nativeMaxScrollTop.value) | 0;\n    wrapperEl.value.scrollLeft = newScrollLeft;\n    wrapperEl.value.scrollTop = newScrollTop;\n    if (nativeMaxScrollLeft.value) {\n      thumbs.horizontal.el.style.transform = `translate3d(${newScrollLeft / nativeMaxScrollLeft.value * maxScrollLeft.value}px, 0, 0)`;\n      thumbs.horizontal.instance.autoHideAfterScroll();\n    }\n    if (nativeMaxScrollTop.value) {\n      thumbs.vertical.el.style.transform = `translate3d(0, ${newScrollTop / nativeMaxScrollTop.value * maxScrollTop.value}px, 0)`;\n      thumbs.vertical.instance.autoHideAfterScroll();\n    }\n    emit(\"scroll\", { target: wrapperEl.value, scrollLeft: newScrollLeft, scrollTop: newScrollTop });\n  };\n  const handleScroll = (evt) => {\n    if (props.simulateScroll)\n      handleSimulateScroll(evt);\n    else\n      handleNativeScroll();\n  };\n  watch(wrapperRect, () => emit(\"wrapperResize\", wrapperRect));\n  watch(contentRect, () => emit(\"contentResize\", contentRect));\n  expose({\n    scrollEl: wrapperEl\n  });\n  return (_ctx, _cache) => {\n    return openBlock(), createElementBlock(\"div\", {\n      class: normalizeClass([\"scrollbar__wrapper\", __props.wrapperClass]),\n      style: normalizeStyle(__props.wrapperStyle)\n    }, [\n      createElementVNode(\"div\", mergeProps({\n        ref: (_value, _refs) => {\n          _refs[\"wrapperEl\"] = _value;\n          wrapperEl.value = _value;\n        },\n        class: \"scrollbar__scroller\"\n      }, _ctx.$attrs, {\n        onWheel: handleScroll,\n        onScroll: handleScroll\n      }), [\n        createElementVNode(\"div\", {\n          ref: (_value, _refs) => {\n            _refs[\"contentEl\"] = _value;\n            contentEl.value = _value;\n          },\n          class: normalizeClass([\"scrollbar__content\", __props.contentClass, { [\"scrollbar__content--fixedThumb\"]: __props.fixedThumb, [`scrollbar__content--${__props.direction}`]: __props.direction }]),\n          style: normalizeStyle(__props.contentStyle)\n        }, [\n          renderSlot(_ctx.$slots, \"default\")\n        ], 6)\n      ], 16),\n      (openBlock(), createElementBlock(Fragment, null, renderList(thumbs, (_, thumbType) => {\n        return createVNode(_sfc_main$1, {\n          ref: (instance) => setThumbsInstance(instance, thumbType),\n          key: thumbType,\n          autoExpand: __props.autoExpand,\n          autoHide: __props.autoHide,\n          autoHideDelay: __props.autoHideDelay,\n          fixedThumb: thumbType === __props.direction ? false : __props.fixedThumb,\n          type: thumbType,\n          scrollInfo: thumbType === \"vertical\" ? scrollHeightInfo.value : scrollWidthInfo.value,\n          thumbWidth: __props.thumbWidth,\n          wrapperEl: wrapperEl.value\n        }, null, 8, [\"autoExpand\", \"autoHide\", \"autoHideDelay\", \"fixedThumb\", \"type\", \"scrollInfo\", \"thumbWidth\", \"wrapperEl\"]);\n      }), 64))\n    ], 6);\n  };\n}\nconst _sfc_main = /* @__PURE__ */ defineComponent(__spreadProps(__spreadValues({}, __default__), {\n  props: {\n    wrapperClass: null,\n    wrapperStyle: null,\n    contentClass: null,\n    contentStyle: null,\n    direction: { default: \"vertical\" },\n    thumbMinSize: { default: 48 },\n    thumbMaxSize: { default: Infinity },\n    thumbWidth: { default: 12 },\n    autoHide: { default: true },\n    autoHideDelay: { default: 900 },\n    autoExpand: { default: true },\n    fixedThumb: null,\n    throttleType: { default: \"debounce\" },\n    throttleWait: { default: 333 },\n    simulateScroll: null\n  },\n  emits: [\"wrapperResize\", \"contentResize\", \"scroll\"],\n  setup\n}));\nexport { _sfc_main as default };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,YAAY,OAAO;AACvB,IAAI,aAAa,OAAO;AACxB,IAAI,oBAAoB,OAAO;AAC/B,IAAI,sBAAsB,OAAO;AACjC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,GAAG,IAAI;AAC1J,IAAI,iBAAiB,CAAC,GAAG,MAAM;AAC7B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,sBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AACpC,MAAI;AACF,aAAS,QAAQ,oBAAoB,CAAC,GAAG;AACvC,UAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,wBAAgB,GAAG,MAAM,EAAE,IAAI,CAAC;AAAA,IACpC;AACF,SAAO;AACT;AACA,IAAI,gBAAgB,CAAC,GAAG,MAAM,WAAW,GAAG,kBAAkB,CAAC,CAAC;AAIhE,IAAM,aAAa,CAAC,eAAe;AACnC,IAAM,gBAAgB;AAAA,EACpB,MAAM;AACR;AACA,SAAS,QAAQ,SAAS,EAAE,OAAO,GAAG;AACpC,QAAM,QAAQ;AACd,aAAW,CAAC,UAAU;AAAA,IACpB,YAAY,MAAM,kBAAkB;AAAA,EACtC,EAAE;AACF,QAAM,qBAAqB,SAAS,MAAM;AACxC,QAAI;AACJ,WAAO,IAAI,KAAK,MAAM,eAAe,OAAO,KAAK,EAAE;AAAA,EACrD,CAAC;AACD,MAAI,gBAAgB;AACpB,MAAI,UAAU,WAAW,IAAI;AAC7B,QAAM,kBAAkB,EAAE,OAAO,GAAG,OAAO,GAAG,WAAW,EAAE;AAC3D,MAAI,YAAY;AAChB,QAAM,qBAAqB,MAAM;AAC/B,oBAAgB,WAAW,MAAM;AAC/B,UAAI,QAAQ;AACV,gBAAQ,MAAM,UAAU,OAAO,wCAAwC;AACzE,sBAAgB;AAAA,IAClB,GAAG,MAAM,aAAa;AAAA,EACxB;AACA,QAAM,qBAAqB,MAAM;AAC/B,QAAI,kBAAkB;AACpB,mBAAa,aAAa;AAAA,EAC9B;AACA,cAAY,kBAAkB;AAC9B,QAAM,oBAAoB,iBAAS,CAAC,QAAQ;AAC1C,QAAI,gBAAgB;AACpB,QAAI,eAAe;AACnB,UAAM,gBAAgB,MAAM,SAAS,eAAe,UAAU;AAC9D,UAAM,gBAAgB,IAAI,aAAa,IAAI,gBAAgB,aAAa,KAAK,MAAM,WAAW,kBAAkB,MAAM,WAAW;AACjI,UAAM,UAAU,SAAS,EAAE,CAAC,MAAM,SAAS,eAAe,SAAS,KAAK,GAAG,gBAAgB,YAAY,cAAc,UAAU,OAAO,CAAC;AAAA,EACzI,GAAG,EAAE;AACL,QAAM,mBAAmB,MAAM;AAC7B,uBAAmB;AACnB,YAAQ,MAAM,oBAAoB,eAAe,iBAAiB;AAClE,YAAQ,MAAM,oBAAoB,aAAa,gBAAgB;AAC/D,YAAQ,MAAM,oBAAoB,iBAAiB,gBAAgB;AACnE,YAAQ,MAAM,oBAAoB,cAAc,gBAAgB;AAChE,aAAS,oBAAoB,cAAc,gBAAgB;AAC3D,QAAI,OAAO,cAAc;AACvB,cAAQ,MAAM,sBAAsB,SAAS;AAC/C,gBAAY;AAAA,EACd;AACA,QAAM,oBAAoB,CAAC,QAAQ;AACjC,QAAI,IAAI,WAAW,IAAI,WAAW;AAChC;AACF,uBAAmB;AACnB,oBAAgB,QAAQ,IAAI;AAC5B,oBAAgB,QAAQ,IAAI;AAC5B,oBAAgB,YAAY,MAAM,UAAU,MAAM,SAAS,eAAe,eAAe,WAAW;AACpG,gBAAY,OAAO,OAAO,SAAS,IAAI;AACvC,YAAQ,MAAM,kBAAkB,SAAS;AACzC,YAAQ,MAAM,iBAAiB,eAAe,iBAAiB;AAC/D,YAAQ,MAAM,iBAAiB,aAAa,gBAAgB;AAC5D,YAAQ,MAAM,iBAAiB,iBAAiB,gBAAgB;AAChE,YAAQ,MAAM,iBAAiB,cAAc,kBAAkB,EAAE,SAAS,MAAM,CAAC;AACjF,aAAS,iBAAiB,cAAc,kBAAkB,EAAE,SAAS,MAAM,CAAC;AAC5E,YAAQ,MAAM,UAAU,IAAI,wCAAwC;AAAA,EACtE;AACA,QAAM,sBAAsB,MAAM;AAChC,uBAAmB;AACnB,QAAI,QAAQ;AACV,cAAQ,MAAM,UAAU,IAAI,wCAAwC;AACtE,uBAAmB;AAAA,EACrB;AACA,SAAO;AAAA,IACL;AAAA,EACF,CAAC;AACD,MAAI,wBAAwB,WAAW,KAAK;AAC5C,MAAI,yBAAyB,WAAW,KAAK;AAC7C,MAAI,aAAa,WAAW,IAAI;AAChC,MAAI,aAAa;AACjB,MAAI,YAAY;AAChB,MAAI,cAAc,SAAS,MAAM,MAAM,cAAc,CAAC,uBAAuB,KAAK;AAClF,QAAM,qBAAqB,CAAC,CAAC,KAAK,MAAM,uBAAuB,QAAQ,MAAM;AAC7E,QAAM,oBAAoB,CAAC,CAAC,KAAK,MAAM,sBAAsB,QAAQ,MAAM;AAC3E,QAAM,UAAU,MAAM;AACpB,QAAI,YAAY;AACd,iBAAW,WAAW;AACtB,mBAAa;AAAA,IACf;AACA,QAAI,WAAW;AACb,gBAAU,WAAW;AACrB,kBAAY;AAAA,IACd;AAAA,EACF;AACA,QAAM,MAAM,CAAC,MAAM,YAAY,MAAM,SAAS,GAAG,MAAM;AACrD,QAAI,CAAC,MAAM,cAAc,CAAC,MAAM;AAC9B,aAAO,QAAQ;AACjB,YAAQ;AACR,gBAAY,IAAI,qBAAqB,mBAAmB,EAAE,WAAW,CAAC,GAAG,GAAG,EAAE,CAAC;AAC/E,cAAU,QAAQ,MAAM,SAAS;AACjC,iBAAa,IAAI,qBAAqB,kBAAkB;AACxD,eAAW,QAAQ,WAAW,KAAK;AAAA,EACrC,CAAC;AACD,cAAY,OAAO;AACnB,SAAO,CAAC,MAAM,WAAW;AACvB,WAAO,UAAU,GAAG,mBAAmB,UAAU,MAAM;AAAA,MACrD,eAAe,gBAAmB,OAAO;AAAA,QACvC,OAAO,eAAe;AAAA,UACpB;AAAA,UACA,gCAAgC,QAAQ,IAAI;AAAA,UAC5C;AAAA,YACE,CAAC,uCAAuC,GAAG,QAAQ;AAAA,YACnD,CAAC,yCAAyC,GAAG,QAAQ;AAAA,UACvD;AAAA,QACF,CAAC;AAAA,QACD,OAAO,eAAe;AAAA,UACpB,OAAO,QAAQ,SAAS,eAAe,GAAG,QAAQ,WAAW,SAAS,OAAO;AAAA,UAC7E,QAAQ,QAAQ,SAAS,aAAa,GAAG,QAAQ,WAAW,SAAS,OAAO;AAAA,UAC5E,UAAU,CAAC,YAAY,QAAQ,aAAa;AAAA,UAC5C,CAAC,QAAQ,SAAS,aAAa,QAAQ,MAAM,GAAG,CAAC,YAAY,QAAQ,QAAQ,GAAG,QAAQ,WAAW,mBAAmB,CAAC;AAAA,QACzH,CAAC;AAAA,QACD,KAAK,CAAC,QAAQ,UAAU;AACtB,gBAAM,SAAS,IAAI;AACnB,kBAAQ,QAAQ;AAAA,QAClB;AAAA,QACA,eAAe,cAAc,mBAAmB,CAAC,MAAM,CAAC;AAAA,MAC1D,GAAG;AAAA,QACD,OAAO,CAAC,MAAM,iBAAiB,EAAE,GAAG,OAAO,CAAC,IAAI,gBAAmB,OAAO;AAAA,UACxE,OAAO,eAAe,CAAC,oBAAoB,qBAAqB,QAAQ,IAAI,EAAE,CAAC;AAAA,QACjF,GAAG,MAAM,CAAC,GAAG,iBAAiB,CAAC,GAAG,OAAO,CAAC;AAAA,MAC5C,GAAG,IAAI,UAAU,GAAG;AAAA,QAClB,CAAC,OAAO,QAAQ,QAAQ,WAAW,SAAS,MAAM,QAAQ,aAAa,sBAAsB,QAAQ,KAAK;AAAA,MAC5G,CAAC;AAAA,MACD,MAAM,aAAa,gBAAgB,UAAU,GAAG,mBAAmB,OAAO;AAAA,QACxE,KAAK;AAAA,QACL,KAAK,CAAC,QAAQ,UAAU;AACtB,gBAAM,YAAY,IAAI;AACtB,qBAAW,QAAQ;AAAA,QACrB;AAAA,QACA,OAAO,eAAe,CAAC,uBAAuB,wBAAwB,QAAQ,IAAI,EAAE,CAAC;AAAA,MACvF,GAAG,MAAM,CAAC,IAAI;AAAA,QACZ,CAAC,OAAO,QAAQ,QAAQ,WAAW,SAAS,CAAC;AAAA,MAC/C,CAAC,IAAI,mBAAmB,IAAI,IAAI;AAAA,IAClC,GAAG,EAAE;AAAA,EACP;AACF;AACA,IAAM,cAA8B,gBAAgB,cAAc,eAAe,CAAC,GAAG,aAAa,GAAG;AAAA,EACnG,OAAO;AAAA,IACL,MAAM;AAAA,IACN,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,eAAe;AAAA,IACf,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,EACb;AAAA,EACA,OAAO;AACT,CAAC,CAAC;AACF,IAAM,gBAAgB;AAAA,EACpB,MAAM;AAAA,EACN,MAAM;AACR;AACA,SAAS,cAAc,MAAM;AAC3B,QAAM,cAAc,YAAY,QAAQ,OAAO,SAAS,KAAK,CAAC;AAC9D,MAAI;AACJ,MAAI;AACF,aAAS,QAAQ,OAAO,SAAS,KAAK,CAAC;AAAA;AAEvC,aAAS,QAAQ,OAAO,SAAS,KAAK,CAAC;AACzC,QAAM,EAAE,MAAM,MAAM,SAAS,IAAI,eAAe,eAAe,CAAC,GAAG,aAAa,GAAG,MAAM;AACzF,QAAM,YAAY,cAAc,KAAK,CAAC,IAAI,WAAW,IAAI;AACzD,QAAM,OAAO,SAAS,EAAE,MAAM,GAAG,KAAK,GAAG,OAAO,GAAG,QAAQ,GAAG,OAAO,GAAG,QAAQ,GAAG,GAAG,GAAG,GAAG,EAAE,CAAC;AAC/F,QAAM,eAAe,MAAM;AACzB,UAAM,UAAU,UAAU,MAAM,sBAAsB;AACtD,SAAK,OAAO,QAAQ;AACpB,SAAK,MAAM,QAAQ;AACnB,SAAK,QAAQ,QAAQ;AACrB,SAAK,SAAS,QAAQ;AACtB,SAAK,QAAQ,QAAQ;AACrB,SAAK,SAAS,QAAQ;AACtB,SAAK,IAAI,QAAQ;AACjB,SAAK,IAAI,QAAQ;AACjB,gBAAY,OAAO,SAAS,SAAS;AAAA,EACvC;AACA,MAAI,WAAW;AACf,MAAI,KAAK;AACT,QAAM,UAAU,MAAM;AACpB,QAAI;AACF,aAAO,oBAAoB,UAAU,QAAQ;AAC/C,QAAI,CAAC;AACH;AACF,OAAG,WAAW;AACd,SAAK;AAAA,EACP;AACA,cAAY,MAAM;AAChB,QAAI,CAAC,UAAU;AACb;AACF,YAAQ;AACR,eAAW;AACX,QAAI,SAAS,cAAc,QAAQ;AACjC,iBAAW,iBAAS,UAAU,IAAI;AAAA,aAC3B,SAAS,cAAc,QAAQ;AACtC,iBAAW,iBAAS,UAAU,IAAI;AACpC,WAAO,iBAAiB,UAAU,QAAQ;AAC1C,SAAK,IAAI,eAAe,QAAQ;AAChC,OAAG,QAAQ,UAAU,KAAK;AAAA,EAC5B,CAAC;AACD,cAAY,OAAO;AACnB,MAAI;AACF,WAAO;AACT,SAAO,CAAC,WAAW,IAAI;AACzB;AAEA,IAAM,cAAc;AAAA,EAClB,MAAM;AAAA,EACN,cAAc;AAChB;AACA,SAAS,MAAM,SAAS,EAAE,QAAQ,KAAK,GAAG;AACxC,QAAM,QAAQ;AACd,QAAM,SAAS;AAAA,IACb,YAAY,EAAE,IAAI,MAAM,UAAU,KAAK;AAAA,IACvC,UAAU,EAAE,IAAI,MAAM,UAAU,KAAK;AAAA,EACvC;AACA,QAAM,oBAAoB,CAAC,UAAU,cAAc;AACjD,QAAI,CAAC,OAAO,SAAS,EAAE;AACrB,aAAO,SAAS,EAAE,WAAW;AAAA,EACjC;AACA,MAAI,YAAY,WAAW,IAAI;AAC/B,MAAI,YAAY,WAAW,IAAI;AAC/B,YAAU,MAAM;AACd,QAAI;AACJ,UAAM,aAAa,MAAM,MAAM,KAAK,UAAU,MAAM,kBAAkB,OAAO,SAAS,GAAG,UAAU;AACnG,eAAW,aAAa,QAAQ;AAC9B,aAAO,SAAS,EAAE,KAAK,WAAW,KAAK,CAAC,QAAQ;AAC9C,YAAI;AACJ,gBAAQ,MAAM,OAAO,OAAO,SAAS,IAAI,cAAc,OAAO,SAAS,IAAI,SAAS,gCAAgC,SAAS,EAAE;AAAA,MACjI,CAAC;AAAA,IACH;AAAA,EACF,CAAC;AACD,QAAM,cAAc,WAAW,WAAW,EAAE,MAAM,MAAM,cAAc,MAAM,MAAM,cAAc,UAAU,wBAAwB,CAAC;AACnI,QAAM,cAAc,WAAW,WAAW,EAAE,MAAM,MAAM,cAAc,MAAM,MAAM,cAAc,UAAU,wBAAwB,CAAC;AACnI,MAAI,qBAAqB,WAAW,CAAC;AACrC,MAAI,sBAAsB,WAAW,CAAC;AACtC,WAAS,0BAA0B;AACjC,uBAAmB,QAAQ,KAAK,IAAI,UAAU,MAAM,eAAe,YAAY,SAAS,GAAG,CAAC;AAC5F,wBAAoB,QAAQ,KAAK,IAAI,UAAU,MAAM,cAAc,YAAY,QAAQ,GAAG,CAAC;AAAA,EAC7F;AACA,MAAI,kBAAkB,SAAS,MAAM;AACnC,WAAO;AAAA,MACL,WAAW,oBAAoB,QAAQ,cAAM,YAAY,QAAQ,UAAU,MAAM,cAAc,YAAY,OAAO,MAAM,eAAe,YAAY,QAAQ,KAAK,MAAM,cAAc,MAAM,YAAY,IAAI;AAAA,MAC1M,iBAAiB,YAAY;AAAA,MAC7B,iBAAiB,YAAY;AAAA,MAC7B,kBAAkB,KAAK,IAAI,YAAY,IAAI;AAAA,IAC7C;AAAA,EACF,CAAC;AACD,MAAI,mBAAmB,SAAS,MAAM;AACpC,WAAO;AAAA,MACL,WAAW,mBAAmB,QAAQ,cAAM,YAAY,SAAS,UAAU,MAAM,eAAe,YAAY,QAAQ,MAAM,eAAe,YAAY,SAAS,KAAK,MAAM,cAAc,MAAM,YAAY,IAAI;AAAA,MAC7M,iBAAiB,YAAY;AAAA,MAC7B,iBAAiB,YAAY;AAAA,MAC7B,kBAAkB,KAAK,IAAI,YAAY,GAAG;AAAA,IAC5C;AAAA,EACF,CAAC;AACD,MAAI,eAAe,SAAS,MAAM,YAAY,SAAS,iBAAiB,MAAM,YAAY,CAAC;AAC3F,MAAI,gBAAgB,SAAS,MAAM,YAAY,QAAQ,gBAAgB,MAAM,YAAY,CAAC;AAC1F,QAAM,qBAAqB,MAAM;AAC/B,QAAI,oBAAoB,OAAO;AAC7B,aAAO,WAAW,GAAG,MAAM,YAAY,eAAe,UAAU,MAAM,aAAa,oBAAoB,QAAQ,cAAc,KAAK;AAClI,aAAO,WAAW,SAAS,oBAAoB;AAAA,IACjD;AACA,QAAI,mBAAmB,OAAO;AAC5B,aAAO,SAAS,GAAG,MAAM,YAAY,kBAAkB,UAAU,MAAM,YAAY,mBAAmB,QAAQ,aAAa,KAAK;AAChI,aAAO,SAAS,SAAS,oBAAoB;AAAA,IAC/C;AAAA,EACF;AACA,QAAM,MAAM,CAAC,oBAAoB,OAAO,mBAAmB,KAAK,GAAG,kBAAkB;AACrF,QAAM,uBAAuB,CAAC,QAAQ;AACpC,QAAI,gBAAgB;AACpB,UAAM,gBAAgB,UAAU,MAAM;AACtC,UAAM,eAAe,UAAU,MAAM;AACrC,UAAM,gBAAgB,cAAM,kBAAkB,OAAO,OAAO,SAAS,IAAI,WAAW,IAAI,GAAG,oBAAoB,KAAK,IAAI;AACxH,UAAM,eAAe,cAAM,iBAAiB,OAAO,OAAO,SAAS,IAAI,WAAW,IAAI,GAAG,mBAAmB,KAAK,IAAI;AACrH,cAAU,MAAM,aAAa;AAC7B,cAAU,MAAM,YAAY;AAC5B,QAAI,oBAAoB,OAAO;AAC7B,aAAO,WAAW,GAAG,MAAM,YAAY,eAAe,gBAAgB,oBAAoB,QAAQ,cAAc,KAAK;AACrH,aAAO,WAAW,SAAS,oBAAoB;AAAA,IACjD;AACA,QAAI,mBAAmB,OAAO;AAC5B,aAAO,SAAS,GAAG,MAAM,YAAY,kBAAkB,eAAe,mBAAmB,QAAQ,aAAa,KAAK;AACnH,aAAO,SAAS,SAAS,oBAAoB;AAAA,IAC/C;AACA,SAAK,UAAU,EAAE,QAAQ,UAAU,OAAO,YAAY,eAAe,WAAW,aAAa,CAAC;AAAA,EAChG;AACA,QAAM,eAAe,CAAC,QAAQ;AAC5B,QAAI,MAAM;AACR,2BAAqB,GAAG;AAAA;AAExB,yBAAmB;AAAA,EACvB;AACA,QAAM,aAAa,MAAM,KAAK,iBAAiB,WAAW,CAAC;AAC3D,QAAM,aAAa,MAAM,KAAK,iBAAiB,WAAW,CAAC;AAC3D,SAAO;AAAA,IACL,UAAU;AAAA,EACZ,CAAC;AACD,SAAO,CAAC,MAAM,WAAW;AACvB,WAAO,UAAU,GAAG,mBAAmB,OAAO;AAAA,MAC5C,OAAO,eAAe,CAAC,sBAAsB,QAAQ,YAAY,CAAC;AAAA,MAClE,OAAO,eAAe,QAAQ,YAAY;AAAA,IAC5C,GAAG;AAAA,MACD,gBAAmB,OAAO,WAAW;AAAA,QACnC,KAAK,CAAC,QAAQ,UAAU;AACtB,gBAAM,WAAW,IAAI;AACrB,oBAAU,QAAQ;AAAA,QACpB;AAAA,QACA,OAAO;AAAA,MACT,GAAG,KAAK,QAAQ;AAAA,QACd,SAAS;AAAA,QACT,UAAU;AAAA,MACZ,CAAC,GAAG;AAAA,QACF,gBAAmB,OAAO;AAAA,UACxB,KAAK,CAAC,QAAQ,UAAU;AACtB,kBAAM,WAAW,IAAI;AACrB,sBAAU,QAAQ;AAAA,UACpB;AAAA,UACA,OAAO,eAAe,CAAC,sBAAsB,QAAQ,cAAc,EAAE,CAAC,gCAAgC,GAAG,QAAQ,YAAY,CAAC,uBAAuB,QAAQ,SAAS,EAAE,GAAG,QAAQ,UAAU,CAAC,CAAC;AAAA,UAC/L,OAAO,eAAe,QAAQ,YAAY;AAAA,QAC5C,GAAG;AAAA,UACD,WAAW,KAAK,QAAQ,SAAS;AAAA,QACnC,GAAG,CAAC;AAAA,MACN,GAAG,EAAE;AAAA,OACJ,UAAU,GAAG,mBAAmB,UAAU,MAAM,WAAW,QAAQ,CAAC,GAAG,cAAc;AACpF,eAAO,YAAY,aAAa;AAAA,UAC9B,KAAK,CAAC,aAAa,kBAAkB,UAAU,SAAS;AAAA,UACxD,KAAK;AAAA,UACL,YAAY,QAAQ;AAAA,UACpB,UAAU,QAAQ;AAAA,UAClB,eAAe,QAAQ;AAAA,UACvB,YAAY,cAAc,QAAQ,YAAY,QAAQ,QAAQ;AAAA,UAC9D,MAAM;AAAA,UACN,YAAY,cAAc,aAAa,iBAAiB,QAAQ,gBAAgB;AAAA,UAChF,YAAY,QAAQ;AAAA,UACpB,WAAW,UAAU;AAAA,QACvB,GAAG,MAAM,GAAG,CAAC,cAAc,YAAY,iBAAiB,cAAc,QAAQ,cAAc,cAAc,WAAW,CAAC;AAAA,MACxH,CAAC,GAAG,EAAE;AAAA,IACR,GAAG,CAAC;AAAA,EACN;AACF;AACA,IAAM,YAA4B,gBAAgB,cAAc,eAAe,CAAC,GAAG,WAAW,GAAG;AAAA,EAC/F,OAAO;AAAA,IACL,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,cAAc;AAAA,IACd,WAAW,EAAE,SAAS,WAAW;AAAA,IACjC,cAAc,EAAE,SAAS,GAAG;AAAA,IAC5B,cAAc,EAAE,SAAS,SAAS;AAAA,IAClC,YAAY,EAAE,SAAS,GAAG;AAAA,IAC1B,UAAU,EAAE,SAAS,KAAK;AAAA,IAC1B,eAAe,EAAE,SAAS,IAAI;AAAA,IAC9B,YAAY,EAAE,SAAS,KAAK;AAAA,IAC5B,YAAY;AAAA,IACZ,cAAc,EAAE,SAAS,WAAW;AAAA,IACpC,cAAc,EAAE,SAAS,IAAI;AAAA,IAC7B,gBAAgB;AAAA,EAClB;AAAA,EACA,OAAO,CAAC,iBAAiB,iBAAiB,QAAQ;AAAA,EAClD;AACF,CAAC,CAAC;", "names": []}