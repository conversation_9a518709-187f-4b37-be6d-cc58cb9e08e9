{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/vector/GeoJSON/index.ts"], "sourcesContent": ["import GeoJSON from './GeoJSON.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nGeoJSON.install = (app: App) => {\r\n  app.component(GeoJSON.name, GeoJSON);\r\n  return app;\r\n};\r\nexport const ElAmapGeojson = GeoJSON as typeof GeoJSON & Plugin;\r\nexport default ElAmapGeojson;\r\n\r\nexport type ElAmapGeojsonInstance = InstanceType<typeof GeoJSON>\r\n"], "names": ["GeoJSON"], "mappings": ";;;AAEAA,MAAQ,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AAC9B,EAAI,GAAA,CAAA,SAAA,CAAUA,MAAQ,CAAA,IAAA,EAAMA,MAAO,CAAA,CAAA;AACnC,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AACO,MAAM,aAAgB,GAAAA;;;;"}