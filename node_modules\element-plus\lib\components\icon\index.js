'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('../../utils/index.js');
var icon$1 = require('./src/icon2.js');
var icon = require('./src/icon.js');
var install = require('../../utils/vue/install.js');

const ElIcon = install.withInstall(icon$1["default"]);

exports.iconProps = icon.iconProps;
exports.ElIcon = ElIcon;
exports["default"] = ElIcon;
//# sourceMappingURL=index.js.map
