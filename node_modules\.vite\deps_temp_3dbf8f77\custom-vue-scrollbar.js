import {
  clamp_default,
  debounce_default,
  throttle_default
} from "./chunk-RHQEB4B4.js";
import {
  Fragment,
  computed2 as computed,
  createBaseVNode,
  createCommentVNode,
  createElementBlock,
  createVNode,
  defineComponent,
  mergeProps,
  normalizeClass,
  normalizeStyle,
  onMounted,
  onUnmounted,
  openBlock,
  reactive,
  renderList,
  renderSlot,
  setBlockTracking,
  shallowRef,
  unref,
  useCssVars,
  vShow,
  watch,
  watchEffect,
  withDirectives,
  withModifiers
} from "./chunk-YHJVOVJ5.js";
import "./chunk-5WWUZCGV.js";

// node_modules/.pnpm/custom-vue-scrollbar@0.0.8__198506a13cb6d6b89d6c0501db2feac6/node_modules/custom-vue-scrollbar/dist/index.js
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var _hoisted_1 = ["onPointerdown"];
var __default__$1 = {
  name: "ScrollbarThumb"
};
function setup$1(__props, { expose }) {
  const props = __props;
  useCssVars((_ctx) => ({
    "1fb98f78": unref(computedThumbWidth)
  }));
  const computedThumbWidth = computed(() => {
    var _a;
    return `${(_a = props.thumbWidth) != null ? _a : 12}px`;
  });
  let autoHideTimer = null;
  let thumbEl = shallowRef(null);
  const pointerDownInfo = { pageX: 0, pageY: 0, scrollPos: 0 };
  let pointerId = null;
  const startAutoHideTimer = () => {
    autoHideTimer = setTimeout(() => {
      if (thumbEl.value)
        thumbEl.value.classList.remove("scrollbar__thumbPlaceholder--scrolling");
      autoHideTimer = null;
    }, props.autoHideDelay);
  };
  const clearAutoHideTimer = () => {
    if (autoHideTimer !== null)
      clearTimeout(autoHideTimer);
  };
  onUnmounted(clearAutoHideTimer);
  const handlePointerMove = throttle_default((evt) => {
    evt.stopPropagation();
    evt.preventDefault();
    const moveDirection = props.type === "horizontal" ? "pageX" : "pageY";
    const moveDistance = (evt[moveDirection] - pointerDownInfo[moveDirection]) / props.scrollInfo.wrapperMainSize * props.scrollInfo.contentMainSize;
    props.wrapperEl.scrollTo({ [props.type === "horizontal" ? "left" : "top"]: pointerDownInfo.scrollPos + moveDistance, behavior: "auto" });
  }, 16);
  const handlePointerEnd = () => {
    startAutoHideTimer();
    thumbEl.value.removeEventListener("pointermove", handlePointerMove);
    thumbEl.value.removeEventListener("pointerup", handlePointerEnd);
    thumbEl.value.removeEventListener("pointercancel", handlePointerEnd);
    thumbEl.value.removeEventListener("mousewheel", handlePointerEnd);
    document.removeEventListener("mousewheel", handlePointerEnd);
    if (typeof pointerId === "number")
      thumbEl.value.releasePointerCapture(pointerId);
    pointerId = null;
  };
  const handlePointerDown = (evt) => {
    if (evt.ctrlKey || evt.button !== 0)
      return;
    clearAutoHideTimer();
    pointerDownInfo.pageX = evt.pageX;
    pointerDownInfo.pageY = evt.pageY;
    pointerDownInfo.scrollPos = props.wrapperEl[props.type === "horizontal" ? "scrollLeft" : "scrollTop"];
    pointerId = evt == null ? void 0 : evt.pointerId;
    thumbEl.value.setPointerCapture(pointerId);
    thumbEl.value.addEventListener("pointermove", handlePointerMove);
    thumbEl.value.addEventListener("pointerup", handlePointerEnd);
    thumbEl.value.addEventListener("pointercancel", handlePointerEnd);
    thumbEl.value.addEventListener("mousewheel", handlePointerEnd, { passive: false });
    document.addEventListener("mousewheel", handlePointerEnd, { passive: false });
    thumbEl.value.classList.add("scrollbar__thumbPlaceholder--scrolling");
  };
  const autoHideAfterScroll = () => {
    clearAutoHideTimer();
    if (thumbEl.value)
      thumbEl.value.classList.add("scrollbar__thumbPlaceholder--scrolling");
    startAutoHideTimer();
  };
  expose({
    autoHideAfterScroll
  });
  let isWrapperIntersecting = shallowRef(false);
  let isShepherdIntersecting = shallowRef(false);
  let shepherdEl = shallowRef(null);
  let shepherdIO = null;
  let wrapperIO = null;
  let shouldFixed = computed(() => props.fixedThumb && !isShepherdIntersecting.value);
  const shepherdIOCallback = ([entry]) => isShepherdIntersecting.value = entry.isIntersecting;
  const wrapperIOCallback = ([entry]) => isWrapperIntersecting.value = entry.isIntersecting;
  const clearIO = () => {
    if (shepherdIO) {
      shepherdIO.disconnect();
      shepherdIO = null;
    }
    if (wrapperIO) {
      wrapperIO.disconnect();
      wrapperIO = null;
    }
  };
  watch(() => [props.fixedThumb, props.wrapperEl], () => {
    if (!props.fixedThumb || !props.wrapperEl)
      return clearIO();
    clearIO();
    wrapperIO = new IntersectionObserver(wrapperIOCallback, { threshold: [0, 0.5] });
    wrapperIO.observe(props.wrapperEl);
    shepherdIO = new IntersectionObserver(shepherdIOCallback);
    shepherdIO.observe(shepherdEl.value);
  });
  onUnmounted(clearIO);
  return (_ctx, _cache) => {
    return openBlock(), createElementBlock(Fragment, null, [
      withDirectives(createBaseVNode("div", {
        class: normalizeClass([
          "scrollbar__thumbPlaceholder",
          `scrollbar__thumbPlaceholder--${__props.type}`,
          {
            ["scrollbar__thumbPlaceholder--autoHide"]: __props.autoHide,
            ["scrollbar__thumbPlaceholder--autoExpand"]: __props.autoExpand
          }
        ]),
        style: normalizeStyle({
          width: __props.type === "horizontal" ? `${__props.scrollInfo.thumbSize}px` : "",
          height: __props.type === "vertical" ? `${__props.scrollInfo.thumbSize}px` : "",
          position: !shouldFixed.value ? "absolute" : "fixed",
          [__props.type === "vertical" ? "top" : "left"]: !shouldFixed.value ? "3px" : `${__props.scrollInfo.boundaryDistance + 3}px`
        }),
        ref: (_value, _refs) => {
          _refs["thumbEl"] = _value;
          thumbEl.value = _value;
        },
        onPointerdown: withModifiers(handlePointerDown, ["stop"])
      }, [
        _cache[0] || (setBlockTracking(-1), _cache[0] = createBaseVNode("div", {
          class: normalizeClass(["scrollbar__thumb", `scrollbar__thumb--${__props.type}`])
        }, null, 2), setBlockTracking(1), _cache[0])
      ], 46, _hoisted_1), [
        [vShow, Boolean(__props.scrollInfo.thumbSize) && (__props.fixedThumb ? isWrapperIntersecting.value : true)]
      ]),
      props.fixedThumb ? withDirectives((openBlock(), createElementBlock("div", {
        key: 0,
        ref: (_value, _refs) => {
          _refs["shepherdEl"] = _value;
          shepherdEl.value = _value;
        },
        class: normalizeClass(["scrollbar__shepherd", `scrollbar__shepherd--${__props.type}`])
      }, null, 2)), [
        [vShow, Boolean(__props.scrollInfo.thumbSize)]
      ]) : createCommentVNode("", true)
    ], 64);
  };
}
var _sfc_main$1 = defineComponent(__spreadProps(__spreadValues({}, __default__$1), {
  props: {
    type: null,
    autoExpand: null,
    autoHide: null,
    autoHideDelay: null,
    fixedThumb: null,
    scrollInfo: null,
    thumbWidth: null,
    wrapperEl: null
  },
  setup: setup$1
}));
var defaultOption = {
  wait: 333,
  type: "debounce"
};
function useMeasure(...args) {
  const hasParamRef = "value" in (args == null ? void 0 : args[0]);
  let option;
  if (hasParamRef)
    option = args == null ? void 0 : args[1];
  else
    option = args == null ? void 0 : args[0];
  const { wait, type, callback } = __spreadValues(__spreadValues({}, defaultOption), option);
  const targetRef = hasParamRef ? args[0] : shallowRef(null);
  const rect = reactive({ left: 0, top: 0, right: 0, bottom: 0, width: 0, height: 0, x: 0, y: 0 });
  const observerFunc = () => {
    const domRect = targetRef.value.getBoundingClientRect();
    rect.left = domRect.left;
    rect.top = domRect.top;
    rect.right = domRect.right;
    rect.bottom = domRect.bottom;
    rect.width = domRect.width;
    rect.height = domRect.height;
    rect.x = domRect.x;
    rect.y = domRect.y;
    callback == null ? void 0 : callback();
  };
  let execFunc = null;
  let ro = null;
  const clearRo = () => {
    if (execFunc)
      window.removeEventListener("resize", execFunc);
    if (!ro)
      return;
    ro.disconnect();
    ro = null;
  };
  watchEffect(() => {
    if (!targetRef.value)
      return;
    clearRo();
    execFunc = observerFunc;
    if (type === "throttle" && wait >= 4)
      execFunc = throttle_default(execFunc, wait);
    else if (type === "debounce" && wait >= 4)
      execFunc = debounce_default(execFunc, wait);
    window.addEventListener("resize", execFunc);
    ro = new ResizeObserver(execFunc);
    ro.observe(targetRef.value);
  });
  onUnmounted(clearRo);
  if (hasParamRef)
    return rect;
  return [targetRef, rect];
}
var __default__ = {
  name: "CustomScrollbar",
  inheritAttrs: false
};
function setup(__props, { expose, emit }) {
  const props = __props;
  const thumbs = {
    horizontal: { el: null, instance: null },
    vertical: { el: null, instance: null }
  };
  const setThumbsInstance = (instance, direction) => {
    if (!thumbs[direction].instance)
      thumbs[direction].instance = instance;
  };
  let wrapperEl = shallowRef(null);
  let contentEl = shallowRef(null);
  onMounted(() => {
    var _a;
    const childNodes = Array.from((_a = wrapperEl.value.parentElement) == null ? void 0 : _a.childNodes);
    for (const thumbType in thumbs) {
      thumbs[thumbType].el = childNodes.find((ele) => {
        var _a2;
        return (_a2 = ele == null ? void 0 : ele.classList) == null ? void 0 : _a2.contains(`scrollbar__thumbPlaceholder--${thumbType}`);
      });
    }
  });
  const wrapperRect = useMeasure(wrapperEl, { wait: props.throttleWait, type: props.throttleType, callback: updateMaxScrollDistance });
  const contentRect = useMeasure(contentEl, { wait: props.throttleWait, type: props.throttleType, callback: updateMaxScrollDistance });
  let nativeMaxScrollTop = shallowRef(0);
  let nativeMaxScrollLeft = shallowRef(0);
  function updateMaxScrollDistance() {
    nativeMaxScrollTop.value = Math.max(wrapperEl.value.scrollHeight - wrapperRect.height | 0, 0);
    nativeMaxScrollLeft.value = Math.max(wrapperEl.value.scrollWidth - wrapperRect.width | 0, 0);
  }
  let scrollWidthInfo = computed(() => {
    return {
      thumbSize: nativeMaxScrollLeft.value ? clamp_default(wrapperRect.width / wrapperEl.value.scrollWidth * wrapperRect.width, props.thumbMinSize > wrapperRect.width ? 48 : props.thumbMinSize, props.thumbMaxSize) : 0,
      contentMainSize: contentRect.width,
      wrapperMainSize: wrapperRect.width,
      boundaryDistance: Math.abs(wrapperRect.left)
    };
  });
  let scrollHeightInfo = computed(() => {
    return {
      thumbSize: nativeMaxScrollTop.value ? clamp_default(wrapperRect.height / wrapperEl.value.scrollHeight * wrapperRect.height, props.thumbMinSize > wrapperRect.height ? 48 : props.thumbMinSize, props.thumbMaxSize) : 0,
      contentMainSize: contentRect.height,
      wrapperMainSize: wrapperRect.height,
      boundaryDistance: Math.abs(wrapperRect.top)
    };
  });
  let maxScrollTop = computed(() => wrapperRect.height - scrollHeightInfo.value.thumbSize - 5);
  let maxScrollLeft = computed(() => wrapperRect.width - scrollWidthInfo.value.thumbSize - 5);
  const handleNativeScroll = () => {
    if (nativeMaxScrollLeft.value) {
      thumbs.horizontal.el.style.transform = `translate3d(${wrapperEl.value.scrollLeft / nativeMaxScrollLeft.value * maxScrollLeft.value}px, 0, 0)`;
      thumbs.horizontal.instance.autoHideAfterScroll();
    }
    if (nativeMaxScrollTop.value) {
      thumbs.vertical.el.style.transform = `translate3d(0, ${wrapperEl.value.scrollTop / nativeMaxScrollTop.value * maxScrollTop.value}px, 0)`;
      thumbs.vertical.instance.autoHideAfterScroll();
    }
  };
  watch(() => [nativeMaxScrollLeft.value, nativeMaxScrollTop.value], handleNativeScroll);
  const handleSimulateScroll = (evt) => {
    evt.stopPropagation();
    const preScrollLeft = wrapperEl.value.scrollLeft;
    const preScrollTop = wrapperEl.value.scrollTop;
    const newScrollLeft = clamp_default(preScrollLeft + ((evt == null ? void 0 : evt.deltaX) || 0), 0, nativeMaxScrollLeft.value) | 0;
    const newScrollTop = clamp_default(preScrollTop + ((evt == null ? void 0 : evt.deltaY) || 0), 0, nativeMaxScrollTop.value) | 0;
    wrapperEl.value.scrollLeft = newScrollLeft;
    wrapperEl.value.scrollTop = newScrollTop;
    if (nativeMaxScrollLeft.value) {
      thumbs.horizontal.el.style.transform = `translate3d(${newScrollLeft / nativeMaxScrollLeft.value * maxScrollLeft.value}px, 0, 0)`;
      thumbs.horizontal.instance.autoHideAfterScroll();
    }
    if (nativeMaxScrollTop.value) {
      thumbs.vertical.el.style.transform = `translate3d(0, ${newScrollTop / nativeMaxScrollTop.value * maxScrollTop.value}px, 0)`;
      thumbs.vertical.instance.autoHideAfterScroll();
    }
    emit("scroll", { target: wrapperEl.value, scrollLeft: newScrollLeft, scrollTop: newScrollTop });
  };
  const handleScroll = (evt) => {
    if (props.simulateScroll)
      handleSimulateScroll(evt);
    else
      handleNativeScroll();
  };
  watch(wrapperRect, () => emit("wrapperResize", wrapperRect));
  watch(contentRect, () => emit("contentResize", contentRect));
  expose({
    scrollEl: wrapperEl
  });
  return (_ctx, _cache) => {
    return openBlock(), createElementBlock("div", {
      class: normalizeClass(["scrollbar__wrapper", __props.wrapperClass]),
      style: normalizeStyle(__props.wrapperStyle)
    }, [
      createBaseVNode("div", mergeProps({
        ref: (_value, _refs) => {
          _refs["wrapperEl"] = _value;
          wrapperEl.value = _value;
        },
        class: "scrollbar__scroller"
      }, _ctx.$attrs, {
        onWheel: handleScroll,
        onScroll: handleScroll
      }), [
        createBaseVNode("div", {
          ref: (_value, _refs) => {
            _refs["contentEl"] = _value;
            contentEl.value = _value;
          },
          class: normalizeClass(["scrollbar__content", __props.contentClass, { ["scrollbar__content--fixedThumb"]: __props.fixedThumb, [`scrollbar__content--${__props.direction}`]: __props.direction }]),
          style: normalizeStyle(__props.contentStyle)
        }, [
          renderSlot(_ctx.$slots, "default")
        ], 6)
      ], 16),
      (openBlock(), createElementBlock(Fragment, null, renderList(thumbs, (_, thumbType) => {
        return createVNode(_sfc_main$1, {
          ref: (instance) => setThumbsInstance(instance, thumbType),
          key: thumbType,
          autoExpand: __props.autoExpand,
          autoHide: __props.autoHide,
          autoHideDelay: __props.autoHideDelay,
          fixedThumb: thumbType === __props.direction ? false : __props.fixedThumb,
          type: thumbType,
          scrollInfo: thumbType === "vertical" ? scrollHeightInfo.value : scrollWidthInfo.value,
          thumbWidth: __props.thumbWidth,
          wrapperEl: wrapperEl.value
        }, null, 8, ["autoExpand", "autoHide", "autoHideDelay", "fixedThumb", "type", "scrollInfo", "thumbWidth", "wrapperEl"]);
      }), 64))
    ], 6);
  };
}
var _sfc_main = defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: {
    wrapperClass: null,
    wrapperStyle: null,
    contentClass: null,
    contentStyle: null,
    direction: { default: "vertical" },
    thumbMinSize: { default: 48 },
    thumbMaxSize: { default: Infinity },
    thumbWidth: { default: 12 },
    autoHide: { default: true },
    autoHideDelay: { default: 900 },
    autoExpand: { default: true },
    fixedThumb: null,
    throttleType: { default: "debounce" },
    throttleWait: { default: 333 },
    simulateScroll: null
  },
  emits: ["wrapperResize", "contentResize", "scroll"],
  setup
}));
export {
  _sfc_main as default
};
//# sourceMappingURL=custom-vue-scrollbar.js.map
