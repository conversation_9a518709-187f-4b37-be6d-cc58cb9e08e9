'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
require('../../../mixins/index.js');
var buildHelper = require('../../../utils/buildHelper.js');
var useRegister = require('../../../mixins/useRegister.js');

var script = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "ElAmapControlControlBar",
    inheritAttrs: false
  },
  __name: "ControlBar",
  props: buildHelper.buildProps({
    // 控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角
    position: {
      type: [String, Object]
    },
    // 相对于地图容器左上角的偏移量，正数代表向右下偏移。默认为AMap.Pixel(10,10)
    offset: {
      type: Array
    },
    // 是否显示倾斜、旋转按钮。默认为 true
    showControlButton: {
      type: Boolean,
      default: true
    }
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister.useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        parentComponent.plugin(["AMap.ControlBar"], () => {
          $amapComponent = new AMap.ControlBar(options);
          parentComponent.addControl($amapComponent);
          resolve($amapComponent);
        });
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
            parentInstance == null ? void 0 : parentInstance.$amapComponent.removeControl($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div");
    };
  }
});

exports.default = script;
//# sourceMappingURL=ControlBar.vue2.js.map
