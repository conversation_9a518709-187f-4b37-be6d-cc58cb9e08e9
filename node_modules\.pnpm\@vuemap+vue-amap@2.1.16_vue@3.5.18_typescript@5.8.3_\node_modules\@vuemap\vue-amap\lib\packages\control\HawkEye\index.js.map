{"version": 3, "file": "index.js", "sources": ["../../../../../packages/control/HawkEye/index.ts"], "sourcesContent": ["import HawkEye from './HawkEye.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nHawkEye.install = (app: App) => {\r\n  app.component(HawkEye.name, HawkEye);\r\n  return app;\r\n};\r\nexport const ElAmapControlHawkEye = HawkEye as typeof HawkEye & Plugin;\r\nexport default ElAmapControlHawkEye;\r\n\r\nexport type ElAmapControlHawkEyeInstance = InstanceType<typeof HawkEye>\r\n"], "names": ["HawkEye"], "mappings": ";;;;;;;AAEAA,mDAAQ,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AAC9B,EAAI,GAAA,CAAA,SAAA,CAAUA,mDAAQ,CAAA,IAAA,EAAMA,mDAAO,CAAA,CAAA;AACnC,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AACO,MAAM,oBAAuB,GAAAA;;;;;"}