{"version": 3, "file": "GPSUtil.mjs", "sources": ["../../../utils/GPSUtil.ts"], "sourcesContent": ["//坐标转换\r\n// eslint-disable-next-line @typescript-eslint/no-loss-of-precision\r\nconst pi = 3.1415926535897932384626;\r\nconst a = 6378245.0;\r\n// eslint-disable-next-line @typescript-eslint/no-loss-of-precision\r\nconst ee = 0.00669342162296594323;\r\nconst x_pi = pi * 3000.0 / 180.0;\r\nconst R = 6378137;\r\n\r\nexport type ResultLngLat = {\r\n    lng: number\r\n    lat: number\r\n}\r\n\r\n//经纬度转xyz协议瓦片编号\r\nexport function lonLatToTileNumbers (lon_deg: number, lat_deg: number, zoom: number): [number, number] {\r\n    const lat_rad = (pi/180)*lat_deg;     //math.radians(lat_deg)  角度转弧度\r\n    const n = Math.pow(2, zoom);\r\n    const xtile = Math.floor((lon_deg + 180.0) / 360.0 * n);\r\n    const ytile = Math.floor((1.0 - Math.asinh(Math.tan(lat_rad)) / pi) / 2.0 * n);\r\n    return [xtile, ytile];\r\n}\r\n\r\n//xyz协议瓦片编号转经纬度\r\nexport function tileNumbersToLonLat (xtile: number, ytile: number, zoom: number): [number, number] {\r\n    const n = Math.pow(2, zoom);\r\n    const lon_deg = xtile / n * 360.0 - 180.0;\r\n    const lat_rad = Math.atan(Math.sinh(pi * (1 - 2 * ytile / n)));\r\n\r\n    const lat_deg = lat_rad * 180.0 / pi;\r\n    return [lon_deg, lat_deg];\r\n}\r\n\r\n/**百度转84*/\r\nexport function bd09_To_gps84 (lng: number, lat: number) {\r\n    const gcj02 = bd09_To_gcj02(lng, lat);\r\n    const map84 = gcj02_To_gps84(gcj02.lng, gcj02.lat);\r\n    return map84;\r\n}\r\n/**84转百度*/\r\nexport function gps84_To_bd09 (lng: number, lat: number): ResultLngLat {\r\n    const gcj02 = gps84_To_gcj02(lng, lat);\r\n    const bd09 = gcj02_To_bd09(gcj02.lng, gcj02.lat);\r\n    return bd09;\r\n}\r\n/**84转火星*/\r\nexport function gps84_To_gcj02 (lng: number, lat: number): ResultLngLat {\r\n    let dLat = transformLat(lng - 105.0, lat - 35.0);\r\n    let dLng = transformLng(lng - 105.0, lat - 35.0);\r\n    const radLat = lat / 180.0 * pi;\r\n    let magic = Math.sin(radLat);\r\n    magic = 1 - ee * magic * magic;\r\n    const sqrtMagic = Math.sqrt(magic);\r\n    dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);\r\n    dLng = (dLng * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);\r\n    const mgLat = lat + dLat;\r\n    const mgLng = lng + dLng;\r\n    const newCoord = {\r\n        lng: mgLng,\r\n        lat: mgLat\r\n    };\r\n    return newCoord;\r\n}\r\n/**火星转84*/\r\nexport function gcj02_To_gps84 (lng: number, lat: number): ResultLngLat {\r\n    const coord = transform(lng, lat);\r\n    const lontitude = lng * 2 - coord.lng;\r\n    const latitude = lat * 2 - coord.lat;\r\n    const newCoord = {\r\n        lng: lontitude,\r\n        lat: latitude\r\n    };\r\n    return newCoord;\r\n}\r\n/**火星转百度*/\r\nexport function gcj02_To_bd09 (x: number, y: number): ResultLngLat {\r\n    const z = Math.sqrt(x * x + y * y) + 0.00002 * Math.sin(y * x_pi);\r\n    const theta = Math.atan2(y, x) + 0.000003 * Math.cos(x * x_pi);\r\n    const bd_lng = z * Math.cos(theta) + 0.0065;\r\n    const bd_lat = z * Math.sin(theta) + 0.006;\r\n    const newCoord = {\r\n        lng: bd_lng,\r\n        lat: bd_lat\r\n    };\r\n    return newCoord;\r\n}\r\n/**百度转火星*/\r\nexport function bd09_To_gcj02 (bd_lng: number, bd_lat: number): ResultLngLat {\r\n    const x = bd_lng - 0.0065;\r\n    const y = bd_lat - 0.006;\r\n    const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);\r\n    const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);\r\n    const gg_lng = z * Math.cos(theta);\r\n    const gg_lat = z * Math.sin(theta);\r\n    const newCoord = {\r\n        lng: gg_lng,\r\n        lat: gg_lat\r\n    };\r\n    return newCoord;\r\n}\r\n\r\n\r\n\r\nfunction transform (lng: number, lat: number): ResultLngLat {\r\n    let dLat = transformLat(lng - 105.0, lat - 35.0);\r\n    let dLng = transformLng(lng - 105.0, lat - 35.0);\r\n    const radLat = lat / 180.0 * pi;\r\n    let magic = Math.sin(radLat);\r\n    magic = 1 - ee * magic * magic;\r\n    const sqrtMagic = Math.sqrt(magic);\r\n    dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * pi);\r\n    dLng = (dLng * 180.0) / (a / sqrtMagic * Math.cos(radLat) * pi);\r\n    const mgLat = lat + dLat;\r\n    const mgLng = lng + dLng;\r\n    const newCoord = {\r\n        lng: mgLng,\r\n        lat: mgLat\r\n    };\r\n    return newCoord;\r\n}\r\n\r\nfunction transformLat (x: number, y: number) {\r\n    let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));\r\n    ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;\r\n    ret += (20.0 * Math.sin(y * pi) + 40.0 * Math.sin(y / 3.0 * pi)) * 2.0 / 3.0;\r\n    ret += (160.0 * Math.sin(y / 12.0 * pi) + 320 * Math.sin(y * pi / 30.0)) * 2.0 / 3.0;\r\n    return ret;\r\n}\r\n\r\nfunction transformLng (x: number, y: number) {\r\n    let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));\r\n    ret += (20.0 * Math.sin(6.0 * x * pi) + 20.0 * Math.sin(2.0 * x * pi)) * 2.0 / 3.0;\r\n    ret += (20.0 * Math.sin(x * pi) + 40.0 * Math.sin(x / 3.0 * pi)) * 2.0 / 3.0;\r\n    ret += (150.0 * Math.sin(x / 12.0 * pi) + 300.0 * Math.sin(x / 30.0 * pi)) * 2.0 / 3.0;\r\n    return ret;\r\n}\r\n"], "names": [], "mappings": "AAEA,MAAM,EAAK,GAAA,iBAAA,CAAA;AACX,MAAM,CAAI,GAAA,OAAA,CAAA;AAEV,MAAM,EAAK,GAAA,oBAAA,CAAA;AACX,MAAM,IAAA,GAAO,KAAK,GAAS,GAAA,GAAA,CAAA;AAC3B,MAAM,CAAI,GAAA,OAAA,CAAA;AAQM,SAAA,mBAAA,CAAqB,OAAiB,EAAA,OAAA,EAAiB,IAAgC,EAAA;AACnG,EAAM,MAAA,OAAA,GAAW,KAAG,GAAK,GAAA,OAAA,CAAA;AACzB,EAAA,MAAM,CAAI,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,EAAG,IAAI,CAAA,CAAA;AAC1B,EAAA,MAAM,QAAQ,IAAK,CAAA,KAAA,CAAA,CAAO,OAAU,GAAA,GAAA,IAAS,MAAQ,CAAC,CAAA,CAAA;AACtD,EAAA,MAAM,KAAQ,GAAA,IAAA,CAAK,KAAO,CAAA,CAAA,CAAA,GAAM,IAAK,CAAA,KAAA,CAAM,IAAK,CAAA,GAAA,CAAI,OAAO,CAAC,CAAI,GAAA,EAAA,IAAM,IAAM,CAAC,CAAA,CAAA;AAC7E,EAAO,OAAA,CAAC,OAAO,KAAK,CAAA,CAAA;AACxB,CAAA;AAGgB,SAAA,mBAAA,CAAqB,KAAe,EAAA,KAAA,EAAe,IAAgC,EAAA;AAC/F,EAAA,MAAM,CAAI,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,EAAG,IAAI,CAAA,CAAA;AAC1B,EAAM,MAAA,OAAA,GAAU,KAAQ,GAAA,CAAA,GAAI,GAAQ,GAAA,GAAA,CAAA;AACpC,EAAM,MAAA,OAAA,GAAU,IAAK,CAAA,IAAA,CAAK,IAAK,CAAA,IAAA,CAAK,MAAM,CAAI,GAAA,CAAA,GAAI,KAAQ,GAAA,CAAA,CAAE,CAAC,CAAA,CAAA;AAE7D,EAAM,MAAA,OAAA,GAAU,UAAU,GAAQ,GAAA,EAAA,CAAA;AAClC,EAAO,OAAA,CAAC,SAAS,OAAO,CAAA,CAAA;AAC5B,CAAA;AAGgB,SAAA,aAAA,CAAe,KAAa,GAAa,EAAA;AACrD,EAAM,MAAA,KAAA,GAAQ,aAAc,CAAA,GAAA,EAAK,GAAG,CAAA,CAAA;AACpC,EAAA,MAAM,KAAQ,GAAA,cAAA,CAAe,KAAM,CAAA,GAAA,EAAK,MAAM,GAAG,CAAA,CAAA;AACjD,EAAO,OAAA,KAAA,CAAA;AACX,CAAA;AAEgB,SAAA,aAAA,CAAe,KAAa,GAA2B,EAAA;AACnE,EAAM,MAAA,KAAA,GAAQ,cAAe,CAAA,GAAA,EAAK,GAAG,CAAA,CAAA;AACrC,EAAA,MAAM,IAAO,GAAA,aAAA,CAAc,KAAM,CAAA,GAAA,EAAK,MAAM,GAAG,CAAA,CAAA;AAC/C,EAAO,OAAA,IAAA,CAAA;AACX,CAAA;AAEgB,SAAA,cAAA,CAAgB,KAAa,GAA2B,EAAA;AACpE,EAAA,IAAI,IAAO,GAAA,YAAA,CAAa,GAAM,GAAA,GAAA,EAAO,MAAM,EAAI,CAAA,CAAA;AAC/C,EAAA,IAAI,IAAO,GAAA,YAAA,CAAa,GAAM,GAAA,GAAA,EAAO,MAAM,EAAI,CAAA,CAAA;AAC/C,EAAM,MAAA,MAAA,GAAS,MAAM,GAAQ,GAAA,EAAA,CAAA;AAC7B,EAAI,IAAA,KAAA,GAAQ,IAAK,CAAA,GAAA,CAAI,MAAM,CAAA,CAAA;AAC3B,EAAQ,KAAA,GAAA,CAAA,GAAI,KAAK,KAAQ,GAAA,KAAA,CAAA;AACzB,EAAM,MAAA,SAAA,GAAY,IAAK,CAAA,IAAA,CAAK,KAAK,CAAA,CAAA;AACjC,EAAA,IAAA,GAAQ,OAAO,GAAW,IAAA,CAAA,IAAK,CAAI,GAAA,EAAA,CAAA,IAAQ,QAAQ,SAAa,CAAA,GAAA,EAAA,CAAA,CAAA;AAChE,EAAA,IAAA,GAAQ,OAAO,GAAU,IAAA,CAAA,GAAI,YAAY,IAAK,CAAA,GAAA,CAAI,MAAM,CAAI,GAAA,EAAA,CAAA,CAAA;AAC5D,EAAA,MAAM,QAAQ,GAAM,GAAA,IAAA,CAAA;AACpB,EAAA,MAAM,QAAQ,GAAM,GAAA,IAAA,CAAA;AACpB,EAAA,MAAM,QAAW,GAAA;AAAA,IACb,GAAK,EAAA,KAAA;AAAA,IACL,GAAK,EAAA,KAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA,QAAA,CAAA;AACX,CAAA;AAEgB,SAAA,cAAA,CAAgB,KAAa,GAA2B,EAAA;AACpE,EAAM,MAAA,KAAA,GAAQ,SAAU,CAAA,GAAA,EAAK,GAAG,CAAA,CAAA;AAChC,EAAM,MAAA,SAAA,GAAY,GAAM,GAAA,CAAA,GAAI,KAAM,CAAA,GAAA,CAAA;AAClC,EAAM,MAAA,QAAA,GAAW,GAAM,GAAA,CAAA,GAAI,KAAM,CAAA,GAAA,CAAA;AACjC,EAAA,MAAM,QAAW,GAAA;AAAA,IACb,GAAK,EAAA,SAAA;AAAA,IACL,GAAK,EAAA,QAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA,QAAA,CAAA;AACX,CAAA;AAEgB,SAAA,aAAA,CAAe,GAAW,CAAyB,EAAA;AAC/D,EAAA,MAAM,CAAI,GAAA,IAAA,CAAK,IAAK,CAAA,CAAA,GAAI,CAAI,GAAA,CAAA,GAAI,CAAC,CAAA,GAAI,IAAU,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,IAAI,CAAA,CAAA;AAChE,EAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,KAAA,CAAM,CAAG,EAAA,CAAC,IAAI,IAAW,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,IAAI,CAAA,CAAA;AAC7D,EAAA,MAAM,MAAS,GAAA,CAAA,GAAI,IAAK,CAAA,GAAA,CAAI,KAAK,CAAI,GAAA,KAAA,CAAA;AACrC,EAAA,MAAM,MAAS,GAAA,CAAA,GAAI,IAAK,CAAA,GAAA,CAAI,KAAK,CAAI,GAAA,IAAA,CAAA;AACrC,EAAA,MAAM,QAAW,GAAA;AAAA,IACb,GAAK,EAAA,MAAA;AAAA,IACL,GAAK,EAAA,MAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA,QAAA,CAAA;AACX,CAAA;AAEgB,SAAA,aAAA,CAAe,QAAgB,MAA8B,EAAA;AACzE,EAAA,MAAM,IAAI,MAAS,GAAA,KAAA,CAAA;AACnB,EAAA,MAAM,IAAI,MAAS,GAAA,IAAA,CAAA;AACnB,EAAA,MAAM,CAAI,GAAA,IAAA,CAAK,IAAK,CAAA,CAAA,GAAI,CAAI,GAAA,CAAA,GAAI,CAAC,CAAA,GAAI,IAAU,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,IAAI,CAAA,CAAA;AAChE,EAAM,MAAA,KAAA,GAAQ,IAAK,CAAA,KAAA,CAAM,CAAG,EAAA,CAAC,IAAI,IAAW,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,IAAI,CAAA,CAAA;AAC7D,EAAA,MAAM,MAAS,GAAA,CAAA,GAAI,IAAK,CAAA,GAAA,CAAI,KAAK,CAAA,CAAA;AACjC,EAAA,MAAM,MAAS,GAAA,CAAA,GAAI,IAAK,CAAA,GAAA,CAAI,KAAK,CAAA,CAAA;AACjC,EAAA,MAAM,QAAW,GAAA;AAAA,IACb,GAAK,EAAA,MAAA;AAAA,IACL,GAAK,EAAA,MAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA,QAAA,CAAA;AACX,CAAA;AAIA,SAAS,SAAA,CAAW,KAAa,GAA2B,EAAA;AACxD,EAAA,IAAI,IAAO,GAAA,YAAA,CAAa,GAAM,GAAA,GAAA,EAAO,MAAM,EAAI,CAAA,CAAA;AAC/C,EAAA,IAAI,IAAO,GAAA,YAAA,CAAa,GAAM,GAAA,GAAA,EAAO,MAAM,EAAI,CAAA,CAAA;AAC/C,EAAM,MAAA,MAAA,GAAS,MAAM,GAAQ,GAAA,EAAA,CAAA;AAC7B,EAAI,IAAA,KAAA,GAAQ,IAAK,CAAA,GAAA,CAAI,MAAM,CAAA,CAAA;AAC3B,EAAQ,KAAA,GAAA,CAAA,GAAI,KAAK,KAAQ,GAAA,KAAA,CAAA;AACzB,EAAM,MAAA,SAAA,GAAY,IAAK,CAAA,IAAA,CAAK,KAAK,CAAA,CAAA;AACjC,EAAA,IAAA,GAAQ,OAAO,GAAW,IAAA,CAAA,IAAK,CAAI,GAAA,EAAA,CAAA,IAAQ,QAAQ,SAAa,CAAA,GAAA,EAAA,CAAA,CAAA;AAChE,EAAA,IAAA,GAAQ,OAAO,GAAU,IAAA,CAAA,GAAI,YAAY,IAAK,CAAA,GAAA,CAAI,MAAM,CAAI,GAAA,EAAA,CAAA,CAAA;AAC5D,EAAA,MAAM,QAAQ,GAAM,GAAA,IAAA,CAAA;AACpB,EAAA,MAAM,QAAQ,GAAM,GAAA,IAAA,CAAA;AACpB,EAAA,MAAM,QAAW,GAAA;AAAA,IACb,GAAK,EAAA,KAAA;AAAA,IACL,GAAK,EAAA,KAAA;AAAA,GACT,CAAA;AACA,EAAO,OAAA,QAAA,CAAA;AACX,CAAA;AAEA,SAAS,YAAA,CAAc,GAAW,CAAW,EAAA;AACzC,EAAA,IAAI,MAAM,CAAS,GAAA,GAAA,CAAA,GAAM,IAAI,CAAM,GAAA,CAAA,GAAI,MAAM,CAAI,GAAA,CAAA,GAAI,GAAM,GAAA,CAAA,GAAI,IAAI,GAAM,GAAA,IAAA,CAAK,KAAK,IAAK,CAAA,GAAA,CAAI,CAAC,CAAC,CAAA,CAAA;AAC9F,EAAA,GAAA,IAAA,CAAQ,EAAO,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAM,IAAI,EAAE,CAAA,GAAI,EAAO,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAM,CAAI,GAAA,EAAE,KAAK,CAAM,GAAA,CAAA,CAAA;AAC/E,EAAA,GAAA,IAAA,CAAQ,EAAO,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,EAAE,CAAA,GAAI,EAAO,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,CAAM,GAAA,EAAE,KAAK,CAAM,GAAA,CAAA,CAAA;AACzE,EAAA,GAAA,IAAA,CAAQ,GAAQ,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,KAAO,EAAE,CAAA,GAAI,GAAM,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,EAAK,GAAA,EAAI,KAAK,CAAM,GAAA,CAAA,CAAA;AACjF,EAAO,OAAA,GAAA,CAAA;AACX,CAAA;AAEA,SAAS,YAAA,CAAc,GAAW,CAAW,EAAA;AACzC,EAAA,IAAI,MAAM,GAAQ,GAAA,CAAA,GAAI,CAAM,GAAA,CAAA,GAAI,MAAM,CAAI,GAAA,CAAA,GAAI,GAAM,GAAA,CAAA,GAAI,IAAI,GAAM,GAAA,IAAA,CAAK,KAAK,IAAK,CAAA,GAAA,CAAI,CAAC,CAAC,CAAA,CAAA;AACvF,EAAA,GAAA,IAAA,CAAQ,EAAO,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAM,IAAI,EAAE,CAAA,GAAI,EAAO,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAM,CAAI,GAAA,EAAE,KAAK,CAAM,GAAA,CAAA,CAAA;AAC/E,EAAA,GAAA,IAAA,CAAQ,EAAO,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,EAAE,CAAA,GAAI,EAAO,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,CAAM,GAAA,EAAE,KAAK,CAAM,GAAA,CAAA,CAAA;AACzE,EAAA,GAAA,IAAA,CAAQ,GAAQ,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,KAAO,EAAE,CAAA,GAAI,GAAQ,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,GAAI,EAAO,GAAA,EAAE,KAAK,CAAM,GAAA,CAAA,CAAA;AACnF,EAAO,OAAA,GAAA,CAAA;AACX;;;;"}