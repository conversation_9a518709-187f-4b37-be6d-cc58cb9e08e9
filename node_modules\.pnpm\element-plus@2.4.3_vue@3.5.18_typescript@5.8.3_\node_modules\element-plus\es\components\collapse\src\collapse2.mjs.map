{"version": 3, "file": "collapse2.mjs", "sources": ["../../../../../../packages/components/collapse/src/collapse.vue"], "sourcesContent": ["<template>\n  <div :class=\"rootKls\">\n    <slot />\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { collapseEmits, collapseProps } from './collapse'\nimport { useCollapse, useCollapseDOM } from './use-collapse'\n\ndefineOptions({\n  name: 'ElCollapse',\n})\nconst props = defineProps(collapseProps)\nconst emit = defineEmits(collapseEmits)\n\nconst { activeNames, setActiveNames } = useCollapse(props, emit)\n\nconst { rootKls } = useCollapseDOM()\n\ndefineExpose({\n  /** @description active names */\n  activeNames,\n  /** @description set active names */\n  setActiveNames,\n})\n</script>\n"], "names": [], "mappings": ";;;;;mCAUc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,EAAE,WAAA,EAAa,cAAmB,EAAA,GAAA,WAAA,CAAY,OAAO,IAAI,CAAA,CAAA;AAE/D,IAAM,MAAA,EAAE,YAAY,cAAe,EAAA,CAAA;AAEnC,IAAa,MAAA,CAAA;AAAA,MAEX,WAAA;AAAA,MAEA,cAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;"}