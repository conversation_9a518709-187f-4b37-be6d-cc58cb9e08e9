{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/collapse-transition/index.ts"], "sourcesContent": ["import CollapseTransition from './src/collapse-transition.vue'\nimport type { App } from 'vue'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nCollapseTransition.install = (app: App): void => {\n  app.component(CollapseTransition.name, CollapseTransition)\n}\n\nconst _CollapseTransition = CollapseTransition as SFCWithInstall<\n  typeof CollapseTransition\n>\n\nexport default _CollapseTransition\nexport const ElCollapseTransition = _CollapseTransition\n"], "names": [], "mappings": ";;AACA,kBAAkB,CAAC,OAAO,GAAG,CAAC,GAAG,KAAK;AACtC,EAAE,GAAG,CAAC,SAAS,CAAC,kBAAkB,CAAC,IAAI,EAAE,kBAAkB,CAAC,CAAC;AAC7D,CAAC,CAAC;AACG,MAAC,mBAAmB,GAAG,mBAAmB;AAEnC,MAAC,oBAAoB,GAAG;;;;"}