{"version": 3, "file": "make-installer.mjs", "sources": ["../../../utils/make-installer.ts"], "sourcesContent": ["import type { App, Plugin } from 'vue';\r\n\r\nexport const makeInstaller = (components: Plugin[] = []) => {\r\n  const apps: App[] = [];\r\n\r\n  const install = (app: App) => {\r\n\r\n    if (apps.includes(app)) return;\r\n    apps.push(app);\r\n\r\n    components.forEach((c) => app.use(c));\r\n  };\r\n\r\n  return {\r\n    install,\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAEO,MAAM,aAAgB,GAAA,CAAC,UAAuB,GAAA,EAAO,KAAA;AAC1D,EAAA,MAAM,OAAc,EAAC,CAAA;AAErB,EAAM,MAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AAE5B,IAAI,IAAA,IAAA,CAAK,SAAS,GAAG,CAAA;AAAG,MAAA,OAAA;AACxB,IAAA,IAAA,CAAK,KAAK,GAAG,CAAA,CAAA;AAEb,IAAA,UAAA,CAAW,QAAQ,CAAC,CAAA,KAAM,GAAI,CAAA,GAAA,CAAI,CAAC,CAAC,CAAA,CAAA;AAAA,GACtC,CAAA;AAEA,EAAO,OAAA;AAAA,IACL,OAAA;AAAA,GACF,CAAA;AACF;;;;"}