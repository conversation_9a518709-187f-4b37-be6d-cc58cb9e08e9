/**
 * useSpeciesSearch Hook 使用示例（简化版）
 * 
 * 这个文件展示了如何使用简化后的 useSpeciesSearch hook
 * 用于人工鉴定场景下的物种搜索（不包含事件类型过滤）
 */

import { useSpeciesSearch } from './useSpeciesSearch';

// 使用示例
export function exampleUsage() {
  const {
    // 状态
    allSpecies,
    searchLoading,
    searchError,
    hasValidData,
    isCacheExpired,

    // 方法
    searchSpecies,
    fetchAllSpecies,
    ensureSpeciesData,
    getAllSpeciesNames,
    clearCache,
    resetSearchState,
    refreshData,
  } = useSpeciesSearch();

  // 示例1: 基础搜索
  const basicSearch = async () => {
    try {
      const results = await searchSpecies('老虎');
      console.log('搜索结果:', results);
    } catch (error) {
      console.error('搜索失败:', error);
    }
  };

  // 示例2: 搜索不同类型的物种
  const searchDifferentSpecies = async () => {
    try {
      const birdResults = await searchSpecies('鸟');
      console.log('鸟类搜索结果:', birdResults);

      const plantResults = await searchSpecies('松');
      console.log('植物搜索结果:', plantResults);

      const animalResults = await searchSpecies('虎');
      console.log('动物搜索结果:', animalResults);
    } catch (error) {
      console.error('搜索失败:', error);
    }
  };

  // 示例3: 获取所有物种名称
  const getAllNames = async () => {
    try {
      const names = await getAllSpeciesNames();
      console.log('所有物种名称:', names);
    } catch (error) {
      console.error('获取物种名称失败:', error);
    }
  };

  return {
    basicSearch,
    searchDifferentSpecies,
    getAllNames,
  };
}

// Vue 组件中的使用示例（简化版）
export const vueComponentExample = `
<template>
  <div>
    <el-input 
      v-model="searchTerm" 
      placeholder="请输入物种名称"
      @input="handleSearch"
    />
    
    <div v-if="searchLoading">搜索中...</div>
    <div v-if="searchError">{{ searchError }}</div>
    
    <ul v-if="searchResults.length > 0">
      <li v-for="species in searchResults" :key="species.name">
        {{ species.name }} {{ species.alias ? '(' + species.alias + ')' : '' }}
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useSpeciesSearch } from '/@/hooks/useSpeciesSearch';

const searchTerm = ref('');
const searchResults = ref([]);

const {
  searchLoading,
  searchError,
  searchSpecies,
} = useSpeciesSearch();

const handleSearch = async (value) => {
  if (!value || value.trim().length === 0) {
    searchResults.value = [];
    return;
  }
  
  try {
    const results = await searchSpecies(value);
    searchResults.value = results;
  } catch (error) {
    console.error('搜索失败:', error);
    searchResults.value = [];
  }
};
</script>
`;

// 人工鉴定场景的使用示例（简化版）
export const manualIdentificationExample = `
// 在人工鉴定组件中使用
<template>
  <div class="manual-identification">
    <el-select
      v-model="selectedSpecies"
      filterable
      remote
      reserve-keyword
      placeholder="请输入物种名称进行搜索"
      :remote-method="handleRemoteSearch"
      :loading="searchLoading"
    >
      <el-option
        v-for="species in speciesOptions"
        :key="species.name"
        :label="species.name"
        :value="species.name"
      >
        <span>{{ species.name }}</span>
        <span v-if="species.alias" style="color: #8492a6; font-size: 13px">
          ({{ species.alias }})
        </span>
      </el-option>
    </el-select>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useSpeciesSearch } from '/@/hooks/useSpeciesSearch';

const selectedSpecies = ref('');
const speciesOptions = ref([]);

const {
  searchLoading,
  searchSpecies,
} = useSpeciesSearch();

const handleRemoteSearch = async (query) => {
  if (!query) {
    speciesOptions.value = [];
    return;
  }
  
  try {
    const results = await searchSpecies(query);
    speciesOptions.value = results;
  } catch (error) {
    console.error('远程搜索失败:', error);
    speciesOptions.value = [];
  }
};
</script>
`;
