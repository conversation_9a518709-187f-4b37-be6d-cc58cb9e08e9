---
name: 🐛 Bug Report
about: Submit a bug report to help us improve
labels: 'bug, needs triage'
---

<!--

  ! PLEASE HELP US HELP YOU !

  Bugs are fixed faster if you include:
  - a repro repository to inspect the code
  - an url to see the problem live

-->

## 🐛 Bug Report

> Fork this [JSFiddle](https://jsfiddle.net/zenorocha/5kk0eysw/) and reproduce your issue.

(A clear and concise description of what the issue is.)

### Have you read the [Contributing Guidelines on issues](https://github.com/zenorocha/clipboard.js/blob/master/contributing.md)?

(Write your answer here.)

### Expected Behaviour

<!--
  How did you expect your project to behave?
  It’s fine if you’re not sure your understanding is correct.
  Write down what you thought would happen.
-->

I thought that by going to the page '...' and pressing the button '...' then '...' would happen.

_Tip: Try to use screenshots, gifs, videos, always remember people better understand with a visual way._

### Actual Behaviour

Instead of '...', what I saw was that '...' happened instead.

### To Reproduce

(Write your steps such as:)

1. Step 1...
1. Step 2...
1. Step 3...

### Browsers Affected

I tested on all major browsers and only IE 11 does not work.

### Operational System

(Place here your Operational System.)
