/// <reference types="@vuemap/amap-jsapi-types" />
declare const _default: import("vue").DefineComponent<{
    defaultType: {
        type: NumberConstructor;
    };
    showTraffic: {
        type: BooleanConstructor;
        default: boolean;
    };
    showRoad: {
        type: BooleanConstructor;
        default: boolean;
    };
} & {
    visible: import("../../../utils/buildHelper").IPropOptions<boolean>;
    zIndex: import("../../../utils/buildHelper").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils/buildHelper").IPropOptions<boolean>;
    extraOptions: import("../../../utils/buildHelper").IPropOptions<any>;
}, {
    emits: (event: "init", ...args: any[]) => void;
    $amapComponent: AMap.MapType;
    $$getInstance: () => AMap.MapType;
    parentInstance: import("../../../mixins").IProvideType | undefined;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], "init", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    defaultType: {
        type: NumberConstructor;
    };
    showTraffic: {
        type: BooleanConstructor;
        default: boolean;
    };
    showRoad: {
        type: BooleanConstructor;
        default: boolean;
    };
} & {
    visible: import("../../../utils/buildHelper").IPropOptions<boolean>;
    zIndex: import("../../../utils/buildHelper").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils/buildHelper").IPropOptions<boolean>;
    extraOptions: import("../../../utils/buildHelper").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
}, {
    showTraffic: boolean;
    showRoad: boolean;
}, {}>;
export default _default;
