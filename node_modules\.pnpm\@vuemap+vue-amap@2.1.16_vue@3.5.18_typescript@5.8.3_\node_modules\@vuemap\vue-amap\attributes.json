{"el-amap/view-mode": {"type": "string", "description": "地图视图模式, 默认为‘2D’，可选’3D’，选择‘3D’会显示 3D 地图效果。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/show-label": {"type": "boolean", "description": "是否展示地图文字和 POI 信息。默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/default-cursor": {"type": "string", "description": "地图默认鼠标样式。参数defaultCursor应符合CSS的cursor属性规范。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/is-hotspot": {"type": "boolean", "description": "是否开启地图热点和标注的 hover 效果。PC端默认是true, 移动端默认是 false。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/wall-color": {"type": "string|array", "description": "地图楼块的侧面颜色，示例：'#ffffff' 或者 [255, 0, 0, 1]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/roof-color": {"type": "string|array", "description": "地图楼块的顶面颜色，示例：'#ffffff' 或者 [255, 0, 0, 1]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/show-building-block": {"type": "boolean", "description": "是否展示地图 3D 楼块，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/sky-color": {"type": "string|array", "description": "天空颜色，3D 模式下带有俯仰角时会显示，示例：'#ffffff' 或者 [255, 0, 0, 1]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/web-g-l-params": {"type": "object", "description": "额外配置的WebGL参数 eg: preserveDrawingBuffer。默认 {}\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/touch-zoom": {"type": "boolean", "description": "地图在移动终端上是否可通过多点触控缩放浏览地图，默认为true。关闭手势缩放地图，请设置为false。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/touch-zoom-center": {"type": "number", "description": "可缺省，当touchZoomCenter=1的时候，手机端双指缩放的以地图中心为中心，否则默认以双指中间点为中心。默认：1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/center": {"type": "array", "description": "初始中心经纬度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/zoom": {"type": "number", "description": "地图显示的缩放级别，可以设置为浮点数；若center与level未赋值，地图初始化默认显示用户所在城市范围。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/zooms": {"type": "array", "description": "图显示的缩放级别范围, 默认为 [2, 20] ，取值范围 [2 ~ 30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/rotation": {"type": "number", "description": "地图顺时针旋转角度，取值范围 [0-360] ，默认值：0\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/pitch": {"type": "number", "description": "俯仰角度，默认 0，最大值根据地图当前 zoom 级别不断增大，2D地图下无效 。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/features": {"type": "array", "description": "设置地图上显示的元素种类, 支持'bg'（地图背景）、'point'（POI点）、'road'（道路）、'building'（建筑物），默认值：['bg','point','road','building']\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/layers": {"type": "array", "description": "地图图层数组，数组可以是图层 中的一个或多个，默认为普通二维地图。 当叠加多个 图层 时，普通二维地图需通过实例化一个TileLayer类实现。 如果你希望创建一个默认底图图层，使用 AMap.createDefaultLayer()\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/resize-enable": {"type": "boolean", "description": "是否监控地图容器尺寸变化，默认值为false。此属性可被 setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/drag-enable": {"type": "boolean", "description": "地图是否可通过鼠标拖拽平移, 默认为 true。此属性可被 setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/zoom-enable": {"type": "boolean", "description": "地图是否可缩放，默认值为 true。此属性可被 setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/jog-enable": {"type": "boolean", "description": "地图是否使用缓动效果，默认值为true。此属性可被setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/pitch-enable": {"type": "boolean", "description": "是否允许设置俯仰角度, 3D 视图下为 true, 2D 视图下无效。。此属性可被setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/rotate-enable": {"type": "boolean", "description": "地图是否可旋转, 图默认为true。此属性可被setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/animate-enable": {"type": "boolean", "description": "地图平移过程中是否使用动画（如调用panBy、panTo、setCenter、setZoomAndCenter等函数, 将对地图产生平移操作, 是否使用动画平移的效果）, 默认为true, 即使用动画。此属性可被 setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/keyboard-enable": {"type": "boolean", "description": "地图是否可通过键盘控制, 默认为true, 方向键控制地图平移，\"+\"和\"-\"可以控制地图的缩放, Ctrl+“→”顺时针旋转，Ctrl+“←”逆时针旋转。此属性可被setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/double-click-zoom": {"type": "boolean", "description": "地图是否可通过双击鼠标放大地图, 默认为true。此属性可被setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/scroll-wheel": {"type": "boolean", "description": "地图是否可通过鼠标滚轮缩放浏览，默认为true。此属性可被setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/show-indoor-map": {"type": "boolean", "description": "是否自动展示室内地图，默认是 false。此属性可被setStatus/getStatus 方法控制\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/map-style": {"type": "string", "description": "设置地图显示样式，目前支持normal（默认样式）、dark（深色样式）、light（浅色样式）、fresh(osm清新风格样式)四种\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/label-reject-mask": {"type": "boolean", "description": "文字是否拒绝掩模图层进行掩模\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/mask": {"type": "array", "description": "为 Map 实例指定掩模的路径，各图层将只显示路径范围内图像，3D视图下有效。 格式为一个经纬度的一维、二维或三维数组。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/terrain": {"type": "boolean", "description": "是否开启地形，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes)"}, "el-amap/resize": {"type": "event", "description": "地图容器大小改变事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/complete": {"type": "event", "description": "地图图块加载完成后触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/mapmove": {"type": "event", "description": "地图平移时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/hotspotclick": {"type": "event", "description": "鼠标点击热点时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/hotspotover": {"type": "event", "description": "鼠标滑过热点时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/hotspotout": {"type": "event", "description": "鼠标移出热点时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/movestart": {"type": "event", "description": "地图平移开始时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/moveend": {"type": "event", "description": "地图平移结束后触发。如地图有拖拽缓动效果，则在缓动结束后触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/zoomchange": {"type": "event", "description": "地图缩放级别更改后触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/zoomstart": {"type": "event", "description": "缩放开始时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/zoomend": {"type": "event", "description": "缩放停止时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/mousemove": {"type": "event", "description": "鼠标在地图上移动时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/mousewheel": {"type": "event", "description": "鼠标滚轮开始缩放地图时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/mouseover": {"type": "event", "description": "鼠标移入地图容器内时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/mouseout": {"type": "event", "description": "鼠标移出地图容器时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/mouseup": {"type": "event", "description": "鼠标在地图上单击抬起时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/mousedown": {"type": "event", "description": "鼠标在地图上单击按下时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/rightclick": {"type": "event", "description": "鼠标右键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/dragstart": {"type": "event", "description": "开始拖拽地图时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/dragging": {"type": "event", "description": "拖拽地图过程中触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/dragend": {"type": "event", "description": "停止拖拽地图时触发。如地图有拖拽缓动效果，则在拽停止，缓动开始前触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events)"}, "el-amap-control-control-bar/position": {"type": "string|object", "description": "控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes)"}, "el-amap-control-control-bar/offset": {"type": "string", "description": "地图默认鼠标样式。参数defaultCursor应符合CSS的cursor属性规范。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes)"}, "el-amap-control-control-bar/show-control-button": {"type": "boolean", "description": "是否显示倾斜、旋转按钮。默认为 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes)"}, "el-amap-control-control-bar/visible": {"type": "boolean", "description": "是否显示，默认true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes)"}, "el-amap-control-control-bar/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes)"}, "el-amap-control-control-bar/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes)"}, "el-amap-control-control-bar/show": {"type": "event", "description": "显示时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#events)"}, "el-amap-control-control-bar/hide": {"type": "event", "description": "隐藏时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#events)"}, "el-amap-control-geolocation/position": {"type": "string", "description": "控件停靠位置,默认为\"RB\". 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/offset": {"type": "array", "description": "缩略图距离悬停位置的像素距离，如 [2,2]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/border-color": {"type": "string", "description": "按钮边框颜色值，同CSS，如'silver'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/border-radius": {"type": "string", "description": "按钮圆角边框值，同CSS，如'5px'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/button-size": {"type": "string", "description": "箭头按钮的像素尺寸，同CSS，如'12px'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/convert": {"type": "boolean", "description": "是否将定位结果转换为高德坐标\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/enable-high-accuracy": {"type": "boolean", "description": "进行浏览器原生定位的时候是否尝试获取较高精度，可能影响定位效率，默认为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/timeout": {"type": "number", "description": "定位的超时时间，毫秒\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/maximum-age": {"type": "number", "description": "浏览器原生定位的缓存时间，毫秒\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/show-button": {"type": "boolean", "description": "是否显示定位按钮，默认为true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/show-circle": {"type": "boolean", "description": "是否显示定位精度圆，默认为true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/show-marker": {"type": "boolean", "description": "是否显示定位点，默认为true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/marker-options": {"type": "markeroptions", "description": "定位点的样式\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/circle-options": {"type": "circleoptions", "description": "定位圆的样式\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/pan-to-location": {"type": "boolean", "description": "定位成功后是否自动移动到响应位置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/zoom-to-accuracy": {"type": "boolean", "description": "定位成功后是否自动调整级别\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/geo-location-first": {"type": "boolean", "description": "优先使用H5定位，默认移动端为true，PC端为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/no-ip-locate": {"type": "number", "description": "是否禁用IP精确定位，默认为0，0:都用 1:手机上不用 2:PC上不用 3:都不用\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/no-geo-location": {"type": "number", "description": "是否禁用浏览器原生定位，默认为0，0:都用 1:手机上不用 2:PC上不用 3:都不用\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/use-native": {"type": "boolean", "description": "是否与高德定位SDK能力结合，需要同时使用安卓版高德定位sdk，否则无效\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/get-city-when-fail": {"type": "boolean", "description": "定位失败之后是否返回基本城市定位信息\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/need-address": {"type": "boolean", "description": "是否需要将定位结果进行逆地理编码操作\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/extensions": {"type": "string", "description": "是否需要详细的逆地理编码信息，默认为'base'只返回基本信息，可选'all'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes)"}, "el-amap-control-geolocation/complete": {"type": "event", "description": "定位结束后触发的事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#events)"}, "el-amap-control-hawk-eye/auto-move": {"type": "boolean", "description": "是否随主图视口变化移动\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/show-rectangle": {"type": "boolean", "description": "是否显示视口矩形\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/show-button": {"type": "boolean", "description": "是否显示打开关闭的按钮\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/map-style": {"type": "string", "description": "缩略图要显示的地图自定义样式，如'amap://styles/dark'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/layers": {"type": "array", "description": "缩略图要显示的图层类型，默认为普通矢量地图\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/width": {"type": "string", "description": "缩略图的宽度，同CSS，如'200px'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/height": {"type": "string", "description": "缩略图的高度，同CSS，如'200px'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/offset": {"type": "array", "description": "缩略图距离地图右下角的像素距离，如 [2,2]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/border-style": {"type": "string", "description": "缩略图的边框样式，同CSS，如\"double solid solid double\"\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/border-color": {"type": "string", "description": "缩略图的边框颜色，同CSS，如'silver'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/border-radius": {"type": "string", "description": "缩略图的边框角度，同CSS，如'5px'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/border-width": {"type": "string", "description": "缩略图的边框宽度，同CSS，如'2px'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/button-size": {"type": "string", "description": "箭头按钮的像素尺寸，同CSS，如'12px'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/visible": {"type": "boolean", "description": "是否显示，默认true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/is-open": {"type": "boolean", "description": "默认是否展开\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes)"}, "el-amap-control-hawk-eye/show": {"type": "event", "description": "显示时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#events)"}, "el-amap-control-hawk-eye/hide": {"type": "event", "description": "隐藏时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#events)"}, "el-amap-control-map-type/default-type": {"type": "number", "description": "初始化默认图层类型。 取值为0：默认底图 取值为1：卫星图 默认值：0\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes)"}, "el-amap-control-map-type/show-traffic": {"type": "boolean", "description": "叠加实时交通图层 默认值：false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes)"}, "el-amap-control-map-type/show-road": {"type": "boolean", "description": "叠加路网图层 默认值：false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes)"}, "el-amap-control-map-type/visible": {"type": "boolean", "description": "是否显示，默认true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes)"}, "el-amap-control-map-type/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes)"}, "el-amap-control-map-type/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes)"}, "el-amap-control-map-type/show": {"type": "event", "description": "显示时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#events)"}, "el-amap-control-map-type/hide": {"type": "event", "description": "隐藏时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#events)"}, "el-amap-control-scale/position": {"type": "string|object", "description": "控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes)"}, "el-amap-control-scale/offset": {"type": "string", "description": "地图默认鼠标样式。参数defaultCursor应符合CSS的cursor属性规范。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes)"}, "el-amap-control-scale/animate-enable": {"type": "array", "description": "相对于地图容器左上角的偏移量，正数代表向右下偏移。默认为AMap.Pixel(10,10)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes)"}, "el-amap-control-scale/visible": {"type": "boolean", "description": "是否显示，默认true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes)"}, "el-amap-control-scale/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes)"}, "el-amap-control-scale/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes)"}, "el-amap-control-scale/show": {"type": "event", "description": "显示时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#events)"}, "el-amap-control-scale/hide": {"type": "event", "description": "隐藏时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#events)"}, "el-amap-search-box/datatype": {"type": "string", "description": "返回的数据类型。可选值：all-返回所有数据类型、poi-返回POI数据类型、bus-返回公交站点数据类型、busline-返回公交线路数据类型目前暂时不支持多种类型\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/input": {"type": "string", "description": "可选参数，用来指定一个input输入框，设定之后，在input输入文字将自动生成下拉选择列表。输入框DOM对象的id值\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/output": {"type": "string", "description": "可选参数，指定一个现有的div的id或者元素，作为展示提示结果的容器，当指定了input的时候有效，缺省的时候将自动创建一个显示结果面板\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/out-put-dir-auto": {"type": "boolean", "description": "默认为true，表示是否在input位于页面较下方的时候自动将输入面板显示在input上方以避免被遮挡\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/close-result-on-scroll": {"type": "boolean", "description": "页面滚动时关闭搜索结果列表，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/lang": {"type": "string", "description": "设置检索语言类型，默认中文 'zh_cn'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/visible": {"type": "boolean", "description": "是否显示，默认true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/type": {"type": "string", "description": "输入提示时限定POI类型，多个类型用“\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/city": {"type": "string", "description": "输入提示时限定城市。可选值：城市名（中文或中文全拼）、citycode、adcode；默认值：“全国”\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/citylimit": {"type": "boolean", "description": "是否强制限制在设置的城市内搜索,默认值为：false，true：强制限制设定城市，false：不强制限制设定城市\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/input-custom": {"type": "boolean", "description": "是否自定义input，自定义的时候将使用用户的inputId，默认 false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/placeholder": {"type": "string", "description": "默认输入框的placeholder属性\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/debounce": {"type": "number", "description": "手动加上防抖功能，默认100毫秒\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes)"}, "el-amap-search-box/select": {"type": "event", "description": "鼠标点击或者回车选中某个POI信息时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#events)"}, "el-amap-search-box/choose": {"type": "event", "description": "鼠标或者键盘上下键选择POI信息时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#events)"}, "el-amap-control-tool-bar/position": {"type": "string|object", "description": "控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#attributes)"}, "el-amap-control-tool-bar/offset": {"type": "string", "description": "地图默认鼠标样式。参数defaultCursor应符合CSS的cursor属性规范。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#attributes)"}, "el-amap-control-tool-bar/visible": {"type": "boolean", "description": "是否显示，默认true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#attributes)"}, "el-amap-control-tool-bar/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#attributes)"}, "el-amap-control-tool-bar/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#attributes)"}, "el-amap-control-tool-bar/show": {"type": "event", "description": "显示时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#events)"}, "el-amap-control-tool-bar/hide": {"type": "event", "description": "隐藏时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#events)"}, "el-amap-info-window/is-custom": {"type": "boolean", "description": "是否自定义窗体。设为true时，信息窗体外框及内容完全按照content所设的值添加（默认为false，即在系统默认的信息窗体外框中显示content内容）\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/auto-move": {"type": "boolean", "description": "是否自动调整窗体到视野内（当信息窗体超出视野范围时，通过该属性设置是否自动平移地图，使信息窗体完全显示）\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/avoid": {"type": "boolean", "description": "autoMove 为 true 时，自动平移到视野内后的上右下左的避让宽度。默认值： [20, 20, 20, 20]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/close-when-click-map": {"type": "boolean", "description": "控制是否在鼠标点击地图后关闭信息窗体，默认false，鼠标点击地图后不关闭信息窗体\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/offset": {"type": "array", "description": "信息窗体显示位置偏移量。默认基准点为信息窗体的底部中心。默认值: [0, 0]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/visible": {"type": "boolean", "description": "是否可见，默认 true。支持sync\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/content": {"type": "string|htmlelement", "description": "显示内容，可以是HTML要素字符串或者HTMLElement对象。也可以根据示例中的方式使用slot实现\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/size": {"type": "array", "description": "信息窗体尺寸（isCustom为true时，该属性无效）\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/anchor": {"type": "string", "description": "信息窗体锚点。默认值：'bottom-center'。可选值：'top-left'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/position": {"type": "array", "description": "信息窗体显示基点位置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes)"}, "el-amap-info-window/init": {"type": "event", "description": "组件实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#events)"}, "el-amap-info-window/open": {"type": "event", "description": "信息窗体打开之后触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#events)"}, "el-amap-info-window/close": {"type": "event", "description": "信息窗体关闭之后触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#events)"}, "el-amap-circle-marker/bubble": {"type": "boolean", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上（自v1.3 新增）默认值：false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/center": {"type": "array", "description": "圆心位置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/radius": {"type": "number", "description": "圆半径，单位:px 最大值64\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/cursor": {"type": "string", "description": "指定鼠标悬停时的鼠标样式，自定义cursor，IE仅支持cur/ani/ico格式，Opera不支持自定义cursor\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/visible": {"type": "boolean", "description": "是否可见\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/z-index": {"type": "number", "description": "多边形覆盖物的叠加顺序。地图上存在多个多边形覆盖物叠加时，通过该属性使级别较高的多边形覆盖物在上层显示默认zIndex：10\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/stroke-color": {"type": "string", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/stroke-opacity": {"type": "float", "description": "轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/stroke-weight": {"type": "number", "description": "轮廓线宽度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/fill-color": {"type": "string", "description": "多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/fill-opacity": {"type": "float", "description": "多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.5\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/draggable": {"type": "boolean", "description": "设置多边形是否可拖拽移动，默认为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/ext-data": {"type": "any", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes)"}, "el-amap-circle-marker/init": {"type": "event", "description": "组件实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/rightclick": {"type": "event", "description": "右键单击\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/hide": {"type": "event", "description": "隐藏\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/show": {"type": "event", "description": "显示\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/mousedown": {"type": "event", "description": "鼠标按下\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/mouseup": {"type": "event", "description": "鼠标抬起\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/mouseover": {"type": "event", "description": "鼠标经过\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/mouseout": {"type": "event", "description": "鼠标移出\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-circle-marker/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events)"}, "el-amap-elastic-marker/top-when-click": {"type": "boolean", "description": "鼠标点击时marker是否置顶，默认false ，不置顶\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/bubble": {"type": "boolean", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上, 默认值：false。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/zooms": {"type": "array", "description": "点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/visible": {"type": "boolean", "description": "点标记是否可见，默认为true。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/z-index": {"type": "number", "description": "点标记的叠加顺序。地图上存在多个点标记叠加时，通过该属性使级别较高的点标记在上层显示\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/position": {"type": "array", "description": "点标记在地图上显示的位置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/offset": {"type": "array", "description": "点标记显示位置偏移量，默认值为 [0,0] 。Marker指定position后，默认以marker左上角位置为基准点（若设置了anchor，则以anchor设置位置为基准点），对准所给定的position位置，若需使marker指定位置对准在position处，需根据marker的尺寸设置一定的偏移量。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/draggable": {"type": "boolean", "description": "设置点标记是否可拖拽移动，默认为false。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/cursor": {"type": "string", "description": "指定鼠标悬停时的鼠，默认值：'pointer'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/title": {"type": "string", "description": "鼠标滑过点标记时的文字提示。不设置则鼠标滑过点标无文字提示。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/clickable": {"type": "boolean", "description": "点标记是否可点击，默认值: true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/zoom-style-mapping": {"type": "object", "description": "表示地图级别与styles中样式的映射，{14:0,15:0,16:1,17:1,}表示14到15级使用styles中的第0个样式，16-17级使用第二个样式\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/styles": {"type": "array", "description": "多个不同样式的数组。每个style对象有用两个参数 icon 和 label\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/ext-data": {"type": "any", "description": "用户自定义属 ，支持JavaScript API任意数据类型，如 Marker的id等。可将自定义数据保存在该属性上，方便后续操作使用。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes)"}, "el-amap-elastic-marker/init": {"type": "event", "description": "`AMap.ElasticMarker`实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/rightclick": {"type": "event", "description": "鼠标右键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/mousemove": {"type": "event", "description": "鼠标移动\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/mouseover": {"type": "event", "description": "鼠标移近点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/mouseout": {"type": "event", "description": "鼠标移出点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/mousedown": {"type": "event", "description": "鼠标在点标记上按下时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/mouseup": {"type": "event", "description": "鼠标在点标记上按下后抬起时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/dragstart": {"type": "event", "description": "开始拖拽点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/dragging": {"type": "event", "description": "鼠标拖拽移动点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/dragend": {"type": "event", "description": "点标记拖拽移动结束触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-elastic-marker/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events)"}, "el-amap-label-marker/name": {"type": "string", "description": "标注名称，作为标注标识，并非最终在地图上显示的文字内容，显示文字内容请设置 opts.text.content\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/rank": {"type": "number", "description": "避让优先级，获取标注的优先级，该优先级用于 labelsLayer 支持避让时，rank 值大的标注会避让掉 rank 值低的标注。默认值：1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/visible": {"type": "boolean", "description": "标注是否可见，默认为true。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/z-index": {"type": "number", "description": "标注的叠加顺序。地图上存在多个点标记叠加时，通过该属性使级别较高的点标记在上层显示，默认zIndex：1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/position": {"type": "array", "description": "标注在地图上显示的位置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/zooms": {"type": "array", "description": "标注显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/icon": {"type": "object", "description": "标注图标设置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/text": {"type": "object", "description": "标注文本设置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/ext-data": {"type": "any", "description": "用户自定义属 ，支持JavaScript API任意数据类型，如 Marker的id等。可将自定义数据保存在该属性上，方便后续操作使用。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/rotation": {"type": "number", "description": "旋转角度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes)"}, "el-amap-label-marker/init": {"type": "event", "description": "`AMap.LabelMarker`实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events)"}, "el-amap-label-marker/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events)"}, "el-amap-label-marker/mousemove": {"type": "event", "description": "鼠标移动\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events)"}, "el-amap-label-marker/mouseover": {"type": "event", "description": "鼠标移近点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events)"}, "el-amap-label-marker/mouseout": {"type": "event", "description": "鼠标移出点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events)"}, "el-amap-label-marker/mousedown": {"type": "event", "description": "鼠标在点标记上按下时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events)"}, "el-amap-label-marker/mouseup": {"type": "event", "description": "鼠标在点标记上按下后抬起时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events)"}, "el-amap-label-marker/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events)"}, "el-amap-label-marker/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events)"}, "el-amap-label-marker/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events)"}, "el-amap-marker-cluster/render-cluster-marker": {"type": "function", "description": "该方法用来实现聚合点的自定义绘制，由开发者自己实现，API 将在绘制每个聚合点的时候调用这个方法，可以实现聚合点样式的灵活设定，指定了 renderClusterMarker 后 styles 无效。<br/>该函数的入参为一个Object，包含如下属性：<br/>1. count: 当前聚合点下聚合的 Marker 的数量<br/>2. marker: 当前聚合点显示的 Marker\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes)"}, "el-amap-marker-cluster/render-marker": {"type": "function", "description": "该方法用来实现非聚合点的自定义绘制，由开发者自己实现，API 将在绘制每个非聚合点的时候调用这个方法<br/>该函数的入参为一个Object，包含如下属性：<br/>marker: 非聚合点 Marker 对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes)"}, "el-amap-marker-cluster/cluster-by-zoom-change": {"type": "boolean", "description": "地图缩放过程中是否聚合。默认值 false。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes)"}, "el-amap-marker-cluster/points": {"type": "array", "description": "数据格式为一组含有经纬度信息的数组，如下所示。其中【经纬度】lnglat 为必填字段，【权重】weight 为可选字段,以权重高的点为中心进行聚合。示例: [{\"lnglat\":[\"113.864691\",\"22.942327\"], weight: 1},{\"lnglat\":[\"113.370643\",\"22.938827\"], weight: 8}]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes)"}, "el-amap-marker-cluster/grid-size": {"type": "number", "description": "聚合计算时网格的像素大小，默认60\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes)"}, "el-amap-marker-cluster/max-zoom": {"type": "number", "description": "最大的聚合级别，大于该级别就不进行相应的聚合。默认值为 18，即小于 18 级的级别均进行聚合，18 及以上级别不进行聚合\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes)"}, "el-amap-marker-cluster/average-center": {"type": "boolean", "description": "聚合点的图标位置是否是所有聚合内点的中心点。默认为 true。数据中如果含有权重值，以权重高的点为中心进行聚合\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes)"}, "el-amap-marker-cluster/styles": {"type": "array", "description": "指定聚合后的点标记的图标样式，[查看示例](https://lbs.amap.com/demo/jsapi-v2/example/mass-markers/markerclusterer)，可缺省，缺省时为默认样式<br/>数据元素分别对应聚合量在1-10,11-100,101-1000…的聚合点的样式；<br/>当用户设置聚合样式少于实际叠加的点数，未设置部分按照系统默认样式显示；<br/>单个图标样式包括以下几个属性：<br/>1. {string} url：图标显示图片的url地址（必选）<br/>2. {AMap.Size} size：图标显示图片的大小（必选）<br/>3. {AMap.Pixel} offset：图标定位在地图上的位置相对于图标左上角的偏移值。默认为(0,0),不偏移（可选）<br/>4. {AMap.Pixel} imageOffset：图片相对于可视区域的偏移值，此功能的作用等同CSS中的background-position属性。默认为(0,0)，不偏移（可选）<br/>5. {String} textColor：文字的颜色，默认为\"#000000\"（可选）<br/>6. {Number} textSize：文字的大小，默认为10（可选）\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes)"}, "el-amap-marker-cluster/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes)"}, "el-amap-marker-cluster/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes)"}, "el-amap-marker-cluster/init": {"type": "event", "description": "`AMap.MarkerCluster`实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#events)"}, "el-amap-marker-cluster/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#events)"}, "el-amap-marker/top-when-click": {"type": "boolean", "description": "鼠标点击时marker是否置顶，默认false ，不置顶\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/bubble": {"type": "boolean", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上, 默认值：false。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/zooms": {"type": "array", "description": "点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/anchor": {"type": "string|array", "description": "设置点标记锚点，可选值：'top-left','top-center','top-right', 'middle-left', 'center', 'middle-right', 'bottom-left', 'bottom-center', 'bottom-right'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/visible": {"type": "boolean", "description": "点标记是否可见，默认为true。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/z-index": {"type": "number", "description": "点标记的叠加顺序。地图上存在多个点标记叠加时，通过该属性使级别较高的点标记在上层显示，默认zIndex：12\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/position": {"type": "array", "description": "点标记在地图上显示的位置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/offset": {"type": "array", "description": "点标记显示位置偏移量，默认值为 [0,0] 。Marker指定position后，默认以marker左上角位置为基准点（若设置了anchor，则以anchor设置位置为基准点），对准所给定的position位置，若需使marker指定位置对准在position处，需根据marker的尺寸设置一定的偏移量。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/icon": {"type": "string amap.icon", "description": "在点标记中显示的图标。可以传一个图标地址，也可以传Icon对象。有合法的content内容设置时，此属性无效。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/content": {"type": "string htmlelement", "description": "点标记显示内容。可以是HTML要素字符串或者HTML DOM对象。content有效时，icon属性将被覆盖。 支持slot\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/draggable": {"type": "boolean", "description": "设置点标记是否可拖拽移动，默认为false。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/cursor": {"type": "string", "description": "指定鼠标悬停时的鼠，默认值：'pointer'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/angle": {"type": "number", "description": "点标记的旋转角度，，广泛用于改变车辆行驶方向。默认值：0\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/title": {"type": "string", "description": "鼠标滑过点标记时的文字提示。不设置则鼠标滑过点标无文字提示。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/clickable": {"type": "boolean", "description": "点标记是否可点击，默认值: true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/label": {"type": "{content|offset: [x|y]}", "description": "添加文本标注，content为文本标注的内容，offset为偏移量，为偏移量,如设置了 direction，以 direction 方位为基准点进行偏移。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/ext-data": {"type": "any", "description": "用户自定义属 ，支持JavaScript API任意数据类型，如 Marker的id等。可将自定义数据保存在该属性上，方便后续操作使用。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/move-options": {"type": "object", "description": "marker修改位置时是否使用moveTo方法，使用moveTo可以动画移动，参数: {duration?: number,easing?: (passedTime: number) => number,autoRotation?: boolean}\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes)"}, "el-amap-marker/init": {"type": "event", "description": "`AMap.Marker`实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/rightclick": {"type": "event", "description": "鼠标右键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/mousemove": {"type": "event", "description": "鼠标移动\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/mouseover": {"type": "event", "description": "鼠标移近点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/mouseout": {"type": "event", "description": "鼠标移出点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/mousedown": {"type": "event", "description": "鼠标在点标记上按下时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/mouseup": {"type": "event", "description": "鼠标在点标记上按下后抬起时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/dragstart": {"type": "event", "description": "开始拖拽点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/dragging": {"type": "event", "description": "鼠标拖拽移动点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/dragend": {"type": "event", "description": "点标记拖拽移动结束触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/moving": {"type": "event", "description": "点标记在执行moveTo，moveAlong动画时触发事件，Object对象的格式是{passedPath:Array.}。其中passedPath为对象在moveAlong或者moveTo过程中走过的路径。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/moveend": {"type": "event", "description": "点标记执行moveTo动画结束时触发事件，也可以由moveAlong方法触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/movealong": {"type": "event", "description": "点标记执行moveAlong动画一次后触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-marker/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events)"}, "el-amap-mass-marks/data": {"type": "array", "description": "海量点数据\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes)"}, "el-amap-mass-marks/visible": {"type": "boolean", "description": "点标记是否可见，默认为true。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes)"}, "el-amap-mass-marks/z-index": {"type": "number", "description": "点标记的叠加顺序。地图上存在多个点标记叠加时，通过该属性使级别较高的点标记在上层显示，默认zIndex：12\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes)"}, "el-amap-mass-marks/zooms": {"type": "array", "description": "点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes)"}, "el-amap-mass-marks/cursor": {"type": "string", "description": "指定鼠标悬停时的鼠，默认值：'pointer'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes)"}, "el-amap-mass-marks/styles": {"type": "array|object", "description": "标号样式，可以是Object代表所有点样式一样，也可以是Array，根据各个点的设定来判断选择哪个样式\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes)"}, "el-amap-mass-marks/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes)"}, "el-amap-mass-marks/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes)"}, "el-amap-mass-marks/init": {"type": "event", "description": "`AMap.MassMarks`实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-mass-marks/complete": {"type": "event", "description": "海量点加载完成事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-mass-marks/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-mass-marks/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-mass-marks/mousemove": {"type": "event", "description": "鼠标移动\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-mass-marks/mouseover": {"type": "event", "description": "鼠标移近点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-mass-marks/mouseout": {"type": "event", "description": "鼠标移出点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-mass-marks/mousedown": {"type": "event", "description": "鼠标在点标记上按下时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-mass-marks/mouseup": {"type": "event", "description": "鼠标在点标记上按下后抬起时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-mass-marks/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-mass-marks/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events)"}, "el-amap-text/top-when-click": {"type": "boolean", "description": "鼠标点击时marker是否置顶，默认false ，不置顶\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/bubble": {"type": "boolean", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上, 默认值：false。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/zooms": {"type": "array", "description": "点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/visible": {"type": "boolean", "description": "点标记是否可见，默认为true。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/z-index": {"type": "number", "description": "点标记的叠加顺序。地图上存在多个点标记叠加时，通过该属性使级别较高的点标记在上层显示，默认zIndex：12\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/position": {"type": "array", "description": "点标记在地图上显示的位置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/offset": {"type": "array", "description": "点标记显示位置偏移量，默认值为 [0,0] 。Marker指定position后，默认以marker左上角位置为基准点（若设置了anchor，则以anchor设置位置为基准点），对准所给定的position位置，若需使marker指定位置对准在position处，需根据marker的尺寸设置一定的偏移量。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/text": {"type": "string", "description": "标记显示的文本内容\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/draggable": {"type": "boolean", "description": "设置点标记是否可拖拽移动，默认为false。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/cursor": {"type": "string", "description": "指定鼠标悬停时的鼠，默认值：'pointer'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/angle": {"type": "number", "description": "点标记的旋转角度，，广泛用于改变车辆行驶方向。默认值：0\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/title": {"type": "string", "description": "鼠标滑过点标记时的文字提示。不设置则鼠标滑过点标无文字提示。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/clickable": {"type": "boolean", "description": "点标记是否可点击，默认值: true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/text-style": {"type": "object", "description": "设置文本样式，Object同css样式表，如:{'background-color':'red'}\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/ext-data": {"type": "any", "description": "用户自定义属 ，支持JavaScript API任意数据类型，如 Marker的id等。可将自定义数据保存在该属性上，方便后续操作使用。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes)"}, "el-amap-text/init": {"type": "event", "description": "实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/rightclick": {"type": "event", "description": "鼠标右键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/mousemove": {"type": "event", "description": "鼠标移动\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/mouseover": {"type": "event", "description": "鼠标移近点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/mouseout": {"type": "event", "description": "鼠标移出点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/mousedown": {"type": "event", "description": "鼠标在点标记上按下时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/mouseup": {"type": "event", "description": "鼠标在点标记上按下后抬起时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/dragstart": {"type": "event", "description": "开始拖拽点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/dragging": {"type": "event", "description": "鼠标拖拽移动点标记时触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/dragend": {"type": "event", "description": "点标记拖拽移动结束触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/moving": {"type": "event", "description": "点标记在执行moveTo，moveAlong动画时触发事件，Object对象的格式是{passedPath:Array.}。其中passedPath为对象在moveAlong或者moveTo过程中走过的路径。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/moveend": {"type": "event", "description": "点标记执行moveTo动画结束时触发事件，也可以由moveAlong方法触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/movealong": {"type": "event", "description": "点标记执行moveAlong动画一次后触发事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-text/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events)"}, "el-amap-mouse-tool/type": {"type": "string", "description": "类型，默认 marker，可选值'marker', 'circle', 'rectangle', 'polyline', 'polygon', 'measureArea', 'rule', 'rectZoomIn', 'rectZoomOut'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes)"}, "el-amap-mouse-tool/draw-options": {"type": "object", "description": "设置绘制的图形的属性，可以实时更改，在切换时会生效，默认 null\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes)"}, "el-amap-mouse-tool/auto-clear": {"type": "boolean", "description": "是否自动清除地图上的绘制的图形，默认true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes)"}, "el-amap-mouse-tool/show-tooltip": {"type": "boolean", "description": "是否显示提示信息，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes)"}, "el-amap-mouse-tool/tooltip-text-map": {"type": "object", "description": "是否可见\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes)"}, "el-amap-mouse-tool/text-options": {"type": "object", "description": "提示信息的text的属性\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes)"}, "el-amap-mouse-tool/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes)"}, "el-amap-mouse-tool/init": {"type": "event", "description": "组件实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#events)"}, "el-amap-mouse-tool/draw": {"type": "event", "description": "绘制事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#events)"}, "el-amap-bezier-curve/bubble": {"type": "boolean", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上 默认 false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/edit-options": {"type": "object", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polylineeditor)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/visible": {"type": "boolean", "description": "是否可见\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/editable": {"type": "boolean", "description": "折线当前是否可编辑\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/path": {"type": "array", "description": "贝瑟尔曲线的路径。描述为一个二维数组规则如下：第一个元素是起点， 之后的元素同时描述控制点和途经点，之后每个元素可以有0个到2个控制点 控制点在前，途经点在最后 [ [lng,lat] ,//起点0 [lng,lat,lng,lat,lng,lat] ,//控制点、控制点、途经点2 [lng,lat,lng,lat] //控制点、途经点3 ] 或者 [ [ [lng,lat] ],//起点0 [ [lng,lat] , [lng,lat] ],//控制点、途经点1 [ [lng,lat] , [lng,lat] , [lng,lat] ],//控制点、控制点、途经点2 [ [lng,lat] , [lng,lat] ]//控制点、途经点3 ]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/z-index": {"type": "number", "description": "折线覆盖物的叠加顺序。默认叠加顺序，先添加的线在底层，后添加的线在上层。通过该属性可调整叠加顺序，使级别较高的折线覆盖物在上层显示。默认zIndex：10\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/stroke-color": {"type": "string", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#006600\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/stroke-opacity": {"type": "number", "description": "线条透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/stroke-weight": {"type": "number", "description": "线条宽度，单位：像素\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/border-weight": {"type": "number", "description": "描边线宽度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/is-outline": {"type": "boolean", "description": "线条是否带描边，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/outline-color": {"type": "string", "description": "线条描边颜色，此项仅在isOutline为true时有效，默认：#000000\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/draggable": {"type": "boolean", "description": "设置多边形是否可拖拽移动，默认为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/stroke-style": {"type": "string", "description": "线样式，实线:solid，虚线:dashed\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/stroke-dasharray": {"type": "array", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/line-join": {"type": "string", "description": "折线拐点的绘制样式，默认值为'miter'尖角，其他可选值：'round'圆角、'bevel'斜角\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/line-cap": {"type": "string", "description": "折线两端线帽的绘制样式，默认值为'butt'无头，其他可选值：'round'圆头、'square'方头\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/geodesic": {"type": "boolean", "description": "是否绘制大地线，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/show-dir": {"type": "boolean", "description": "是否延路径显示白色方向箭头,默认false。建议折线宽度大于6时使用\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/ext-data": {"type": "any", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes)"}, "el-amap-bezier-curve/init": {"type": "event", "description": "高德组件实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/rightclick": {"type": "event", "description": "鼠标右键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/hide": {"type": "event", "description": "隐藏\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/show": {"type": "event", "description": "显示\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/mousedown": {"type": "event", "description": "鼠标按下\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/mouseup": {"type": "event", "description": "鼠标抬起\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/mouseover": {"type": "event", "description": "鼠标经过\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/mouseout": {"type": "event", "description": "鼠标移出\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/addnode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/removenode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/adjust": {"type": "event", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/add": {"type": "event", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-bezier-curve/end": {"type": "event", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events)"}, "el-amap-circle/bubble": {"type": "boolean", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上（自v1.3 新增）默认值：false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/edit-options": {"type": "object", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polygoneditor)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/center": {"type": "array", "description": "圆心位置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/radius": {"type": "number", "description": "圆半径，单位:米\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/visible": {"type": "boolean", "description": "是否可见\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/editable": {"type": "boolean", "description": "多边形当前是否可编辑 (启动编辑时需要关闭 draggable，不然会导致图形被移走，但操作点还在原位)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/z-index": {"type": "number", "description": "多边形覆盖物的叠加顺序。地图上存在多个多边形覆盖物叠加时，通过该属性使级别较高的多边形覆盖物在上层显示默认zIndex：10\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/stroke-color": {"type": "string", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/stroke-opacity": {"type": "float", "description": "轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/stroke-weight": {"type": "number", "description": "轮廓线宽度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/fill-color": {"type": "string", "description": "多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/fill-opacity": {"type": "float", "description": "多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/ext-data": {"type": "any", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/stroke-style": {"type": "string", "description": "轮廓线样式，实线:solid，虚线:dashed\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/stroke-dasharray": {"type": "array", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/draggable": {"type": "boolean", "description": "设置多边形是否可拖拽移动，默认为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes)"}, "el-amap-circle/init": {"type": "event", "description": "组件实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/rightclick": {"type": "event", "description": "右键单击\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/hide": {"type": "event", "description": "隐藏\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/show": {"type": "event", "description": "显示\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/mousedown": {"type": "event", "description": "鼠标按下\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/mouseup": {"type": "event", "description": "鼠标抬起\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/mouseover": {"type": "event", "description": "鼠标经过\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/mouseout": {"type": "event", "description": "鼠标移出\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/addnode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/removenode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/move": {"type": "event", "description": "移动覆盖物时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/adjust": {"type": "event", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/add": {"type": "event", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-circle/end": {"type": "event", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events)"}, "el-amap-ellipse/bubble": {"type": "boolean", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上（自v1.3 新增）默认值：false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/edit-options": {"type": "object", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polygoneditor)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/center": {"type": "array", "description": "圆心位置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/radius": {"type": "array", "description": "椭圆的半径，用2个元素的数组表示，单位：米 如： radius: [1000, 2000] 表示横向半径是1000，纵向的半径是2000 默认值： [1000, 1000]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/visible": {"type": "boolean", "description": "是否可见\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/editable": {"type": "boolean", "description": "多边形当前是否可编辑 (启动编辑时需要关闭 draggable，不然会导致图形被移走，但操作点还在原位)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/z-index": {"type": "number", "description": "多边形覆盖物的叠加顺序。地图上存在多个多边形覆盖物叠加时，通过该属性使级别较高的多边形覆盖物在上层显示默认zIndex：10\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/stroke-color": {"type": "string", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/stroke-opacity": {"type": "float", "description": "轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/stroke-weight": {"type": "number", "description": "轮廓线宽度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/fill-color": {"type": "string", "description": "多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/fill-opacity": {"type": "float", "description": "多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/ext-data": {"type": "any", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/stroke-style": {"type": "string", "description": "轮廓线样式，实线:solid，虚线:dashed\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/stroke-dasharray": {"type": "array", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/draggable": {"type": "boolean", "description": "设置多边形是否可拖拽移动，默认为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes)"}, "el-amap-ellipse/init": {"type": "event", "description": "组件实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/rightclick": {"type": "event", "description": "右键单击\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/hide": {"type": "event", "description": "隐藏\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/show": {"type": "event", "description": "显示\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/mousedown": {"type": "event", "description": "鼠标按下\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/mouseup": {"type": "event", "description": "鼠标抬起\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/mouseover": {"type": "event", "description": "鼠标经过\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/mouseout": {"type": "event", "description": "鼠标移出\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/addnode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/removenode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/move": {"type": "event", "description": "移动覆盖物时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/adjust": {"type": "event", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/add": {"type": "event", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-ellipse/end": {"type": "event", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events)"}, "el-amap-geojson/marker-options": {"type": "object", "description": "marker的默认样式。存在getMarker参数时，该属性失效。该属性里的参数会全部带入marker中，但会被geojson的properties中的属性给覆盖。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes)"}, "el-amap-geojson/get-marker": {"type": "funtion ", "description": "指定点要素的绘制方式，缺省时为Marker的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes)"}, "el-amap-geojson/polyline-options": {"type": "object", "description": "polyline的默认样式。存在getPolyline参数时，该属性失效。该属性里的参数会全部带入polyline中，但会被geojson的properties中的属性给覆盖。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes)"}, "el-amap-geojson/get-polyline": {"type": "funtion ", "description": "指定线要素的绘制方式，缺省时为Polyline的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes)"}, "el-amap-geojson/polygon-options": {"type": "object", "description": "polygon的默认样式。存在getPolygon参数时，该属性失效。该属性里的参数会全部带入polygon中，但会被geojson的properties中的属性给覆盖。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes)"}, "el-amap-geojson/get-polygon": {"type": "funtion ", "description": "指定线要素的绘制方式，缺省时为Polygon的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes)"}, "el-amap-geojson/geo": {"type": "object", "description": "要加载的标准GeoJSON对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes)"}, "el-amap-geojson/visible": {"type": "boolean", "description": "是否可见\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes)"}, "el-amap-geojson/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes)"}, "el-amap-geojson/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes)"}, "el-amap-geojson/init": {"type": "event", "description": "组件实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-geojson/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-geojson/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-geojson/rightclick": {"type": "event", "description": "右键单击\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-geojson/mousedown": {"type": "event", "description": "鼠标按下\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-geojson/mouseup": {"type": "event", "description": "鼠标抬起\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-geojson/mouseover": {"type": "event", "description": "鼠标经过\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-geojson/mouseout": {"type": "event", "description": "鼠标移出\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-geojson/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-geojson/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-geojson/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events)"}, "el-amap-polygon/bubble": {"type": "boolean", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上（自v1.3 新增）默认值：false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/edit-options": {"type": "object", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polygoneditor)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/path": {"type": "array", "description": "多边形轮廓线的节点坐标数组。 支持 单个普通多边形(`{Array }`)，单个带孔多边形(`{Array<Array >}`)，多个带孔多边形(`{Array<Array<Array >>}`)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/visible": {"type": "boolean", "description": "是否可见\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/editable": {"type": "boolean", "description": "多边形当前是否可编辑 (启动编辑时需要关闭 draggable，不然会导致图形被移走，但操作点还在原位)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/z-index": {"type": "number", "description": "多边形覆盖物的叠加顺序。地图上存在多个多边形覆盖物叠加时，通过该属性使级别较高的多边形覆盖物在上层显示默认zIndex：10\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/stroke-color": {"type": "string", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/stroke-opacity": {"type": "float", "description": "轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/stroke-weight": {"type": "number", "description": "轮廓线宽度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/fill-color": {"type": "string", "description": "多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/fill-opacity": {"type": "float", "description": "多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/ext-data": {"type": "any", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/stroke-style": {"type": "string", "description": "轮廓线样式，实线:solid，虚线:dashed\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/stroke-dasharray": {"type": "array", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/draggable": {"type": "boolean", "description": "设置多边形是否可拖拽移动，默认为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes)"}, "el-amap-polygon/init": {"type": "event", "description": "组件实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/rightclick": {"type": "event", "description": "右键单击\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/hide": {"type": "event", "description": "隐藏\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/show": {"type": "event", "description": "显示\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/mousedown": {"type": "event", "description": "鼠标按下\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/mouseup": {"type": "event", "description": "鼠标抬起\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/mouseover": {"type": "event", "description": "鼠标经过\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/mouseout": {"type": "event", "description": "鼠标移出\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/addnode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/removenode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/move": {"type": "event", "description": "移动覆盖物时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/adjust": {"type": "event", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/add": {"type": "event", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polygon/end": {"type": "event", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events)"}, "el-amap-polyline/bubble": {"type": "boolean", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上 默认 false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/edit-options": {"type": "object", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polylineeditor)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/visible": {"type": "boolean", "description": "是否可见\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/editable": {"type": "boolean", "description": "折线当前是否可编辑\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/path": {"type": "array", "description": "polyline 路径，支持 lineString 和 MultiLineString\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/z-index": {"type": "number", "description": "折线覆盖物的叠加顺序。默认叠加顺序，先添加的线在底层，后添加的线在上层。通过该属性可调整叠加顺序，使级别较高的折线覆盖物在上层显示。默认zIndex：10\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/stroke-color": {"type": "string", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#006600\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/stroke-opacity": {"type": "number", "description": "线条透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/stroke-weight": {"type": "number", "description": "线条宽度，单位：像素\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/border-weight": {"type": "number", "description": "描边线宽度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/is-outline": {"type": "boolean", "description": "线条是否带描边，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/outline-color": {"type": "string", "description": "线条描边颜色，此项仅在isOutline为true时有效，默认：#000000\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/draggable": {"type": "boolean", "description": "设置多边形是否可拖拽移动，默认为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/stroke-style": {"type": "string", "description": "线样式，实线:solid，虚线:dashed\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/stroke-dasharray": {"type": "array", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/line-join": {"type": "string", "description": "折线拐点的绘制样式，默认值为'miter'尖角，其他可选值：'round'圆角、'bevel'斜角\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/line-cap": {"type": "string", "description": "折线两端线帽的绘制样式，默认值为'butt'无头，其他可选值：'round'圆头、'square'方头\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/geodesic": {"type": "boolean", "description": "是否绘制大地线，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/show-dir": {"type": "boolean", "description": "是否延路径显示白色方向箭头,默认false。建议折线宽度大于6时使用\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/ext-data": {"type": "any", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes)"}, "el-amap-polyline/init": {"type": "event", "description": "高德组件实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/rightclick": {"type": "event", "description": "鼠标右键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/hide": {"type": "event", "description": "隐藏\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/show": {"type": "event", "description": "显示\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/mousedown": {"type": "event", "description": "鼠标按下\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/mouseup": {"type": "event", "description": "鼠标抬起\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/mouseover": {"type": "event", "description": "鼠标经过\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/mouseout": {"type": "event", "description": "鼠标移出\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/addnode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/removenode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/adjust": {"type": "event", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/add": {"type": "event", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-polyline/end": {"type": "event", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events)"}, "el-amap-rectangle/bubble": {"type": "boolean", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上（自v1.3 新增）默认值：false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/edit-options": {"type": "object", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polygoneditor)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/bounds": {"type": "array", "description": "矩形的范围, Array[Array[lng, lat]]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/visible": {"type": "boolean", "description": "是否可见\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/editable": {"type": "boolean", "description": "多边形当前是否可编辑 (启动编辑时需要关闭 draggable，不然会导致图形被移走，但操作点还在原位)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/z-index": {"type": "number", "description": "多边形覆盖物的叠加顺序。地图上存在多个多边形覆盖物叠加时，通过该属性使级别较高的多边形覆盖物在上层显示默认zIndex：10\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/stroke-color": {"type": "string", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/stroke-opacity": {"type": "float", "description": "轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/stroke-weight": {"type": "number", "description": "轮廓线宽度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/fill-color": {"type": "string", "description": "多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/fill-opacity": {"type": "float", "description": "多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/ext-data": {"type": "any", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/stroke-style": {"type": "string", "description": "轮廓线样式，实线:solid，虚线:dashed\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/stroke-dasharray": {"type": "array", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/draggable": {"type": "boolean", "description": "设置多边形是否可拖拽移动，默认为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes)"}, "el-amap-rectangle/init": {"type": "event", "description": "组件实例\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/click": {"type": "event", "description": "鼠标左键单击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/dblclick": {"type": "event", "description": "鼠标左键双击事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/rightclick": {"type": "event", "description": "右键单击\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/hide": {"type": "event", "description": "隐藏\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/show": {"type": "event", "description": "显示\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/mousedown": {"type": "event", "description": "鼠标按下\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/mouseup": {"type": "event", "description": "鼠标抬起\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/mouseover": {"type": "event", "description": "鼠标经过\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/mouseout": {"type": "event", "description": "鼠标移出\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/touchstart": {"type": "event", "description": "触摸开始时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/touchmove": {"type": "event", "description": "触摸移动进行中时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/touchend": {"type": "event", "description": "触摸结束时触发事件，仅适用移动设备\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/addnode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/removenode": {"type": "event", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/move": {"type": "event", "description": "移动覆盖物时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/adjust": {"type": "event", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/add": {"type": "event", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-rectangle/end": {"type": "event", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events)"}, "el-amap-layer-canvas/canvas": {"type": "htmlcanvaselement", "description": "Canvas DOM 对象\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes)"}, "el-amap-layer-canvas/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes)"}, "el-amap-layer-canvas/bounds": {"type": "array|amap.bounds", "description": "图片的范围大小经纬度，如果传递数字数组类型: [minlng,minlat,maxlng,maxlat]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes)"}, "el-amap-layer-canvas/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes)"}, "el-amap-layer-canvas/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：6\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes)"}, "el-amap-layer-canvas/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes)"}, "el-amap-layer-canvas/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes)"}, "el-amap-layer-canvas/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes)"}, "el-amap-layer-canvas/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#events)"}, "el-amap-layer-canvas/complete": {"type": "event", "description": "加载完成事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#events)"}, "el-amap-layer-custom-xyz/url": {"type": "string", "description": "瓦片地址，支持 {s} {x} {y} {z}，示例：`http://webst0{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}`\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/subdomains": {"type": "string[]", "description": "子域名数组，当url中设置{s}后，该属性必填\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/tile-type": {"type": "'xyz' | 'bd09'", "description": "瓦片分割类型，默认是`xyz`，xyz代表瓦片是编号是从左上角开始，百度瓦片是由中间开始，所以需要区分普通瓦片还是百度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/proj": {"type": "'wgs84' | 'gcj02' | 'bd09'", "description": "瓦片使用的坐标系，默认是`gcj02`\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/zooms": {"type": "[number|number]", "description": "图层缩放等级范围，默认 [2, 18]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/opacity": {"type": "number", "description": "图层透明度，默认为 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/visible": {"type": "boolean", "description": "图层是否可见，默认为 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/z-index": {"type": "number", "description": "图层的层级，默认为 120\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/debug": {"type": "boolean", "description": "开启debug后瓦片上将显示瓦片编号\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/mask": {"type": "number[][] | number[][][]   | number[][][][]", "description": "瓦片掩膜，数据结构与AMap.Map的mask参数一致\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/cache-size": {"type": "number", "description": "瓦片缓存数量，默认512，不限制缓存瓦片数\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/tile-max-zoom": {"type": "number", "description": "瓦片在服务器的最大层级，当地图zoom超过该层级后直接使用该层级作为做大层级瓦片，默认18\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/altitude": {"type": "number", "description": "加载的瓦片海拔，设置该值后，在3D模式下瓦片将浮空，默认：0\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes)"}, "el-amap-layer-custom-xyz/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#events)"}, "el-amap-layer-custom/canvas": {"type": "htmlelement", "description": "canvas 对象,必填\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes)"}, "el-amap-layer-custom/render": {"type": "function", "description": "绘制函数，初始化完成时候，开发者需要给该图层设定render方法，该方法需要实现图层的绘制，API会在合适的时机自动调用该方法\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes)"}, "el-amap-layer-custom/always-render": {"type": "boolean", "description": "是否主动，默认 false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes)"}, "el-amap-layer-custom/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-20]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes)"}, "el-amap-layer-custom/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes)"}, "el-amap-layer-custom/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：120\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes)"}, "el-amap-layer-custom/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes)"}, "el-amap-layer-custom/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes)"}, "el-amap-layer-custom/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes)"}, "el-amap-layer-custom/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#events)"}, "el-amap-layer-district-cluster/z-index": {"type": "number", "description": "图层的层级，默认为 10\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes)"}, "el-amap-layer-district-cluster/visible": {"type": "boolean", "description": "图层是否可见，默认为 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes)"}, "el-amap-layer-district-cluster/data": {"type": "array", "description": "数据源数组，每个元素即为点相关的信息\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes)"}, "el-amap-layer-district-cluster/get-position": {"type": "function", "description": "返回数据项中的经纬度信息\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes)"}, "el-amap-layer-district-cluster/auto-set-fit-view": {"type": "boolean", "description": "是否在绘制后自动调整地图视野以适合全部点，默认true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes)"}, "el-amap-layer-district-cluster/top-adcodes": {"type": "number[]", "description": "顶层区划的adcode列表。默认为[100000]，即全国范围.假如仅需要展示河北和北京，可以设置为[130000, 110000],\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes)"}, "el-amap-layer-district-cluster/excluded-adcodes": {"type": "number[]", "description": "需要排除的区划的adcode列表\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes)"}, "el-amap-layer-district-cluster/render-options": {"type": "renderoptions", "description": "绘制的引擎的参数\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes)"}, "el-amap-layer-district-cluster/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes)"}, "el-amap-layer-district-cluster/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#events)"}, "el-amap-layer-district-cluster/featureClick": {"type": "event", "description": "鼠标点击feature对应的区域时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#events)"}, "el-amap-layer-district-cluster/featureMouseover": {"type": "event", "description": "鼠标移入feature对应的区域时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#events)"}, "el-amap-layer-district-cluster/featureMouseout": {"type": "event", "description": "鼠标移出feature对应的区域时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#events)"}, "el-amap-layer-district-cluster/clusterMarkerClick": {"type": "event", "description": "鼠标点击聚合标注时触发\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#events)"}, "el-amap-layer-flexible/cache-size": {"type": "number", "description": "缓存瓦片数量\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes)"}, "el-amap-layer-flexible/create-tile": {"type": "function", "description": "由开发者实现，由API自动调用，xyz分别为切片横向纵向编号和层级，切片大小 256。假设每次创建的贴片为A(支持img或者canvas)，当创建或者获取成功时请回调success(A)，不需要显示或者失败时请回调fail()\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes)"}, "el-amap-layer-flexible/tile-size": {"type": "number", "description": "切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes)"}, "el-amap-layer-flexible/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes)"}, "el-amap-layer-flexible/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes)"}, "el-amap-layer-flexible/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes)"}, "el-amap-layer-flexible/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes)"}, "el-amap-layer-flexible/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes)"}, "el-amap-layer-flexible/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes)"}, "el-amap-layer-flexible/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#events)"}, "el-amap-layer-flexible/complete": {"type": "event", "description": "图块切片加载完成事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#events)"}, "el-amap-layer-gl-custom/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#events)"}, "el-amap-layer-gl-custom/render": {"type": "function", "description": "绘制函数，初始化完成时候，开发者需要给该图层设定render方法，该方法需要实现图层的绘制，API会在合适的时机自动调用该方法\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes)"}, "el-amap-layer-gl-custom/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-20]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes)"}, "el-amap-layer-gl-custom/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes)"}, "el-amap-layer-gl-custom/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：120\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes)"}, "el-amap-layer-gl-custom/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes)"}, "el-amap-layer-gl-custom/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes)"}, "el-amap-layer-gl-custom/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes)"}, "el-amap-layer-heat-map/radius": {"type": "number", "description": "热力图中单个点的半径，默认：30，单位：pixel\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes)"}, "el-amap-layer-heat-map/gradient": {"type": "object", "description": "热力图的渐变区间，热力图按照设置的颜色及间隔显示热力图，例{0.4:'rgb(0, 255, 255)',0.85:'rgb(100, 0, 255)',},其中 key 表示间隔位置，取值范围： [0,1] ，value 为颜色值。默认：heatmap.js标准配色方案\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes)"}, "el-amap-layer-heat-map/config": {"type": "object", "description": "3d参数在组件中被重命名为config，参数内容与3d一致。3D热力图属性\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes)"}, "el-amap-layer-heat-map/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [3-20]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes)"}, "el-amap-layer-heat-map/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes)"}, "el-amap-layer-heat-map/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：130\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes)"}, "el-amap-layer-heat-map/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes)"}, "el-amap-layer-heat-map/data-set": {"type": "object", "description": "热力图数据集 {data: points, max: 100}\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes)"}, "el-amap-layer-heat-map/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes)"}, "el-amap-layer-heat-map/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes)"}, "el-amap-layer-heat-map/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#events)"}, "el-amap-layer-image/url": {"type": "string", "description": "图片地址链接\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes)"}, "el-amap-layer-image/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes)"}, "el-amap-layer-image/bounds": {"type": "array|amap.bounds", "description": "图片的范围大小经纬度，如果传递数字数组类型: [minlng,minlat,maxlng,maxlat]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes)"}, "el-amap-layer-image/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes)"}, "el-amap-layer-image/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：6\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes)"}, "el-amap-layer-image/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes)"}, "el-amap-layer-image/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes)"}, "el-amap-layer-image/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes)"}, "el-amap-layer-image/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#events)"}, "el-amap-layer-image/complete": {"type": "event", "description": "加载完成事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#events)"}, "el-amap-layer-labels/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes)"}, "el-amap-layer-labels/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes)"}, "el-amap-layer-labels/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：120\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes)"}, "el-amap-layer-labels/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes)"}, "el-amap-layer-labels/collision": {"type": "boolean", "description": "标注层内的标注是否避让\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes)"}, "el-amap-layer-labels/allow-collision": {"type": "boolean", "description": "标注层内的标注是否允许其它标注层对它避让\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes)"}, "el-amap-layer-labels/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes)"}, "el-amap-layer-labels/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes)"}, "el-amap-layer-labels/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#events)"}, "el-amap-layer-tiles3d/url": {"type": "string", "description": "3d Tiles 入口文件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes)"}, "el-amap-layer-tiles3d/three-script-url": {"type": "string", "description": "ThreeJS的文件加载地址\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes)"}, "el-amap-layer-tiles3d/three-gltf-loader": {"type": "string", "description": "threeJS的GltfLoader文件加载地址\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes)"}, "el-amap-layer-tiles3d/layer-style": {"type": "object", "description": "图层样式\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes)"}, "el-amap-layer-tiles3d/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes)"}, "el-amap-layer-tiles3d/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes)"}, "el-amap-layer-tiles3d/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#events)"}, "el-amap-layer-vector/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#attributes)"}, "el-amap-layer-vector/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：120\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#attributes)"}, "el-amap-layer-vector/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#attributes)"}, "el-amap-layer-vector/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#attributes)"}, "el-amap-layer-vector/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#events)"}, "el-amap-layer-video/url": {"type": "string", "description": "视频地址\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes)"}, "el-amap-layer-video/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes)"}, "el-amap-layer-video/bounds": {"type": "array|amap.bounds", "description": "图片的范围大小经纬度，如果传递数字数组类型: [minlng,minlat,maxlng,maxlat]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes)"}, "el-amap-layer-video/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes)"}, "el-amap-layer-video/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：6\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes)"}, "el-amap-layer-video/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes)"}, "el-amap-layer-video/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes)"}, "el-amap-layer-video/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes)"}, "el-amap-layer-video/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#events)"}, "el-amap-layer-buildings/wall-color": {"type": "array<string>|string", "description": "楼块侧面颜色，支持 rgba、rgb、十六进制等\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes)"}, "el-amap-layer-buildings/roof-color": {"type": "array<string>|string", "description": "楼块顶面颜色，支持 rgba、rgb、十六进制等\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes)"}, "el-amap-layer-buildings/height-factor": {"type": "number", "description": "楼块的高度系数因子，默认为 1，也就是正常高度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes)"}, "el-amap-layer-buildings/style-opts": {"type": "object", "description": "楼块的围栏和样式设置\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes)"}, "el-amap-layer-buildings/zooms": {"type": "array", "description": "图层缩放等级范围，默认 [2, 20]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes)"}, "el-amap-layer-buildings/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes)"}, "el-amap-layer-buildings/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes)"}, "el-amap-layer-buildings/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes)"}, "el-amap-layer-buildings/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes)"}, "el-amap-layer-buildings/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes)"}, "el-amap-layer-buildings/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#events)"}, "el-amap-layer-buildings/complete": {"type": "event", "description": "图块切片加载完成事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#events)"}, "el-amap-layer-default/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes)"}, "el-amap-layer-default/visible": {"type": "boolean", "description": "是否可见，默认为true。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes)"}, "el-amap-layer-default/z-index": {"type": "number", "description": "默认zIndex：0\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes)"}, "el-amap-layer-default/opacity": {"type": "number", "description": "透明度\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes)"}, "el-amap-layer-default/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes)"}, "el-amap-layer-default/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes)"}, "el-amap-layer-default/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#events)"}, "el-amap-layer-district/type": {"type": "string", "description": "类型，可选值：World 世界, Country 国家, Province 省市\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/depth": {"type": "number", "description": "设定数据的层级深度，depth为0的时候只显示国家面，depth为1的时候显示省级， 当国家为中国时设置depth为2的可以显示市一级\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/adcode": {"type": "string", "description": "行政区的编码，可从链接下载[adcode与省市行政区对照表](https://a.amap.com/lbs/static/file/AMap_adcode_citycode.xlsx.zip)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/s-o-c": {"type": "string", "description": "设定显示的国家 [SOC 国家代码、名称、Bounds对照表下载](https://a.amap.com/jsapi_demos/static/demo-center/js/soc-list.json)\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/styles": {"type": "object", "description": "为简易行政区图设定各面的填充颜色和描边颜色。 styles各字段的值可以是颜色值，也可以是一个返回颜色值* 的回调函数function。支持的颜色格式有：<br/>1. #RRGGBB，如：'#FFFFFF' <br/>2. rgba()，如：'rgba(255,255,255,1)'<br/> 3. rgb()，如：'rgb(255,255,255)'<br/>4. [r,g,b,a] ，如： [1,1,1,1]<br/>5. ''，代表不赋予颜色\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes)"}, "el-amap-layer-district/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#events)"}, "el-amap-layer-indoor-map/cursor": {"type": "string", "description": "指定鼠标悬停到店铺面时的鼠标样式\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes)"}, "el-amap-layer-indoor-map/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes)"}, "el-amap-layer-indoor-map/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes)"}, "el-amap-layer-indoor-map/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes)"}, "el-amap-layer-indoor-map/hide-floor-bar": {"type": "boolean", "description": "是否隐藏楼层切换控件，默认值：false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes)"}, "el-amap-layer-indoor-map/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes)"}, "el-amap-layer-indoor-map/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes)"}, "el-amap-layer-indoor-map/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#events)"}, "el-amap-layer-road-net/tile-size": {"type": "number", "description": "切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes)"}, "el-amap-layer-road-net/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes)"}, "el-amap-layer-road-net/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes)"}, "el-amap-layer-road-net/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes)"}, "el-amap-layer-road-net/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes)"}, "el-amap-layer-road-net/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes)"}, "el-amap-layer-road-net/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes)"}, "el-amap-layer-road-net/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#events)"}, "el-amap-layer-road-net/complete": {"type": "event", "description": "图块切片加载完成事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#events)"}, "el-amap-layer-satellite/tile-size": {"type": "number", "description": "切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes)"}, "el-amap-layer-satellite/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes)"}, "el-amap-layer-satellite/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes)"}, "el-amap-layer-satellite/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes)"}, "el-amap-layer-satellite/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes)"}, "el-amap-layer-satellite/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes)"}, "el-amap-layer-satellite/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes)"}, "el-amap-layer-satellite/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#events)"}, "el-amap-layer-satellite/complete": {"type": "event", "description": "图块切片加载完成事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#events)"}, "el-amap-layer-tile/data-zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes)"}, "el-amap-layer-tile/tile-size": {"type": "number", "description": "切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes)"}, "el-amap-layer-tile/tile-url": {"type": "string", "description": "切片取图地址 如：' https://abc{0,1,2,3}.amap.com/tile?x=[x]&y=[y]&z=[z] ' [x] 、 [y] 、 [z] 分别替代切片的xyz。\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes)"}, "el-amap-layer-tile/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes)"}, "el-amap-layer-tile/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes)"}, "el-amap-layer-tile/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes)"}, "el-amap-layer-tile/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes)"}, "el-amap-layer-tile/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes)"}, "el-amap-layer-tile/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes)"}, "el-amap-layer-tile/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#events)"}, "el-amap-layer-tile/complete": {"type": "event", "description": "图块切片加载完成事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#events)"}, "el-amap-layer-traffic/auto-refresh": {"type": "boolean", "description": "是否自动更新数据，默认开启\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes)"}, "el-amap-layer-traffic/tile-size": {"type": "number", "description": "切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes)"}, "el-amap-layer-traffic/interval": {"type": "number", "description": "自动更新数据的间隔毫秒数，默认 180ms\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes)"}, "el-amap-layer-traffic/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes)"}, "el-amap-layer-traffic/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes)"}, "el-amap-layer-traffic/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes)"}, "el-amap-layer-traffic/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes)"}, "el-amap-layer-traffic/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes)"}, "el-amap-layer-traffic/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes)"}, "el-amap-layer-traffic/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#events)"}, "el-amap-layer-traffic/complete": {"type": "event", "description": "图块切片加载完成事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#events)"}, "el-amap-layer-mapbox-vector-tile/url": {"type": "string", "description": "MVT 数据的链接地址\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes)"}, "el-amap-layer-mapbox-vector-tile/data-zooms": {"type": "array", "description": "瓦片数据等级范围，超过范围会使用最大/最小等级的数据，默认范围 [2,18]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes)"}, "el-amap-layer-mapbox-vector-tile/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2,22]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes)"}, "el-amap-layer-mapbox-vector-tile/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes)"}, "el-amap-layer-mapbox-vector-tile/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes)"}, "el-amap-layer-mapbox-vector-tile/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes)"}, "el-amap-layer-mapbox-vector-tile/styles": {"type": "object", "description": "样式\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes)"}, "el-amap-layer-mapbox-vector-tile/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes)"}, "el-amap-layer-mapbox-vector-tile/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes)"}, "el-amap-layer-mapbox-vector-tile/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#events)"}, "el-amap-layer-wms/blend": {"type": "boolean", "description": "地图级别切换时，不同级别的图片是否进行混合，如图层的图像内容为部分透明请设置为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes)"}, "el-amap-layer-wms/url": {"type": "string", "description": "wmts服务的url地址，如：' https://services.arcgisonline.com/arcgis/rest/services/'+ 'Demographics/USA_Population_Density/MapServer/WMTS/'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes)"}, "el-amap-layer-wms/params": {"type": "object", "description": "OGC标准的WMS地图服务的GetMap接口的参数，包括VERSION、LAYERS、STYLES、FORMAT、TRANSPARENT等，<br/>CRS、BBOX、REQUEST、WIDTH、HEIGHT等参数请勿添加，例如：<br/>{ </br/>  LAYERS: 'topp:states',<br/>  VERSION:'1.3.0',<br/>  FORMAT:'image/png'<br/>  }\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes)"}, "el-amap-layer-wms/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes)"}, "el-amap-layer-wms/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes)"}, "el-amap-layer-wms/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes)"}, "el-amap-layer-wms/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes)"}, "el-amap-layer-wms/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes)"}, "el-amap-layer-wms/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes)"}, "el-amap-layer-wms/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#events)"}, "el-amap-layer-wms/complete": {"type": "event", "description": "图块切片加载完成事件\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#events)"}, "el-amap-layer-wmts/blend": {"type": "boolean", "description": "地图级别切换时，不同级别的图片是否进行混合，如图层的图像内容为部分透明请设置为false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes)"}, "el-amap-layer-wmts/url": {"type": "string", "description": "wmts服务的url地址，如：' https://services.arcgisonline.com/arcgis/rest/services/'+ 'Demographics/USA_Population_Density/MapServer/WMTS/'\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes)"}, "el-amap-layer-wmts/params": {"type": "object", "description": "OGC标准的WMS地图服务的GetMap接口的参数，包括VERSION、LAYERS、STYLES、FORMAT、TRANSPARENT等，<br/>CRS、BBOX、REQUEST、WIDTH、HEIGHT等参数请勿添加，例如：<br/>{ </br/>  LAYERS: 'topp:states',<br/>  VERSION:'1.3.0',<br/>  FORMAT:'image/png'<br/>  }\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes)"}, "el-amap-layer-wmts/zooms": {"type": "array", "description": "支持的缩放级别范围，默认范围 [2-30]\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes)"}, "el-amap-layer-wmts/visible": {"type": "boolean", "description": "是否显示，默认 true\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes)"}, "el-amap-layer-wmts/z-index": {"type": "number", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes)"}, "el-amap-layer-wmts/opacity": {"type": "number", "description": "透明度，默认 1\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes)"}, "el-amap-layer-wmts/re-event-when-update": {"type": "boolean", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes)"}, "el-amap-layer-wmts/extra-options": {"type": "object", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes)"}, "el-amap-layer-wmts/init": {"type": "event", "description": "实例初始化结束\n\n[Docs](https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#events)"}}