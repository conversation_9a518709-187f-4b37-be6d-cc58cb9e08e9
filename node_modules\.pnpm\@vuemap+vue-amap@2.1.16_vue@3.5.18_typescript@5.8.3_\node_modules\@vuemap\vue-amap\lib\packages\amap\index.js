'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('./amap.vue.js');
var amap_vue_vue_type_script_setup_true_lang = require('./amap.vue2.js');

amap_vue_vue_type_script_setup_true_lang.default.install = (app) => {
  app.component(amap_vue_vue_type_script_setup_true_lang.default.name, amap_vue_vue_type_script_setup_true_lang.default);
  return app;
};
const ElAmap = amap_vue_vue_type_script_setup_true_lang.default;

exports.ElAmap = ElAmap;
exports.default = ElAmap;
//# sourceMappingURL=index.js.map
