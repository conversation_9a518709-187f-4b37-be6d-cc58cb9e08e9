{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/image/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Image from './src/image.vue'\n\nexport const ElImage = withInstall(Image)\nexport default ElImage\n\nexport * from './src/image'\n"], "names": ["withInstall", "Image"], "mappings": ";;;;;;;;;AAEY,MAAC,OAAO,GAAGA,mBAAW,CAACC,kBAAK;;;;;;;"}