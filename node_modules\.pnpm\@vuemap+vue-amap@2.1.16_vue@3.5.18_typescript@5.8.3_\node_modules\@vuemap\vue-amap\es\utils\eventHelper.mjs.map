{"version": 3, "file": "eventHelper.mjs", "sources": ["../../../utils/eventHelper.ts"], "sourcesContent": ["/**\r\n * 给地图实例绑定事件\r\n * @param instance\r\n * @param eventName\r\n * @param handler\r\n */\r\nexport function bindInstanceEvent (instance: any, eventName: string, handler: any){\r\n  if(!instance || !instance.on){\r\n    return;\r\n  }\r\n  instance.on(eventName, handler);\r\n}\r\n\r\n/**\r\n * 从地图实例上移除事件\r\n * @param instance\r\n * @param eventName\r\n * @param handler\r\n */\r\nexport function removeInstanceEvent (instance: any, eventName: string, handler: any){\r\n  if(!instance || !instance.off){\r\n    return;\r\n  }\r\n  instance.off(eventName, handler);\r\n}"], "names": [], "mappings": "AAMgB,SAAA,iBAAA,CAAmB,QAAe,EAAA,SAAA,EAAmB,OAAa,EAAA;AAChF,EAAA,IAAG,CAAC,QAAA,IAAY,CAAC,QAAA,CAAS,EAAG,EAAA;AAC3B,IAAA,OAAA;AAAA,GACF;AACA,EAAS,QAAA,CAAA,EAAA,CAAG,WAAW,OAAO,CAAA,CAAA;AAChC,CAAA;AAQgB,SAAA,mBAAA,CAAqB,QAAe,EAAA,SAAA,EAAmB,OAAa,EAAA;AAClF,EAAA,IAAG,CAAC,QAAA,IAAY,CAAC,QAAA,CAAS,GAAI,EAAA;AAC5B,IAAA,OAAA;AAAA,GACF;AACA,EAAS,QAAA,CAAA,GAAA,CAAI,WAAW,OAAO,CAAA,CAAA;AACjC;;;;"}