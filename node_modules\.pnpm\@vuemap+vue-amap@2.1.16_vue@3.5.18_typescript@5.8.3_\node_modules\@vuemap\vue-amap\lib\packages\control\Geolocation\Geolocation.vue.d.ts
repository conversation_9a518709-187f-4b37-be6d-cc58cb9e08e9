/// <reference types="@vuemap/amap-jsapi-types" />
declare const _default: import("vue").DefineComponent<{
    position: {
        type: import("vue").PropType<"LT" | "RT" | "LB" | "RB">;
    };
    offset: {
        type: ArrayConstructor;
    };
    borderColor: {
        type: StringConstructor;
    };
    borderRadius: {
        type: StringConstructor;
    };
    buttonSize: {
        type: StringConstructor;
    };
    convert: {
        type: BooleanConstructor;
        default: boolean;
    };
    enableHighAccuracy: {
        type: BooleanConstructor;
        default: boolean;
    };
    timeout: {
        type: NumberConstructor;
    };
    maximumAge: {
        type: NumberConstructor;
    };
    showButton: {
        type: BooleanConstructor;
        default: boolean;
    };
    showCircle: {
        type: BooleanConstructor;
        default: boolean;
    };
    showMarker: {
        type: BooleanConstructor;
        default: boolean;
    };
    markerOptions: {
        type: ObjectConstructor;
    };
    circleOptions: {
        type: ObjectConstructor;
    };
    panToLocation: {
        type: BooleanConstructor;
        default: boolean;
    };
    zoomToAccuracy: {
        type: BooleanConstructor;
        default: boolean;
    };
    GeoLocationFirst: {
        type: BooleanConstructor;
        default: boolean;
    };
    noIpLocate: {
        type: NumberConstructor;
    };
    noGeoLocation: {
        type: NumberConstructor;
    };
    useNative: {
        type: BooleanConstructor;
        default: boolean;
    };
    getCityWhenFail: {
        type: BooleanConstructor;
        default: boolean;
    };
    needAddress: {
        type: BooleanConstructor;
        default: boolean;
    };
    extensions: {
        type: import("vue").PropType<"base" | "all">;
        validator: (value: string) => boolean;
    };
} & {
    visible: import("../../..").IPropOptions<boolean>;
    zIndex: import("../../..").IPropOptions<number>;
    reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
    extraOptions: import("../../..").IPropOptions<any>;
}, {
    emits: (event: "init" | "complete", ...args: any[]) => void;
    $amapComponent: AMap.Geolocation;
    emitComplete: (e: any) => void;
    $$getInstance: () => AMap.Geolocation;
    parentInstance: import("../../../mixins").IProvideType | undefined;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("init" | "complete")[], "init" | "complete", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    position: {
        type: import("vue").PropType<"LT" | "RT" | "LB" | "RB">;
    };
    offset: {
        type: ArrayConstructor;
    };
    borderColor: {
        type: StringConstructor;
    };
    borderRadius: {
        type: StringConstructor;
    };
    buttonSize: {
        type: StringConstructor;
    };
    convert: {
        type: BooleanConstructor;
        default: boolean;
    };
    enableHighAccuracy: {
        type: BooleanConstructor;
        default: boolean;
    };
    timeout: {
        type: NumberConstructor;
    };
    maximumAge: {
        type: NumberConstructor;
    };
    showButton: {
        type: BooleanConstructor;
        default: boolean;
    };
    showCircle: {
        type: BooleanConstructor;
        default: boolean;
    };
    showMarker: {
        type: BooleanConstructor;
        default: boolean;
    };
    markerOptions: {
        type: ObjectConstructor;
    };
    circleOptions: {
        type: ObjectConstructor;
    };
    panToLocation: {
        type: BooleanConstructor;
        default: boolean;
    };
    zoomToAccuracy: {
        type: BooleanConstructor;
        default: boolean;
    };
    GeoLocationFirst: {
        type: BooleanConstructor;
        default: boolean;
    };
    noIpLocate: {
        type: NumberConstructor;
    };
    noGeoLocation: {
        type: NumberConstructor;
    };
    useNative: {
        type: BooleanConstructor;
        default: boolean;
    };
    getCityWhenFail: {
        type: BooleanConstructor;
        default: boolean;
    };
    needAddress: {
        type: BooleanConstructor;
        default: boolean;
    };
    extensions: {
        type: import("vue").PropType<"base" | "all">;
        validator: (value: string) => boolean;
    };
} & {
    visible: import("../../..").IPropOptions<boolean>;
    zIndex: import("../../..").IPropOptions<number>;
    reEventWhenUpdate: import("../../..").IPropOptions<boolean>;
    extraOptions: import("../../..").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
    onComplete?: ((...args: any[]) => any) | undefined;
}, {
    showButton: boolean;
    convert: boolean;
    enableHighAccuracy: boolean;
    showCircle: boolean;
    showMarker: boolean;
    panToLocation: boolean;
    zoomToAccuracy: boolean;
    GeoLocationFirst: boolean;
    useNative: boolean;
    getCityWhenFail: boolean;
    needAddress: boolean;
}, {}>;
export default _default;
