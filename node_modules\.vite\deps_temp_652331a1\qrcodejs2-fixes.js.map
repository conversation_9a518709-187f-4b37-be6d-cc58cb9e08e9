{"version": 3, "sources": ["../../.pnpm/qrcodejs2-fixes@0.0.2/node_modules/qrcodejs2-fixes/qrcode.js"], "sourcesContent": ["/**\n * @fileoverview\n * - Using the 'QRCode for Javascript library'\n * - Fixed dataset of 'QRCode for Javascript library' for support full-spec.\n * - this library has no dependencies.\n *\n * <AUTHOR>\n * @see <a href=\"http://www.d-project.com/\" target=\"_blank\">http://www.d-project.com/</a>\n * @see <a href=\"http://jeromeetienne.github.com/jquery-qrcode/\" target=\"_blank\">http://jeromeetienne.github.com/jquery-qrcode/</a>\n */\nvar QRCode;\n\n(function (root, factory) {\n\n\t/* CommonJS */\n\tif (typeof exports == 'object') module.exports = factory()\n\n\t/* AMD module */\n\telse if (typeof define == 'function' && define.amd) define(factory)\n\n\t/* Global */\n\telse root.QRCode = factory()\n\n}(this, function () {\t//---------------------------------------------------------------------\n\t// QRCode for JavaScript\n\t//\n\t// Copyright (c) 2009 <PERSON><PERSON><PERSON>\n\t//\n\t// URL: http://www.d-project.com/\n\t//\n\t// Licensed under the MIT license:\n\t//   http://www.opensource.org/licenses/mit-license.php\n\t//\n\t// The word \"QR Code\" is registered trademark of\n\t// DENSO WAVE INCORPORATED\n\t//   http://www.denso-wave.com/qrcode/faqpatent-e.html\n\t//\n\t//---------------------------------------------------------------------\n\tfunction QR8bitByte(data) {\n\t\tthis.mode = QRMode.MODE_8BIT_BYTE;\n\t\tthis.data = data;\n\t\tthis.parsedData = [];\n\n\t\t// Added to support UTF-8 Characters\n\t\tfor (var i = 0, l = this.data.length; i < l; i++) {\n\t\t\tvar byteArray = [];\n\t\t\tvar code = this.data.charCodeAt(i);\n\n\t\t\tif (code > 0x10000) {\n\t\t\t\tbyteArray[0] = 0xF0 | ((code & 0x1C0000) >>> 18);\n\t\t\t\tbyteArray[1] = 0x80 | ((code & 0x3F000) >>> 12);\n\t\t\t\tbyteArray[2] = 0x80 | ((code & 0xFC0) >>> 6);\n\t\t\t\tbyteArray[3] = 0x80 | (code & 0x3F);\n\t\t\t} else if (code > 0x800) {\n\t\t\t\tbyteArray[0] = 0xE0 | ((code & 0xF000) >>> 12);\n\t\t\t\tbyteArray[1] = 0x80 | ((code & 0xFC0) >>> 6);\n\t\t\t\tbyteArray[2] = 0x80 | (code & 0x3F);\n\t\t\t} else if (code > 0x80) {\n\t\t\t\tbyteArray[0] = 0xC0 | ((code & 0x7C0) >>> 6);\n\t\t\t\tbyteArray[1] = 0x80 | (code & 0x3F);\n\t\t\t} else {\n\t\t\t\tbyteArray[0] = code;\n\t\t\t}\n\n\t\t\tthis.parsedData.push(byteArray);\n\t\t}\n\n\t\tthis.parsedData = Array.prototype.concat.apply([], this.parsedData);\n\n\t\tif (this.parsedData.length != this.data.length) {\n\t\t\tthis.parsedData.unshift(191);\n\t\t\tthis.parsedData.unshift(187);\n\t\t\tthis.parsedData.unshift(239);\n\t\t}\n\t}\n\n\tQR8bitByte.prototype = {\n\t\tgetLength: function (buffer) {\n\t\t\treturn this.parsedData.length;\n\t\t},\n\t\twrite: function (buffer) {\n\t\t\tfor (var i = 0, l = this.parsedData.length; i < l; i++) {\n\t\t\t\tbuffer.put(this.parsedData[i], 8);\n\t\t\t}\n\t\t}\n\t};\n\n\tfunction QRCodeModel(typeNumber, errorCorrectLevel) {\n\t\tthis.typeNumber = typeNumber;\n\t\tthis.errorCorrectLevel = errorCorrectLevel;\n\t\tthis.modules = null;\n\t\tthis.moduleCount = 0;\n\t\tthis.dataCache = null;\n\t\tthis.dataList = [];\n\t}\n\n\tQRCodeModel.prototype={addData:function(data){var newData=new QR8bitByte(data);this.dataList.push(newData);this.dataCache=null;},isDark:function(row,col){if(row<0||this.moduleCount<=row||col<0||this.moduleCount<=col){throw new Error(row+\",\"+col);}\n\t\t\treturn this.modules[row][col];},getModuleCount:function(){return this.moduleCount;},make:function(){this.makeImpl(false,this.getBestMaskPattern());},makeImpl:function(test,maskPattern){this.moduleCount=this.typeNumber*4+17;this.modules=new Array(this.moduleCount);for(var row=0;row<this.moduleCount;row++){this.modules[row]=new Array(this.moduleCount);for(var col=0;col<this.moduleCount;col++){this.modules[row][col]=null;}}\n\t\t\tthis.setupPositionProbePattern(0,0);this.setupPositionProbePattern(this.moduleCount-7,0);this.setupPositionProbePattern(0,this.moduleCount-7);this.setupPositionAdjustPattern();this.setupTimingPattern();this.setupTypeInfo(test,maskPattern);if(this.typeNumber>=7){this.setupTypeNumber(test);}\n\t\t\tif(this.dataCache==null){this.dataCache=QRCodeModel.createData(this.typeNumber,this.errorCorrectLevel,this.dataList);}\n\t\t\tthis.mapData(this.dataCache,maskPattern);},setupPositionProbePattern:function(row,col){for(var r=-1;r<=7;r++){if(row+r<=-1||this.moduleCount<=row+r)continue;for(var c=-1;c<=7;c++){if(col+c<=-1||this.moduleCount<=col+c)continue;if((0<=r&&r<=6&&(c==0||c==6))||(0<=c&&c<=6&&(r==0||r==6))||(2<=r&&r<=4&&2<=c&&c<=4)){this.modules[row+r][col+c]=true;}else{this.modules[row+r][col+c]=false;}}}},getBestMaskPattern:function(){var minLostPoint=0;var pattern=0;for(var i=0;i<8;i++){this.makeImpl(true,i);var lostPoint=QRUtil.getLostPoint(this);if(i==0||minLostPoint>lostPoint){minLostPoint=lostPoint;pattern=i;}}\n\t\t\treturn pattern;},createMovieClip:function(target_mc,instance_name,depth){var qr_mc=target_mc.createEmptyMovieClip(instance_name,depth);var cs=1;this.make();for(var row=0;row<this.modules.length;row++){var y=row*cs;for(var col=0;col<this.modules[row].length;col++){var x=col*cs;var dark=this.modules[row][col];if(dark){qr_mc.beginFill(0,100);qr_mc.moveTo(x,y);qr_mc.lineTo(x+cs,y);qr_mc.lineTo(x+cs,y+cs);qr_mc.lineTo(x,y+cs);qr_mc.endFill();}}}\n\t\t\treturn qr_mc;},setupTimingPattern:function(){for(var r=8;r<this.moduleCount-8;r++){if(this.modules[r][6]!=null){continue;}\n\t\t\tthis.modules[r][6]=(r%2==0);}\n\t\t\tfor(var c=8;c<this.moduleCount-8;c++){if(this.modules[6][c]!=null){continue;}\n\t\t\t\tthis.modules[6][c]=(c%2==0);}},setupPositionAdjustPattern:function(){var pos=QRUtil.getPatternPosition(this.typeNumber);for(var i=0;i<pos.length;i++){for(var j=0;j<pos.length;j++){var row=pos[i];var col=pos[j];if(this.modules[row][col]!=null){continue;}\n\t\t\tfor(var r=-2;r<=2;r++){for(var c=-2;c<=2;c++){if(r==-2||r==2||c==-2||c==2||(r==0&&c==0)){this.modules[row+r][col+c]=true;}else{this.modules[row+r][col+c]=false;}}}}}},setupTypeNumber:function(test){var bits=QRUtil.getBCHTypeNumber(this.typeNumber);for(var i=0;i<18;i++){var mod=(!test&&((bits>>i)&1)==1);this.modules[Math.floor(i/3)][i%3+this.moduleCount-8-3]=mod;}\n\t\t\tfor(var i=0;i<18;i++){var mod=(!test&&((bits>>i)&1)==1);this.modules[i%3+this.moduleCount-8-3][Math.floor(i/3)]=mod;}},setupTypeInfo:function(test,maskPattern){var data=(this.errorCorrectLevel<<3)|maskPattern;var bits=QRUtil.getBCHTypeInfo(data);for(var i=0;i<15;i++){var mod=(!test&&((bits>>i)&1)==1);if(i<6){this.modules[i][8]=mod;}else if(i<8){this.modules[i+1][8]=mod;}else{this.modules[this.moduleCount-15+i][8]=mod;}}\n\t\t\tfor(var i=0;i<15;i++){var mod=(!test&&((bits>>i)&1)==1);if(i<8){this.modules[8][this.moduleCount-i-1]=mod;}else if(i<9){this.modules[8][15-i-1+1]=mod;}else{this.modules[8][15-i-1]=mod;}}\n\t\t\tthis.modules[this.moduleCount-8][8]=(!test);},mapData:function(data,maskPattern){var inc=-1;var row=this.moduleCount-1;var bitIndex=7;var byteIndex=0;for(var col=this.moduleCount-1;col>0;col-=2){if(col==6)col--;while(true){for(var c=0;c<2;c++){if(this.modules[row][col-c]==null){var dark=false;if(byteIndex<data.length){dark=(((data[byteIndex]>>>bitIndex)&1)==1);}\n\t\t\tvar mask=QRUtil.getMask(maskPattern,row,col-c);if(mask){dark=!dark;}\n\t\t\tthis.modules[row][col-c]=dark;bitIndex--;if(bitIndex==-1){byteIndex++;bitIndex=7;}}}\n\t\t\trow+=inc;if(row<0||this.moduleCount<=row){row-=inc;inc=-inc;break;}}}}};QRCodeModel.PAD0=0xEC;QRCodeModel.PAD1=0x11;QRCodeModel.createData=function(typeNumber,errorCorrectLevel,dataList){var rsBlocks=QRRSBlock.getRSBlocks(typeNumber,errorCorrectLevel);var buffer=new QRBitBuffer();for(var i=0;i<dataList.length;i++){var data=dataList[i];buffer.put(data.mode,4);buffer.put(data.getLength(),QRUtil.getLengthInBits(data.mode,typeNumber));data.write(buffer);}\n\t\tvar totalDataCount=0;for(var i=0;i<rsBlocks.length;i++){totalDataCount+=rsBlocks[i].dataCount;}\n\t\tif(buffer.getLengthInBits()>totalDataCount*8){throw new Error(\"code length overflow. (\"\n\t\t\t+buffer.getLengthInBits()\n\t\t\t+\">\"\n\t\t\t+totalDataCount*8\n\t\t\t+\")\");}\n\t\tif(buffer.getLengthInBits()+4<=totalDataCount*8){buffer.put(0,4);}\n\t\twhile(buffer.getLengthInBits()%8!=0){buffer.putBit(false);}\n\t\twhile(true){if(buffer.getLengthInBits()>=totalDataCount*8){break;}\n\t\t\tbuffer.put(QRCodeModel.PAD0,8);if(buffer.getLengthInBits()>=totalDataCount*8){break;}\n\t\t\tbuffer.put(QRCodeModel.PAD1,8);}\n\t\treturn QRCodeModel.createBytes(buffer,rsBlocks);};QRCodeModel.createBytes=function(buffer,rsBlocks){var offset=0;var maxDcCount=0;var maxEcCount=0;var dcdata=new Array(rsBlocks.length);var ecdata=new Array(rsBlocks.length);for(var r=0;r<rsBlocks.length;r++){var dcCount=rsBlocks[r].dataCount;var ecCount=rsBlocks[r].totalCount-dcCount;maxDcCount=Math.max(maxDcCount,dcCount);maxEcCount=Math.max(maxEcCount,ecCount);dcdata[r]=new Array(dcCount);for(var i=0;i<dcdata[r].length;i++){dcdata[r][i]=0xff&buffer.buffer[i+offset];}\n\t\toffset+=dcCount;var rsPoly=QRUtil.getErrorCorrectPolynomial(ecCount);var rawPoly=new QRPolynomial(dcdata[r],rsPoly.getLength()-1);var modPoly=rawPoly.mod(rsPoly);ecdata[r]=new Array(rsPoly.getLength()-1);for(var i=0;i<ecdata[r].length;i++){var modIndex=i+modPoly.getLength()-ecdata[r].length;ecdata[r][i]=(modIndex>=0)?modPoly.get(modIndex):0;}}\n\t\tvar totalCodeCount=0;for(var i=0;i<rsBlocks.length;i++){totalCodeCount+=rsBlocks[i].totalCount;}\n\t\tvar data=new Array(totalCodeCount);var index=0;for(var i=0;i<maxDcCount;i++){for(var r=0;r<rsBlocks.length;r++){if(i<dcdata[r].length){data[index++]=dcdata[r][i];}}}\n\t\tfor(var i=0;i<maxEcCount;i++){for(var r=0;r<rsBlocks.length;r++){if(i<ecdata[r].length){data[index++]=ecdata[r][i];}}}\n\t\treturn data;};var QRMode={MODE_NUMBER:1<<0,MODE_ALPHA_NUM:1<<1,MODE_8BIT_BYTE:1<<2,MODE_KANJI:1<<3};var QRErrorCorrectLevel={L:1,M:0,Q:3,H:2};var QRMaskPattern={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};var QRUtil={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:(1<<10)|(1<<8)|(1<<5)|(1<<4)|(1<<2)|(1<<1)|(1<<0),G18:(1<<12)|(1<<11)|(1<<10)|(1<<9)|(1<<8)|(1<<5)|(1<<2)|(1<<0),G15_MASK:(1<<14)|(1<<12)|(1<<10)|(1<<4)|(1<<1),getBCHTypeInfo:function(data){var d=data<<10;while(QRUtil.getBCHDigit(d)-QRUtil.getBCHDigit(QRUtil.G15)>=0){d^=(QRUtil.G15<<(QRUtil.getBCHDigit(d)-QRUtil.getBCHDigit(QRUtil.G15)));}\n\t\t\treturn((data<<10)|d)^QRUtil.G15_MASK;},getBCHTypeNumber:function(data){var d=data<<12;while(QRUtil.getBCHDigit(d)-QRUtil.getBCHDigit(QRUtil.G18)>=0){d^=(QRUtil.G18<<(QRUtil.getBCHDigit(d)-QRUtil.getBCHDigit(QRUtil.G18)));}\n\t\t\treturn(data<<12)|d;},getBCHDigit:function(data){var digit=0;while(data!=0){digit++;data>>>=1;}\n\t\t\treturn digit;},getPatternPosition:function(typeNumber){return QRUtil.PATTERN_POSITION_TABLE[typeNumber-1];},getMask:function(maskPattern,i,j){switch(maskPattern){case QRMaskPattern.PATTERN000:return(i+j)%2==0;case QRMaskPattern.PATTERN001:return i%2==0;case QRMaskPattern.PATTERN010:return j%3==0;case QRMaskPattern.PATTERN011:return(i+j)%3==0;case QRMaskPattern.PATTERN100:return(Math.floor(i/2)+Math.floor(j/3))%2==0;case QRMaskPattern.PATTERN101:return(i*j)%2+(i*j)%3==0;case QRMaskPattern.PATTERN110:return((i*j)%2+(i*j)%3)%2==0;case QRMaskPattern.PATTERN111:return((i*j)%3+(i+j)%2)%2==0;default:throw new Error(\"bad maskPattern:\"+maskPattern);}},getErrorCorrectPolynomial:function(errorCorrectLength){var a=new QRPolynomial([1],0);for(var i=0;i<errorCorrectLength;i++){a=a.multiply(new QRPolynomial([1,QRMath.gexp(i)],0));}\n\t\t\treturn a;},getLengthInBits:function(mode,type){if(1<=type&&type<10){switch(mode){case QRMode.MODE_NUMBER:return 10;case QRMode.MODE_ALPHA_NUM:return 9;case QRMode.MODE_8BIT_BYTE:return 8;case QRMode.MODE_KANJI:return 8;default:throw new Error(\"mode:\"+mode);}}else if(type<27){switch(mode){case QRMode.MODE_NUMBER:return 12;case QRMode.MODE_ALPHA_NUM:return 11;case QRMode.MODE_8BIT_BYTE:return 16;case QRMode.MODE_KANJI:return 10;default:throw new Error(\"mode:\"+mode);}}else if(type<41){switch(mode){case QRMode.MODE_NUMBER:return 14;case QRMode.MODE_ALPHA_NUM:return 13;case QRMode.MODE_8BIT_BYTE:return 16;case QRMode.MODE_KANJI:return 12;default:throw new Error(\"mode:\"+mode);}}else{throw new Error(\"type:\"+type);}},getLostPoint:function(qrCode){var moduleCount=qrCode.getModuleCount();var lostPoint=0;for(var row=0;row<moduleCount;row++){for(var col=0;col<moduleCount;col++){var sameCount=0;var dark=qrCode.isDark(row,col);for(var r=-1;r<=1;r++){if(row+r<0||moduleCount<=row+r){continue;}\n\t\t\tfor(var c=-1;c<=1;c++){if(col+c<0||moduleCount<=col+c){continue;}\n\t\t\t\tif(r==0&&c==0){continue;}\n\t\t\t\tif(dark==qrCode.isDark(row+r,col+c)){sameCount++;}}}\n\t\t\tif(sameCount>5){lostPoint+=(3+sameCount-5);}}}\n\t\t\tfor(var row=0;row<moduleCount-1;row++){for(var col=0;col<moduleCount-1;col++){var count=0;if(qrCode.isDark(row,col))count++;if(qrCode.isDark(row+1,col))count++;if(qrCode.isDark(row,col+1))count++;if(qrCode.isDark(row+1,col+1))count++;if(count==0||count==4){lostPoint+=3;}}}\n\t\t\tfor(var row=0;row<moduleCount;row++){for(var col=0;col<moduleCount-6;col++){if(qrCode.isDark(row,col)&&!qrCode.isDark(row,col+1)&&qrCode.isDark(row,col+2)&&qrCode.isDark(row,col+3)&&qrCode.isDark(row,col+4)&&!qrCode.isDark(row,col+5)&&qrCode.isDark(row,col+6)){lostPoint+=40;}}}\n\t\t\tfor(var col=0;col<moduleCount;col++){for(var row=0;row<moduleCount-6;row++){if(qrCode.isDark(row,col)&&!qrCode.isDark(row+1,col)&&qrCode.isDark(row+2,col)&&qrCode.isDark(row+3,col)&&qrCode.isDark(row+4,col)&&!qrCode.isDark(row+5,col)&&qrCode.isDark(row+6,col)){lostPoint+=40;}}}\n\t\t\tvar darkCount=0;for(var col=0;col<moduleCount;col++){for(var row=0;row<moduleCount;row++){if(qrCode.isDark(row,col)){darkCount++;}}}\n\t\t\tvar ratio=Math.abs(100*darkCount/moduleCount/moduleCount-50)/5;lostPoint+=ratio*10;return lostPoint;}};var QRMath={glog:function(n){if(n<1){throw new Error(\"glog(\"+n+\")\");}\n\t\t\treturn QRMath.LOG_TABLE[n];},gexp:function(n){while(n<0){n+=255;}\n\t\t\twhile(n>=256){n-=255;}\n\t\t\treturn QRMath.EXP_TABLE[n];},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)};for(var i=0;i<8;i++){QRMath.EXP_TABLE[i]=1<<i;}\n\tfor(var i=8;i<256;i++){QRMath.EXP_TABLE[i]=QRMath.EXP_TABLE[i-4]^QRMath.EXP_TABLE[i-5]^QRMath.EXP_TABLE[i-6]^QRMath.EXP_TABLE[i-8];}\n\tfor(var i=0;i<255;i++){QRMath.LOG_TABLE[QRMath.EXP_TABLE[i]]=i;}\n\tfunction QRPolynomial(num,shift){if(num.length==undefined){throw new Error(num.length+\"/\"+shift);}\n\t\tvar offset=0;while(offset<num.length&&num[offset]==0){offset++;}\n\t\tthis.num=new Array(num.length-offset+shift);for(var i=0;i<num.length-offset;i++){this.num[i]=num[i+offset];}}\n\tQRPolynomial.prototype={get:function(index){return this.num[index];},getLength:function(){return this.num.length;},multiply:function(e){var num=new Array(this.getLength()+e.getLength()-1);for(var i=0;i<this.getLength();i++){for(var j=0;j<e.getLength();j++){num[i+j]^=QRMath.gexp(QRMath.glog(this.get(i))+QRMath.glog(e.get(j)));}}\n\t\t\treturn new QRPolynomial(num,0);},mod:function(e){if(this.getLength()-e.getLength()<0){return this;}\n\t\t\tvar ratio=QRMath.glog(this.get(0))-QRMath.glog(e.get(0));var num=new Array(this.getLength());for(var i=0;i<this.getLength();i++){num[i]=this.get(i);}\n\t\t\tfor(var i=0;i<e.getLength();i++){num[i]^=QRMath.gexp(QRMath.glog(e.get(i))+ratio);}\n\t\t\treturn new QRPolynomial(num,0).mod(e);}};function QRRSBlock(totalCount,dataCount){this.totalCount=totalCount;this.dataCount=dataCount;}\n\tQRRSBlock.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];QRRSBlock.getRSBlocks=function(typeNumber,errorCorrectLevel){var rsBlock=QRRSBlock.getRsBlockTable(typeNumber,errorCorrectLevel);if(rsBlock==undefined){throw new Error(\"bad rs block @ typeNumber:\"+typeNumber+\"/errorCorrectLevel:\"+errorCorrectLevel);}\n\t\tvar length=rsBlock.length/3;var list=[];for(var i=0;i<length;i++){var count=rsBlock[i*3+0];var totalCount=rsBlock[i*3+1];var dataCount=rsBlock[i*3+2];for(var j=0;j<count;j++){list.push(new QRRSBlock(totalCount,dataCount));}}\n\t\treturn list;};QRRSBlock.getRsBlockTable=function(typeNumber,errorCorrectLevel){switch(errorCorrectLevel){case QRErrorCorrectLevel.L:return QRRSBlock.RS_BLOCK_TABLE[(typeNumber-1)*4+0];case QRErrorCorrectLevel.M:return QRRSBlock.RS_BLOCK_TABLE[(typeNumber-1)*4+1];case QRErrorCorrectLevel.Q:return QRRSBlock.RS_BLOCK_TABLE[(typeNumber-1)*4+2];case QRErrorCorrectLevel.H:return QRRSBlock.RS_BLOCK_TABLE[(typeNumber-1)*4+3];default:return undefined;}};function QRBitBuffer(){this.buffer=[];this.length=0;}\n\tQRBitBuffer.prototype={get:function(index){var bufIndex=Math.floor(index/8);return((this.buffer[bufIndex]>>>(7-index%8))&1)==1;},put:function(num,length){for(var i=0;i<length;i++){this.putBit(((num>>>(length-i-1))&1)==1);}},getLengthInBits:function(){return this.length;},putBit:function(bit){var bufIndex=Math.floor(this.length/8);if(this.buffer.length<=bufIndex){this.buffer.push(0);}\n\t\t\tif(bit){this.buffer[bufIndex]|=(0x80>>>(this.length%8));}\n\t\t\tthis.length++;}};var QRCodeLimitLength=[[17,14,11,7],[32,26,20,14],[53,42,32,24],[78,62,46,34],[106,84,60,44],[134,106,74,58],[154,122,86,64],[192,152,108,84],[230,180,130,98],[271,213,151,119],[321,251,177,137],[367,287,203,155],[425,331,241,177],[458,362,258,194],[520,412,292,220],[586,450,322,250],[644,504,364,280],[718,560,394,310],[792,624,442,338],[858,666,482,382],[929,711,509,403],[1003,779,565,439],[1091,857,611,461],[1171,911,661,511],[1273,997,715,535],[1367,1059,751,593],[1465,1125,805,625],[1528,1190,868,658],[1628,1264,908,698],[1732,1370,982,742],[1840,1452,1030,790],[1952,1538,1112,842],[2068,1628,1168,898],[2188,1722,1228,958],[2303,1809,1283,983],[2431,1911,1351,1051],[2563,1989,1423,1093],[2699,2099,1499,1139],[2809,2213,1579,1219],[2953,2331,1663,1273]];\n\n\tfunction _isSupportCanvas() {\n\t\treturn typeof CanvasRenderingContext2D != \"undefined\";\n\t}\n\n\t// android 2.x doesn't support Data-URI spec\n\tfunction _getAndroid() {\n\t\tvar android = false;\n\t\tvar sAgent = navigator.userAgent;\n\n\t\tif (/android/i.test(sAgent)) { // android\n\t\t\tandroid = true;\n\t\t\tvar aMat = sAgent.toString().match(/android ([0-9]\\.[0-9])/i);\n\n\t\t\tif (aMat && aMat[1]) {\n\t\t\t\tandroid = parseFloat(aMat[1]);\n\t\t\t}\n\t\t}\n\n\t\treturn android;\n\t}\n\n\tvar svgDrawer = (function() {\n\n\t\tvar Drawing = function (el, htOption) {\n\t\t\tthis._el = el;\n\t\t\tthis._htOption = htOption;\n\t\t};\n\n\t\tDrawing.prototype.draw = function (oQRCode) {\n\t\t\tvar _htOption = this._htOption;\n\t\t\tvar _el = this._el;\n\t\t\tvar nCount = oQRCode.getModuleCount();\n\t\t\tvar nWidth = Math.floor(_htOption.width / nCount);\n\t\t\tvar nHeight = Math.floor(_htOption.height / nCount);\n\n\t\t\tthis.clear();\n\n\t\t\tfunction makeSVG(tag, attrs) {\n\t\t\t\tvar el = document.createElementNS('http://www.w3.org/2000/svg', tag);\n\t\t\t\tfor (var k in attrs)\n\t\t\t\t\tif (attrs.hasOwnProperty(k)) el.setAttribute(k, attrs[k]);\n\t\t\t\treturn el;\n\t\t\t}\n\n\t\t\tvar svg = makeSVG(\"svg\" , {'viewBox': '0 0 ' + String(nCount) + \" \" + String(nCount), 'width': '100%', 'height': '100%', 'fill': _htOption.colorLight});\n\t\t\tsvg.setAttributeNS(\"http://www.w3.org/2000/xmlns/\", \"xmlns:xlink\", \"http://www.w3.org/1999/xlink\");\n\t\t\t_el.appendChild(svg);\n\n\t\t\tsvg.appendChild(makeSVG(\"rect\", {\"fill\": _htOption.colorLight, \"width\": \"100%\", \"height\": \"100%\"}));\n\t\t\tsvg.appendChild(makeSVG(\"rect\", {\"fill\": _htOption.colorDark, \"width\": \"1\", \"height\": \"1\", \"id\": \"template\"}));\n\n\t\t\tfor (var row = 0; row < nCount; row++) {\n\t\t\t\tfor (var col = 0; col < nCount; col++) {\n\t\t\t\t\tif (oQRCode.isDark(row, col)) {\n\t\t\t\t\t\tvar child = makeSVG(\"use\", {\"x\": String(col), \"y\": String(row)});\n\t\t\t\t\t\tchild.setAttributeNS(\"http://www.w3.org/1999/xlink\", \"href\", \"#template\")\n\t\t\t\t\t\tsvg.appendChild(child);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t};\n\t\tDrawing.prototype.clear = function () {\n\t\t\twhile (this._el.hasChildNodes())\n\t\t\t\tthis._el.removeChild(this._el.lastChild);\n\t\t};\n\t\treturn Drawing;\n\t})();\n\n\tvar useSVG = document.documentElement.tagName.toLowerCase() === \"svg\";\n\n\t// Drawing in DOM by using Table tag\n\tvar Drawing = useSVG ? svgDrawer : !_isSupportCanvas() ? (function () {\n\t\tvar Drawing = function (el, htOption) {\n\t\t\tthis._el = el;\n\t\t\tthis._htOption = htOption;\n\t\t};\n\n\t\t/**\n\t\t * Draw the QRCode\n\t\t *\n\t\t * @param {QRCode} oQRCode\n\t\t */\n\t\tDrawing.prototype.draw = function (oQRCode) {\n\t\t\tvar _htOption = this._htOption;\n\t\t\tvar _el = this._el;\n\t\t\tvar nCount = oQRCode.getModuleCount();\n\t\t\tvar nWidth = Math.floor(_htOption.width / nCount);\n\t\t\tvar nHeight = Math.floor(_htOption.height / nCount);\n\t\t\tvar aHTML = ['<table style=\"border:0;border-collapse:collapse;\">'];\n\n\t\t\tfor (var row = 0; row < nCount; row++) {\n\t\t\t\taHTML.push('<tr>');\n\n\t\t\t\tfor (var col = 0; col < nCount; col++) {\n\t\t\t\t\taHTML.push('<td style=\"border:0;border-collapse:collapse;padding:0;margin:0;width:' + nWidth + 'px;height:' + nHeight + 'px;background-color:' + (oQRCode.isDark(row, col) ? _htOption.colorDark : _htOption.colorLight) + ';\"></td>');\n\t\t\t\t}\n\n\t\t\t\taHTML.push('</tr>');\n\t\t\t}\n\n\t\t\taHTML.push('</table>');\n\t\t\t_el.innerHTML = aHTML.join('');\n\n\t\t\t// Fix the margin values as real size.\n\t\t\tvar elTable = _el.childNodes[0];\n\t\t\tvar nLeftMarginTable = (_htOption.width - elTable.offsetWidth) / 2;\n\t\t\tvar nTopMarginTable = (_htOption.height - elTable.offsetHeight) / 2;\n\n\t\t\tif (nLeftMarginTable > 0 && nTopMarginTable > 0) {\n\t\t\t\telTable.style.margin = nTopMarginTable + \"px \" + nLeftMarginTable + \"px\";\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * Clear the QRCode\n\t\t */\n\t\tDrawing.prototype.clear = function () {\n\t\t\tthis._el.innerHTML = '';\n\t\t};\n\n\t\treturn Drawing;\n\t})() : (function () { // Drawing in Canvas\n\t\tfunction _onMakeImage() {\n\t\t\tthis._elImage.src = this._elCanvas.toDataURL(\"image/png\");\n\t\t\tthis._elImage.style.display = \"block\";\n\t\t\tthis._elCanvas.style.display = \"none\";\n\t\t}\n\n\t\t// Android 2.1 bug workaround\n\t\t// http://code.google.com/p/android/issues/detail?id=5141\n\t\tif (this && this._android && this._android <= 2.1) {\n\t\t\tvar factor = 1 / window.devicePixelRatio;\n\t\t\tvar drawImage = CanvasRenderingContext2D.prototype.drawImage;\n\t\t\tCanvasRenderingContext2D.prototype.drawImage = function (image, sx, sy, sw, sh, dx, dy, dw, dh) {\n\t\t\t\tif ((\"nodeName\" in image) && /img/i.test(image.nodeName)) {\n\t\t\t\t\tfor (var i = arguments.length - 1; i >= 1; i--) {\n\t\t\t\t\t\targuments[i] = arguments[i] * factor;\n\t\t\t\t\t}\n\t\t\t\t} else if (typeof dw == \"undefined\") {\n\t\t\t\t\targuments[1] *= factor;\n\t\t\t\t\targuments[2] *= factor;\n\t\t\t\t\targuments[3] *= factor;\n\t\t\t\t\targuments[4] *= factor;\n\t\t\t\t}\n\n\t\t\t\tdrawImage.apply(this, arguments);\n\t\t\t};\n\t\t}\n\n\t\t/**\n\t\t * Check whether the user's browser supports Data URI or not\n\t\t *\n\t\t * @private\n\t\t * @param {Function} fSuccess Occurs if it supports Data URI\n\t\t * @param {Function} fFail Occurs if it doesn't support Data URI\n\t\t */\n\t\tfunction _safeSetDataURI(fSuccess, fFail) {\n\t\t\tvar self = this;\n\t\t\tself._fFail = fFail;\n\t\t\tself._fSuccess = fSuccess;\n\n\t\t\t// Check it just once\n\t\t\tif (self._bSupportDataURI === null) {\n\t\t\t\tvar el = document.createElement(\"img\");\n\t\t\t\tvar fOnError = function() {\n\t\t\t\t\tself._bSupportDataURI = false;\n\n\t\t\t\t\tif (self._fFail) {\n\t\t\t\t\t\tself._fFail.call(self);\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t\tvar fOnSuccess = function() {\n\t\t\t\t\tself._bSupportDataURI = true;\n\n\t\t\t\t\tif (self._fSuccess) {\n\t\t\t\t\t\tself._fSuccess.call(self);\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tel.onabort = fOnError;\n\t\t\t\tel.onerror = fOnError;\n\t\t\t\tel.onload = fOnSuccess;\n\t\t\t\tel.src = \"data:image/gif;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAHElEQVQI12P4//8/w38GIAXDIBKE0DHxgljNBAAO9TXL0Y4OHwAAAABJRU5ErkJggg==\"; // the Image contains 1px data.\n\t\t\t\treturn;\n\t\t\t} else if (self._bSupportDataURI === true && self._fSuccess) {\n\t\t\t\tself._fSuccess.call(self);\n\t\t\t} else if (self._bSupportDataURI === false && self._fFail) {\n\t\t\t\tself._fFail.call(self);\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * Drawing QRCode by using canvas\n\t\t *\n\t\t * @constructor\n\t\t * @param {HTMLElement} el\n\t\t * @param {Object} htOption QRCode Options\n\t\t */\n\t\tvar Drawing = function (el, htOption) {\n\t\t\tthis._bIsPainted = false;\n\t\t\tthis._android = _getAndroid();\n\n\t\t\tthis._htOption = htOption;\n\t\t\tthis._elCanvas = document.createElement(\"canvas\");\n\t\t\tthis._elCanvas.width = htOption.width;\n\t\t\tthis._elCanvas.height = htOption.height;\n\t\t\tel.appendChild(this._elCanvas);\n\t\t\tthis._el = el;\n\t\t\tthis._oContext = this._elCanvas.getContext(\"2d\");\n\t\t\tthis._bIsPainted = false;\n\t\t\tthis._elImage = document.createElement(\"img\");\n\t\t\tthis._elImage.alt = \"Scan me!\";\n\t\t\tthis._elImage.style.display = \"none\";\n\t\t\tthis._el.appendChild(this._elImage);\n\t\t\tthis._bSupportDataURI = null;\n\t\t};\n\n\t\t/**\n\t\t * Draw the QRCode\n\t\t *\n\t\t * @param {QRCode} oQRCode\n\t\t */\n\t\tDrawing.prototype.draw = function (oQRCode) {\n\t\t\tvar _elImage = this._elImage;\n\t\t\tvar _oContext = this._oContext;\n\t\t\tvar _htOption = this._htOption;\n\n\t\t\tvar nCount = oQRCode.getModuleCount();\n\t\t\tvar nWidth = _htOption.width / nCount;\n\t\t\tvar nHeight = _htOption.height / nCount;\n\t\t\tvar nRoundedWidth = Math.round(nWidth);\n\t\t\tvar nRoundedHeight = Math.round(nHeight);\n\n\t\t\t_elImage.style.display = \"none\";\n\t\t\tthis.clear();\n\n\t\t\tfor (var row = 0; row < nCount; row++) {\n\t\t\t\tfor (var col = 0; col < nCount; col++) {\n\t\t\t\t\tvar bIsDark = oQRCode.isDark(row, col);\n\t\t\t\t\tvar nLeft = col * nWidth;\n\t\t\t\t\tvar nTop = row * nHeight;\n\t\t\t\t\t_oContext.strokeStyle = bIsDark ? _htOption.colorDark : _htOption.colorLight;\n\t\t\t\t\t_oContext.lineWidth = 1;\n\t\t\t\t\t_oContext.fillStyle = bIsDark ? _htOption.colorDark : _htOption.colorLight;\n\t\t\t\t\t_oContext.fillRect(nLeft, nTop, nWidth, nHeight);\n\n\t\t\t\t\t// 안티 앨리어싱 방지 처리\n\t\t\t\t\t_oContext.strokeRect(\n\t\t\t\t\t\tMath.floor(nLeft) + 0.5,\n\t\t\t\t\t\tMath.floor(nTop) + 0.5,\n\t\t\t\t\t\tnRoundedWidth,\n\t\t\t\t\t\tnRoundedHeight\n\t\t\t\t\t);\n\n\t\t\t\t\t_oContext.strokeRect(\n\t\t\t\t\t\tMath.ceil(nLeft) - 0.5,\n\t\t\t\t\t\tMath.ceil(nTop) - 0.5,\n\t\t\t\t\t\tnRoundedWidth,\n\t\t\t\t\t\tnRoundedHeight\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tthis._bIsPainted = true;\n\t\t};\n\n\t\t/**\n\t\t * Make the image from Canvas if the browser supports Data URI.\n\t\t */\n\t\tDrawing.prototype.makeImage = function () {\n\t\t\tif (this._bIsPainted) {\n\t\t\t\t_safeSetDataURI.call(this, _onMakeImage);\n\t\t\t}\n\t\t};\n\n\t\t/**\n\t\t * Return whether the QRCode is painted or not\n\t\t *\n\t\t * @return {Boolean}\n\t\t */\n\t\tDrawing.prototype.isPainted = function () {\n\t\t\treturn this._bIsPainted;\n\t\t};\n\n\t\t/**\n\t\t * Clear the QRCode\n\t\t */\n\t\tDrawing.prototype.clear = function () {\n\t\t\tthis._oContext.clearRect(0, 0, this._elCanvas.width, this._elCanvas.height);\n\t\t\tthis._bIsPainted = false;\n\t\t};\n\n\t\t/**\n\t\t * @private\n\t\t * @param {Number} nNumber\n\t\t */\n\t\tDrawing.prototype.round = function (nNumber) {\n\t\t\tif (!nNumber) {\n\t\t\t\treturn nNumber;\n\t\t\t}\n\n\t\t\treturn Math.floor(nNumber * 1000) / 1000;\n\t\t};\n\n\t\treturn Drawing;\n\t})();\n\n\t/**\n\t * Get the type by string length\n\t *\n\t * @private\n\t * @param {String} sText\n\t * @param {Number} nCorrectLevel\n\t * @return {Number} type\n\t */\n\tfunction _getTypeNumber(sText, nCorrectLevel) {\n\t\tvar nType = 1;\n\t\tvar length = _getUTF8Length(sText);\n\n\t\tfor (var i = 0, len = QRCodeLimitLength.length; i < len; i++) {\n\t\t\tvar nLimit = 0;\n\n\t\t\tswitch (nCorrectLevel) {\n\t\t\t\tcase QRErrorCorrectLevel.L :\n\t\t\t\t\tnLimit = QRCodeLimitLength[i][0];\n\t\t\t\t\tbreak;\n\t\t\t\tcase QRErrorCorrectLevel.M :\n\t\t\t\t\tnLimit = QRCodeLimitLength[i][1];\n\t\t\t\t\tbreak;\n\t\t\t\tcase QRErrorCorrectLevel.Q :\n\t\t\t\t\tnLimit = QRCodeLimitLength[i][2];\n\t\t\t\t\tbreak;\n\t\t\t\tcase QRErrorCorrectLevel.H :\n\t\t\t\t\tnLimit = QRCodeLimitLength[i][3];\n\t\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tif (length <= nLimit) {\n\t\t\t\tbreak;\n\t\t\t} else {\n\t\t\t\tnType++;\n\t\t\t}\n\t\t}\n\n\t\tif (nType > QRCodeLimitLength.length) {\n\t\t\tthrow new Error(\"Too long data\");\n\t\t}\n\n\t\treturn nType;\n\t}\n\n\tfunction _getUTF8Length(sText) {\n\t\tvar replacedText = encodeURI(sText).toString().replace(/\\%[0-9a-fA-F]{2}/g, 'a');\n\t\treturn replacedText.length + (replacedText.length != sText ? 3 : 0);\n\t}\n\n\t/**\n\t * @class QRCode\n\t * @constructor\n\t * @example\n\t * new QRCode(document.getElementById(\"test\"), \"http://jindo.dev.naver.com/collie\");\n\t *\n\t * @example\n\t * var oQRCode = new QRCode(\"test\", {\n\t *    text : \"http://naver.com\",\n\t *    width : 128,\n\t *    height : 128\n\t * });\n\t *\n\t * oQRCode.clear(); // Clear the QRCode.\n\t * oQRCode.makeCode(\"http://map.naver.com\"); // Re-create the QRCode.\n\t *\n\t * @param {HTMLElement|String} el target element or 'id' attribute of element.\n\t * @param {Object|String} vOption\n\t * @param {String} vOption.text QRCode link data\n\t * @param {Number} [vOption.width=256]\n\t * @param {Number} [vOption.height=256]\n\t * @param {String} [vOption.colorDark=\"#000000\"]\n\t * @param {String} [vOption.colorLight=\"#ffffff\"]\n\t * @param {QRCode.CorrectLevel} [vOption.correctLevel=QRCode.CorrectLevel.H] [L|M|Q|H]\n\t */\n\tQRCode = function (el, vOption) {\n\t\tthis._htOption = {\n\t\t\twidth : 256,\n\t\t\theight : 256,\n\t\t\ttypeNumber : 4,\n\t\t\tcolorDark : \"#000000\",\n\t\t\tcolorLight : \"#ffffff\",\n\t\t\tcorrectLevel : QRErrorCorrectLevel.H\n\t\t};\n\n\t\tif (typeof vOption === 'string') {\n\t\t\tvOption\t= {\n\t\t\t\ttext : vOption\n\t\t\t};\n\t\t}\n\n\t\t// Overwrites options\n\t\tif (vOption) {\n\t\t\tfor (var i in vOption) {\n\t\t\t\tthis._htOption[i] = vOption[i];\n\t\t\t}\n\t\t}\n\n\t\tif (typeof el == \"string\") {\n\t\t\tel = document.getElementById(el);\n\t\t}\n\n\t\tif (this._htOption.useSVG) {\n\t\t\tDrawing = svgDrawer;\n\t\t}\n\n\t\tthis._android = _getAndroid();\n\t\tthis._el = el;\n\t\tthis._oQRCode = null;\n\t\tthis._oDrawing = new Drawing(this._el, this._htOption);\n\n\t\tif (this._htOption.text) {\n\t\t\tthis.makeCode(this._htOption.text);\n\t\t}\n\t};\n\n\t/**\n\t * Make the QRCode\n\t *\n\t * @param {String} sText link data\n\t */\n\tQRCode.prototype.makeCode = function (sText) {\n\t\tthis._oQRCode = new QRCodeModel(_getTypeNumber(sText, this._htOption.correctLevel), this._htOption.correctLevel);\n\t\tthis._oQRCode.addData(sText);\n\t\tthis._oQRCode.make();\n\t\tthis._el.title = sText;\n\t\tthis._oDrawing.draw(this._oQRCode);\n\t\tthis.makeImage();\n\t};\n\n\t/**\n\t * Make the Image from Canvas element\n\t * - It occurs automatically\n\t * - Android below 3 doesn't support Data-URI spec.\n\t *\n\t * @private\n\t */\n\tQRCode.prototype.makeImage = function () {\n\t\tif (typeof this._oDrawing.makeImage == \"function\" && (!this._android || this._android >= 3)) {\n\t\t\tthis._oDrawing.makeImage();\n\t\t}\n\t};\n\n\t/**\n\t * Clear the QRCode\n\t */\n\tQRCode.prototype.clear = function () {\n\t\tthis._oDrawing.clear();\n\t};\n\n\t/**\n\t * @name QRCode.CorrectLevel\n\t */\n\tQRCode.CorrectLevel = QRErrorCorrectLevel;\n\n\treturn QRCode;\n\n}));\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,QAAI;AAEJ,KAAC,SAAU,MAAM,SAAS;AAGzB,UAAI,OAAO,WAAW;AAAU,eAAO,UAAU,QAAQ;AAAA,eAGhD,OAAO,UAAU,cAAc,OAAO;AAAK,eAAO,OAAO;AAAA;AAG7D,aAAK,SAAS,QAAQ;AAAA,IAE5B,GAAE,SAAM,WAAY;AAenB,eAAS,WAAW,MAAM;AACzB,aAAK,OAAO,OAAO;AACnB,aAAK,OAAO;AACZ,aAAK,aAAa,CAAC;AAGnB,iBAASA,KAAI,GAAG,IAAI,KAAK,KAAK,QAAQA,KAAI,GAAGA,MAAK;AACjD,cAAI,YAAY,CAAC;AACjB,cAAI,OAAO,KAAK,KAAK,WAAWA,EAAC;AAEjC,cAAI,OAAO,OAAS;AACnB,sBAAU,CAAC,IAAI,OAAS,OAAO,aAAc;AAC7C,sBAAU,CAAC,IAAI,OAAS,OAAO,YAAa;AAC5C,sBAAU,CAAC,IAAI,OAAS,OAAO,UAAW;AAC1C,sBAAU,CAAC,IAAI,MAAQ,OAAO;AAAA,UAC/B,WAAW,OAAO,MAAO;AACxB,sBAAU,CAAC,IAAI,OAAS,OAAO,WAAY;AAC3C,sBAAU,CAAC,IAAI,OAAS,OAAO,UAAW;AAC1C,sBAAU,CAAC,IAAI,MAAQ,OAAO;AAAA,UAC/B,WAAW,OAAO,KAAM;AACvB,sBAAU,CAAC,IAAI,OAAS,OAAO,UAAW;AAC1C,sBAAU,CAAC,IAAI,MAAQ,OAAO;AAAA,UAC/B,OAAO;AACN,sBAAU,CAAC,IAAI;AAAA,UAChB;AAEA,eAAK,WAAW,KAAK,SAAS;AAAA,QAC/B;AAEA,aAAK,aAAa,MAAM,UAAU,OAAO,MAAM,CAAC,GAAG,KAAK,UAAU;AAElE,YAAI,KAAK,WAAW,UAAU,KAAK,KAAK,QAAQ;AAC/C,eAAK,WAAW,QAAQ,GAAG;AAC3B,eAAK,WAAW,QAAQ,GAAG;AAC3B,eAAK,WAAW,QAAQ,GAAG;AAAA,QAC5B;AAAA,MACD;AAEA,iBAAW,YAAY;AAAA,QACtB,WAAW,SAAU,QAAQ;AAC5B,iBAAO,KAAK,WAAW;AAAA,QACxB;AAAA,QACA,OAAO,SAAU,QAAQ;AACxB,mBAASA,KAAI,GAAG,IAAI,KAAK,WAAW,QAAQA,KAAI,GAAGA,MAAK;AACvD,mBAAO,IAAI,KAAK,WAAWA,EAAC,GAAG,CAAC;AAAA,UACjC;AAAA,QACD;AAAA,MACD;AAEA,eAAS,YAAY,YAAY,mBAAmB;AACnD,aAAK,aAAa;AAClB,aAAK,oBAAoB;AACzB,aAAK,UAAU;AACf,aAAK,cAAc;AACnB,aAAK,YAAY;AACjB,aAAK,WAAW,CAAC;AAAA,MAClB;AAEA,kBAAY,YAAU,EAAC,SAAQ,SAAS,MAAK;AAAC,YAAI,UAAQ,IAAI,WAAW,IAAI;AAAE,aAAK,SAAS,KAAK,OAAO;AAAE,aAAK,YAAU;AAAA,MAAK,GAAE,QAAO,SAAS,KAAI,KAAI;AAAC,YAAG,MAAI,KAAG,KAAK,eAAa,OAAK,MAAI,KAAG,KAAK,eAAa,KAAI;AAAC,gBAAM,IAAI,MAAM,MAAI,MAAI,GAAG;AAAA,QAAE;AACpP,eAAO,KAAK,QAAQ,GAAG,EAAE,GAAG;AAAA,MAAE,GAAE,gBAAe,WAAU;AAAC,eAAO,KAAK;AAAA,MAAY,GAAE,MAAK,WAAU;AAAC,aAAK,SAAS,OAAM,KAAK,mBAAmB,CAAC;AAAA,MAAE,GAAE,UAAS,SAAS,MAAK,aAAY;AAAC,aAAK,cAAY,KAAK,aAAW,IAAE;AAAG,aAAK,UAAQ,IAAI,MAAM,KAAK,WAAW;AAAE,iBAAQ,MAAI,GAAE,MAAI,KAAK,aAAY,OAAM;AAAC,eAAK,QAAQ,GAAG,IAAE,IAAI,MAAM,KAAK,WAAW;AAAE,mBAAQ,MAAI,GAAE,MAAI,KAAK,aAAY,OAAM;AAAC,iBAAK,QAAQ,GAAG,EAAE,GAAG,IAAE;AAAA,UAAK;AAAA,QAAC;AACva,aAAK,0BAA0B,GAAE,CAAC;AAAE,aAAK,0BAA0B,KAAK,cAAY,GAAE,CAAC;AAAE,aAAK,0BAA0B,GAAE,KAAK,cAAY,CAAC;AAAE,aAAK,2BAA2B;AAAE,aAAK,mBAAmB;AAAE,aAAK,cAAc,MAAK,WAAW;AAAE,YAAG,KAAK,cAAY,GAAE;AAAC,eAAK,gBAAgB,IAAI;AAAA,QAAE;AACjS,YAAG,KAAK,aAAW,MAAK;AAAC,eAAK,YAAU,YAAY,WAAW,KAAK,YAAW,KAAK,mBAAkB,KAAK,QAAQ;AAAA,QAAE;AACrH,aAAK,QAAQ,KAAK,WAAU,WAAW;AAAA,MAAE,GAAE,2BAA0B,SAAS,KAAI,KAAI;AAAC,iBAAQ,IAAE,IAAG,KAAG,GAAE,KAAI;AAAC,cAAG,MAAI,KAAG,MAAI,KAAK,eAAa,MAAI;AAAE;AAAS,mBAAQ,IAAE,IAAG,KAAG,GAAE,KAAI;AAAC,gBAAG,MAAI,KAAG,MAAI,KAAK,eAAa,MAAI;AAAE;AAAS,gBAAI,KAAG,KAAG,KAAG,MAAI,KAAG,KAAG,KAAG,MAAM,KAAG,KAAG,KAAG,MAAI,KAAG,KAAG,KAAG,MAAM,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,KAAG,GAAG;AAAC,mBAAK,QAAQ,MAAI,CAAC,EAAE,MAAI,CAAC,IAAE;AAAA,YAAK,OAAK;AAAC,mBAAK,QAAQ,MAAI,CAAC,EAAE,MAAI,CAAC,IAAE;AAAA,YAAM;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,GAAE,oBAAmB,WAAU;AAAC,YAAI,eAAa;AAAE,YAAI,UAAQ;AAAE,iBAAQA,KAAE,GAAEA,KAAE,GAAEA,MAAI;AAAC,eAAK,SAAS,MAAKA,EAAC;AAAE,cAAI,YAAU,OAAO,aAAa,IAAI;AAAE,cAAGA,MAAG,KAAG,eAAa,WAAU;AAAC,2BAAa;AAAU,sBAAQA;AAAA,UAAE;AAAA,QAAC;AACzlB,eAAO;AAAA,MAAQ,GAAE,iBAAgB,SAAS,WAAU,eAAc,OAAM;AAAC,YAAI,QAAM,UAAU,qBAAqB,eAAc,KAAK;AAAE,YAAI,KAAG;AAAE,aAAK,KAAK;AAAE,iBAAQ,MAAI,GAAE,MAAI,KAAK,QAAQ,QAAO,OAAM;AAAC,cAAI,IAAE,MAAI;AAAG,mBAAQ,MAAI,GAAE,MAAI,KAAK,QAAQ,GAAG,EAAE,QAAO,OAAM;AAAC,gBAAI,IAAE,MAAI;AAAG,gBAAI,OAAK,KAAK,QAAQ,GAAG,EAAE,GAAG;AAAE,gBAAG,MAAK;AAAC,oBAAM,UAAU,GAAE,GAAG;AAAE,oBAAM,OAAO,GAAE,CAAC;AAAE,oBAAM,OAAO,IAAE,IAAG,CAAC;AAAE,oBAAM,OAAO,IAAE,IAAG,IAAE,EAAE;AAAE,oBAAM,OAAO,GAAE,IAAE,EAAE;AAAE,oBAAM,QAAQ;AAAA,YAAE;AAAA,UAAC;AAAA,QAAC;AAC3b,eAAO;AAAA,MAAM,GAAE,oBAAmB,WAAU;AAAC,iBAAQ,IAAE,GAAE,IAAE,KAAK,cAAY,GAAE,KAAI;AAAC,cAAG,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAG,MAAK;AAAC;AAAA,UAAS;AACzH,eAAK,QAAQ,CAAC,EAAE,CAAC,IAAG,IAAE,KAAG;AAAA,QAAG;AAC5B,iBAAQ,IAAE,GAAE,IAAE,KAAK,cAAY,GAAE,KAAI;AAAC,cAAG,KAAK,QAAQ,CAAC,EAAE,CAAC,KAAG,MAAK;AAAC;AAAA,UAAS;AAC3E,eAAK,QAAQ,CAAC,EAAE,CAAC,IAAG,IAAE,KAAG;AAAA,QAAG;AAAA,MAAC,GAAE,4BAA2B,WAAU;AAAC,YAAI,MAAI,OAAO,mBAAmB,KAAK,UAAU;AAAE,iBAAQA,KAAE,GAAEA,KAAE,IAAI,QAAOA,MAAI;AAAC,mBAAQ,IAAE,GAAE,IAAE,IAAI,QAAO,KAAI;AAAC,gBAAI,MAAI,IAAIA,EAAC;AAAE,gBAAI,MAAI,IAAI,CAAC;AAAE,gBAAG,KAAK,QAAQ,GAAG,EAAE,GAAG,KAAG,MAAK;AAAC;AAAA,YAAS;AAC7P,qBAAQ,IAAE,IAAG,KAAG,GAAE,KAAI;AAAC,uBAAQ,IAAE,IAAG,KAAG,GAAE,KAAI;AAAC,oBAAG,KAAG,MAAI,KAAG,KAAG,KAAG,MAAI,KAAG,KAAI,KAAG,KAAG,KAAG,GAAG;AAAC,uBAAK,QAAQ,MAAI,CAAC,EAAE,MAAI,CAAC,IAAE;AAAA,gBAAK,OAAK;AAAC,uBAAK,QAAQ,MAAI,CAAC,EAAE,MAAI,CAAC,IAAE;AAAA,gBAAM;AAAA,cAAC;AAAA,YAAC;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,GAAE,iBAAgB,SAAS,MAAK;AAAC,YAAI,OAAK,OAAO,iBAAiB,KAAK,UAAU;AAAE,iBAAQA,KAAE,GAAEA,KAAE,IAAGA,MAAI;AAAC,cAAI,MAAK,CAAC,SAAQ,QAAMA,KAAG,MAAI;AAAG,eAAK,QAAQ,KAAK,MAAMA,KAAE,CAAC,CAAC,EAAEA,KAAE,IAAE,KAAK,cAAY,IAAE,CAAC,IAAE;AAAA,QAAI;AAC5W,iBAAQA,KAAE,GAAEA,KAAE,IAAGA,MAAI;AAAC,cAAI,MAAK,CAAC,SAAQ,QAAMA,KAAG,MAAI;AAAG,eAAK,QAAQA,KAAE,IAAE,KAAK,cAAY,IAAE,CAAC,EAAE,KAAK,MAAMA,KAAE,CAAC,CAAC,IAAE;AAAA,QAAI;AAAA,MAAC,GAAE,eAAc,SAAS,MAAK,aAAY;AAAC,YAAI,OAAM,KAAK,qBAAmB,IAAG;AAAY,YAAI,OAAK,OAAO,eAAe,IAAI;AAAE,iBAAQA,KAAE,GAAEA,KAAE,IAAGA,MAAI;AAAC,cAAI,MAAK,CAAC,SAAQ,QAAMA,KAAG,MAAI;AAAG,cAAGA,KAAE,GAAE;AAAC,iBAAK,QAAQA,EAAC,EAAE,CAAC,IAAE;AAAA,UAAI,WAASA,KAAE,GAAE;AAAC,iBAAK,QAAQA,KAAE,CAAC,EAAE,CAAC,IAAE;AAAA,UAAI,OAAK;AAAC,iBAAK,QAAQ,KAAK,cAAY,KAAGA,EAAC,EAAE,CAAC,IAAE;AAAA,UAAI;AAAA,QAAC;AACta,iBAAQA,KAAE,GAAEA,KAAE,IAAGA,MAAI;AAAC,cAAI,MAAK,CAAC,SAAQ,QAAMA,KAAG,MAAI;AAAG,cAAGA,KAAE,GAAE;AAAC,iBAAK,QAAQ,CAAC,EAAE,KAAK,cAAYA,KAAE,CAAC,IAAE;AAAA,UAAI,WAASA,KAAE,GAAE;AAAC,iBAAK,QAAQ,CAAC,EAAE,KAAGA,KAAE,IAAE,CAAC,IAAE;AAAA,UAAI,OAAK;AAAC,iBAAK,QAAQ,CAAC,EAAE,KAAGA,KAAE,CAAC,IAAE;AAAA,UAAI;AAAA,QAAC;AACzL,aAAK,QAAQ,KAAK,cAAY,CAAC,EAAE,CAAC,IAAG,CAAC;AAAA,MAAM,GAAE,SAAQ,SAAS,MAAK,aAAY;AAAC,YAAI,MAAI;AAAG,YAAI,MAAI,KAAK,cAAY;AAAE,YAAI,WAAS;AAAE,YAAI,YAAU;AAAE,iBAAQ,MAAI,KAAK,cAAY,GAAE,MAAI,GAAE,OAAK,GAAE;AAAC,cAAG,OAAK;AAAE;AAAM,iBAAM,MAAK;AAAC,qBAAQ,IAAE,GAAE,IAAE,GAAE,KAAI;AAAC,kBAAG,KAAK,QAAQ,GAAG,EAAE,MAAI,CAAC,KAAG,MAAK;AAAC,oBAAI,OAAK;AAAM,oBAAG,YAAU,KAAK,QAAO;AAAC,0BAAQ,KAAK,SAAS,MAAI,WAAU,MAAI;AAAA,gBAAG;AAC3W,oBAAI,OAAK,OAAO,QAAQ,aAAY,KAAI,MAAI,CAAC;AAAE,oBAAG,MAAK;AAAC,yBAAK,CAAC;AAAA,gBAAK;AACnE,qBAAK,QAAQ,GAAG,EAAE,MAAI,CAAC,IAAE;AAAK;AAAW,oBAAG,YAAU,IAAG;AAAC;AAAY,6BAAS;AAAA,gBAAE;AAAA,cAAC;AAAA,YAAC;AACnF,mBAAK;AAAI,gBAAG,MAAI,KAAG,KAAK,eAAa,KAAI;AAAC,qBAAK;AAAI,oBAAI,CAAC;AAAI;AAAA,YAAM;AAAA,UAAC;AAAA,QAAC;AAAA,MAAC,EAAC;AAAE,kBAAY,OAAK;AAAK,kBAAY,OAAK;AAAK,kBAAY,aAAW,SAAS,YAAW,mBAAkB,UAAS;AAAC,YAAI,WAAS,UAAU,YAAY,YAAW,iBAAiB;AAAE,YAAI,SAAO,IAAI,YAAY;AAAE,iBAAQA,KAAE,GAAEA,KAAE,SAAS,QAAOA,MAAI;AAAC,cAAI,OAAK,SAASA,EAAC;AAAE,iBAAO,IAAI,KAAK,MAAK,CAAC;AAAE,iBAAO,IAAI,KAAK,UAAU,GAAE,OAAO,gBAAgB,KAAK,MAAK,UAAU,CAAC;AAAE,eAAK,MAAM,MAAM;AAAA,QAAE;AACvc,YAAI,iBAAe;AAAE,iBAAQA,KAAE,GAAEA,KAAE,SAAS,QAAOA,MAAI;AAAC,4BAAgB,SAASA,EAAC,EAAE;AAAA,QAAU;AAC9F,YAAG,OAAO,gBAAgB,IAAE,iBAAe,GAAE;AAAC,gBAAM,IAAI,MAAM,4BAC5D,OAAO,gBAAgB,IACvB,MACA,iBAAe,IACf,GAAG;AAAA,QAAE;AACP,YAAG,OAAO,gBAAgB,IAAE,KAAG,iBAAe,GAAE;AAAC,iBAAO,IAAI,GAAE,CAAC;AAAA,QAAE;AACjE,eAAM,OAAO,gBAAgB,IAAE,KAAG,GAAE;AAAC,iBAAO,OAAO,KAAK;AAAA,QAAE;AAC1D,eAAM,MAAK;AAAC,cAAG,OAAO,gBAAgB,KAAG,iBAAe,GAAE;AAAC;AAAA,UAAM;AAChE,iBAAO,IAAI,YAAY,MAAK,CAAC;AAAE,cAAG,OAAO,gBAAgB,KAAG,iBAAe,GAAE;AAAC;AAAA,UAAM;AACpF,iBAAO,IAAI,YAAY,MAAK,CAAC;AAAA,QAAE;AAChC,eAAO,YAAY,YAAY,QAAO,QAAQ;AAAA,MAAE;AAAE,kBAAY,cAAY,SAAS,QAAO,UAAS;AAAC,YAAI,SAAO;AAAE,YAAI,aAAW;AAAE,YAAI,aAAW;AAAE,YAAI,SAAO,IAAI,MAAM,SAAS,MAAM;AAAE,YAAI,SAAO,IAAI,MAAM,SAAS,MAAM;AAAE,iBAAQ,IAAE,GAAE,IAAE,SAAS,QAAO,KAAI;AAAC,cAAI,UAAQ,SAAS,CAAC,EAAE;AAAU,cAAI,UAAQ,SAAS,CAAC,EAAE,aAAW;AAAQ,uBAAW,KAAK,IAAI,YAAW,OAAO;AAAE,uBAAW,KAAK,IAAI,YAAW,OAAO;AAAE,iBAAO,CAAC,IAAE,IAAI,MAAM,OAAO;AAAE,mBAAQA,KAAE,GAAEA,KAAE,OAAO,CAAC,EAAE,QAAOA,MAAI;AAAC,mBAAO,CAAC,EAAEA,EAAC,IAAE,MAAK,OAAO,OAAOA,KAAE,MAAM;AAAA,UAAE;AAC1gB,oBAAQ;AAAQ,cAAI,SAAO,OAAO,0BAA0B,OAAO;AAAE,cAAI,UAAQ,IAAI,aAAa,OAAO,CAAC,GAAE,OAAO,UAAU,IAAE,CAAC;AAAE,cAAI,UAAQ,QAAQ,IAAI,MAAM;AAAE,iBAAO,CAAC,IAAE,IAAI,MAAM,OAAO,UAAU,IAAE,CAAC;AAAE,mBAAQA,KAAE,GAAEA,KAAE,OAAO,CAAC,EAAE,QAAOA,MAAI;AAAC,gBAAI,WAASA,KAAE,QAAQ,UAAU,IAAE,OAAO,CAAC,EAAE;AAAO,mBAAO,CAAC,EAAEA,EAAC,IAAG,YAAU,IAAG,QAAQ,IAAI,QAAQ,IAAE;AAAA,UAAE;AAAA,QAAC;AACxV,YAAI,iBAAe;AAAE,iBAAQA,KAAE,GAAEA,KAAE,SAAS,QAAOA,MAAI;AAAC,4BAAgB,SAASA,EAAC,EAAE;AAAA,QAAW;AAC/F,YAAI,OAAK,IAAI,MAAM,cAAc;AAAE,YAAI,QAAM;AAAE,iBAAQA,KAAE,GAAEA,KAAE,YAAWA,MAAI;AAAC,mBAAQ,IAAE,GAAE,IAAE,SAAS,QAAO,KAAI;AAAC,gBAAGA,KAAE,OAAO,CAAC,EAAE,QAAO;AAAC,mBAAK,OAAO,IAAE,OAAO,CAAC,EAAEA,EAAC;AAAA,YAAE;AAAA,UAAC;AAAA,QAAC;AACpK,iBAAQA,KAAE,GAAEA,KAAE,YAAWA,MAAI;AAAC,mBAAQ,IAAE,GAAE,IAAE,SAAS,QAAO,KAAI;AAAC,gBAAGA,KAAE,OAAO,CAAC,EAAE,QAAO;AAAC,mBAAK,OAAO,IAAE,OAAO,CAAC,EAAEA,EAAC;AAAA,YAAE;AAAA,UAAC;AAAA,QAAC;AACrH,eAAO;AAAA,MAAK;AAAE,UAAI,SAAO,EAAC,aAAY,KAAG,GAAE,gBAAe,KAAG,GAAE,gBAAe,KAAG,GAAE,YAAW,KAAG,EAAC;AAAE,UAAI,sBAAoB,EAAC,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,GAAE,EAAC;AAAE,UAAI,gBAAc,EAAC,YAAW,GAAE,YAAW,GAAE,YAAW,GAAE,YAAW,GAAE,YAAW,GAAE,YAAW,GAAE,YAAW,GAAE,YAAW,EAAC;AAAE,UAAI,SAAO,EAAC,wBAAuB,CAAC,CAAC,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,KAAI,KAAI,GAAG,CAAC,GAAE,KAAK,KAAG,KAAK,KAAG,IAAI,KAAG,IAAI,KAAG,IAAI,KAAG,IAAI,KAAG,IAAI,KAAG,GAAG,KAAK,KAAG,KAAK,KAAG,KAAK,KAAG,KAAK,KAAG,IAAI,KAAG,IAAI,KAAG,IAAI,KAAG,IAAI,KAAG,GAAG,UAAU,KAAG,KAAK,KAAG,KAAK,KAAG,KAAK,KAAG,IAAI,KAAG,GAAG,gBAAe,SAAS,MAAK;AAAC,YAAI,IAAE,QAAM;AAAG,eAAM,OAAO,YAAY,CAAC,IAAE,OAAO,YAAY,OAAO,GAAG,KAAG,GAAE;AAAC,eAAI,OAAO,OAAM,OAAO,YAAY,CAAC,IAAE,OAAO,YAAY,OAAO,GAAG;AAAA,QAAI;AAC1uC,gBAAQ,QAAM,KAAI,KAAG,OAAO;AAAA,MAAS,GAAE,kBAAiB,SAAS,MAAK;AAAC,YAAI,IAAE,QAAM;AAAG,eAAM,OAAO,YAAY,CAAC,IAAE,OAAO,YAAY,OAAO,GAAG,KAAG,GAAE;AAAC,eAAI,OAAO,OAAM,OAAO,YAAY,CAAC,IAAE,OAAO,YAAY,OAAO,GAAG;AAAA,QAAI;AAC7N,eAAO,QAAM,KAAI;AAAA,MAAE,GAAE,aAAY,SAAS,MAAK;AAAC,YAAI,QAAM;AAAE,eAAM,QAAM,GAAE;AAAC;AAAQ,oBAAQ;AAAA,QAAE;AAC7F,eAAO;AAAA,MAAM,GAAE,oBAAmB,SAAS,YAAW;AAAC,eAAO,OAAO,uBAAuB,aAAW,CAAC;AAAA,MAAE,GAAE,SAAQ,SAAS,aAAYA,IAAE,GAAE;AAAC,gBAAO,aAAY;AAAA,UAAC,KAAK,cAAc;AAAW,oBAAOA,KAAE,KAAG,KAAG;AAAA,UAAE,KAAK,cAAc;AAAW,mBAAOA,KAAE,KAAG;AAAA,UAAE,KAAK,cAAc;AAAW,mBAAO,IAAE,KAAG;AAAA,UAAE,KAAK,cAAc;AAAW,oBAAOA,KAAE,KAAG,KAAG;AAAA,UAAE,KAAK,cAAc;AAAW,oBAAO,KAAK,MAAMA,KAAE,CAAC,IAAE,KAAK,MAAM,IAAE,CAAC,KAAG,KAAG;AAAA,UAAE,KAAK,cAAc;AAAW,mBAAOA,KAAE,IAAG,IAAGA,KAAE,IAAG,KAAG;AAAA,UAAE,KAAK,cAAc;AAAW,oBAAQA,KAAE,IAAG,IAAGA,KAAE,IAAG,KAAG,KAAG;AAAA,UAAE,KAAK,cAAc;AAAW,oBAAQA,KAAE,IAAG,KAAGA,KAAE,KAAG,KAAG,KAAG;AAAA,UAAE;AAAQ,kBAAM,IAAI,MAAM,qBAAmB,WAAW;AAAA,QAAE;AAAA,MAAC,GAAE,2BAA0B,SAAS,oBAAmB;AAAC,YAAI,IAAE,IAAI,aAAa,CAAC,CAAC,GAAE,CAAC;AAAE,iBAAQA,KAAE,GAAEA,KAAE,oBAAmBA,MAAI;AAAC,cAAE,EAAE,SAAS,IAAI,aAAa,CAAC,GAAE,OAAO,KAAKA,EAAC,CAAC,GAAE,CAAC,CAAC;AAAA,QAAE;AAC3zB,eAAO;AAAA,MAAE,GAAE,iBAAgB,SAAS,MAAK,MAAK;AAAC,YAAG,KAAG,QAAM,OAAK,IAAG;AAAC,kBAAO,MAAK;AAAA,YAAC,KAAK,OAAO;AAAY,qBAAO;AAAA,YAAG,KAAK,OAAO;AAAe,qBAAO;AAAA,YAAE,KAAK,OAAO;AAAe,qBAAO;AAAA,YAAE,KAAK,OAAO;AAAW,qBAAO;AAAA,YAAE;AAAQ,oBAAM,IAAI,MAAM,UAAQ,IAAI;AAAA,UAAE;AAAA,QAAC,WAAS,OAAK,IAAG;AAAC,kBAAO,MAAK;AAAA,YAAC,KAAK,OAAO;AAAY,qBAAO;AAAA,YAAG,KAAK,OAAO;AAAe,qBAAO;AAAA,YAAG,KAAK,OAAO;AAAe,qBAAO;AAAA,YAAG,KAAK,OAAO;AAAW,qBAAO;AAAA,YAAG;AAAQ,oBAAM,IAAI,MAAM,UAAQ,IAAI;AAAA,UAAE;AAAA,QAAC,WAAS,OAAK,IAAG;AAAC,kBAAO,MAAK;AAAA,YAAC,KAAK,OAAO;AAAY,qBAAO;AAAA,YAAG,KAAK,OAAO;AAAe,qBAAO;AAAA,YAAG,KAAK,OAAO;AAAe,qBAAO;AAAA,YAAG,KAAK,OAAO;AAAW,qBAAO;AAAA,YAAG;AAAQ,oBAAM,IAAI,MAAM,UAAQ,IAAI;AAAA,UAAE;AAAA,QAAC,OAAK;AAAC,gBAAM,IAAI,MAAM,UAAQ,IAAI;AAAA,QAAE;AAAA,MAAC,GAAE,cAAa,SAAS,QAAO;AAAC,YAAI,cAAY,OAAO,eAAe;AAAE,YAAI,YAAU;AAAE,iBAAQ,MAAI,GAAE,MAAI,aAAY,OAAM;AAAC,mBAAQ,MAAI,GAAE,MAAI,aAAY,OAAM;AAAC,gBAAI,YAAU;AAAE,gBAAI,OAAK,OAAO,OAAO,KAAI,GAAG;AAAE,qBAAQ,IAAE,IAAG,KAAG,GAAE,KAAI;AAAC,kBAAG,MAAI,IAAE,KAAG,eAAa,MAAI,GAAE;AAAC;AAAA,cAAS;AAC/9B,uBAAQ,IAAE,IAAG,KAAG,GAAE,KAAI;AAAC,oBAAG,MAAI,IAAE,KAAG,eAAa,MAAI,GAAE;AAAC;AAAA,gBAAS;AAC/D,oBAAG,KAAG,KAAG,KAAG,GAAE;AAAC;AAAA,gBAAS;AACxB,oBAAG,QAAM,OAAO,OAAO,MAAI,GAAE,MAAI,CAAC,GAAE;AAAC;AAAA,gBAAY;AAAA,cAAC;AAAA,YAAC;AACpD,gBAAG,YAAU,GAAE;AAAC,2BAAY,IAAE,YAAU;AAAA,YAAG;AAAA,UAAC;AAAA,QAAC;AAC7C,iBAAQ,MAAI,GAAE,MAAI,cAAY,GAAE,OAAM;AAAC,mBAAQ,MAAI,GAAE,MAAI,cAAY,GAAE,OAAM;AAAC,gBAAI,QAAM;AAAE,gBAAG,OAAO,OAAO,KAAI,GAAG;AAAE;AAAQ,gBAAG,OAAO,OAAO,MAAI,GAAE,GAAG;AAAE;AAAQ,gBAAG,OAAO,OAAO,KAAI,MAAI,CAAC;AAAE;AAAQ,gBAAG,OAAO,OAAO,MAAI,GAAE,MAAI,CAAC;AAAE;AAAQ,gBAAG,SAAO,KAAG,SAAO,GAAE;AAAC,2BAAW;AAAA,YAAE;AAAA,UAAC;AAAA,QAAC;AAChR,iBAAQ,MAAI,GAAE,MAAI,aAAY,OAAM;AAAC,mBAAQ,MAAI,GAAE,MAAI,cAAY,GAAE,OAAM;AAAC,gBAAG,OAAO,OAAO,KAAI,GAAG,KAAG,CAAC,OAAO,OAAO,KAAI,MAAI,CAAC,KAAG,OAAO,OAAO,KAAI,MAAI,CAAC,KAAG,OAAO,OAAO,KAAI,MAAI,CAAC,KAAG,OAAO,OAAO,KAAI,MAAI,CAAC,KAAG,CAAC,OAAO,OAAO,KAAI,MAAI,CAAC,KAAG,OAAO,OAAO,KAAI,MAAI,CAAC,GAAE;AAAC,2BAAW;AAAA,YAAG;AAAA,UAAC;AAAA,QAAC;AACrR,iBAAQ,MAAI,GAAE,MAAI,aAAY,OAAM;AAAC,mBAAQ,MAAI,GAAE,MAAI,cAAY,GAAE,OAAM;AAAC,gBAAG,OAAO,OAAO,KAAI,GAAG,KAAG,CAAC,OAAO,OAAO,MAAI,GAAE,GAAG,KAAG,OAAO,OAAO,MAAI,GAAE,GAAG,KAAG,OAAO,OAAO,MAAI,GAAE,GAAG,KAAG,OAAO,OAAO,MAAI,GAAE,GAAG,KAAG,CAAC,OAAO,OAAO,MAAI,GAAE,GAAG,KAAG,OAAO,OAAO,MAAI,GAAE,GAAG,GAAE;AAAC,2BAAW;AAAA,YAAG;AAAA,UAAC;AAAA,QAAC;AACrR,YAAI,YAAU;AAAE,iBAAQ,MAAI,GAAE,MAAI,aAAY,OAAM;AAAC,mBAAQ,MAAI,GAAE,MAAI,aAAY,OAAM;AAAC,gBAAG,OAAO,OAAO,KAAI,GAAG,GAAE;AAAC;AAAA,YAAY;AAAA,UAAC;AAAA,QAAC;AACnI,YAAI,QAAM,KAAK,IAAI,MAAI,YAAU,cAAY,cAAY,EAAE,IAAE;AAAE,qBAAW,QAAM;AAAG,eAAO;AAAA,MAAU,EAAC;AAAE,UAAI,SAAO,EAAC,MAAK,SAAS,GAAE;AAAC,YAAG,IAAE,GAAE;AAAC,gBAAM,IAAI,MAAM,UAAQ,IAAE,GAAG;AAAA,QAAE;AAC3K,eAAO,OAAO,UAAU,CAAC;AAAA,MAAE,GAAE,MAAK,SAAS,GAAE;AAAC,eAAM,IAAE,GAAE;AAAC,eAAG;AAAA,QAAI;AAChE,eAAM,KAAG,KAAI;AAAC,eAAG;AAAA,QAAI;AACrB,eAAO,OAAO,UAAU,CAAC;AAAA,MAAE,GAAE,WAAU,IAAI,MAAM,GAAG,GAAE,WAAU,IAAI,MAAM,GAAG,EAAC;AAAE,eAAQ,IAAE,GAAE,IAAE,GAAE,KAAI;AAAC,eAAO,UAAU,CAAC,IAAE,KAAG;AAAA,MAAE;AAChI,eAAQ,IAAE,GAAE,IAAE,KAAI,KAAI;AAAC,eAAO,UAAU,CAAC,IAAE,OAAO,UAAU,IAAE,CAAC,IAAE,OAAO,UAAU,IAAE,CAAC,IAAE,OAAO,UAAU,IAAE,CAAC,IAAE,OAAO,UAAU,IAAE,CAAC;AAAA,MAAE;AACnI,eAAQ,IAAE,GAAE,IAAE,KAAI,KAAI;AAAC,eAAO,UAAU,OAAO,UAAU,CAAC,CAAC,IAAE;AAAA,MAAE;AAC/D,eAAS,aAAa,KAAI,OAAM;AAAC,YAAG,IAAI,UAAQ,QAAU;AAAC,gBAAM,IAAI,MAAM,IAAI,SAAO,MAAI,KAAK;AAAA,QAAE;AAChG,YAAI,SAAO;AAAE,eAAM,SAAO,IAAI,UAAQ,IAAI,MAAM,KAAG,GAAE;AAAC;AAAA,QAAS;AAC/D,aAAK,MAAI,IAAI,MAAM,IAAI,SAAO,SAAO,KAAK;AAAE,iBAAQA,KAAE,GAAEA,KAAE,IAAI,SAAO,QAAOA,MAAI;AAAC,eAAK,IAAIA,EAAC,IAAE,IAAIA,KAAE,MAAM;AAAA,QAAE;AAAA,MAAC;AAC7G,mBAAa,YAAU,EAAC,KAAI,SAAS,OAAM;AAAC,eAAO,KAAK,IAAI,KAAK;AAAA,MAAE,GAAE,WAAU,WAAU;AAAC,eAAO,KAAK,IAAI;AAAA,MAAO,GAAE,UAAS,SAAS,GAAE;AAAC,YAAI,MAAI,IAAI,MAAM,KAAK,UAAU,IAAE,EAAE,UAAU,IAAE,CAAC;AAAE,iBAAQA,KAAE,GAAEA,KAAE,KAAK,UAAU,GAAEA,MAAI;AAAC,mBAAQ,IAAE,GAAE,IAAE,EAAE,UAAU,GAAE,KAAI;AAAC,gBAAIA,KAAE,CAAC,KAAG,OAAO,KAAK,OAAO,KAAK,KAAK,IAAIA,EAAC,CAAC,IAAE,OAAO,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AAAA,UAAE;AAAA,QAAC;AACtU,eAAO,IAAI,aAAa,KAAI,CAAC;AAAA,MAAE,GAAE,KAAI,SAAS,GAAE;AAAC,YAAG,KAAK,UAAU,IAAE,EAAE,UAAU,IAAE,GAAE;AAAC,iBAAO;AAAA,QAAK;AAClG,YAAI,QAAM,OAAO,KAAK,KAAK,IAAI,CAAC,CAAC,IAAE,OAAO,KAAK,EAAE,IAAI,CAAC,CAAC;AAAE,YAAI,MAAI,IAAI,MAAM,KAAK,UAAU,CAAC;AAAE,iBAAQA,KAAE,GAAEA,KAAE,KAAK,UAAU,GAAEA,MAAI;AAAC,cAAIA,EAAC,IAAE,KAAK,IAAIA,EAAC;AAAA,QAAE;AACpJ,iBAAQA,KAAE,GAAEA,KAAE,EAAE,UAAU,GAAEA,MAAI;AAAC,cAAIA,EAAC,KAAG,OAAO,KAAK,OAAO,KAAK,EAAE,IAAIA,EAAC,CAAC,IAAE,KAAK;AAAA,QAAE;AAClF,eAAO,IAAI,aAAa,KAAI,CAAC,EAAE,IAAI,CAAC;AAAA,MAAE,EAAC;AAAE,eAAS,UAAU,YAAW,WAAU;AAAC,aAAK,aAAW;AAAW,aAAK,YAAU;AAAA,MAAU;AACxI,gBAAU,iBAAe,CAAC,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,CAAC,GAAE,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,IAAG,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,IAAG,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,IAAG,GAAE,KAAI,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,IAAG,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,IAAG,KAAI,GAAG,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,GAAE,KAAI,KAAI,IAAG,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,GAAE,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,KAAI,KAAI,GAAE,KAAI,GAAG,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,IAAG,IAAG,EAAE,CAAC;AAAE,gBAAU,cAAY,SAAS,YAAW,mBAAkB;AAAC,YAAI,UAAQ,UAAU,gBAAgB,YAAW,iBAAiB;AAAE,YAAG,WAAS,QAAU;AAAC,gBAAM,IAAI,MAAM,+BAA6B,aAAW,wBAAsB,iBAAiB;AAAA,QAAE;AACtjG,YAAI,SAAO,QAAQ,SAAO;AAAE,YAAI,OAAK,CAAC;AAAE,iBAAQA,KAAE,GAAEA,KAAE,QAAOA,MAAI;AAAC,cAAI,QAAM,QAAQA,KAAE,IAAE,CAAC;AAAE,cAAI,aAAW,QAAQA,KAAE,IAAE,CAAC;AAAE,cAAI,YAAU,QAAQA,KAAE,IAAE,CAAC;AAAE,mBAAQ,IAAE,GAAE,IAAE,OAAM,KAAI;AAAC,iBAAK,KAAK,IAAI,UAAU,YAAW,SAAS,CAAC;AAAA,UAAE;AAAA,QAAC;AAC/N,eAAO;AAAA,MAAK;AAAE,gBAAU,kBAAgB,SAAS,YAAW,mBAAkB;AAAC,gBAAO,mBAAkB;AAAA,UAAC,KAAK,oBAAoB;AAAE,mBAAO,UAAU,gBAAgB,aAAW,KAAG,IAAE,CAAC;AAAA,UAAE,KAAK,oBAAoB;AAAE,mBAAO,UAAU,gBAAgB,aAAW,KAAG,IAAE,CAAC;AAAA,UAAE,KAAK,oBAAoB;AAAE,mBAAO,UAAU,gBAAgB,aAAW,KAAG,IAAE,CAAC;AAAA,UAAE,KAAK,oBAAoB;AAAE,mBAAO,UAAU,gBAAgB,aAAW,KAAG,IAAE,CAAC;AAAA,UAAE;AAAQ,mBAAO;AAAA,QAAU;AAAA,MAAC;AAAE,eAAS,cAAa;AAAC,aAAK,SAAO,CAAC;AAAE,aAAK,SAAO;AAAA,MAAE;AACtf,kBAAY,YAAU,EAAC,KAAI,SAAS,OAAM;AAAC,YAAI,WAAS,KAAK,MAAM,QAAM,CAAC;AAAE,gBAAQ,KAAK,OAAO,QAAQ,MAAK,IAAE,QAAM,IAAI,MAAI;AAAA,MAAE,GAAE,KAAI,SAAS,KAAI,QAAO;AAAC,iBAAQA,KAAE,GAAEA,KAAE,QAAOA,MAAI;AAAC,eAAK,QAAS,QAAO,SAAOA,KAAE,IAAI,MAAI,CAAC;AAAA,QAAE;AAAA,MAAC,GAAE,iBAAgB,WAAU;AAAC,eAAO,KAAK;AAAA,MAAO,GAAE,QAAO,SAAS,KAAI;AAAC,YAAI,WAAS,KAAK,MAAM,KAAK,SAAO,CAAC;AAAE,YAAG,KAAK,OAAO,UAAQ,UAAS;AAAC,eAAK,OAAO,KAAK,CAAC;AAAA,QAAE;AAC/X,YAAG,KAAI;AAAC,eAAK,OAAO,QAAQ,KAAI,QAAQ,KAAK,SAAO;AAAA,QAAI;AACxD,aAAK;AAAA,MAAS,EAAC;AAAE,UAAI,oBAAkB,CAAC,CAAC,IAAG,IAAG,IAAG,CAAC,GAAE,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,IAAG,IAAG,IAAG,EAAE,GAAE,CAAC,KAAI,IAAG,IAAG,EAAE,GAAE,CAAC,KAAI,KAAI,IAAG,EAAE,GAAE,CAAC,KAAI,KAAI,IAAG,EAAE,GAAE,CAAC,KAAI,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,KAAI,EAAE,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,KAAI,KAAI,KAAI,GAAG,GAAE,CAAC,MAAK,KAAI,KAAI,GAAG,GAAE,CAAC,MAAK,KAAI,KAAI,GAAG,GAAE,CAAC,MAAK,KAAI,KAAI,GAAG,GAAE,CAAC,MAAK,KAAI,KAAI,GAAG,GAAE,CAAC,MAAK,MAAK,KAAI,GAAG,GAAE,CAAC,MAAK,MAAK,KAAI,GAAG,GAAE,CAAC,MAAK,MAAK,KAAI,GAAG,GAAE,CAAC,MAAK,MAAK,KAAI,GAAG,GAAE,CAAC,MAAK,MAAK,KAAI,GAAG,GAAE,CAAC,MAAK,MAAK,MAAK,GAAG,GAAE,CAAC,MAAK,MAAK,MAAK,GAAG,GAAE,CAAC,MAAK,MAAK,MAAK,GAAG,GAAE,CAAC,MAAK,MAAK,MAAK,GAAG,GAAE,CAAC,MAAK,MAAK,MAAK,GAAG,GAAE,CAAC,MAAK,MAAK,MAAK,IAAI,GAAE,CAAC,MAAK,MAAK,MAAK,IAAI,GAAE,CAAC,MAAK,MAAK,MAAK,IAAI,GAAE,CAAC,MAAK,MAAK,MAAK,IAAI,GAAE,CAAC,MAAK,MAAK,MAAK,IAAI,CAAC;AAEhxB,eAAS,mBAAmB;AAC3B,eAAO,OAAO,4BAA4B;AAAA,MAC3C;AAGA,eAAS,cAAc;AACtB,YAAI,UAAU;AACd,YAAI,SAAS,UAAU;AAEvB,YAAI,WAAW,KAAK,MAAM,GAAG;AAC5B,oBAAU;AACV,cAAI,OAAO,OAAO,SAAS,EAAE,MAAM,yBAAyB;AAE5D,cAAI,QAAQ,KAAK,CAAC,GAAG;AACpB,sBAAU,WAAW,KAAK,CAAC,CAAC;AAAA,UAC7B;AAAA,QACD;AAEA,eAAO;AAAA,MACR;AAEA,UAAI,YAAa,WAAW;AAE3B,YAAIC,WAAU,SAAU,IAAI,UAAU;AACrC,eAAK,MAAM;AACX,eAAK,YAAY;AAAA,QAClB;AAEA,QAAAA,SAAQ,UAAU,OAAO,SAAU,SAAS;AAC3C,cAAI,YAAY,KAAK;AACrB,cAAI,MAAM,KAAK;AACf,cAAI,SAAS,QAAQ,eAAe;AACpC,cAAI,SAAS,KAAK,MAAM,UAAU,QAAQ,MAAM;AAChD,cAAI,UAAU,KAAK,MAAM,UAAU,SAAS,MAAM;AAElD,eAAK,MAAM;AAEX,mBAAS,QAAQ,KAAK,OAAO;AAC5B,gBAAI,KAAK,SAAS,gBAAgB,8BAA8B,GAAG;AACnE,qBAAS,KAAK;AACb,kBAAI,MAAM,eAAe,CAAC;AAAG,mBAAG,aAAa,GAAG,MAAM,CAAC,CAAC;AACzD,mBAAO;AAAA,UACR;AAEA,cAAI,MAAM,QAAQ,OAAQ,EAAC,WAAW,SAAS,OAAO,MAAM,IAAI,MAAM,OAAO,MAAM,GAAG,SAAS,QAAQ,UAAU,QAAQ,QAAQ,UAAU,WAAU,CAAC;AACtJ,cAAI,eAAe,iCAAiC,eAAe,8BAA8B;AACjG,cAAI,YAAY,GAAG;AAEnB,cAAI,YAAY,QAAQ,QAAQ,EAAC,QAAQ,UAAU,YAAY,SAAS,QAAQ,UAAU,OAAM,CAAC,CAAC;AAClG,cAAI,YAAY,QAAQ,QAAQ,EAAC,QAAQ,UAAU,WAAW,SAAS,KAAK,UAAU,KAAK,MAAM,WAAU,CAAC,CAAC;AAE7G,mBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACtC,qBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACtC,kBAAI,QAAQ,OAAO,KAAK,GAAG,GAAG;AAC7B,oBAAI,QAAQ,QAAQ,OAAO,EAAC,KAAK,OAAO,GAAG,GAAG,KAAK,OAAO,GAAG,EAAC,CAAC;AAC/D,sBAAM,eAAe,gCAAgC,QAAQ,WAAW;AACxE,oBAAI,YAAY,KAAK;AAAA,cACtB;AAAA,YACD;AAAA,UACD;AAAA,QACD;AACA,QAAAA,SAAQ,UAAU,QAAQ,WAAY;AACrC,iBAAO,KAAK,IAAI,cAAc;AAC7B,iBAAK,IAAI,YAAY,KAAK,IAAI,SAAS;AAAA,QACzC;AACA,eAAOA;AAAA,MACR,EAAG;AAEH,UAAI,SAAS,SAAS,gBAAgB,QAAQ,YAAY,MAAM;AAGhE,UAAI,UAAU,SAAS,YAAY,CAAC,iBAAiB,IAAK,WAAY;AACrE,YAAIA,WAAU,SAAU,IAAI,UAAU;AACrC,eAAK,MAAM;AACX,eAAK,YAAY;AAAA,QAClB;AAOA,QAAAA,SAAQ,UAAU,OAAO,SAAU,SAAS;AAC3C,cAAI,YAAY,KAAK;AACrB,cAAI,MAAM,KAAK;AACf,cAAI,SAAS,QAAQ,eAAe;AACpC,cAAI,SAAS,KAAK,MAAM,UAAU,QAAQ,MAAM;AAChD,cAAI,UAAU,KAAK,MAAM,UAAU,SAAS,MAAM;AAClD,cAAI,QAAQ,CAAC,oDAAoD;AAEjE,mBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACtC,kBAAM,KAAK,MAAM;AAEjB,qBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACtC,oBAAM,KAAK,2EAA2E,SAAS,eAAe,UAAU,0BAA0B,QAAQ,OAAO,KAAK,GAAG,IAAI,UAAU,YAAY,UAAU,cAAc,UAAU;AAAA,YACtO;AAEA,kBAAM,KAAK,OAAO;AAAA,UACnB;AAEA,gBAAM,KAAK,UAAU;AACrB,cAAI,YAAY,MAAM,KAAK,EAAE;AAG7B,cAAI,UAAU,IAAI,WAAW,CAAC;AAC9B,cAAI,oBAAoB,UAAU,QAAQ,QAAQ,eAAe;AACjE,cAAI,mBAAmB,UAAU,SAAS,QAAQ,gBAAgB;AAElE,cAAI,mBAAmB,KAAK,kBAAkB,GAAG;AAChD,oBAAQ,MAAM,SAAS,kBAAkB,QAAQ,mBAAmB;AAAA,UACrE;AAAA,QACD;AAKA,QAAAA,SAAQ,UAAU,QAAQ,WAAY;AACrC,eAAK,IAAI,YAAY;AAAA,QACtB;AAEA,eAAOA;AAAA,MACR,EAAG,IAAK,WAAY;AACnB,iBAAS,eAAe;AACvB,eAAK,SAAS,MAAM,KAAK,UAAU,UAAU,WAAW;AACxD,eAAK,SAAS,MAAM,UAAU;AAC9B,eAAK,UAAU,MAAM,UAAU;AAAA,QAChC;AAIA,YAAI,QAAQ,KAAK,YAAY,KAAK,YAAY,KAAK;AAClD,cAAI,SAAS,IAAI,OAAO;AACxB,cAAI,YAAY,yBAAyB,UAAU;AACnD,mCAAyB,UAAU,YAAY,SAAU,OAAO,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AAC/F,gBAAK,cAAc,SAAU,OAAO,KAAK,MAAM,QAAQ,GAAG;AACzD,uBAASD,KAAI,UAAU,SAAS,GAAGA,MAAK,GAAGA,MAAK;AAC/C,0BAAUA,EAAC,IAAI,UAAUA,EAAC,IAAI;AAAA,cAC/B;AAAA,YACD,WAAW,OAAO,MAAM,aAAa;AACpC,wBAAU,CAAC,KAAK;AAChB,wBAAU,CAAC,KAAK;AAChB,wBAAU,CAAC,KAAK;AAChB,wBAAU,CAAC,KAAK;AAAA,YACjB;AAEA,sBAAU,MAAM,MAAM,SAAS;AAAA,UAChC;AAAA,QACD;AASA,iBAAS,gBAAgB,UAAU,OAAO;AACzC,cAAI,OAAO;AACX,eAAK,SAAS;AACd,eAAK,YAAY;AAGjB,cAAI,KAAK,qBAAqB,MAAM;AACnC,gBAAI,KAAK,SAAS,cAAc,KAAK;AACrC,gBAAI,WAAW,WAAW;AACzB,mBAAK,mBAAmB;AAExB,kBAAI,KAAK,QAAQ;AAChB,qBAAK,OAAO,KAAK,IAAI;AAAA,cACtB;AAAA,YACD;AACA,gBAAI,aAAa,WAAW;AAC3B,mBAAK,mBAAmB;AAExB,kBAAI,KAAK,WAAW;AACnB,qBAAK,UAAU,KAAK,IAAI;AAAA,cACzB;AAAA,YACD;AAEA,eAAG,UAAU;AACb,eAAG,UAAU;AACb,eAAG,SAAS;AACZ,eAAG,MAAM;AACT;AAAA,UACD,WAAW,KAAK,qBAAqB,QAAQ,KAAK,WAAW;AAC5D,iBAAK,UAAU,KAAK,IAAI;AAAA,UACzB,WAAW,KAAK,qBAAqB,SAAS,KAAK,QAAQ;AAC1D,iBAAK,OAAO,KAAK,IAAI;AAAA,UACtB;AAAA,QACD;AAAC;AASD,YAAIC,WAAU,SAAU,IAAI,UAAU;AACrC,eAAK,cAAc;AACnB,eAAK,WAAW,YAAY;AAE5B,eAAK,YAAY;AACjB,eAAK,YAAY,SAAS,cAAc,QAAQ;AAChD,eAAK,UAAU,QAAQ,SAAS;AAChC,eAAK,UAAU,SAAS,SAAS;AACjC,aAAG,YAAY,KAAK,SAAS;AAC7B,eAAK,MAAM;AACX,eAAK,YAAY,KAAK,UAAU,WAAW,IAAI;AAC/C,eAAK,cAAc;AACnB,eAAK,WAAW,SAAS,cAAc,KAAK;AAC5C,eAAK,SAAS,MAAM;AACpB,eAAK,SAAS,MAAM,UAAU;AAC9B,eAAK,IAAI,YAAY,KAAK,QAAQ;AAClC,eAAK,mBAAmB;AAAA,QACzB;AAOA,QAAAA,SAAQ,UAAU,OAAO,SAAU,SAAS;AAC3C,cAAI,WAAW,KAAK;AACpB,cAAI,YAAY,KAAK;AACrB,cAAI,YAAY,KAAK;AAErB,cAAI,SAAS,QAAQ,eAAe;AACpC,cAAI,SAAS,UAAU,QAAQ;AAC/B,cAAI,UAAU,UAAU,SAAS;AACjC,cAAI,gBAAgB,KAAK,MAAM,MAAM;AACrC,cAAI,iBAAiB,KAAK,MAAM,OAAO;AAEvC,mBAAS,MAAM,UAAU;AACzB,eAAK,MAAM;AAEX,mBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACtC,qBAAS,MAAM,GAAG,MAAM,QAAQ,OAAO;AACtC,kBAAI,UAAU,QAAQ,OAAO,KAAK,GAAG;AACrC,kBAAI,QAAQ,MAAM;AAClB,kBAAI,OAAO,MAAM;AACjB,wBAAU,cAAc,UAAU,UAAU,YAAY,UAAU;AAClE,wBAAU,YAAY;AACtB,wBAAU,YAAY,UAAU,UAAU,YAAY,UAAU;AAChE,wBAAU,SAAS,OAAO,MAAM,QAAQ,OAAO;AAG/C,wBAAU;AAAA,gBACT,KAAK,MAAM,KAAK,IAAI;AAAA,gBACpB,KAAK,MAAM,IAAI,IAAI;AAAA,gBACnB;AAAA,gBACA;AAAA,cACD;AAEA,wBAAU;AAAA,gBACT,KAAK,KAAK,KAAK,IAAI;AAAA,gBACnB,KAAK,KAAK,IAAI,IAAI;AAAA,gBAClB;AAAA,gBACA;AAAA,cACD;AAAA,YACD;AAAA,UACD;AAEA,eAAK,cAAc;AAAA,QACpB;AAKA,QAAAA,SAAQ,UAAU,YAAY,WAAY;AACzC,cAAI,KAAK,aAAa;AACrB,4BAAgB,KAAK,MAAM,YAAY;AAAA,UACxC;AAAA,QACD;AAOA,QAAAA,SAAQ,UAAU,YAAY,WAAY;AACzC,iBAAO,KAAK;AAAA,QACb;AAKA,QAAAA,SAAQ,UAAU,QAAQ,WAAY;AACrC,eAAK,UAAU,UAAU,GAAG,GAAG,KAAK,UAAU,OAAO,KAAK,UAAU,MAAM;AAC1E,eAAK,cAAc;AAAA,QACpB;AAMA,QAAAA,SAAQ,UAAU,QAAQ,SAAU,SAAS;AAC5C,cAAI,CAAC,SAAS;AACb,mBAAO;AAAA,UACR;AAEA,iBAAO,KAAK,MAAM,UAAU,GAAI,IAAI;AAAA,QACrC;AAEA,eAAOA;AAAA,MACR,EAAG;AAUH,eAAS,eAAe,OAAO,eAAe;AAC7C,YAAI,QAAQ;AACZ,YAAI,SAAS,eAAe,KAAK;AAEjC,iBAASD,KAAI,GAAG,MAAM,kBAAkB,QAAQA,KAAI,KAAKA,MAAK;AAC7D,cAAI,SAAS;AAEb,kBAAQ,eAAe;AAAA,YACtB,KAAK,oBAAoB;AACxB,uBAAS,kBAAkBA,EAAC,EAAE,CAAC;AAC/B;AAAA,YACD,KAAK,oBAAoB;AACxB,uBAAS,kBAAkBA,EAAC,EAAE,CAAC;AAC/B;AAAA,YACD,KAAK,oBAAoB;AACxB,uBAAS,kBAAkBA,EAAC,EAAE,CAAC;AAC/B;AAAA,YACD,KAAK,oBAAoB;AACxB,uBAAS,kBAAkBA,EAAC,EAAE,CAAC;AAC/B;AAAA,UACF;AAEA,cAAI,UAAU,QAAQ;AACrB;AAAA,UACD,OAAO;AACN;AAAA,UACD;AAAA,QACD;AAEA,YAAI,QAAQ,kBAAkB,QAAQ;AACrC,gBAAM,IAAI,MAAM,eAAe;AAAA,QAChC;AAEA,eAAO;AAAA,MACR;AAEA,eAAS,eAAe,OAAO;AAC9B,YAAI,eAAe,UAAU,KAAK,EAAE,SAAS,EAAE,QAAQ,qBAAqB,GAAG;AAC/E,eAAO,aAAa,UAAU,aAAa,UAAU,QAAQ,IAAI;AAAA,MAClE;AA2BA,eAAS,SAAU,IAAI,SAAS;AAC/B,aAAK,YAAY;AAAA,UAChB,OAAQ;AAAA,UACR,QAAS;AAAA,UACT,YAAa;AAAA,UACb,WAAY;AAAA,UACZ,YAAa;AAAA,UACb,cAAe,oBAAoB;AAAA,QACpC;AAEA,YAAI,OAAO,YAAY,UAAU;AAChC,oBAAU;AAAA,YACT,MAAO;AAAA,UACR;AAAA,QACD;AAGA,YAAI,SAAS;AACZ,mBAASA,MAAK,SAAS;AACtB,iBAAK,UAAUA,EAAC,IAAI,QAAQA,EAAC;AAAA,UAC9B;AAAA,QACD;AAEA,YAAI,OAAO,MAAM,UAAU;AAC1B,eAAK,SAAS,eAAe,EAAE;AAAA,QAChC;AAEA,YAAI,KAAK,UAAU,QAAQ;AAC1B,oBAAU;AAAA,QACX;AAEA,aAAK,WAAW,YAAY;AAC5B,aAAK,MAAM;AACX,aAAK,WAAW;AAChB,aAAK,YAAY,IAAI,QAAQ,KAAK,KAAK,KAAK,SAAS;AAErD,YAAI,KAAK,UAAU,MAAM;AACxB,eAAK,SAAS,KAAK,UAAU,IAAI;AAAA,QAClC;AAAA,MACD;AAOA,aAAO,UAAU,WAAW,SAAU,OAAO;AAC5C,aAAK,WAAW,IAAI,YAAY,eAAe,OAAO,KAAK,UAAU,YAAY,GAAG,KAAK,UAAU,YAAY;AAC/G,aAAK,SAAS,QAAQ,KAAK;AAC3B,aAAK,SAAS,KAAK;AACnB,aAAK,IAAI,QAAQ;AACjB,aAAK,UAAU,KAAK,KAAK,QAAQ;AACjC,aAAK,UAAU;AAAA,MAChB;AASA,aAAO,UAAU,YAAY,WAAY;AACxC,YAAI,OAAO,KAAK,UAAU,aAAa,eAAe,CAAC,KAAK,YAAY,KAAK,YAAY,IAAI;AAC5F,eAAK,UAAU,UAAU;AAAA,QAC1B;AAAA,MACD;AAKA,aAAO,UAAU,QAAQ,WAAY;AACpC,aAAK,UAAU,MAAM;AAAA,MACtB;AAKA,aAAO,eAAe;AAEtB,aAAO;AAAA,IAER,CAAC;AAAA;AAAA;", "names": ["i", "Drawing"]}