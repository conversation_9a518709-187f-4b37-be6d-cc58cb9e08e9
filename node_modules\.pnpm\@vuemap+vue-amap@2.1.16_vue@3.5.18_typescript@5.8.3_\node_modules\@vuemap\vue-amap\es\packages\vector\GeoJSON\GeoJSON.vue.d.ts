/// <reference types="@vuemap/amap-jsapi-types" />
import type { PropType } from 'vue';
declare const _default: import("vue").DefineComponent<{
    geo: {
        type: ObjectConstructor;
        required: true;
    };
    markerOptions: {
        type: PropType<AMap.MarkerOptions>;
    };
    getMarker: {
        type: FunctionConstructor;
    };
    polylineOptions: {
        type: PropType<AMap.PolylineOptions>;
    };
    getPolyline: {
        type: FunctionConstructor;
    };
    polygonOptions: {
        type: PropType<AMap.PolygonOptions>;
    };
    getPolygon: {
        type: FunctionConstructor;
    };
} & {
    visible: import("../../../utils").IPropOptions<boolean>;
    zIndex: import("../../../utils").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils").IPropOptions<boolean>;
    extraOptions: import("../../../utils").IPropOptions<any>;
}, {
    props: import("@vue/shared").LooseRequired<{
        readonly geo: Record<string, any>;
        readonly visible?: boolean | undefined;
        readonly zIndex?: number | undefined;
        readonly reEventWhenUpdate?: boolean | undefined;
        readonly extraOptions?: any;
        readonly markerOptions?: AMap.MarkerOptions | undefined;
        readonly getMarker?: Function | undefined;
        readonly polylineOptions?: AMap.PolylineOptions | undefined;
        readonly getPolyline?: Function | undefined;
        readonly polygonOptions?: AMap.PolygonOptions | undefined;
        readonly getPolygon?: Function | undefined;
        readonly onInit?: ((...args: any[]) => any) | undefined;
    } & {}>;
    emits: (event: "init", ...args: any[]) => void;
    $amapComponent: AMap.GeoJSON;
    $$getInstance: () => AMap.GeoJSON;
    parentInstance: import("../../../mixins").IProvideType | undefined;
    createMarker: (geojson: any, lnglat: [number, number]) => AMap.Marker;
    createPolyline: (geojson: any, lnglat: any) => AMap.Polyline;
    createPolygon: (geojson: any, lnglat: any) => AMap.Polygon;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], "init", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    geo: {
        type: ObjectConstructor;
        required: true;
    };
    markerOptions: {
        type: PropType<AMap.MarkerOptions>;
    };
    getMarker: {
        type: FunctionConstructor;
    };
    polylineOptions: {
        type: PropType<AMap.PolylineOptions>;
    };
    getPolyline: {
        type: FunctionConstructor;
    };
    polygonOptions: {
        type: PropType<AMap.PolygonOptions>;
    };
    getPolygon: {
        type: FunctionConstructor;
    };
} & {
    visible: import("../../../utils").IPropOptions<boolean>;
    zIndex: import("../../../utils").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils").IPropOptions<boolean>;
    extraOptions: import("../../../utils").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
}, {}, {}>;
export default _default;
