{"version": 3, "file": "GeoJSON.vue2.mjs", "sources": ["../../../../../packages/vector/GeoJSON/GeoJSON.vue"], "sourcesContent": ["<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {merge} from \"lodash-es\";\r\nimport {useRegister} from \"../../../mixins\";\r\nimport {buildProps} from \"../../../utils\";\r\nimport type { PropType} from 'vue';\r\n\r\ndefineOptions({\r\n  name: 'ElAmapGeojson',\r\n  inheritAttrs: false\r\n});\r\n\r\nconst props = defineProps(buildProps({\r\n  // 要加载的标准GeoJSON对象\r\n  geo: {\r\n    type: Object,\r\n    required: true\r\n  },\r\n  // marker的默认样式\r\n  markerOptions: {\r\n    type: Object as PropType<AMap.MarkerOptions>\r\n  },\r\n  // 指定点要素的绘制方式，缺省时为Marker的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径\r\n  getMarker: {\r\n    type: Function\r\n  },\r\n  // polyline的默认样式\r\n  polylineOptions: {\r\n    type: Object as PropType<AMap.PolylineOptions>\r\n  },\r\n  // 指定线要素的绘制方式，缺省时为Polyline的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径\r\n  getPolyline: {\r\n    type: Function\r\n  },\r\n  // polygon的默认样式\r\n  polygonOptions: {\r\n    type: Object as PropType<AMap.PolygonOptions>\r\n  },\r\n  // 指定面要素的绘制方式，缺省时为Polygon的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径\r\n  getPolygon: {\r\n    type: Function\r\n  } \r\n}));\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: AMap.GeoJSON;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<AMap.GeoJSON, AMap.Map>((options, parentComponent) => {\r\n  return new Promise<AMap.GeoJSON>((resolve) => {\r\n    AMap.plugin(['AMap.GeoJSON'], () => {\r\n      if (!options.getMarker) {\r\n        options.getMarker = createMarker;\r\n      }\r\n      if (!options.getPolyline) {\r\n        options.getPolyline = createPolyline;\r\n      }\r\n      if (!options.getPolygon) {\r\n        options.getPolygon = createPolygon;\r\n      }\r\n      $amapComponent = new AMap.GeoJSON(options);\r\n      parentComponent.add($amapComponent);\r\n      resolve($amapComponent);\r\n    });\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  propsRedirect: {\r\n    geo: 'geoJSON'\r\n  },\r\n  watchRedirectFn: {\r\n    __geoJSON (value) {\r\n      if($amapComponent){\r\n        $amapComponent.importData(value);\r\n      }\r\n    }\r\n  },\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance?.isDestroy){\r\n        parentInstance?.$amapComponent.remove($amapComponent);\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\nconst createMarker = (geojson: any, lnglat: [number, number]) => {\r\n  let options = props.markerOptions || {};\r\n  options = merge({}, options, geojson.properties);\r\n  options.position = lnglat;\r\n  return new AMap.Marker(options);\r\n};\r\nconst createPolyline = (geojson: any, lnglat: any) => {\r\n  let options = props.polylineOptions || {};\r\n  options = merge({}, options, geojson.properties);\r\n  options.path = lnglat;\r\n  return new AMap.Polyline(options);\r\n};\r\nconst createPolygon = (geojson: any, lnglat: any) => {\r\n  let options = props.polygonOptions || {};\r\n  options = merge({}, options, geojson.properties);\r\n  options.path = lnglat;\r\n  return new AMap.Polygon(options);\r\n};\r\n\r\ndefineExpose({\r\n  $$getInstance\r\n});\r\n\r\n</script>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAeA,IAAA,MAAM,KAAQ,GAAA,OAAA,CAAA;AA+Bd,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAI,IAAA,cAAA,CAAA;AAEJ,IAAA,MAAM,EAAC,aAAe,EAAA,cAAA,KAAkB,WAAoC,CAAA,CAAC,SAAS,eAAoB,KAAA;AACxG,MAAO,OAAA,IAAI,OAAsB,CAAA,CAAC,OAAY,KAAA;AAC5C,QAAA,IAAA,CAAK,MAAO,CAAA,CAAC,cAAc,CAAA,EAAG,MAAM;AAClC,UAAI,IAAA,CAAC,QAAQ,SAAW,EAAA;AACtB,YAAA,OAAA,CAAQ,SAAY,GAAA,YAAA,CAAA;AAAA,WACtB;AACA,UAAI,IAAA,CAAC,QAAQ,WAAa,EAAA;AACxB,YAAA,OAAA,CAAQ,WAAc,GAAA,cAAA,CAAA;AAAA,WACxB;AACA,UAAI,IAAA,CAAC,QAAQ,UAAY,EAAA;AACvB,YAAA,OAAA,CAAQ,UAAa,GAAA,aAAA,CAAA;AAAA,WACvB;AACA,UAAiB,cAAA,GAAA,IAAI,IAAK,CAAA,OAAA,CAAQ,OAAO,CAAA,CAAA;AACzC,UAAA,eAAA,CAAgB,IAAI,cAAc,CAAA,CAAA;AAClC,UAAA,OAAA,CAAQ,cAAc,CAAA,CAAA;AAAA,SACvB,CAAA,CAAA;AAAA,OACF,CAAA,CAAA;AAAA,KAEA,EAAA;AAAA,MACD,KAAA;AAAA,MACA,aAAe,EAAA;AAAA,QACb,GAAK,EAAA,SAAA;AAAA,OACP;AAAA,MACA,eAAiB,EAAA;AAAA,QACf,UAAW,KAAO,EAAA;AAChB,UAAA,IAAG,cAAe,EAAA;AAChB,YAAA,cAAA,CAAe,WAAW,KAAK,CAAA,CAAA;AAAA,WACjC;AAAA,SACF;AAAA,OACF;AAAA,MACA,gBAAoB,GAAA;AAClB,QAAI,IAAA,cAAA,KAAkB,iDAAgB,cAAgB,CAAA,EAAA;AACpD,UAAG,IAAA,EAAC,iDAAgB,SAAU,CAAA,EAAA;AAC5B,YAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAgB,eAAe,MAAO,CAAA,cAAA,CAAA,CAAA;AAAA,WACxC;AACA,UAAiB,cAAA,GAAA,IAAA,CAAA;AAAA,SACnB;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,CAAC,OAAA,EAAc,MAA6B,KAAA;AAC/D,MAAI,IAAA,OAAA,GAAU,KAAM,CAAA,aAAA,IAAiB,EAAC,CAAA;AACtC,MAAA,OAAA,GAAU,KAAM,CAAA,EAAI,EAAA,OAAA,EAAS,QAAQ,UAAU,CAAA,CAAA;AAC/C,MAAA,OAAA,CAAQ,QAAW,GAAA,MAAA,CAAA;AACnB,MAAO,OAAA,IAAI,IAAK,CAAA,MAAA,CAAO,OAAO,CAAA,CAAA;AAAA,KAChC,CAAA;AACA,IAAM,MAAA,cAAA,GAAiB,CAAC,OAAA,EAAc,MAAgB,KAAA;AACpD,MAAI,IAAA,OAAA,GAAU,KAAM,CAAA,eAAA,IAAmB,EAAC,CAAA;AACxC,MAAA,OAAA,GAAU,KAAM,CAAA,EAAI,EAAA,OAAA,EAAS,QAAQ,UAAU,CAAA,CAAA;AAC/C,MAAA,OAAA,CAAQ,IAAO,GAAA,MAAA,CAAA;AACf,MAAO,OAAA,IAAI,IAAK,CAAA,QAAA,CAAS,OAAO,CAAA,CAAA;AAAA,KAClC,CAAA;AACA,IAAM,MAAA,aAAA,GAAgB,CAAC,OAAA,EAAc,MAAgB,KAAA;AACnD,MAAI,IAAA,OAAA,GAAU,KAAM,CAAA,cAAA,IAAkB,EAAC,CAAA;AACvC,MAAA,OAAA,GAAU,KAAM,CAAA,EAAI,EAAA,OAAA,EAAS,QAAQ,UAAU,CAAA,CAAA;AAC/C,MAAA,OAAA,CAAQ,IAAO,GAAA,MAAA,CAAA;AACf,MAAO,OAAA,IAAI,IAAK,CAAA,OAAA,CAAQ,OAAO,CAAA,CAAA;AAAA,KACjC,CAAA;AAEA,IAAa,QAAA,CAAA;AAAA,MACX,aAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;"}