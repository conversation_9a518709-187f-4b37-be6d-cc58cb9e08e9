{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/color-picker/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport ColorPicker from './src/color-picker.vue'\n\nexport const ElColorPicker = withInstall(ColorPicker)\nexport default ElColorPicker\n\nexport * from './src/color-picker'\n"], "names": ["withInstall", "ColorPicker"], "mappings": ";;;;;;;;;AAEY,MAAC,aAAa,GAAGA,mBAAW,CAACC,wBAAW;;;;;;;;"}