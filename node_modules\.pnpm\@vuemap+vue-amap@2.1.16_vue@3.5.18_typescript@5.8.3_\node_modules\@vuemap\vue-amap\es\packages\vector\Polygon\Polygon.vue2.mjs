import { defineComponent, useAttrs, openBlock, createElementBlock } from 'vue';
import { debounce } from 'lodash-es';
import '../../../mixins/index.mjs';
import { useEditor } from '../../../mixins/useEditor.mjs';
import '../../../utils/index.mjs';
import { propsTypes } from './props.mjs';
import { useRegister } from '../../../mixins/useRegister.mjs';
import { isMapInstance, isOverlayGroupInstance, isVectorLayerInstance, convertLnglat } from '../../../utils/util.mjs';

var script = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapPolygon",
    inheritAttrs: false
  },
  __name: "Polygon",
  props: propsTypes,
  emits: ["init", "update:path"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let destroying = false;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new AMap.Polygon(options);
        if (isMapInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        } else if (isOverlayGroupInstance(parentComponent)) {
          parentComponent.addOverlay($amapComponent);
        } else if (isVectorLayerInstance(parentComponent)) {
          parentComponent.add($amapComponent);
        }
        bindModelEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: {
        __zIndex(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ zIndex: value });
          }
        },
        __strokeColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeColor: value });
          }
        },
        __strokeOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeOpacity: value });
          }
        },
        __strokeWeight(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeWeight: value });
          }
        },
        __fillColor(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillColor: value });
          }
        },
        __fillOpacity(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ fillOpacity: value });
          }
        },
        __strokeStyle(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeStyle: value });
          }
        },
        __strokeDasharray(value) {
          if ($amapComponent) {
            $amapComponent.setOptions({ strokeDasharray: value });
          }
        },
        __editable(flag) {
          createEditor().then(() => {
            flag ? resetEditor() : editor.close();
          });
        },
        __path(path) {
          if ($amapComponent) {
            $amapComponent.setPath(path);
            resetEditor();
          }
        },
        __draggable(flag) {
          $amapComponent.setOptions({
            draggable: flag
          });
          if (editor) {
            if (editor.editingPolyObj && editor.editingPolyObj.origin_options) {
              editor.editingPolyObj.origin_options.draggable = flag;
            }
          }
          if (props.editable) {
            resetEditor();
          }
        }
      },
      destroyComponent() {
        destroying = true;
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (editor) {
            if (!parentInstance.isDestroy) {
              editor.close();
            }
            editor = null;
          }
          if (!parentInstance.isDestroy) {
            if (isMapInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            } else if (isOverlayGroupInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.removeOverlay($amapComponent);
            } else if (isVectorLayerInstance(parentInstance.$amapComponent)) {
              parentInstance.$amapComponent.remove($amapComponent);
            }
          }
          if ($amapComponent.destroy) {
            $amapComponent.destroy();
          }
          $amapComponent = null;
        }
      }
    });
    const resetEditor = debounce(() => {
      if (editor && props.editable) {
        editor.close();
        editor.open();
      }
    }, 50);
    const bindModelEvents = () => {
      $amapComponent.on("dragend", () => {
        emitModel($amapComponent);
      });
      $amapComponent.on("touchend", () => {
        emitModel($amapComponent);
      });
    };
    const emitModel = (target) => {
      if (destroying) {
        return;
      }
      const paths = target.getPath();
      const pathArray = paths == null ? void 0 : paths.map(convertLnglat);
      emits("update:path", pathArray);
    };
    let editor;
    const attrs = useAttrs();
    const createEditor = () => {
      return new Promise((resolve) => {
        if (editor) {
          resolve();
        } else {
          AMap.plugin(["AMap.PolygonEditor"], () => {
            editor = new AMap.PolygonEditor(parentInstance == null ? void 0 : parentInstance.$amapComponent, $amapComponent, props.editOptions);
            useEditor(editor, attrs);
            bindEditorModelEvents();
            resolve();
          });
        }
      });
    };
    const bindEditorModelEvents = () => {
      editor.on("addnode", (e) => {
        emitModel(e.target);
      });
      editor.on("adjust", (e) => {
        emitModel(e.target);
      });
      editor.on("removenode", (e) => {
        emitModel(e.target);
      });
      editor.on("add", (e) => {
        emitModel(e.target);
      });
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

export { script as default };
//# sourceMappingURL=Polygon.vue2.mjs.map
