{"version": 3, "file": "HawkEye.vue2.js", "sources": ["../../../../../packages/control/HawkEye/HawkEye.vue"], "sourcesContent": ["<template />\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"../../../mixins\";\r\nimport {propsTypes} from \"./props\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapControlHawkEye',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(propsTypes);\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: AMap.HawkEye;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<AMap.HawkEye, AMap.Map>((options, parentComponent) => {\r\n  return new Promise<AMap.HawkEye>((resolve) => {\r\n    parentComponent.plugin(['AMap.HawkEye'], () => {\r\n      $amapComponent = new AMap.HawkEye(options);\r\n      parentComponent.addControl($amapComponent);\r\n      resolve($amapComponent);\r\n    });\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  watchRedirectFn: {\r\n    __isOpen (flag: boolean){\r\n      !flag ? $amapComponent.close() : $amapComponent.open();\r\n    }\r\n  },\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance?.isDestroy){\r\n        parentInstance?.$amapComponent.removeControl($amapComponent);\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance\r\n});\r\n\r\n</script>\r\n"], "names": ["useRegister"], "mappings": ";;;;;;;;;;;;;;;;;;AAYA,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAI,IAAA,cAAA,CAAA;AAEJ,IAAA,MAAM,EAAC,aAAe,EAAA,cAAA,KAAkBA,uBAAoC,CAAA,CAAC,SAAS,eAAoB,KAAA;AACxG,MAAO,OAAA,IAAI,OAAsB,CAAA,CAAC,OAAY,KAAA;AAC5C,QAAA,eAAA,CAAgB,MAAO,CAAA,CAAC,cAAc,CAAA,EAAG,MAAM;AAC7C,UAAiB,cAAA,GAAA,IAAI,IAAK,CAAA,OAAA,CAAQ,OAAO,CAAA,CAAA;AACzC,UAAA,eAAA,CAAgB,WAAW,cAAc,CAAA,CAAA;AACzC,UAAA,OAAA,CAAQ,cAAc,CAAA,CAAA;AAAA,SACvB,CAAA,CAAA;AAAA,OACF,CAAA,CAAA;AAAA,KAEA,EAAA;AAAA,MACD,KAAA;AAAA,MACA,eAAiB,EAAA;AAAA,QACf,SAAU,IAAc,EAAA;AACtB,UAAA,CAAC,IAAO,GAAA,cAAA,CAAe,KAAM,EAAA,GAAI,eAAe,IAAK,EAAA,CAAA;AAAA,SACvD;AAAA,OACF;AAAA,MACA,gBAAoB,GAAA;AAClB,QAAI,IAAA,cAAA,KAAkB,iDAAgB,cAAgB,CAAA,EAAA;AACpD,UAAG,IAAA,EAAC,iDAAgB,SAAU,CAAA,EAAA;AAC5B,YAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAgB,eAAe,aAAc,CAAA,cAAA,CAAA,CAAA;AAAA,WAC/C;AACA,UAAiB,cAAA,GAAA,IAAA,CAAA;AAAA,SACnB;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AAED,IAAa,QAAA,CAAA;AAAA,MACX,aAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;"}