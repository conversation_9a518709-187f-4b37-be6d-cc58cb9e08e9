const pi = 3.141592653589793;
const a = 6378245;
const ee = 0.006693421622965943;
const x_pi = pi * 3e3 / 180;
const R = 6378137;
function lonLatToTileNumbers(lon_deg, lat_deg, zoom) {
  const lat_rad = pi / 180 * lat_deg;
  const n = Math.pow(2, zoom);
  const xtile = Math.floor((lon_deg + 180) / 360 * n);
  const ytile = Math.floor((1 - Math.asinh(Math.tan(lat_rad)) / pi) / 2 * n);
  return [xtile, ytile];
}
function tileNumbersToLonLat(xtile, ytile, zoom) {
  const n = Math.pow(2, zoom);
  const lon_deg = xtile / n * 360 - 180;
  const lat_rad = Math.atan(Math.sinh(pi * (1 - 2 * ytile / n)));
  const lat_deg = lat_rad * 180 / pi;
  return [lon_deg, lat_deg];
}
function bd09_To_gps84(lng, lat) {
  const gcj02 = bd09_To_gcj02(lng, lat);
  const map84 = gcj02_To_gps84(gcj02.lng, gcj02.lat);
  return map84;
}
function gps84_To_bd09(lng, lat) {
  const gcj02 = gps84_To_gcj02(lng, lat);
  const bd09 = gcj02_To_bd09(gcj02.lng, gcj02.lat);
  return bd09;
}
function gps84_To_gcj02(lng, lat) {
  let dLat = transformLat(lng - 105, lat - 35);
  let dLng = transformLng(lng - 105, lat - 35);
  const radLat = lat / 180 * pi;
  let magic = Math.sin(radLat);
  magic = 1 - ee * magic * magic;
  const sqrtMagic = Math.sqrt(magic);
  dLat = dLat * 180 / (a * (1 - ee) / (magic * sqrtMagic) * pi);
  dLng = dLng * 180 / (a / sqrtMagic * Math.cos(radLat) * pi);
  const mgLat = lat + dLat;
  const mgLng = lng + dLng;
  const newCoord = {
    lng: mgLng,
    lat: mgLat
  };
  return newCoord;
}
function gcj02_To_gps84(lng, lat) {
  const coord = transform(lng, lat);
  const lontitude = lng * 2 - coord.lng;
  const latitude = lat * 2 - coord.lat;
  const newCoord = {
    lng: lontitude,
    lat: latitude
  };
  return newCoord;
}
function gcj02_To_bd09(x, y) {
  const z = Math.sqrt(x * x + y * y) + 2e-5 * Math.sin(y * x_pi);
  const theta = Math.atan2(y, x) + 3e-6 * Math.cos(x * x_pi);
  const bd_lng = z * Math.cos(theta) + 65e-4;
  const bd_lat = z * Math.sin(theta) + 6e-3;
  const newCoord = {
    lng: bd_lng,
    lat: bd_lat
  };
  return newCoord;
}
function bd09_To_gcj02(bd_lng, bd_lat) {
  const x = bd_lng - 65e-4;
  const y = bd_lat - 6e-3;
  const z = Math.sqrt(x * x + y * y) - 2e-5 * Math.sin(y * x_pi);
  const theta = Math.atan2(y, x) - 3e-6 * Math.cos(x * x_pi);
  const gg_lng = z * Math.cos(theta);
  const gg_lat = z * Math.sin(theta);
  const newCoord = {
    lng: gg_lng,
    lat: gg_lat
  };
  return newCoord;
}
function transform(lng, lat) {
  let dLat = transformLat(lng - 105, lat - 35);
  let dLng = transformLng(lng - 105, lat - 35);
  const radLat = lat / 180 * pi;
  let magic = Math.sin(radLat);
  magic = 1 - ee * magic * magic;
  const sqrtMagic = Math.sqrt(magic);
  dLat = dLat * 180 / (a * (1 - ee) / (magic * sqrtMagic) * pi);
  dLng = dLng * 180 / (a / sqrtMagic * Math.cos(radLat) * pi);
  const mgLat = lat + dLat;
  const mgLng = lng + dLng;
  const newCoord = {
    lng: mgLng,
    lat: mgLat
  };
  return newCoord;
}
function transformLat(x, y) {
  let ret = -100 + 2 * x + 3 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
  ret += (20 * Math.sin(6 * x * pi) + 20 * Math.sin(2 * x * pi)) * 2 / 3;
  ret += (20 * Math.sin(y * pi) + 40 * Math.sin(y / 3 * pi)) * 2 / 3;
  ret += (160 * Math.sin(y / 12 * pi) + 320 * Math.sin(y * pi / 30)) * 2 / 3;
  return ret;
}
function transformLng(x, y) {
  let ret = 300 + x + 2 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
  ret += (20 * Math.sin(6 * x * pi) + 20 * Math.sin(2 * x * pi)) * 2 / 3;
  ret += (20 * Math.sin(x * pi) + 40 * Math.sin(x / 3 * pi)) * 2 / 3;
  ret += (150 * Math.sin(x / 12 * pi) + 300 * Math.sin(x / 30 * pi)) * 2 / 3;
  return ret;
}

export { bd09_To_gcj02, bd09_To_gps84, gcj02_To_bd09, gcj02_To_gps84, gps84_To_bd09, gps84_To_gcj02, lonLatToTileNumbers, tileNumbersToLonLat };
//# sourceMappingURL=GPSUtil.mjs.map
