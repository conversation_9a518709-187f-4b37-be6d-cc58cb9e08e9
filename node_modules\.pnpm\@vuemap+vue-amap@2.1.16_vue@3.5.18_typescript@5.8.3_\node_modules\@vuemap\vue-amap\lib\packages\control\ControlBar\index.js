'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('./ControlBar.vue.js');
var ControlBar_vue_vue_type_script_setup_true_lang = require('./ControlBar.vue2.js');

ControlBar_vue_vue_type_script_setup_true_lang.default.install = (app) => {
  app.component(ControlBar_vue_vue_type_script_setup_true_lang.default.name, ControlBar_vue_vue_type_script_setup_true_lang.default);
  return app;
};
const ElAmapControlControlBar = ControlBar_vue_vue_type_script_setup_true_lang.default;

exports.ElAmapControlControlBar = ElAmapControlControlBar;
exports.default = ElAmapControlControlBar;
//# sourceMappingURL=index.js.map
