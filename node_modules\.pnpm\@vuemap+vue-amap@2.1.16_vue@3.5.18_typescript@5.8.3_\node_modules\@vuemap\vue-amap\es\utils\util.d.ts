/**
 * 判断对象是不是map实例对象
 * @param instance
 * @returns {string|boolean}
 */
export function isMapInstance(instance: any): string | boolean;
/**
 * 判断对象是不是OverlayGroup实例对象
 * @param instance
 * @returns {string|boolean}
 */
export function isOverlayGroupInstance(instance: any): string | boolean;
/**
 * 判断对象是不是IndoorMap实例对象
 * @param instance
 * @returns {string|boolean}
 */
export function isIndoorMapInstance(instance: any): string | boolean;
/**
 * 判断对象是不是LabelsLayer实例对象
 * @param instance
 * @returns {string|boolean}
 */
export function isLabelsLayerInstance(instance: any): string | boolean;
/**
 * 判断对象是不是VectorLayer实例对象
 * @param instance
 * @returns {string|boolean}
 */
export function isVectorLayerInstance(instance: any): string | boolean;
/**
 * 将$props中的事件名称转换为地图组件需要的事件名
 * @param functionName
 * @returns {string|*}
 */
export function convertEventToLowerCase(functionName: any): string | any;
/**
 * 加载JS文件
 * @param url
 * @param callback
 */
export function loadScript(url: any, callback: any): void;
export function convertLnglat(lnglat: any): any;
/**
 * 将字符串的第一个字符调整为大写
 * @param prop
 */
export function upperCamelCase(prop: any): any;
export const eventReg: RegExp;
