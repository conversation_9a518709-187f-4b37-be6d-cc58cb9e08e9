declare let lazyAMapApiLoaderInstance: any;
interface AMapLoaderOptions {
    key: string;
    version?: string;
    plugins?: string[];
    Loca?: {
        version?: string;
    };
    AMapUI?: {
        version?: string;
        plugins?: string[];
    };
    serviceHost?: string;
    securityJsCode?: string;
    offline?: boolean;
}
export declare const initAMapApiLoader: (config: AMapLoaderOptions) => void;
export { lazyAMapApiLoaderInstance };
export { resetJsApi } from './amap-api-loader';
