import type { WatchStopHandle } from "vue";
declare const _default: import("vue").DefineComponent<{
    visible: {
        type: BooleanConstructor;
        default: boolean;
    };
    zIndex: {
        type: NumberConstructor;
    };
    reEventWhenUpdate: {
        type: BooleanConstructor;
        default: boolean;
    };
    extraOptions: {
        type: ObjectConstructor;
    };
}, unknown, {
    needInitComponents: (() => void)[];
    unwatchFns: WatchStopHandle[];
    propsRedirect: {};
    converters: {};
    isDestroy: boolean;
    cacheEvents: {};
    isMounted: boolean;
}, {}, {
    getHandlerFun(prop: any): any;
    convertProps(): {};
    convertProxyToRaw(value: any): any;
    convertSignalProp(key: any, sourceData: any): any;
    registerEvents(): void;
    unregisterEvents(): void;
    setPropWatchers(): void;
    initProps(): void;
    lazyRegister(): void;
    addChildComponent(component: () => void): void;
    createChildren(): void;
    register(): void;
    registerRest(instance: any): void;
    $$getInstance(): any;
    destroyComponent(): void;
    __visible(flag: any): void;
    __zIndex(value: any): void;
}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], "init", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    visible: {
        type: BooleanConstructor;
        default: boolean;
    };
    zIndex: {
        type: NumberConstructor;
    };
    reEventWhenUpdate: {
        type: BooleanConstructor;
        default: boolean;
    };
    extraOptions: {
        type: ObjectConstructor;
    };
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
}, {
    visible: boolean;
    reEventWhenUpdate: boolean;
}, {}>;
export default _default;
