{"version": 3, "file": "Geolocation.vue2.js", "sources": ["../../../../../packages/control/Geolocation/Geolocation.vue"], "sourcesContent": ["<template />\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"../../../mixins\";\r\nimport {propsType} from \"./props\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapControlGeolocation',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(propsType);\r\n\r\nconst emits = defineEmits(['init', 'complete']);\r\n\r\nlet $amapComponent: AMap.Geolocation;\r\n\r\nconst emitComplete = (e: any) => {\r\n  emits('complete', e);\r\n};\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<AMap.Geolocation, AMap.Map>((options, parentComponent) => {\r\n  return new Promise<AMap.Geolocation>((resolve) => {\r\n    parentComponent.plugin(['AMap.Geolocation'], () => {\r\n      $amapComponent = new AMap.Geolocation(options);\r\n      parentComponent.addControl($amapComponent);\r\n      $amapComponent.on('complete', emitComplete);\r\n      resolve($amapComponent);\r\n    });\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  destroyComponent () {\r\n    $amapComponent.off('complete', emitComplete);\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance?.isDestroy){\r\n        parentInstance?.$amapComponent.removeControl($amapComponent);\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance\r\n});\r\n</script>\r\n"], "names": ["useRegister"], "mappings": ";;;;;;;;;;;;;;;;;;AAaA,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAI,IAAA,cAAA,CAAA;AAEJ,IAAM,MAAA,YAAA,GAAe,CAAC,CAAW,KAAA;AAC/B,MAAA,KAAA,CAAM,YAAY,CAAC,CAAA,CAAA;AAAA,KACrB,CAAA;AAEA,IAAA,MAAM,EAAC,aAAe,EAAA,cAAA,KAAkBA,uBAAwC,CAAA,CAAC,SAAS,eAAoB,KAAA;AAC5G,MAAO,OAAA,IAAI,OAA0B,CAAA,CAAC,OAAY,KAAA;AAChD,QAAA,eAAA,CAAgB,MAAO,CAAA,CAAC,kBAAkB,CAAA,EAAG,MAAM;AACjD,UAAiB,cAAA,GAAA,IAAI,IAAK,CAAA,WAAA,CAAY,OAAO,CAAA,CAAA;AAC7C,UAAA,eAAA,CAAgB,WAAW,cAAc,CAAA,CAAA;AACzC,UAAe,cAAA,CAAA,EAAA,CAAG,YAAY,YAAY,CAAA,CAAA;AAC1C,UAAA,OAAA,CAAQ,cAAc,CAAA,CAAA;AAAA,SACvB,CAAA,CAAA;AAAA,OACF,CAAA,CAAA;AAAA,KAEA,EAAA;AAAA,MACD,KAAA;AAAA,MACA,gBAAoB,GAAA;AAClB,QAAe,cAAA,CAAA,GAAA,CAAI,YAAY,YAAY,CAAA,CAAA;AAC3C,QAAI,IAAA,cAAA,KAAkB,iDAAgB,cAAgB,CAAA,EAAA;AACpD,UAAG,IAAA,EAAC,iDAAgB,SAAU,CAAA,EAAA;AAC5B,YAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAgB,eAAe,aAAc,CAAA,cAAA,CAAA,CAAA;AAAA,WAC/C;AACA,UAAiB,cAAA,GAAA,IAAA,CAAA;AAAA,SACnB;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AAED,IAAa,QAAA,CAAA;AAAA,MACX,aAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;"}