/*! @vuemap/vue-amap-loca v2.1.2 */

import { getCurrentInstance, inject, onMounted, onBeforeUnmount, onBeforeUpdate, onUpdated, nextTick, isProxy, toRaw, unref, watch, defineComponent, openBlock, createElementBlock, provide, renderSlot } from 'vue';

const makeInstaller = (components = []) => {
  const apps = [];
  const install = (app) => {
    if (apps.includes(app))
      return;
    apps.push(app);
    components.forEach((c) => app.use(c));
  };
  return {
    install
  };
};

function convertEventToLowerCase(functionName) {
  if (!functionName || functionName.length < 4) {
    return functionName;
  }
  const func = functionName.substring(3, functionName.length);
  const firstLetter = functionName[2].toLowerCase();
  return firstLetter + func;
}
const eventReg = /^on[A-Z]+/;
function upperCamelCase(prop) {
  if (!prop) {
    return prop;
  }
  return prop.charAt(0).toUpperCase() + prop.slice(1);
}

function bindInstanceEvent(instance, eventName, handler) {
  if (!instance || !instance.on) {
    return;
  }
  instance.on(eventName, handler);
}
function removeInstanceEvent(instance, eventName, handler) {
  if (!instance || !instance.off) {
    return;
  }
  instance.off(eventName, handler);
}

const commonProps = {
  visible: {
    type: Boolean,
    default: true
  },
  zIndex: {
    type: Number
  },
  reEventWhenUpdate: {
    type: Boolean,
    default: false
  },
  extraOptions: {
    type: Object
  }
};
const buildProps = (props) => {
  return Object.assign({}, commonProps, props);
};

const provideKey = "parentInstance";
const useRegister = (_init, params) => {
  let componentInstance = getCurrentInstance();
  let { props, attrs } = componentInstance;
  let parentInstance = inject(provideKey, void 0);
  const emits = params.emits;
  let isMounted = false;
  let $amapComponent;
  onMounted(() => {
    if (parentInstance) {
      if (parentInstance.$amapComponent) {
        register();
      } else {
        parentInstance.addChildComponent(register);
      }
    } else if (params.isRoot) {
      register();
    }
  });
  onBeforeUnmount(() => {
    if (!$amapComponent) {
      return;
    }
    unregisterEvents();
    stopWatchers();
    if (params.destroyComponent) {
      params.destroyComponent();
    } else {
      destroyComponent();
    }
    if (params.provideData) {
      params.provideData.isDestroy = true;
    }
    parentInstance = void 0;
    props = void 0;
    attrs = void 0;
    componentInstance = void 0;
    $amapComponent = void 0;
  });
  onBeforeUpdate(() => {
    if (props.reEventWhenUpdate && isMounted && $amapComponent) {
      unregisterEvents();
    }
  });
  onUpdated(() => {
    if (props.reEventWhenUpdate && isMounted && $amapComponent) {
      registerEvents();
    }
  });
  const register = () => {
    const options = convertProps();
    _init(options, parentInstance == null ? void 0 : parentInstance.$amapComponent).then((mapInstance) => {
      $amapComponent = mapInstance;
      registerEvents();
      initProps();
      setPropWatchers();
      Object.assign(componentInstance.ctx, componentInstance.exposed);
      emits("init", $amapComponent, componentInstance.ctx);
      nextTick(() => {
        createChildren();
      }).then();
      isMounted = true;
    });
  };
  const initProps = () => {
    const propsList = ["editable", "visible", "zooms"];
    propsList.forEach((propStr) => {
      if (props[propStr] !== void 0) {
        const handleFun = getHandlerFun(propStr);
        handleFun && handleFun.call($amapComponent, convertProxyToRaw(convertSignalProp(propStr, props[propStr])));
      }
    });
  };
  const propsRedirect = params.propsRedirect || {};
  const convertProps = () => {
    const propsCache = {};
    if (props.extraOptions) {
      Object.assign(propsCache, props.extraOptions);
    }
    Object.keys(props).forEach((_key) => {
      let key = _key;
      const propsValue = convertSignalProp(key, props[key]);
      if (propsValue !== void 0) {
        if (propsRedirect && propsRedirect[_key]) {
          key = propsRedirect[key];
        }
        propsCache[key] = propsValue;
      }
    });
    return propsCache;
  };
  const converters = params.converts || {};
  const convertSignalProp = (key, sourceData) => {
    if (converters && converters[key]) {
      return converters[key].call(void 0, sourceData);
    }
    return sourceData;
  };
  const convertProxyToRaw = (value) => {
    if (isProxy(value)) {
      return toRaw(value);
    }
    return unref(value);
  };
  let unwatchFns = [];
  let watchRedirectFn = Object.assign({
    __visible: (flag) => {
      if (!!$amapComponent && !!$amapComponent["show"] && !!$amapComponent["hide"]) {
        !flag ? $amapComponent["hide"]() : $amapComponent["show"]();
      }
    },
    __zIndex(value) {
      if ($amapComponent && $amapComponent["setzIndex"]) {
        $amapComponent["setzIndex"](value);
      }
    }
  }, params.watchRedirectFn || {});
  const setPropWatchers = () => {
    Object.keys(props).forEach((prop) => {
      let handleProp = prop;
      if (propsRedirect && propsRedirect[prop])
        handleProp = propsRedirect[prop];
      const handleFun = getHandlerFun(handleProp);
      if (!handleFun)
        return;
      const watchOptions = {
        deep: false
      };
      const propValueType = Object.prototype.toString.call(props[prop]);
      if (propValueType === "[object Object]" || propValueType === "[object Array]") {
        watchOptions.deep = true;
      }
      const unwatch = watch(() => props[prop], (nv) => {
        handleFun.call($amapComponent, convertProxyToRaw(convertSignalProp(prop, nv)));
      }, watchOptions);
      unwatchFns.push(unwatch);
    });
  };
  const stopWatchers = () => {
    unwatchFns.forEach((fn) => fn());
    unwatchFns = [];
    watchRedirectFn = void 0;
  };
  const getHandlerFun = (prop) => {
    if (watchRedirectFn[`__${prop}`]) {
      return watchRedirectFn[`__${prop}`];
    }
    if (!$amapComponent) {
      return null;
    }
    return $amapComponent[`set${upperCamelCase(prop)}`];
  };
  const cacheEvents = {};
  const registerEvents = () => {
    Object.keys(attrs).forEach((key) => {
      if (eventReg.test(key)) {
        const eventKey = convertEventToLowerCase(key);
        bindInstanceEvent($amapComponent, eventKey, attrs[key]);
        cacheEvents[eventKey] = attrs[key];
      }
    });
  };
  const unregisterEvents = () => {
    Object.keys(cacheEvents).forEach((eventKey) => {
      removeInstanceEvent($amapComponent, eventKey, cacheEvents[eventKey]);
      delete cacheEvents[eventKey];
    });
  };
  const createChildren = () => {
    const needInitComponents = params.needInitComponents || [];
    while (needInitComponents.length > 0) {
      needInitComponents[0]();
      needInitComponents.splice(0, 1);
    }
  };
  const destroyComponent = () => {
    if (!$amapComponent) {
      return;
    }
    $amapComponent.setMap && $amapComponent.setMap(null);
    $amapComponent.close && $amapComponent.close();
    $amapComponent.editor && $amapComponent.editor.close();
  };
  function $$getInstance() {
    return $amapComponent;
  }
  return {
    $$getInstance,
    parentInstance,
    isMounted
  };
};

const buildLocaProps = (props) => {
  return Object.assign({}, commonProps, {
    sourceUrl: {
      type: String
    },
    sourceData: {
      type: Object
    },
    geoBufferSource: {
      type: [ArrayBuffer, String],
      default() {
        return null;
      }
    },
    layerStyle: {
      type: Object
    },
    defaultStyleValue: {
      type: Object,
      default() {
        return {};
      }
    },
    zooms: {
      type: Array
    },
    opacity: {
      type: Number
    },
    initEvents: {
      type: Boolean,
      default: true
    },
    visibleDuration: {
      type: Number,
      default: 0
    },
    onClick: {
      type: Function,
      default: null
    },
    onMousemove: {
      type: Function,
      default: null
    },
    onRightclick: {
      type: Function,
      default: null
    }
  }, props);
};
const commonEmitNames = ["init", "mousemove", "click", "rightclick"];

function useWatchFn(options) {
  return {
    __layerStyle(style) {
      nextTick(() => {
        var _a;
        if ((_a = options.$amapComponent()) == null ? void 0 : _a.setStyle) {
          options.$amapComponent().setStyle(style);
        }
      }).then();
    },
    __sourceUrl() {
      nextTick(() => {
        options.setSource();
      }).then();
    },
    __sourceData() {
      nextTick(() => {
        options.setSource();
      }).then();
    },
    __geoBufferSource() {
      nextTick(() => {
        options.setSource();
      }).then();
    },
    __visible(flag) {
      const $amapComponent = options.$amapComponent();
      if (($amapComponent == null ? void 0 : $amapComponent.show) && ($amapComponent == null ? void 0 : $amapComponent.hide)) {
        !flag ? $amapComponent.hide(options.props.visibleDuration) : $amapComponent.show(options.props.visibleDuration);
      }
    }
  };
}
function useLocaEvents(options) {
  let isDragging = false;
  let isRotating = false;
  let source;
  const { parentInstance, $amapComponent, emits, props, setStyle } = options;
  const setSource = () => {
    if (source) {
      source.destroy();
      source = null;
    }
    if (props.geoBufferSource) {
      if (typeof props.geoBufferSource === "string") {
        source = new Loca.GeoBufferSource({
          url: props.geoBufferSource
        });
      } else {
        source = new Loca.GeoBufferSource({
          data: props.geoBufferSource
        });
      }
    } else if (props.sourceUrl) {
      source = new Loca.GeoJSONSource({
        url: props.sourceUrl
      });
    } else if (props.sourceData) {
      source = new Loca.GeoJSONSource({
        data: props.sourceData
      });
    } else {
      source = new Loca.GeoJSONSource({});
    }
    $amapComponent.setSource(source);
  };
  const initComplete = () => {
    if (props.initEvents) {
      bindEvents();
    }
  };
  const bindEvents = () => {
    if (parentInstance) {
      const map = parentInstance.getMap();
      if (props.onClick !== null) {
        map.on("click", clickMap);
      }
      if (props.onMousemove !== null) {
        map.on("mousemove", mouseMoveMap);
        map.on("dragstart", dragStart);
        map.on("dragend", dragEnd);
        map.on("rotatestart", rotateStart);
        map.on("rotateend", rotateEnd);
        map.on("mouseout", mouseoutMap);
      }
      if (props.onRightclick !== null) {
        map.on("rightclick", rightclickMap);
      }
    }
  };
  const clickMap = (e) => {
    const feature = _getFeature(e);
    emits("click", feature, e);
  };
  const rightclickMap = (e) => {
    const feature = _getFeature(e);
    emits("rightclick", feature, e);
  };
  const mouseMoveMap = (e) => {
    if (isDragging || isRotating) {
      return;
    }
    const feature = _getFeature(e);
    emits("mousemove", feature, e);
  };
  const _getFeature = (e) => {
    return $amapComponent.queryFeature(e.pixel.toArray());
  };
  const dragStart = () => {
    isDragging = true;
  };
  const dragEnd = () => {
    isDragging = false;
  };
  const mouseoutMap = () => {
    isDragging = false;
    isRotating = false;
  };
  const rotateStart = () => {
    isRotating = true;
  };
  const rotateEnd = () => {
    isRotating = false;
  };
  const unBindEvents = () => {
    if (parentInstance) {
      const map = parentInstance.getMap();
      map.off("click", clickMap);
      map.off("rightclick", rightclickMap);
      map.off("mousemove", mouseMoveMap);
      map.off("dragstart", dragStart);
      map.off("dragend", dragEnd);
      map.off("rotatestart", rotateStart);
      map.off("rotateend", rotateEnd);
      map.off("mouseout", mouseoutMap);
    }
  };
  setSource();
  setStyle();
  parentInstance == null ? void 0 : parentInstance.$amapComponent.add($amapComponent);
  initComplete();
  const _destroyComponent = () => {
    unBindEvents();
    if (!(parentInstance == null ? void 0 : parentInstance.isDestroy)) {
      parentInstance == null ? void 0 : parentInstance.$amapComponent.remove($amapComponent);
      $amapComponent.destroy();
    }
    if (source) {
      source.destroy();
      source = null;
    }
  };
  return {
    _destroyComponent,
    setSource
  };
}

var script$h = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaGrid",
    inheritAttrs: false
  },
  __name: "GridLayer",
  props: buildLocaProps({
    // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。
    cullface: {
      type: String
    },
    // 面是否接受光照，光照信息在 loca 对象中配置
    acceptLight: {
      type: Boolean,
      default: true
    },
    // 立体网格的粗糙度，值越高，说明表面越粗糙。
    shininess: {
      type: Number
    },
    // 当面有厚度的时候，有没有侧面和底面
    hasSide: {
      type: Boolean,
      default: true
    },
    // 是否开启深度检测，开启后可能会影响zIndex
    depth: {
      type: Boolean,
      default: true
    }
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.GridLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              topColor: "#fff",
              sideTopColor: "#fff",
              sideBottomColor: "#fff",
              altitude: 0,
              height: 0,
              radius: 1e3,
              gap: 0,
              unit: "meter"
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              topColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.topColor === void 0 ? style.topColor : feature.properties.topColor;
              },
              sideTopColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideTopColor === void 0 ? style.sideTopColor : feature.properties.sideTopColor;
              },
              sideBottomColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideBottomColor === void 0 ? style.sideBottomColor : feature.properties.sideBottomColor;
              },
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              },
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              radius: style.radius,
              gap: style.gap,
              unit: style.unit
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$h.__file = "src/vue-amap-loca/packages/GridLayer/GridLayer.vue";

script$h.install = (app) => {
  app.component(script$h.name, script$h);
  return app;
};
const ElAmapLocaGrid = script$h;

var script$g = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaHeatmap",
    inheritAttrs: false
  },
  __name: "HeatMapLayer",
  props: buildLocaProps({
    depth: {
      type: Boolean,
      default: true
    }
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.HeatMapLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              radius: 20,
              value: 10,
              gradient: { 0.5: "blue", 0.65: "rgb(117,211,248)", 0.7: "rgb(0, 255, 0)", 0.9: "#ffea00", 1: "red" },
              opacity: [0, 1],
              height: 100,
              heightBezier: [0.4, 0.2, 0.4, 0.8],
              max: null,
              min: null,
              unit: "px",
              difference: false
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              radius: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.radius === void 0 ? style.radius : feature.properties.radius;
              },
              value: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.value === void 0 ? style.value : feature.properties.value;
              },
              gradient: style.gradient,
              opacity: style.opacity,
              height: style.height,
              heightBezier: style.heightBezier,
              max: style.max,
              min: style.min,
              unit: style.unit,
              difference: style.difference
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$g.__file = "src/vue-amap-loca/packages/HeatMapLayer/HeatMapLayer.vue";

script$g.install = (app) => {
  app.component(script$g.name, script$g);
  return app;
};
const ElAmapLocaHeatmap = script$g;

var script$f = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaHexagon",
    inheritAttrs: false
  },
  __name: "HexagonLayer",
  props: buildLocaProps({
    cullface: {
      type: String
    },
    // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。
    acceptLight: {
      type: Boolean,
      default: true
    },
    // 面是否接受光照，光照信息在 loca 对象中配置
    shininess: {
      type: Number
    },
    // 立体网格的粗糙度，值越高，说明表面越粗糙。
    hasSide: {
      type: Boolean,
      default: true
    },
    // 当面有厚度的时候，有没有侧面和底面
    depth: {
      type: Boolean,
      default: true
    }
    // 是否开启深度检测，开启后可能会影响zIndex
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.HexagonLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              topColor: "#fff",
              sideTopColor: "#fff",
              sideBottomColor: "#fff",
              altitude: 0,
              height: 0,
              radius: 1e3,
              gap: 0,
              unit: "meter"
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              topColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.topColor === void 0 ? style.topColor : feature.properties.topColor;
              },
              sideTopColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideTopColor === void 0 ? style.sideTopColor : feature.properties.sideTopColor;
              },
              sideBottomColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideBottomColor === void 0 ? style.sideBottomColor : feature.properties.sideBottomColor;
              },
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              },
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              radius: style.radius,
              gap: style.gap,
              unit: style.unit
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$f.__file = "src/vue-amap-loca/packages/HexagonLayer/HexagonLayer.vue";

script$f.install = (app) => {
  app.component(script$f.name, script$f);
  return app;
};
const ElAmapLocaHexagon = script$f;

var script$e = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaIcon",
    inheritAttrs: false
  },
  __name: "IconLayer",
  props: buildLocaProps({}),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.IconLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              unit: "px",
              icon: "",
              iconSize: [20, 20],
              rotation: 0,
              opacity: 1,
              offset: [0, 0]
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              unit: style.unit,
              icon: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.icon === void 0 ? style.icon : feature.properties.icon;
              },
              iconSize: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.iconSize === void 0 ? style.iconSize : feature.properties.iconSize;
              },
              rotation: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.rotation === void 0 ? style.rotation : feature.properties.rotation;
              },
              opacity: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.opacity === void 0 ? style.opacity : feature.properties.opacity;
              },
              offset: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.offset === void 0 ? style.offset : feature.properties.offset;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$e.__file = "src/vue-amap-loca/packages/IconLayer/IconLayer.vue";

script$e.install = (app) => {
  app.component(script$e.name, script$e);
  return app;
};
const ElAmapLocaIcon = script$e;

var script$d = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaLine",
    inheritAttrs: false
  },
  __name: "LineLayer",
  props: buildLocaProps({}),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.LineLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              color: "#fff",
              lineWidth: 2,
              altitude: 0,
              borderWidth: 0,
              borderColor: "#fff",
              dashArray: [10, 0, 10, 0]
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              color: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.color === void 0 ? style.color : feature.properties.color;
              },
              lineWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.lineWidth === void 0 ? style.lineWidth : feature.properties.lineWidth;
              },
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              },
              borderWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderWidth === void 0 ? style.borderWidth : feature.properties.borderWidth;
              },
              borderColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderColor === void 0 ? style.borderColor : feature.properties.borderColor;
              },
              dashArray: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.dashArray === void 0 ? style.dashArray : feature.properties.dashArray;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$d.__file = "src/vue-amap-loca/packages/LineLayer/LineLayer.vue";

script$d.install = (app) => {
  app.component(script$d.name, script$d);
  return app;
};
const ElAmapLocaLine = script$d;

var script$c = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaLink",
    inheritAttrs: false
  },
  __name: "LinkLayer",
  props: buildLocaProps({}),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.LinkLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              lineColors: ["rgba(255,255,255,1)", "rgba(255,255,255,0)"],
              height: 100,
              smoothSteps: 100
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              lineColors: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.lineColors === void 0 ? style.lineColors : feature.properties.lineColors;
              },
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              smoothSteps: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.smoothSteps === void 0 ? style.smoothSteps : feature.properties.smoothSteps;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$c.__file = "src/vue-amap-loca/packages/LinkLayer/LinkLayer.vue";

script$c.install = (app) => {
  app.component(script$c.name, script$c);
  return app;
};
const ElAmapLocaLink = script$c;

var script$b = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLoca",
    inheritAttrs: false
  },
  __name: "Loca",
  props: buildLocaProps({
    ambLight: {
      type: Object
    },
    // 环境光
    dirLight: {
      type: Object
    },
    // 平行光
    pointLight: {
      type: Object
    },
    // 点光
    onClick: {
      type: Function,
      default: null
    },
    onMousemove: {
      type: Function,
      default: null
    },
    onRightclick: {
      type: Function,
      default: null
    },
    eventOptions: {
      type: Object,
      default: () => ({
        hitFirst: true
      })
    }
    // 对于事件是否只触发第一个选中的数据
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const needInitComponents = [];
    const provideData = {
      $amapComponent: void 0,
      addChildComponent(cb) {
        needInitComponents.push(cb);
      },
      isDestroy: false,
      getMap: () => {
        return getMap();
      }
    };
    provide(provideKey, provideData);
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let isDragging = false;
    let isRotating = false;
    let hitFirst = true;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.Container({
          map: parentComponent
        });
        provideData.$amapComponent = $amapComponent;
        if (options.ambLight) {
          $amapComponent.ambLight = options.ambLight;
        }
        if (options.dirLight) {
          $amapComponent.dirLight = options.dirLight;
        }
        if (options.pointLight) {
          $amapComponent.pointLight = options.pointLight;
        }
        hitFirst = options.eventOptions.hitFirst;
        bindEvents();
        resolve($amapComponent);
      });
    }, {
      emits,
      needInitComponents,
      provideData,
      destroyComponent() {
        if ($amapComponent) {
          unBindEvents();
          if ($amapComponent.animate && $amapComponent.animate.stop) {
            $amapComponent.animate.stop();
          }
          $amapComponent.destroy();
          $amapComponent = null;
        }
      }
    });
    const getMap = () => {
      return parentInstance == null ? void 0 : parentInstance.$amapComponent;
    };
    const bindEvents = () => {
      if (parentInstance) {
        const map = getMap();
        if (props.onClick !== null) {
          map.on("click", clickMap);
        }
        if (props.onMousemove !== null) {
          map.on("mousemove", mouseMoveMap);
          map.on("dragstart", dragStart);
          map.on("dragend", dragEnd);
          map.on("rotatestart", rotateStart);
          map.on("rotateend", rotateEnd);
          map.on("mouseout", mouseoutMap);
        }
        if (props.onRightclick !== null) {
          map.on("rightclick", rightclickMap);
        }
      }
    };
    const clickMap = (e) => {
      const features = _getFeature(e);
      emits("click", features, e);
    };
    const rightclickMap = (e) => {
      const features = _getFeature(e);
      emits("rightclick", features, e);
    };
    const mouseMoveMap = (e) => {
      if (isDragging || isRotating) {
        return;
      }
      const features = _getFeature(e);
      emits("mousemove", features, e);
    };
    const _getFeature = (e) => {
      const features = [];
      if ($amapComponent.layers) {
        const layers = [];
        $amapComponent.layers.forEach((v) => {
          layers.push(v);
        });
        layers.sort((a, b) => b.zIndex - a.zIndex);
        const layerLen = layers.length;
        for (let i = 0; i < layerLen; i++) {
          const temp = layers[i].queryFeature(e.pixel.toArray());
          if (temp) {
            features.push(temp);
            if (hitFirst) {
              break;
            }
          }
        }
      }
      return features;
    };
    const dragStart = () => {
      isDragging = true;
    };
    const dragEnd = () => {
      isDragging = false;
    };
    const mouseoutMap = () => {
      isDragging = false;
      isRotating = false;
    };
    const rotateStart = () => {
      isRotating = true;
    };
    const rotateEnd = () => {
      isRotating = false;
    };
    const unBindEvents = () => {
      if (parentInstance) {
        const map = getMap();
        map.off("click", clickMap);
        map.off("rightclick", rightclickMap);
        map.off("mousemove", mouseMoveMap);
        map.off("dragstart", dragStart);
        map.off("dragend", dragEnd);
        map.off("rotatestart", rotateStart);
        map.off("rotateend", rotateEnd);
        map.off("mouseout", mouseoutMap);
      }
    };
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div", null, [
        renderSlot(_ctx.$slots, "default")
      ]);
    };
  }
});

script$b.__file = "src/vue-amap-loca/packages/Loca/Loca.vue";

script$b.install = (app) => {
  app.component(script$b.name, script$b);
  return app;
};
const ElAmapLoca = script$b;

var script$a = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaPoint",
    inheritAttrs: false
  },
  __name: "PointLayer",
  props: buildLocaProps({
    // 图层里面元素的叠加效果，normal：正常透明度叠加，lighter：叠加后可能更加明亮
    blend: {
      type: String
    }
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PointLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              radius: 20,
              color: "#fff",
              unit: "px",
              borderWidth: 10,
              borderColor: "#fff",
              blurWidth: -1
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              radius: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.radius === void 0 ? style.radius : feature.properties.radius;
              },
              color: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.color === void 0 ? style.color : feature.properties.color;
              },
              unit: style.unit,
              borderWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderWidth === void 0 ? style.borderWidth : feature.properties.borderWidth;
              },
              borderColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderColor === void 0 ? style.borderColor : feature.properties.borderColor;
              },
              blurWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.blurWidth === void 0 ? style.blurWidth : feature.properties.blurWidth;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$a.__file = "src/vue-amap-loca/packages/PointLayer/PointLayer.vue";

script$a.install = (app) => {
  app.component(script$a.name, script$a);
  return app;
};
const ElAmapLocaPoint = script$a;

var script$9 = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaPolygon",
    inheritAttrs: false
  },
  __name: "PolygonLayer",
  props: buildLocaProps({
    cullface: {
      type: String
    },
    // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。
    acceptLight: {
      type: Boolean,
      default: true
    },
    // 面是否接受光照，光照信息在 loca 对象中配置
    shininess: {
      type: Number
    },
    // 立体网格的粗糙度，值越高，说明表面越粗糙。
    hasSide: {
      type: Boolean,
      default: true
    },
    // 当面有厚度的时候，有没有侧面
    hasBottom: {
      type: Boolean,
      default: false
    },
    //当面有厚度的时候，有没有底面。
    blockHide: {
      type: Boolean,
      default: true
    },
    //是否开启被遮挡的面隐藏，默认开启，如果关闭，在有透明度的时候，会显示出被遮挡的面。
    depth: {
      type: Boolean,
      default: true
    }
    // 是否开启深度检测，开启后可能会影响zIndex
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PolygonLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              topColor: "#fff",
              sideTopColor: "#fff",
              sideBottomColor: "#fff",
              altitude: 0,
              height: 0,
              texture: null,
              textureSize: [20, 3],
              label: void 0,
              labelAltitude: 0
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              topColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.topColor === void 0 ? style.topColor : feature.properties.topColor;
              },
              sideTopColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideTopColor === void 0 ? style.sideTopColor : feature.properties.sideTopColor;
              },
              sideBottomColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideBottomColor === void 0 ? style.sideBottomColor : feature.properties.sideBottomColor;
              },
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              },
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              texture: style.texture,
              textureSize: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.textureSize === void 0 ? style.textureSize : feature.properties.textureSize;
              },
              label: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.label === void 0 ? style.label : feature.properties.label;
              },
              labelAltitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.labelAltitude === void 0 ? style.labelAltitude : feature.properties.labelAltitude;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$9.__file = "src/vue-amap-loca/packages/PolygonLayer/PolygonLayer.vue";

script$9.install = (app) => {
  app.component(script$9.name, script$9);
  return app;
};
const ElAmapLocaPolygon = script$9;

var script$8 = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaPrism",
    inheritAttrs: false
  },
  __name: "PrismLayer",
  props: buildLocaProps({
    cullface: {
      type: String
    },
    // 剔除背面/前面的面（选择剔除将会提升性能），可选：back/front/none，back是剔除背面的面，front是剔除前面的面，none是不进行剔除。
    acceptLight: {
      type: Boolean,
      default: true
    },
    // 面是否接受光照，光照信息在 loca 对象中配置
    shininess: {
      type: Number
    },
    // 立体网格的粗糙度，值越高，说明表面越粗糙。
    hasSide: {
      type: Boolean,
      default: true
    },
    // 当面有厚度的时候，有没有侧面和底面
    depth: {
      type: Boolean,
      default: true
    }
    // 是否开启深度检测，开启后可能会影响zIndex
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PrismLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              radius: 20,
              unit: "px",
              sideNumber: 3,
              rotation: 0,
              altitude: 0,
              height: 100,
              topColor: "#fff",
              sideTopColor: "#fff",
              sideBottomColor: "#fff"
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              radius: style.radius,
              unit: style.unit,
              sideNumber: style.sideNumber,
              rotation: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.rotation === void 0 ? style.rotation : feature.properties.rotation;
              },
              altitude: style.altitude,
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              topColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.topColor === void 0 ? style.topColor : feature.properties.topColor;
              },
              sideTopColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideTopColor === void 0 ? style.sideTopColor : feature.properties.sideTopColor;
              },
              sideBottomColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.sideBottomColor === void 0 ? style.sideBottomColor : feature.properties.sideBottomColor;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$8.__file = "src/vue-amap-loca/packages/PrismLayer/PrismLayer.vue";

script$8.install = (app) => {
  app.component(script$8.name, script$8);
  return app;
};
const ElAmapLocaPrism = script$8;

var script$7 = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaPulseLine",
    inheritAttrs: false
  },
  __name: "PulseLineLayer",
  props: buildLocaProps({
    depth: {
      type: Boolean,
      default: true
    }
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PulseLineLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              lineWidth: 1,
              headColor: "rgba(0, 0, 0, 0.75)",
              trailColor: "rgba(0, 0, 0, 0.25)",
              altitude: 0,
              interval: 1,
              duration: 2e3
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              lineWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.lineWidth === void 0 ? style.lineWidth : feature.properties.lineWidth;
              },
              headColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.headColor === void 0 ? style.headColor : feature.properties.headColor;
              },
              trailColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.trailColor === void 0 ? style.trailColor : feature.properties.trailColor;
              },
              altitude: style.altitude,
              interval: style.interval,
              duration: style.duration
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$7.__file = "src/vue-amap-loca/packages/PulseLineLayer/PulseLineLayer.vue";

script$7.install = (app) => {
  app.component(script$7.name, script$7);
  return app;
};
const ElAmapLocaPulseLine = script$7;

var script$6 = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaPulseLink",
    inheritAttrs: false
  },
  __name: "PulseLinkLayer",
  props: buildLocaProps({
    depth: {
      type: Boolean,
      default: true
    }
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PulseLinkLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              lineColors: ["#fff"],
              height: 100,
              maxHeightScale: 0,
              smoothSteps: 50,
              lineWidth: [1, 1],
              unit: "px",
              dash: [4e3, 0, 4e3, 0],
              speed: 100,
              headColor: "rgba(0, 0, 0, 0.75)",
              trailColor: "rgba(0, 0, 0, 0.25)",
              flowLength: 100
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              lineColors: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.lineColors === void 0 ? style.lineColors : feature.properties.lineColors;
              },
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              maxHeightScale: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.maxHeightScale === void 0 ? style.maxHeightScale : feature.properties.maxHeightScale;
              },
              smoothSteps: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.smoothSteps === void 0 ? style.smoothSteps : feature.properties.smoothSteps;
              },
              lineWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.lineWidth === void 0 ? style.lineWidth : feature.properties.lineWidth;
              },
              unit: style.unit,
              dash: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.dash === void 0 ? style.dash : feature.properties.dash;
              },
              speed: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.speed === void 0 ? style.speed : feature.properties.speed;
              },
              headColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.headColor === void 0 ? style.headColor : feature.properties.headColor;
              },
              trailColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.trailColor === void 0 ? style.trailColor : feature.properties.trailColor;
              },
              flowLength: style.flowLength
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$6.__file = "src/vue-amap-loca/packages/PulseLinkLayer/PulseLinkLayer.vue";

script$6.install = (app) => {
  app.component(script$6.name, script$6);
  return app;
};
const ElAmapLocaPulseLink = script$6;

var script$5 = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaScatter",
    inheritAttrs: false
  },
  __name: "ScatterLayer",
  props: buildLocaProps({}),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.ScatterLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              size: [20, 20],
              rotation: 0,
              color: "rgba(200,200,200,1)",
              altitude: 0,
              borderWidth: 0,
              borderColor: "rgba(250,250,250,1)",
              texture: null,
              unit: "px",
              animate: false,
              duration: 0
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              size: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.size === void 0 ? style.size : feature.properties.size;
              },
              rotation: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.rotation === void 0 ? style.rotation : feature.properties.rotation;
              },
              color: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.color === void 0 ? style.color : feature.properties.color;
              },
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              },
              borderWidth: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderWidth === void 0 ? style.borderWidth : feature.properties.borderWidth;
              },
              borderColor: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.borderColor === void 0 ? style.borderColor : feature.properties.borderColor;
              },
              texture: style.texture,
              unit: style.unit,
              animate: style.animate,
              duration: style.duration
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$5.__file = "src/vue-amap-loca/packages/ScatterLayer/ScatterLayer.vue";

script$5.install = (app) => {
  app.component(script$5.name, script$5);
  return app;
};
const ElAmapLocaScatter = script$5;

var script$4 = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaZMarker",
    inheritAttrs: false
  },
  __name: "ZMarkerLayer",
  props: buildLocaProps({}),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.ZMarkerLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              unit: "px",
              content: "",
              size: [20, 20],
              rotation: 0,
              alwaysFront: false,
              altitude: 0
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              unit: style.unit,
              content: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.content === void 0 ? style.content : feature.properties.content;
              },
              size: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.size === void 0 ? style.size : feature.properties.size;
              },
              rotation: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.rotation === void 0 ? style.rotation : feature.properties.rotation;
              },
              alwaysFront: style.alwaysFront,
              altitude: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.altitude === void 0 ? style.altitude : feature.properties.altitude;
              }
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$4.__file = "src/vue-amap-loca/packages/ZMarkerLayer/ZMarkerLayer.vue";

script$4.install = (app) => {
  app.component(script$4.name, script$4);
  return app;
};
const ElAmapLocaZMarker = script$4;

var script$3 = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaAmbientLight",
    inheritAttrs: false
  },
  __name: "AmbientLight",
  props: buildProps({
    // 环境光颜色。
    color: {
      type: String
    },
    // 环境光强度。
    intensity: {
      type: Number
    }
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.AmbientLight(options);
        parentComponent.addLight($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!parentInstance.isDestroy) {
            parentInstance.$amapComponent.removeLight($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$3.__file = "src/vue-amap-loca/packages/AmbientLight/AmbientLight.vue";

script$3.install = (app) => {
  app.component(script$3.name, script$3);
  return app;
};
const ElAmapLocaAmbientLight = script$3;

var script$2 = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaDirectionalLight",
    inheritAttrs: false
  },
  __name: "DirectionalLight",
  props: buildProps({
    color: {
      type: String
    },
    // 环境光颜色。
    intensity: {
      type: Number
    },
    // 环境光强度。
    position: {
      type: Array,
      required: true
    },
    // 坐标位置
    target: {
      type: Array
    }
    // 光射向的目标位置
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.DirectionalLight(options);
        parentComponent.addLight($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!parentInstance.isDestroy) {
            parentInstance.$amapComponent.removeLight($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$2.__file = "src/vue-amap-loca/packages/DirectionalLight/DirectionalLight.vue";

script$2.install = (app) => {
  app.component(script$2.name, script$2);
  return app;
};
const ElAmapLocaDirectionalLight = script$2;

var script$1 = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaPointLight",
    inheritAttrs: false
  },
  __name: "PointLight",
  props: buildProps({
    color: {
      type: String
    },
    // 点光颜色。
    intensity: {
      type: Number
    },
    // 光照强度。
    position: {
      type: Array,
      required: true
    },
    // 点光位置
    distance: {
      type: Number
    }
    // 距离表示从光源到光照强度为 0 的位置，0 就是光不会消失
  }),
  emits: ["init"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const emits = __emit;
    let $amapComponent;
    const { $$getInstance, parentInstance } = useRegister((options, parentComponent) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.PointLight(options);
        parentComponent.addLight($amapComponent);
        resolve($amapComponent);
      });
    }, {
      emits,
      destroyComponent() {
        if ($amapComponent && (parentInstance == null ? void 0 : parentInstance.$amapComponent)) {
          if (!parentInstance.isDestroy) {
            parentInstance.$amapComponent.removeLight($amapComponent);
          }
          $amapComponent = null;
        }
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script$1.__file = "src/vue-amap-loca/packages/PointLight/PointLight.vue";

script$1.install = (app) => {
  app.component(script$1.name, script$1);
  return app;
};
const ElAmapLocaPointLight = script$1;

var script = /* @__PURE__ */ defineComponent({
  ...{
    name: "ElAmapLocaLaser",
    inheritAttrs: false
  },
  __name: "LaserLayer",
  props: buildLocaProps({
    depth: {
      type: Boolean,
      default: true
    }
    // 图层中的要素是否具有前后遮盖关系，默认开启
  }),
  emits: commonEmitNames,
  setup(__props, { expose: __expose, emit: __emit }) {
    const props = __props;
    const emits = __emit;
    let $amapComponent;
    let _destroyComponent;
    let _setSource;
    const { $$getInstance, parentInstance } = useRegister((options) => {
      return new Promise((resolve) => {
        $amapComponent = new Loca.LaserLayer(options);
        const useResult = useLocaEvents({
          parentInstance,
          $amapComponent,
          emits,
          props,
          setStyle() {
            const defaultStyleValue = {
              unit: "px",
              height: 200,
              color: "rgba(255,255,0,0.5)",
              angle: 0,
              lineWidth: 2,
              trailLength: 30,
              duration: 2e3,
              interval: 0,
              delay: 0,
              repeat: void 0
            };
            const style = Object.assign({}, defaultStyleValue, props.defaultStyleValue);
            const defaultLayerStyle = {
              unit: style.unit,
              height: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.height === void 0 ? style.height : feature.properties.height;
              },
              color: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.color === void 0 ? style.color : feature.properties.color;
              },
              angle: style.angle,
              lineWidth: style.lineWidth,
              trailLength: style.trailLength,
              duration: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.duration === void 0 ? style.duration : feature.properties.duration;
              },
              interval: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.interval === void 0 ? style.interval : feature.properties.interval;
              },
              delay: (index, feature) => {
                feature.properties = feature.properties || {};
                return feature.properties.delay === void 0 ? style.delay : feature.properties.delay;
              },
              repeat: style.repeat
            };
            const layerStyle = Object.assign({}, defaultLayerStyle, props.layerStyle);
            $amapComponent.setStyle(layerStyle);
          }
        });
        _destroyComponent = useResult._destroyComponent;
        _setSource = useResult.setSource;
        resolve($amapComponent);
      });
    }, {
      emits,
      watchRedirectFn: useWatchFn({
        setSource() {
          if (_setSource) {
            _setSource();
          }
        },
        $amapComponent: () => $amapComponent,
        props
      }),
      destroyComponent() {
        if (_destroyComponent) {
          _destroyComponent();
        }
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return openBlock(), createElementBlock("div");
    };
  }
});

script.__file = "src/vue-amap-loca/packages/LaserLayer/LaserLayer.vue";

script.install = (app) => {
  app.component(script.name, script);
  return app;
};
const ElAmapLocaLaser = script;

var Components = [
  ElAmapLocaGrid,
  ElAmapLocaHeatmap,
  ElAmapLocaHexagon,
  ElAmapLocaIcon,
  ElAmapLocaLine,
  ElAmapLocaLink,
  ElAmapLoca,
  ElAmapLocaPoint,
  ElAmapLocaPolygon,
  ElAmapLocaPrism,
  ElAmapLocaPulseLine,
  ElAmapLocaPulseLink,
  ElAmapLocaScatter,
  ElAmapLocaZMarker,
  ElAmapLocaAmbientLight,
  ElAmapLocaDirectionalLight,
  ElAmapLocaPointLight,
  ElAmapLocaLaser
];

var installer = makeInstaller([...Components]);

const install = installer.install;

export { ElAmapLoca, ElAmapLocaAmbientLight, ElAmapLocaDirectionalLight, ElAmapLocaGrid, ElAmapLocaHeatmap, ElAmapLocaHexagon, ElAmapLocaIcon, ElAmapLocaLaser, ElAmapLocaLine, ElAmapLocaLink, ElAmapLocaPoint, ElAmapLocaPointLight, ElAmapLocaPolygon, ElAmapLocaPrism, ElAmapLocaPulseLine, ElAmapLocaPulseLink, ElAmapLocaScatter, ElAmapLocaZMarker, installer as default, install };
