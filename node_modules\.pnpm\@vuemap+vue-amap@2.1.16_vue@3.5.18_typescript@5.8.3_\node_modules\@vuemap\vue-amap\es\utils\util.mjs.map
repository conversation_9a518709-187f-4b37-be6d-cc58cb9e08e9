{"version": 3, "file": "util.mjs", "sources": ["../../../utils/util.js"], "sourcesContent": ["/**\r\n * 判断对象是不是map实例对象\r\n * @param instance\r\n * @returns {string|boolean}\r\n */\r\nexport function isMapInstance(instance) {\r\n  if (!instance) {\r\n    return false;\r\n  }\r\n  return instance instanceof AMap.Map;\r\n}\r\n\r\n/**\r\n * 判断对象是不是OverlayGroup实例对象\r\n * @param instance\r\n * @returns {string|boolean}\r\n */\r\nexport function isOverlayGroupInstance(instance) {\r\n  if (!instance) {\r\n    return false;\r\n  }\r\n  return instance instanceof AMap.OverlayGroup;\r\n}\r\n\r\n/**\r\n * 判断对象是不是IndoorMap实例对象\r\n * @param instance\r\n * @returns {string|boolean}\r\n */\r\nexport function isIndoorMapInstance(instance) {\r\n  if (!instance) {\r\n    return false;\r\n  }\r\n  return instance instanceof AMap.IndoorMap;\r\n}\r\n\r\n/**\r\n * 判断对象是不是LabelsLayer实例对象\r\n * @param instance\r\n * @returns {string|boolean}\r\n */\r\nexport function isLabelsLayerInstance(instance) {\r\n  if (!instance) {\r\n    return false;\r\n  }\r\n  return instance instanceof AMap.LabelsLayer;\r\n}\r\n\r\n/**\r\n * 判断对象是不是VectorLayer实例对象\r\n * @param instance\r\n * @returns {string|boolean}\r\n */\r\nexport function isVectorLayerInstance(instance) {\r\n  if (!instance) {\r\n    return false;\r\n  }\r\n  return instance instanceof AMap.VectorLayer;\r\n}\r\n\r\n/**\r\n * 将$props中的事件名称转换为地图组件需要的事件名\r\n * @param functionName\r\n * @returns {string|*}\r\n */\r\nexport function convertEventToLowerCase(functionName){\r\n  if(!functionName || functionName.length < 4){\r\n    return functionName;\r\n  }\r\n  const func = functionName.substring(3, functionName.length);\r\n  const firstLetter = functionName[2].toLowerCase();\r\n  return firstLetter + func;\r\n}\r\n\r\nexport const eventReg = /^on[A-Z]+/;\r\n\r\n/**\r\n * 加载JS文件\r\n * @param url\r\n * @param callback\r\n */\r\nexport function loadScript(url, callback){\r\n  if(!url){\r\n    throw new Error('请传入url');\r\n  }\r\n  const script = document.createElement('script');\r\n  script.type = 'text/javascript';\r\n  script.async = true;\r\n  script.defer = true;\r\n  script.src = url;\r\n  document.body.appendChild(script);\r\n  if(callback){\r\n    script.addEventListener('load',() => {\r\n      callback();\r\n    });\r\n  }\r\n}\r\n\r\nexport function convertLnglat(lnglat){\r\n  if(Array.isArray(lnglat)){\r\n    return lnglat.map(convertLnglat);\r\n  }\r\n  return lnglat.toArray();\r\n}\r\n\r\n/**\r\n * 将字符串的第一个字符调整为大写\r\n * @param prop\r\n */\r\nexport function upperCamelCase(prop){\r\n  if(!prop){\r\n    return prop;\r\n  }\r\n  return prop.charAt(0).toUpperCase() + prop.slice(1);\r\n}\r\n"], "names": [], "mappings": "AAKO,SAAS,cAAc,QAAU,EAAA;AACtC,EAAA,IAAI,CAAC,QAAU,EAAA;AACb,IAAO,OAAA,KAAA,CAAA;AAAA,GACT;AACA,EAAA,OAAO,oBAAoB,IAAK,CAAA,GAAA,CAAA;AAClC,CAAA;AAOO,SAAS,uBAAuB,QAAU,EAAA;AAC/C,EAAA,IAAI,CAAC,QAAU,EAAA;AACb,IAAO,OAAA,KAAA,CAAA;AAAA,GACT;AACA,EAAA,OAAO,oBAAoB,IAAK,CAAA,YAAA,CAAA;AAClC,CAAA;AAOO,SAAS,oBAAoB,QAAU,EAAA;AAC5C,EAAA,IAAI,CAAC,QAAU,EAAA;AACb,IAAO,OAAA,KAAA,CAAA;AAAA,GACT;AACA,EAAA,OAAO,oBAAoB,IAAK,CAAA,SAAA,CAAA;AAClC,CAAA;AAOO,SAAS,sBAAsB,QAAU,EAAA;AAC9C,EAAA,IAAI,CAAC,QAAU,EAAA;AACb,IAAO,OAAA,KAAA,CAAA;AAAA,GACT;AACA,EAAA,OAAO,oBAAoB,IAAK,CAAA,WAAA,CAAA;AAClC,CAAA;AAOO,SAAS,sBAAsB,QAAU,EAAA;AAC9C,EAAA,IAAI,CAAC,QAAU,EAAA;AACb,IAAO,OAAA,KAAA,CAAA;AAAA,GACT;AACA,EAAA,OAAO,oBAAoB,IAAK,CAAA,WAAA,CAAA;AAClC,CAAA;AAOO,SAAS,wBAAwB,YAAa,EAAA;AACnD,EAAA,IAAG,CAAC,YAAA,IAAgB,YAAa,CAAA,MAAA,GAAS,CAAE,EAAA;AAC1C,IAAO,OAAA,YAAA,CAAA;AAAA,GACT;AACA,EAAA,MAAM,IAAO,GAAA,YAAA,CAAa,SAAU,CAAA,CAAA,EAAG,aAAa,MAAM,CAAA,CAAA;AAC1D,EAAA,MAAM,WAAc,GAAA,YAAA,CAAa,CAAC,CAAA,CAAE,WAAY,EAAA,CAAA;AAChD,EAAA,OAAO,WAAc,GAAA,IAAA,CAAA;AACvB,CAAA;AAEO,MAAM,QAAW,GAAA,YAAA;AAOR,SAAA,UAAA,CAAW,KAAK,QAAS,EAAA;AACvC,EAAA,IAAG,CAAC,GAAI,EAAA;AACN,IAAM,MAAA,IAAI,MAAM,uBAAQ,CAAA,CAAA;AAAA,GAC1B;AACA,EAAM,MAAA,MAAA,GAAS,QAAS,CAAA,aAAA,CAAc,QAAQ,CAAA,CAAA;AAC9C,EAAA,MAAA,CAAO,IAAO,GAAA,iBAAA,CAAA;AACd,EAAA,MAAA,CAAO,KAAQ,GAAA,IAAA,CAAA;AACf,EAAA,MAAA,CAAO,KAAQ,GAAA,IAAA,CAAA;AACf,EAAA,MAAA,CAAO,GAAM,GAAA,GAAA,CAAA;AACb,EAAS,QAAA,CAAA,IAAA,CAAK,YAAY,MAAM,CAAA,CAAA;AAChC,EAAA,IAAG,QAAS,EAAA;AACV,IAAO,MAAA,CAAA,gBAAA,CAAiB,QAAO,MAAM;AACnC,MAAS,QAAA,EAAA,CAAA;AAAA,KACV,CAAA,CAAA;AAAA,GACH;AACF,CAAA;AAEO,SAAS,cAAc,MAAO,EAAA;AACnC,EAAG,IAAA,KAAA,CAAM,OAAQ,CAAA,MAAM,CAAE,EAAA;AACvB,IAAO,OAAA,MAAA,CAAO,IAAI,aAAa,CAAA,CAAA;AAAA,GACjC;AACA,EAAA,OAAO,OAAO,OAAQ,EAAA,CAAA;AACxB,CAAA;AAMO,SAAS,eAAe,IAAK,EAAA;AAClC,EAAA,IAAG,CAAC,IAAK,EAAA;AACP,IAAO,OAAA,IAAA,CAAA;AAAA,GACT;AACA,EAAO,OAAA,IAAA,CAAK,OAAO,CAAC,CAAA,CAAE,aAAgB,GAAA,IAAA,CAAK,MAAM,CAAC,CAAA,CAAA;AACpD;;;;"}