{"version": 3, "file": "index.mjs", "sources": ["../../index.ts"], "sourcesContent": ["import installer from './defaults';\r\nexport * from './services';\r\nexport * from './packages';\r\nexport * from './mixins';\r\nexport { default } from './defaults';\r\nexport * from './hooks';\r\nexport * from './utils';\r\nexport const install = installer.install;\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOO,MAAM,UAAU,SAAU,CAAA;;;;"}