import type { PromiseConstructor } from "./es6-promise";
/**
 * @constructor
 * @param {Function} fn
 */
export declare function PromisePolyfill(fn: any): void;
export declare namespace PromisePolyfill {
    var all: (arr: any) => any;
    var allSettled: (arr: any) => any;
    var resolve: (value: any) => any;
    var reject: (value: any) => any;
    var race: (arr: any) => any;
    var _immediateFn: (fn: any) => void;
    var _unhandledRejectionFn: (err: any) => void;
}
export declare var PromisePolyfillConstructor: PromiseConstructor;
