function toPixel(arr) {
  return new AMap.Pixel(arr[0], arr[1]);
}
function toSize(arr) {
  return new AMap.Size(arr[0], arr[1]);
}
function pixelTo(pixel) {
  if (Array.isArray(pixel))
    return pixel;
  return [pixel.getX(), pixel.getY()];
}
function toLngLat(arr) {
  return new AMap.LngLat(arr[0], arr[1]);
}
function lngLatTo(lngLat) {
  if (!lngLat)
    return;
  if (Array.isArray(lngLat))
    return lngLat.slice();
  return [lngLat.getLng(), lngLat.getLat()];
}
function toBounds(arrs) {
  return new AMap.Bounds(toLngLat(arrs[0]), toLngLat(arrs[1]));
}

export { lngLatTo, pixelTo, toBounds, toLngLat, toPixel, toSize };
//# sourceMappingURL=convert-helper.mjs.map
