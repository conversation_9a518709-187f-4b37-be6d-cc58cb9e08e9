{"version": 3, "file": "ControlBar.vue2.js", "sources": ["../../../../../packages/control/ControlBar/ControlBar.vue"], "sourcesContent": ["<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"../../../mixins\";\r\nimport {buildProps} from \"../../../utils/buildHelper\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapControlControlBar',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(buildProps({\r\n  // 控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角\r\n  position: {\r\n    type: [String, Object]\r\n  },\r\n  // 相对于地图容器左上角的偏移量，正数代表向右下偏移。默认为AMap.Pixel(10,10)\r\n  offset: {\r\n    type: Array\r\n  },\r\n  // 是否显示倾斜、旋转按钮。默认为 true\r\n  showControlButton: {\r\n    type: Boolean,\r\n    default: true\r\n  }\r\n}));\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: AMap.ControlBar;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<AMap.ControlBar, AMap.Map>((options, parentComponent) => {\r\n  return new Promise<AMap.ControlBar>((resolve) => {\r\n    parentComponent.plugin(['AMap.ControlBar'], () => {\r\n      $amapComponent = new AMap.ControlBar(options);\r\n      parentComponent.addControl($amapComponent);\r\n      resolve($amapComponent);\r\n    });\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance?.isDestroy){\r\n        parentInstance?.$amapComponent.removeControl($amapComponent);\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance\r\n});\r\n\r\n</script>\r\n"], "names": ["useRegister"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAI,IAAA,cAAA,CAAA;AAEJ,IAAA,MAAM,EAAC,aAAe,EAAA,cAAA,KAAkBA,uBAAuC,CAAA,CAAC,SAAS,eAAoB,KAAA;AAC3G,MAAO,OAAA,IAAI,OAAyB,CAAA,CAAC,OAAY,KAAA;AAC/C,QAAA,eAAA,CAAgB,MAAO,CAAA,CAAC,iBAAiB,CAAA,EAAG,MAAM;AAChD,UAAiB,cAAA,GAAA,IAAI,IAAK,CAAA,UAAA,CAAW,OAAO,CAAA,CAAA;AAC5C,UAAA,eAAA,CAAgB,WAAW,cAAc,CAAA,CAAA;AACzC,UAAA,OAAA,CAAQ,cAAc,CAAA,CAAA;AAAA,SACvB,CAAA,CAAA;AAAA,OACF,CAAA,CAAA;AAAA,KAEA,EAAA;AAAA,MACD,KAAA;AAAA,MACA,gBAAoB,GAAA;AAClB,QAAI,IAAA,cAAA,KAAkB,iDAAgB,cAAgB,CAAA,EAAA;AACpD,UAAG,IAAA,EAAC,iDAAgB,SAAU,CAAA,EAAA;AAC5B,YAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAgB,eAAe,aAAc,CAAA,cAAA,CAAA,CAAA;AAAA,WAC/C;AACA,UAAiB,cAAA,GAAA,IAAA,CAAA;AAAA,SACnB;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AAED,IAAa,QAAA,CAAA;AAAA,MACX,aAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;"}