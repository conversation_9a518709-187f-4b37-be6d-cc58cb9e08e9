{"version": 3, "file": "buildHelper.mjs", "sources": ["../../../utils/buildHelper.ts"], "sourcesContent": ["import type {ComponentObjectPropsOptions, PropType} from \"vue\";\r\n\r\nexport type IPropData = Record<string, unknown>\r\nexport interface IPropOptions<T = any> {\r\n  type?: PropType<T> | true | null;\r\n  required?: boolean;\r\n  default?: any;\r\n  validator?(value: unknown, props: IPropData): boolean;\r\n}\r\nexport interface ICommonProps{\r\n  // 是否显隐\r\n  visible: IPropOptions<boolean>\r\n  // 层级\r\n  zIndex: IPropOptions<number>\r\n  // 是否在更新组件后重新注册事件\r\n  reEventWhenUpdate: IPropOptions<boolean>\r\n  // 额外参数，用于在初始化组件时提供prop中未定义的属性\r\n  extraOptions: IPropOptions,\r\n}\r\n\r\nexport const commonProps: ICommonProps = {\r\n  visible: {\r\n    type: Boolean,\r\n    default: true\r\n  },\r\n  zIndex: {\r\n    type: Number,\r\n  },\r\n  reEventWhenUpdate: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  extraOptions: {\r\n    type: Object\r\n  },\r\n};\r\n\r\n/**\r\n * 合并生成基础的属性\r\n * @param props\r\n */\r\nexport const buildProps = <Props extends ComponentObjectPropsOptions >(props: Props): Props & {\r\n  [K  in keyof ICommonProps]: ICommonProps[K]\r\n} => {\r\n  return Object.assign({}, commonProps, props);\r\n};\r\n"], "names": [], "mappings": "AAoBO,MAAM,WAA4B,GAAA;AAAA,EACvC,OAAS,EAAA;AAAA,IACP,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA,EACA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,MAAA;AAAA,GACR;AAAA,EACA,iBAAmB,EAAA;AAAA,IACjB,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA,EACA,YAAc,EAAA;AAAA,IACZ,IAAM,EAAA,MAAA;AAAA,GACR;AACF,EAAA;AAMa,MAAA,UAAA,GAAa,CAA6C,KAElE,KAAA;AACH,EAAA,OAAO,MAAO,CAAA,MAAA,CAAO,EAAC,EAAG,aAAa,KAAK,CAAA,CAAA;AAC7C;;;;"}