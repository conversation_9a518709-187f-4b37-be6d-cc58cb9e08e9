{"version": 3, "file": "useWeather.js", "sources": ["../../../hooks/useWeather.ts"], "sourcesContent": ["/**\r\n * 天气插件hook返回值\r\n */\r\nexport interface WeatherPromise {\r\n    /**\r\n     * 通过城市名称、区域编码（如杭州市、330100），查询目标城市/区域的实时天气状况\r\n     * @param {string} city 城市名称或者区域编码（如：“杭州市”/“330100”）\r\n     * @return {Promise<WeatherLiveResult>}\r\n     */\r\n    getLive: (city: string) => Promise<AMap.WeatherLiveResult>;\r\n    /**\r\n     * 通过城市名称、区域编码（如北京市、110000），查询目标城市/区域的天气预报状况。\r\n     * @param {string} city 城市名称或者区域编码（如：“杭州市”/“330100”）\r\n     * @return {Promise<WeatherForecastResult>}\r\n     */\r\n    getForecast: (city: string) => Promise<AMap.WeatherForecastResult>;\r\n}\r\n\r\n\r\n\r\n/**\r\n * 天气插件hook，返回天气插件里的方法，全部Promise处理\r\n */\r\nexport function useWeather () {\r\n    return new Promise<WeatherPromise>((resolve) => {\r\n        AMap.plugin('AMap.Weather', function () {\r\n            const weather = new AMap.Weather();\r\n            /**\r\n             * 查询实时天气信息。\r\n             * \r\n             * @param  {string} city 城市名称或者区域编码（如：“杭州市”/“330100”）\r\n             */\r\n            const getLive = (city: string) => {\r\n                return new Promise<AMap.WeatherLiveResult>((resolve, reject) => {\r\n                    weather.getLive(city, function (err, result) {\r\n                        if (!err && result.info === 'OK') {\r\n                            resolve(result);\r\n                        } else {\r\n                            reject({\r\n                                result\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n            };\r\n            /**\r\n             * 查询四天预报天气，包括查询当天天气信息\r\n             * @param  {string} city 城市名称或者区域编码（如：“杭州市”/“330100”）\r\n             */\r\n            const getForecast = (city: string) => {\r\n                return new Promise<AMap.WeatherForecastResult>((resolve, reject) => {\r\n                    weather.getForecast(city, function (err, result) {\r\n                        if (!err && result.info === 'OK') {\r\n                            resolve(result);\r\n                        } else {\r\n                            reject({\r\n                                result\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n            };\r\n            resolve({\r\n                getLive,\r\n                getForecast\r\n            });\r\n        });\r\n    });\r\n}"], "names": ["resolve"], "mappings": ";;AAuBO,SAAS,UAAc,GAAA;AAC1B,EAAO,OAAA,IAAI,OAAwB,CAAA,CAAC,OAAY,KAAA;AAC5C,IAAK,IAAA,CAAA,MAAA,CAAO,gBAAgB,WAAY;AACpC,MAAM,MAAA,OAAA,GAAU,IAAI,IAAA,CAAK,OAAQ,EAAA,CAAA;AAMjC,MAAM,MAAA,OAAA,GAAU,CAAC,IAAiB,KAAA;AAC9B,QAAA,OAAO,IAAI,OAAA,CAAgC,CAACA,QAAAA,EAAS,MAAW,KAAA;AAC5D,UAAA,OAAA,CAAQ,OAAQ,CAAA,IAAA,EAAM,SAAU,GAAA,EAAK,MAAQ,EAAA;AACzC,YAAA,IAAI,CAAC,GAAA,IAAO,MAAO,CAAA,IAAA,KAAS,IAAM,EAAA;AAC9B,cAAAA,SAAQ,MAAM,CAAA,CAAA;AAAA,aACX,MAAA;AACH,cAAO,MAAA,CAAA;AAAA,gBACH,MAAA;AAAA,eACH,CAAA,CAAA;AAAA,aACL;AAAA,WACH,CAAA,CAAA;AAAA,SACJ,CAAA,CAAA;AAAA,OACL,CAAA;AAKA,MAAM,MAAA,WAAA,GAAc,CAAC,IAAiB,KAAA;AAClC,QAAA,OAAO,IAAI,OAAA,CAAoC,CAACA,QAAAA,EAAS,MAAW,KAAA;AAChE,UAAA,OAAA,CAAQ,WAAY,CAAA,IAAA,EAAM,SAAU,GAAA,EAAK,MAAQ,EAAA;AAC7C,YAAA,IAAI,CAAC,GAAA,IAAO,MAAO,CAAA,IAAA,KAAS,IAAM,EAAA;AAC9B,cAAAA,SAAQ,MAAM,CAAA,CAAA;AAAA,aACX,MAAA;AACH,cAAO,MAAA,CAAA;AAAA,gBACH,MAAA;AAAA,eACH,CAAA,CAAA;AAAA,aACL;AAAA,WACH,CAAA,CAAA;AAAA,SACJ,CAAA,CAAA;AAAA,OACL,CAAA;AACA,MAAQ,OAAA,CAAA;AAAA,QACJ,OAAA;AAAA,QACA,WAAA;AAAA,OACH,CAAA,CAAA;AAAA,KACJ,CAAA,CAAA;AAAA,GACJ,CAAA,CAAA;AACL;;;;"}