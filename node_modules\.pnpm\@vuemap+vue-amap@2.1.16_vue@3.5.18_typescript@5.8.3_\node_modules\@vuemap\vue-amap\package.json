{"name": "@vuemap/vue-amap", "version": "2.1.16", "description": "高德地图vue3版本封装", "keywords": ["vue-amap", "amap", "map", "vue3", "vue", "高德"], "exports": {".": {"types": "./es/index.d.ts", "require": "./lib/index.js", "import": "./es/index.mjs"}, "./es": "./es/index.mjs", "./lib": "./lib/index.js", "./*": "./*"}, "main": "lib/index.js", "module": "es/index.mjs", "style": "dist/style.css", "unpkg": "dist/index.js", "jsdelivr": "dist/index.js", "types": "es/index.d.ts", "sideEffects": ["dist/*", "lib/packages/**/*", "es/packages/**/*"], "repository": "https://github.com/yangyanggu/vue-amap.git", "author": "guyangyang <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://gitee.com/guyangyang/vue-amap/issues"}, "homepage": "https://vue-amap.guyixi.cn", "dependencies": {"@vuemap/amap-jsapi-types": "^0.0.17", "@vuemap/amap-jsapi-loader": "1.0.4", "@vuemap/district-cluster": "0.0.12", "@vuemap/amap-xyz-layer": "0.0.15", "lodash-es": "^4.17.21"}, "devDependencies": {}, "peerDependencies": {"vue": "3"}, "vetur": {"tags": "./tags.json", "attributes": "./attributes.json"}, "web-types": "./web-types.json"}