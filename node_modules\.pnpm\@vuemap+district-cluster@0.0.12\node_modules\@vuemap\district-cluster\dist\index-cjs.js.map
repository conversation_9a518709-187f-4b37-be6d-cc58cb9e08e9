{"version": 3, "file": "index-cjs.js", "sources": ["../src/packages/event/index.ts", "../node_modules/.pnpm/@turf+helpers@6.5.0/node_modules/@turf/helpers/dist/es/index.js", "../node_modules/.pnpm/@turf+invariant@6.5.0/node_modules/@turf/invariant/dist/es/index.js", "../node_modules/.pnpm/splaytree@3.1.1/node_modules/splaytree/dist/splay.esm.js", "../node_modules/.pnpm/polygon-clipping@0.15.3/node_modules/polygon-clipping/dist/polygon-clipping.esm.js", "../node_modules/.pnpm/@turf+intersect@6.5.0/node_modules/@turf/intersect/dist/es/index.js", "../src/packages/layer/utils.js", "../src/packages/layer/Const.ts", "../node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/identity.js", "../node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/transform.js", "../node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/reverse.js", "../node_modules/.pnpm/topojson-client@3.1.0/node_modules/topojson-client/src/feature.js", "../src/packages/layer/geomUtils.ts", "../src/packages/layer/bbIdxBuilder.ts", "../src/packages/layer/BoundsItem.ts", "../src/packages/layer/distDataParser.ts", "../src/packages/layer/SphericalMercator.ts", "../src/packages/layer/AreaNode.ts", "../src/packages/layer/DistrictExplorer.ts", "../src/packages/layer/DistMgr.ts", "../src/packages/layer/DistCounter.ts", "../src/packages/layer/BaseRender.ts", "../src/packages/layer/PointItem.ts", "../src/packages/layer/index.ts"], "sourcesContent": [null, "/**\n * @module helpers\n */\n/**\n * Earth Radius used with the Harvesine formula and approximates using a spherical (non-ellipsoid) Earth.\n *\n * @memberof helpers\n * @type {number}\n */\nexport var earthRadius = 6371008.8;\n/**\n * Unit of measurement factors using a spherical (non-ellipsoid) earth radius.\n *\n * @memberof helpers\n * @type {Object}\n */\nexport var factors = {\n    centimeters: earthRadius * 100,\n    centimetres: earthRadius * 100,\n    degrees: earthRadius / 111325,\n    feet: earthRadius * 3.28084,\n    inches: earthRadius * 39.37,\n    kilometers: earthRadius / 1000,\n    kilometres: earthRadius / 1000,\n    meters: earthRadius,\n    metres: earthRadius,\n    miles: earthRadius / 1609.344,\n    millimeters: earthRadius * 1000,\n    millimetres: earthRadius * 1000,\n    nauticalmiles: earthRadius / 1852,\n    radians: 1,\n    yards: earthRadius * 1.0936,\n};\n/**\n * Units of measurement factors based on 1 meter.\n *\n * @memberof helpers\n * @type {Object}\n */\nexport var unitsFactors = {\n    centimeters: 100,\n    centimetres: 100,\n    degrees: 1 / 111325,\n    feet: 3.28084,\n    inches: 39.37,\n    kilometers: 1 / 1000,\n    kilometres: 1 / 1000,\n    meters: 1,\n    metres: 1,\n    miles: 1 / 1609.344,\n    millimeters: 1000,\n    millimetres: 1000,\n    nauticalmiles: 1 / 1852,\n    radians: 1 / earthRadius,\n    yards: 1.0936133,\n};\n/**\n * Area of measurement factors based on 1 square meter.\n *\n * @memberof helpers\n * @type {Object}\n */\nexport var areaFactors = {\n    acres: 0.000247105,\n    centimeters: 10000,\n    centimetres: 10000,\n    feet: 10.763910417,\n    hectares: 0.0001,\n    inches: 1550.003100006,\n    kilometers: 0.000001,\n    kilometres: 0.000001,\n    meters: 1,\n    metres: 1,\n    miles: 3.86e-7,\n    millimeters: 1000000,\n    millimetres: 1000000,\n    yards: 1.195990046,\n};\n/**\n * Wraps a GeoJSON {@link Geometry} in a GeoJSON {@link Feature}.\n *\n * @name feature\n * @param {Geometry} geometry input geometry\n * @param {Object} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {string|number} [options.id] Identifier associated with the Feature\n * @returns {Feature} a GeoJSON Feature\n * @example\n * var geometry = {\n *   \"type\": \"Point\",\n *   \"coordinates\": [110, 50]\n * };\n *\n * var feature = turf.feature(geometry);\n *\n * //=feature\n */\nexport function feature(geom, properties, options) {\n    if (options === void 0) { options = {}; }\n    var feat = { type: \"Feature\" };\n    if (options.id === 0 || options.id) {\n        feat.id = options.id;\n    }\n    if (options.bbox) {\n        feat.bbox = options.bbox;\n    }\n    feat.properties = properties || {};\n    feat.geometry = geom;\n    return feat;\n}\n/**\n * Creates a GeoJSON {@link Geometry} from a Geometry string type & coordinates.\n * For GeometryCollection type use `helpers.geometryCollection`\n *\n * @name geometry\n * @param {string} type Geometry Type\n * @param {Array<any>} coordinates Coordinates\n * @param {Object} [options={}] Optional Parameters\n * @returns {Geometry} a GeoJSON Geometry\n * @example\n * var type = \"Point\";\n * var coordinates = [110, 50];\n * var geometry = turf.geometry(type, coordinates);\n * // => geometry\n */\nexport function geometry(type, coordinates, _options) {\n    if (_options === void 0) { _options = {}; }\n    switch (type) {\n        case \"Point\":\n            return point(coordinates).geometry;\n        case \"LineString\":\n            return lineString(coordinates).geometry;\n        case \"Polygon\":\n            return polygon(coordinates).geometry;\n        case \"MultiPoint\":\n            return multiPoint(coordinates).geometry;\n        case \"MultiLineString\":\n            return multiLineString(coordinates).geometry;\n        case \"MultiPolygon\":\n            return multiPolygon(coordinates).geometry;\n        default:\n            throw new Error(type + \" is invalid\");\n    }\n}\n/**\n * Creates a {@link Point} {@link Feature} from a Position.\n *\n * @name point\n * @param {Array<number>} coordinates longitude, latitude position (each in decimal degrees)\n * @param {Object} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {string|number} [options.id] Identifier associated with the Feature\n * @returns {Feature<Point>} a Point feature\n * @example\n * var point = turf.point([-75.343, 39.984]);\n *\n * //=point\n */\nexport function point(coordinates, properties, options) {\n    if (options === void 0) { options = {}; }\n    if (!coordinates) {\n        throw new Error(\"coordinates is required\");\n    }\n    if (!Array.isArray(coordinates)) {\n        throw new Error(\"coordinates must be an Array\");\n    }\n    if (coordinates.length < 2) {\n        throw new Error(\"coordinates must be at least 2 numbers long\");\n    }\n    if (!isNumber(coordinates[0]) || !isNumber(coordinates[1])) {\n        throw new Error(\"coordinates must contain numbers\");\n    }\n    var geom = {\n        type: \"Point\",\n        coordinates: coordinates,\n    };\n    return feature(geom, properties, options);\n}\n/**\n * Creates a {@link Point} {@link FeatureCollection} from an Array of Point coordinates.\n *\n * @name points\n * @param {Array<Array<number>>} coordinates an array of Points\n * @param {Object} [properties={}] Translate these properties to each Feature\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north]\n * associated with the FeatureCollection\n * @param {string|number} [options.id] Identifier associated with the FeatureCollection\n * @returns {FeatureCollection<Point>} Point Feature\n * @example\n * var points = turf.points([\n *   [-75, 39],\n *   [-80, 45],\n *   [-78, 50]\n * ]);\n *\n * //=points\n */\nexport function points(coordinates, properties, options) {\n    if (options === void 0) { options = {}; }\n    return featureCollection(coordinates.map(function (coords) {\n        return point(coords, properties);\n    }), options);\n}\n/**\n * Creates a {@link Polygon} {@link Feature} from an Array of LinearRings.\n *\n * @name polygon\n * @param {Array<Array<Array<number>>>} coordinates an array of LinearRings\n * @param {Object} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {string|number} [options.id] Identifier associated with the Feature\n * @returns {Feature<Polygon>} Polygon Feature\n * @example\n * var polygon = turf.polygon([[[-5, 52], [-4, 56], [-2, 51], [-7, 54], [-5, 52]]], { name: 'poly1' });\n *\n * //=polygon\n */\nexport function polygon(coordinates, properties, options) {\n    if (options === void 0) { options = {}; }\n    for (var _i = 0, coordinates_1 = coordinates; _i < coordinates_1.length; _i++) {\n        var ring = coordinates_1[_i];\n        if (ring.length < 4) {\n            throw new Error(\"Each LinearRing of a Polygon must have 4 or more Positions.\");\n        }\n        for (var j = 0; j < ring[ring.length - 1].length; j++) {\n            // Check if first point of Polygon contains two numbers\n            if (ring[ring.length - 1][j] !== ring[0][j]) {\n                throw new Error(\"First and last Position are not equivalent.\");\n            }\n        }\n    }\n    var geom = {\n        type: \"Polygon\",\n        coordinates: coordinates,\n    };\n    return feature(geom, properties, options);\n}\n/**\n * Creates a {@link Polygon} {@link FeatureCollection} from an Array of Polygon coordinates.\n *\n * @name polygons\n * @param {Array<Array<Array<Array<number>>>>} coordinates an array of Polygon coordinates\n * @param {Object} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {string|number} [options.id] Identifier associated with the FeatureCollection\n * @returns {FeatureCollection<Polygon>} Polygon FeatureCollection\n * @example\n * var polygons = turf.polygons([\n *   [[[-5, 52], [-4, 56], [-2, 51], [-7, 54], [-5, 52]]],\n *   [[[-15, 42], [-14, 46], [-12, 41], [-17, 44], [-15, 42]]],\n * ]);\n *\n * //=polygons\n */\nexport function polygons(coordinates, properties, options) {\n    if (options === void 0) { options = {}; }\n    return featureCollection(coordinates.map(function (coords) {\n        return polygon(coords, properties);\n    }), options);\n}\n/**\n * Creates a {@link LineString} {@link Feature} from an Array of Positions.\n *\n * @name lineString\n * @param {Array<Array<number>>} coordinates an array of Positions\n * @param {Object} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {string|number} [options.id] Identifier associated with the Feature\n * @returns {Feature<LineString>} LineString Feature\n * @example\n * var linestring1 = turf.lineString([[-24, 63], [-23, 60], [-25, 65], [-20, 69]], {name: 'line 1'});\n * var linestring2 = turf.lineString([[-14, 43], [-13, 40], [-15, 45], [-10, 49]], {name: 'line 2'});\n *\n * //=linestring1\n * //=linestring2\n */\nexport function lineString(coordinates, properties, options) {\n    if (options === void 0) { options = {}; }\n    if (coordinates.length < 2) {\n        throw new Error(\"coordinates must be an array of two or more positions\");\n    }\n    var geom = {\n        type: \"LineString\",\n        coordinates: coordinates,\n    };\n    return feature(geom, properties, options);\n}\n/**\n * Creates a {@link LineString} {@link FeatureCollection} from an Array of LineString coordinates.\n *\n * @name lineStrings\n * @param {Array<Array<Array<number>>>} coordinates an array of LinearRings\n * @param {Object} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north]\n * associated with the FeatureCollection\n * @param {string|number} [options.id] Identifier associated with the FeatureCollection\n * @returns {FeatureCollection<LineString>} LineString FeatureCollection\n * @example\n * var linestrings = turf.lineStrings([\n *   [[-24, 63], [-23, 60], [-25, 65], [-20, 69]],\n *   [[-14, 43], [-13, 40], [-15, 45], [-10, 49]]\n * ]);\n *\n * //=linestrings\n */\nexport function lineStrings(coordinates, properties, options) {\n    if (options === void 0) { options = {}; }\n    return featureCollection(coordinates.map(function (coords) {\n        return lineString(coords, properties);\n    }), options);\n}\n/**\n * Takes one or more {@link Feature|Features} and creates a {@link FeatureCollection}.\n *\n * @name featureCollection\n * @param {Feature[]} features input features\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {string|number} [options.id] Identifier associated with the Feature\n * @returns {FeatureCollection} FeatureCollection of Features\n * @example\n * var locationA = turf.point([-75.343, 39.984], {name: 'Location A'});\n * var locationB = turf.point([-75.833, 39.284], {name: 'Location B'});\n * var locationC = turf.point([-75.534, 39.123], {name: 'Location C'});\n *\n * var collection = turf.featureCollection([\n *   locationA,\n *   locationB,\n *   locationC\n * ]);\n *\n * //=collection\n */\nexport function featureCollection(features, options) {\n    if (options === void 0) { options = {}; }\n    var fc = { type: \"FeatureCollection\" };\n    if (options.id) {\n        fc.id = options.id;\n    }\n    if (options.bbox) {\n        fc.bbox = options.bbox;\n    }\n    fc.features = features;\n    return fc;\n}\n/**\n * Creates a {@link Feature<MultiLineString>} based on a\n * coordinate array. Properties can be added optionally.\n *\n * @name multiLineString\n * @param {Array<Array<Array<number>>>} coordinates an array of LineStrings\n * @param {Object} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {string|number} [options.id] Identifier associated with the Feature\n * @returns {Feature<MultiLineString>} a MultiLineString feature\n * @throws {Error} if no coordinates are passed\n * @example\n * var multiLine = turf.multiLineString([[[0,0],[10,10]]]);\n *\n * //=multiLine\n */\nexport function multiLineString(coordinates, properties, options) {\n    if (options === void 0) { options = {}; }\n    var geom = {\n        type: \"MultiLineString\",\n        coordinates: coordinates,\n    };\n    return feature(geom, properties, options);\n}\n/**\n * Creates a {@link Feature<MultiPoint>} based on a\n * coordinate array. Properties can be added optionally.\n *\n * @name multiPoint\n * @param {Array<Array<number>>} coordinates an array of Positions\n * @param {Object} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {string|number} [options.id] Identifier associated with the Feature\n * @returns {Feature<MultiPoint>} a MultiPoint feature\n * @throws {Error} if no coordinates are passed\n * @example\n * var multiPt = turf.multiPoint([[0,0],[10,10]]);\n *\n * //=multiPt\n */\nexport function multiPoint(coordinates, properties, options) {\n    if (options === void 0) { options = {}; }\n    var geom = {\n        type: \"MultiPoint\",\n        coordinates: coordinates,\n    };\n    return feature(geom, properties, options);\n}\n/**\n * Creates a {@link Feature<MultiPolygon>} based on a\n * coordinate array. Properties can be added optionally.\n *\n * @name multiPolygon\n * @param {Array<Array<Array<Array<number>>>>} coordinates an array of Polygons\n * @param {Object} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {string|number} [options.id] Identifier associated with the Feature\n * @returns {Feature<MultiPolygon>} a multipolygon feature\n * @throws {Error} if no coordinates are passed\n * @example\n * var multiPoly = turf.multiPolygon([[[[0,0],[0,10],[10,10],[10,0],[0,0]]]]);\n *\n * //=multiPoly\n *\n */\nexport function multiPolygon(coordinates, properties, options) {\n    if (options === void 0) { options = {}; }\n    var geom = {\n        type: \"MultiPolygon\",\n        coordinates: coordinates,\n    };\n    return feature(geom, properties, options);\n}\n/**\n * Creates a {@link Feature<GeometryCollection>} based on a\n * coordinate array. Properties can be added optionally.\n *\n * @name geometryCollection\n * @param {Array<Geometry>} geometries an array of GeoJSON Geometries\n * @param {Object} [properties={}] an Object of key-value pairs to add as properties\n * @param {Object} [options={}] Optional Parameters\n * @param {Array<number>} [options.bbox] Bounding Box Array [west, south, east, north] associated with the Feature\n * @param {string|number} [options.id] Identifier associated with the Feature\n * @returns {Feature<GeometryCollection>} a GeoJSON GeometryCollection Feature\n * @example\n * var pt = turf.geometry(\"Point\", [100, 0]);\n * var line = turf.geometry(\"LineString\", [[101, 0], [102, 1]]);\n * var collection = turf.geometryCollection([pt, line]);\n *\n * // => collection\n */\nexport function geometryCollection(geometries, properties, options) {\n    if (options === void 0) { options = {}; }\n    var geom = {\n        type: \"GeometryCollection\",\n        geometries: geometries,\n    };\n    return feature(geom, properties, options);\n}\n/**\n * Round number to precision\n *\n * @param {number} num Number\n * @param {number} [precision=0] Precision\n * @returns {number} rounded number\n * @example\n * turf.round(120.4321)\n * //=120\n *\n * turf.round(120.4321, 2)\n * //=120.43\n */\nexport function round(num, precision) {\n    if (precision === void 0) { precision = 0; }\n    if (precision && !(precision >= 0)) {\n        throw new Error(\"precision must be a positive number\");\n    }\n    var multiplier = Math.pow(10, precision || 0);\n    return Math.round(num * multiplier) / multiplier;\n}\n/**\n * Convert a distance measurement (assuming a spherical Earth) from radians to a more friendly unit.\n * Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet\n *\n * @name radiansToLength\n * @param {number} radians in radians across the sphere\n * @param {string} [units=\"kilometers\"] can be degrees, radians, miles, inches, yards, metres,\n * meters, kilometres, kilometers.\n * @returns {number} distance\n */\nexport function radiansToLength(radians, units) {\n    if (units === void 0) { units = \"kilometers\"; }\n    var factor = factors[units];\n    if (!factor) {\n        throw new Error(units + \" units is invalid\");\n    }\n    return radians * factor;\n}\n/**\n * Convert a distance measurement (assuming a spherical Earth) from a real-world unit into radians\n * Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet\n *\n * @name lengthToRadians\n * @param {number} distance in real units\n * @param {string} [units=\"kilometers\"] can be degrees, radians, miles, inches, yards, metres,\n * meters, kilometres, kilometers.\n * @returns {number} radians\n */\nexport function lengthToRadians(distance, units) {\n    if (units === void 0) { units = \"kilometers\"; }\n    var factor = factors[units];\n    if (!factor) {\n        throw new Error(units + \" units is invalid\");\n    }\n    return distance / factor;\n}\n/**\n * Convert a distance measurement (assuming a spherical Earth) from a real-world unit into degrees\n * Valid units: miles, nauticalmiles, inches, yards, meters, metres, centimeters, kilometres, feet\n *\n * @name lengthToDegrees\n * @param {number} distance in real units\n * @param {string} [units=\"kilometers\"] can be degrees, radians, miles, inches, yards, metres,\n * meters, kilometres, kilometers.\n * @returns {number} degrees\n */\nexport function lengthToDegrees(distance, units) {\n    return radiansToDegrees(lengthToRadians(distance, units));\n}\n/**\n * Converts any bearing angle from the north line direction (positive clockwise)\n * and returns an angle between 0-360 degrees (positive clockwise), 0 being the north line\n *\n * @name bearingToAzimuth\n * @param {number} bearing angle, between -180 and +180 degrees\n * @returns {number} angle between 0 and 360 degrees\n */\nexport function bearingToAzimuth(bearing) {\n    var angle = bearing % 360;\n    if (angle < 0) {\n        angle += 360;\n    }\n    return angle;\n}\n/**\n * Converts an angle in radians to degrees\n *\n * @name radiansToDegrees\n * @param {number} radians angle in radians\n * @returns {number} degrees between 0 and 360 degrees\n */\nexport function radiansToDegrees(radians) {\n    var degrees = radians % (2 * Math.PI);\n    return (degrees * 180) / Math.PI;\n}\n/**\n * Converts an angle in degrees to radians\n *\n * @name degreesToRadians\n * @param {number} degrees angle between 0 and 360 degrees\n * @returns {number} angle in radians\n */\nexport function degreesToRadians(degrees) {\n    var radians = degrees % 360;\n    return (radians * Math.PI) / 180;\n}\n/**\n * Converts a length to the requested unit.\n * Valid units: miles, nauticalmiles, inches, yards, meters, metres, kilometers, centimeters, feet\n *\n * @param {number} length to be converted\n * @param {Units} [originalUnit=\"kilometers\"] of the length\n * @param {Units} [finalUnit=\"kilometers\"] returned unit\n * @returns {number} the converted length\n */\nexport function convertLength(length, originalUnit, finalUnit) {\n    if (originalUnit === void 0) { originalUnit = \"kilometers\"; }\n    if (finalUnit === void 0) { finalUnit = \"kilometers\"; }\n    if (!(length >= 0)) {\n        throw new Error(\"length must be a positive number\");\n    }\n    return radiansToLength(lengthToRadians(length, originalUnit), finalUnit);\n}\n/**\n * Converts a area to the requested unit.\n * Valid units: kilometers, kilometres, meters, metres, centimetres, millimeters, acres, miles, yards, feet, inches, hectares\n * @param {number} area to be converted\n * @param {Units} [originalUnit=\"meters\"] of the distance\n * @param {Units} [finalUnit=\"kilometers\"] returned unit\n * @returns {number} the converted area\n */\nexport function convertArea(area, originalUnit, finalUnit) {\n    if (originalUnit === void 0) { originalUnit = \"meters\"; }\n    if (finalUnit === void 0) { finalUnit = \"kilometers\"; }\n    if (!(area >= 0)) {\n        throw new Error(\"area must be a positive number\");\n    }\n    var startFactor = areaFactors[originalUnit];\n    if (!startFactor) {\n        throw new Error(\"invalid original units\");\n    }\n    var finalFactor = areaFactors[finalUnit];\n    if (!finalFactor) {\n        throw new Error(\"invalid final units\");\n    }\n    return (area / startFactor) * finalFactor;\n}\n/**\n * isNumber\n *\n * @param {*} num Number to validate\n * @returns {boolean} true/false\n * @example\n * turf.isNumber(123)\n * //=true\n * turf.isNumber('foo')\n * //=false\n */\nexport function isNumber(num) {\n    return !isNaN(num) && num !== null && !Array.isArray(num);\n}\n/**\n * isObject\n *\n * @param {*} input variable to validate\n * @returns {boolean} true/false\n * @example\n * turf.isObject({elevation: 10})\n * //=true\n * turf.isObject('foo')\n * //=false\n */\nexport function isObject(input) {\n    return !!input && input.constructor === Object;\n}\n/**\n * Validate BBox\n *\n * @private\n * @param {Array<number>} bbox BBox to validate\n * @returns {void}\n * @throws Error if BBox is not valid\n * @example\n * validateBBox([-180, -40, 110, 50])\n * //=OK\n * validateBBox([-180, -40])\n * //=Error\n * validateBBox('Foo')\n * //=Error\n * validateBBox(5)\n * //=Error\n * validateBBox(null)\n * //=Error\n * validateBBox(undefined)\n * //=Error\n */\nexport function validateBBox(bbox) {\n    if (!bbox) {\n        throw new Error(\"bbox is required\");\n    }\n    if (!Array.isArray(bbox)) {\n        throw new Error(\"bbox must be an Array\");\n    }\n    if (bbox.length !== 4 && bbox.length !== 6) {\n        throw new Error(\"bbox must be an Array of 4 or 6 numbers\");\n    }\n    bbox.forEach(function (num) {\n        if (!isNumber(num)) {\n            throw new Error(\"bbox must only contain numbers\");\n        }\n    });\n}\n/**\n * Validate Id\n *\n * @private\n * @param {string|number} id Id to validate\n * @returns {void}\n * @throws Error if Id is not valid\n * @example\n * validateId([-180, -40, 110, 50])\n * //=Error\n * validateId([-180, -40])\n * //=Error\n * validateId('Foo')\n * //=OK\n * validateId(5)\n * //=OK\n * validateId(null)\n * //=Error\n * validateId(undefined)\n * //=Error\n */\nexport function validateId(id) {\n    if (!id) {\n        throw new Error(\"id is required\");\n    }\n    if ([\"string\", \"number\"].indexOf(typeof id) === -1) {\n        throw new Error(\"id must be a number or a string\");\n    }\n}\n", "import { isNumber, } from \"@turf/helpers\";\n/**\n * Unwrap a coordinate from a Point Feature, Geometry or a single coordinate.\n *\n * @name getCoord\n * @param {Array<number>|Geometry<Point>|Feature<Point>} coord GeoJSON Point or an Array of numbers\n * @returns {Array<number>} coordinates\n * @example\n * var pt = turf.point([10, 10]);\n *\n * var coord = turf.getCoord(pt);\n * //= [10, 10]\n */\nexport function getCoord(coord) {\n    if (!coord) {\n        throw new Error(\"coord is required\");\n    }\n    if (!Array.isArray(coord)) {\n        if (coord.type === \"Feature\" &&\n            coord.geometry !== null &&\n            coord.geometry.type === \"Point\") {\n            return coord.geometry.coordinates;\n        }\n        if (coord.type === \"Point\") {\n            return coord.coordinates;\n        }\n    }\n    if (Array.isArray(coord) &&\n        coord.length >= 2 &&\n        !Array.isArray(coord[0]) &&\n        !Array.isArray(coord[1])) {\n        return coord;\n    }\n    throw new Error(\"coord must be GeoJSON Point or an Array of numbers\");\n}\n/**\n * Unwrap coordinates from a Feature, Geometry Object or an Array\n *\n * @name getCoords\n * @param {Array<any>|Geometry|Feature} coords Feature, Geometry Object or an Array\n * @returns {Array<any>} coordinates\n * @example\n * var poly = turf.polygon([[[119.32, -8.7], [119.55, -8.69], [119.51, -8.54], [119.32, -8.7]]]);\n *\n * var coords = turf.getCoords(poly);\n * //= [[[119.32, -8.7], [119.55, -8.69], [119.51, -8.54], [119.32, -8.7]]]\n */\nexport function getCoords(coords) {\n    if (Array.isArray(coords)) {\n        return coords;\n    }\n    // Feature\n    if (coords.type === \"Feature\") {\n        if (coords.geometry !== null) {\n            return coords.geometry.coordinates;\n        }\n    }\n    else {\n        // Geometry\n        if (coords.coordinates) {\n            return coords.coordinates;\n        }\n    }\n    throw new Error(\"coords must be GeoJSON Feature, Geometry Object or an Array\");\n}\n/**\n * Checks if coordinates contains a number\n *\n * @name containsNumber\n * @param {Array<any>} coordinates GeoJSON Coordinates\n * @returns {boolean} true if Array contains a number\n */\nexport function containsNumber(coordinates) {\n    if (coordinates.length > 1 &&\n        isNumber(coordinates[0]) &&\n        isNumber(coordinates[1])) {\n        return true;\n    }\n    if (Array.isArray(coordinates[0]) && coordinates[0].length) {\n        return containsNumber(coordinates[0]);\n    }\n    throw new Error(\"coordinates must only contain numbers\");\n}\n/**\n * Enforce expectations about types of GeoJSON objects for Turf.\n *\n * @name geojsonType\n * @param {GeoJSON} value any GeoJSON object\n * @param {string} type expected GeoJSON type\n * @param {string} name name of calling function\n * @throws {Error} if value is not the expected type.\n */\nexport function geojsonType(value, type, name) {\n    if (!type || !name) {\n        throw new Error(\"type and name required\");\n    }\n    if (!value || value.type !== type) {\n        throw new Error(\"Invalid input to \" +\n            name +\n            \": must be a \" +\n            type +\n            \", given \" +\n            value.type);\n    }\n}\n/**\n * Enforce expectations about types of {@link Feature} inputs for Turf.\n * Internally this uses {@link geojsonType} to judge geometry types.\n *\n * @name featureOf\n * @param {Feature} feature a feature with an expected geometry type\n * @param {string} type expected GeoJSON type\n * @param {string} name name of calling function\n * @throws {Error} error if value is not the expected type.\n */\nexport function featureOf(feature, type, name) {\n    if (!feature) {\n        throw new Error(\"No feature passed\");\n    }\n    if (!name) {\n        throw new Error(\".featureOf() requires a name\");\n    }\n    if (!feature || feature.type !== \"Feature\" || !feature.geometry) {\n        throw new Error(\"Invalid input to \" + name + \", Feature with geometry required\");\n    }\n    if (!feature.geometry || feature.geometry.type !== type) {\n        throw new Error(\"Invalid input to \" +\n            name +\n            \": must be a \" +\n            type +\n            \", given \" +\n            feature.geometry.type);\n    }\n}\n/**\n * Enforce expectations about types of {@link FeatureCollection} inputs for Turf.\n * Internally this uses {@link geojsonType} to judge geometry types.\n *\n * @name collectionOf\n * @param {FeatureCollection} featureCollection a FeatureCollection for which features will be judged\n * @param {string} type expected GeoJSON type\n * @param {string} name name of calling function\n * @throws {Error} if value is not the expected type.\n */\nexport function collectionOf(featureCollection, type, name) {\n    if (!featureCollection) {\n        throw new Error(\"No featureCollection passed\");\n    }\n    if (!name) {\n        throw new Error(\".collectionOf() requires a name\");\n    }\n    if (!featureCollection || featureCollection.type !== \"FeatureCollection\") {\n        throw new Error(\"Invalid input to \" + name + \", FeatureCollection required\");\n    }\n    for (var _i = 0, _a = featureCollection.features; _i < _a.length; _i++) {\n        var feature = _a[_i];\n        if (!feature || feature.type !== \"Feature\" || !feature.geometry) {\n            throw new Error(\"Invalid input to \" + name + \", Feature with geometry required\");\n        }\n        if (!feature.geometry || feature.geometry.type !== type) {\n            throw new Error(\"Invalid input to \" +\n                name +\n                \": must be a \" +\n                type +\n                \", given \" +\n                feature.geometry.type);\n        }\n    }\n}\n/**\n * Get Geometry from Feature or Geometry Object\n *\n * @param {Feature|Geometry} geojson GeoJSON Feature or Geometry Object\n * @returns {Geometry|null} GeoJSON Geometry Object\n * @throws {Error} if geojson is not a Feature or Geometry Object\n * @example\n * var point = {\n *   \"type\": \"Feature\",\n *   \"properties\": {},\n *   \"geometry\": {\n *     \"type\": \"Point\",\n *     \"coordinates\": [110, 40]\n *   }\n * }\n * var geom = turf.getGeom(point)\n * //={\"type\": \"Point\", \"coordinates\": [110, 40]}\n */\nexport function getGeom(geojson) {\n    if (geojson.type === \"Feature\") {\n        return geojson.geometry;\n    }\n    return geojson;\n}\n/**\n * Get GeoJSON object's type, Geometry type is prioritize.\n *\n * @param {GeoJSON} geojson GeoJSON object\n * @param {string} [name=\"geojson\"] name of the variable to display in error message (unused)\n * @returns {string} GeoJSON type\n * @example\n * var point = {\n *   \"type\": \"Feature\",\n *   \"properties\": {},\n *   \"geometry\": {\n *     \"type\": \"Point\",\n *     \"coordinates\": [110, 40]\n *   }\n * }\n * var geom = turf.getType(point)\n * //=\"Point\"\n */\nexport function getType(geojson, _name) {\n    if (geojson.type === \"FeatureCollection\") {\n        return \"FeatureCollection\";\n    }\n    if (geojson.type === \"GeometryCollection\") {\n        return \"GeometryCollection\";\n    }\n    if (geojson.type === \"Feature\" && geojson.geometry !== null) {\n        return geojson.geometry.type;\n    }\n    return geojson.type;\n}\n", "/**\n * splaytree v3.1.1\n * Fast Splay tree for Node and browser\n *\n * <AUTHOR> <<EMAIL>>\n * @license MIT\n * @preserve\n */\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n\r\nfunction __generator(thisArg, body) {\r\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\r\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\r\n    function verb(n) { return function (v) { return step([n, v]); }; }\r\n    function step(op) {\r\n        if (f) throw new TypeError(\"Generator is already executing.\");\r\n        while (_) try {\r\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\r\n            if (y = 0, t) op = [op[0] & 2, t.value];\r\n            switch (op[0]) {\r\n                case 0: case 1: t = op; break;\r\n                case 4: _.label++; return { value: op[1], done: false };\r\n                case 5: _.label++; y = op[1]; op = [0]; continue;\r\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\r\n                default:\r\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\r\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\r\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\r\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\r\n                    if (t[2]) _.ops.pop();\r\n                    _.trys.pop(); continue;\r\n            }\r\n            op = body.call(thisArg, _);\r\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\r\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\r\n    }\r\n}\n\nvar Node = /** @class */ (function () {\r\n    function Node(key, data) {\r\n        this.next = null;\r\n        this.key = key;\r\n        this.data = data;\r\n        this.left = null;\r\n        this.right = null;\r\n    }\r\n    return Node;\r\n}());\n\n/* follows \"An implementation of top-down splaying\"\r\n * by D. Sleator <<EMAIL>> March 1992\r\n */\r\nfunction DEFAULT_COMPARE(a, b) {\r\n    return a > b ? 1 : a < b ? -1 : 0;\r\n}\r\n/**\r\n * Simple top down splay, not requiring i to be in the tree t.\r\n */\r\nfunction splay(i, t, comparator) {\r\n    var N = new Node(null, null);\r\n    var l = N;\r\n    var r = N;\r\n    while (true) {\r\n        var cmp = comparator(i, t.key);\r\n        //if (i < t.key) {\r\n        if (cmp < 0) {\r\n            if (t.left === null)\r\n                break;\r\n            //if (i < t.left.key) {\r\n            if (comparator(i, t.left.key) < 0) {\r\n                var y = t.left; /* rotate right */\r\n                t.left = y.right;\r\n                y.right = t;\r\n                t = y;\r\n                if (t.left === null)\r\n                    break;\r\n            }\r\n            r.left = t; /* link right */\r\n            r = t;\r\n            t = t.left;\r\n            //} else if (i > t.key) {\r\n        }\r\n        else if (cmp > 0) {\r\n            if (t.right === null)\r\n                break;\r\n            //if (i > t.right.key) {\r\n            if (comparator(i, t.right.key) > 0) {\r\n                var y = t.right; /* rotate left */\r\n                t.right = y.left;\r\n                y.left = t;\r\n                t = y;\r\n                if (t.right === null)\r\n                    break;\r\n            }\r\n            l.right = t; /* link left */\r\n            l = t;\r\n            t = t.right;\r\n        }\r\n        else\r\n            break;\r\n    }\r\n    /* assemble */\r\n    l.right = t.left;\r\n    r.left = t.right;\r\n    t.left = N.right;\r\n    t.right = N.left;\r\n    return t;\r\n}\r\nfunction insert(i, data, t, comparator) {\r\n    var node = new Node(i, data);\r\n    if (t === null) {\r\n        node.left = node.right = null;\r\n        return node;\r\n    }\r\n    t = splay(i, t, comparator);\r\n    var cmp = comparator(i, t.key);\r\n    if (cmp < 0) {\r\n        node.left = t.left;\r\n        node.right = t;\r\n        t.left = null;\r\n    }\r\n    else if (cmp >= 0) {\r\n        node.right = t.right;\r\n        node.left = t;\r\n        t.right = null;\r\n    }\r\n    return node;\r\n}\r\nfunction split(key, v, comparator) {\r\n    var left = null;\r\n    var right = null;\r\n    if (v) {\r\n        v = splay(key, v, comparator);\r\n        var cmp = comparator(v.key, key);\r\n        if (cmp === 0) {\r\n            left = v.left;\r\n            right = v.right;\r\n        }\r\n        else if (cmp < 0) {\r\n            right = v.right;\r\n            v.right = null;\r\n            left = v;\r\n        }\r\n        else {\r\n            left = v.left;\r\n            v.left = null;\r\n            right = v;\r\n        }\r\n    }\r\n    return { left: left, right: right };\r\n}\r\nfunction merge(left, right, comparator) {\r\n    if (right === null)\r\n        return left;\r\n    if (left === null)\r\n        return right;\r\n    right = splay(left.key, right, comparator);\r\n    right.left = left;\r\n    return right;\r\n}\r\n/**\r\n * Prints level of the tree\r\n */\r\nfunction printRow(root, prefix, isTail, out, printNode) {\r\n    if (root) {\r\n        out(\"\" + prefix + (isTail ? '└── ' : '├── ') + printNode(root) + \"\\n\");\r\n        var indent = prefix + (isTail ? '    ' : '│   ');\r\n        if (root.left)\r\n            printRow(root.left, indent, false, out, printNode);\r\n        if (root.right)\r\n            printRow(root.right, indent, true, out, printNode);\r\n    }\r\n}\r\nvar Tree = /** @class */ (function () {\r\n    function Tree(comparator) {\r\n        if (comparator === void 0) { comparator = DEFAULT_COMPARE; }\r\n        this._root = null;\r\n        this._size = 0;\r\n        this._comparator = comparator;\r\n    }\r\n    /**\r\n     * Inserts a key, allows duplicates\r\n     */\r\n    Tree.prototype.insert = function (key, data) {\r\n        this._size++;\r\n        return this._root = insert(key, data, this._root, this._comparator);\r\n    };\r\n    /**\r\n     * Adds a key, if it is not present in the tree\r\n     */\r\n    Tree.prototype.add = function (key, data) {\r\n        var node = new Node(key, data);\r\n        if (this._root === null) {\r\n            node.left = node.right = null;\r\n            this._size++;\r\n            this._root = node;\r\n        }\r\n        var comparator = this._comparator;\r\n        var t = splay(key, this._root, comparator);\r\n        var cmp = comparator(key, t.key);\r\n        if (cmp === 0)\r\n            this._root = t;\r\n        else {\r\n            if (cmp < 0) {\r\n                node.left = t.left;\r\n                node.right = t;\r\n                t.left = null;\r\n            }\r\n            else if (cmp > 0) {\r\n                node.right = t.right;\r\n                node.left = t;\r\n                t.right = null;\r\n            }\r\n            this._size++;\r\n            this._root = node;\r\n        }\r\n        return this._root;\r\n    };\r\n    /**\r\n     * @param  {Key} key\r\n     * @return {Node|null}\r\n     */\r\n    Tree.prototype.remove = function (key) {\r\n        this._root = this._remove(key, this._root, this._comparator);\r\n    };\r\n    /**\r\n     * Deletes i from the tree if it's there\r\n     */\r\n    Tree.prototype._remove = function (i, t, comparator) {\r\n        var x;\r\n        if (t === null)\r\n            return null;\r\n        t = splay(i, t, comparator);\r\n        var cmp = comparator(i, t.key);\r\n        if (cmp === 0) { /* found it */\r\n            if (t.left === null) {\r\n                x = t.right;\r\n            }\r\n            else {\r\n                x = splay(i, t.left, comparator);\r\n                x.right = t.right;\r\n            }\r\n            this._size--;\r\n            return x;\r\n        }\r\n        return t; /* It wasn't there */\r\n    };\r\n    /**\r\n     * Removes and returns the node with smallest key\r\n     */\r\n    Tree.prototype.pop = function () {\r\n        var node = this._root;\r\n        if (node) {\r\n            while (node.left)\r\n                node = node.left;\r\n            this._root = splay(node.key, this._root, this._comparator);\r\n            this._root = this._remove(node.key, this._root, this._comparator);\r\n            return { key: node.key, data: node.data };\r\n        }\r\n        return null;\r\n    };\r\n    /**\r\n     * Find without splaying\r\n     */\r\n    Tree.prototype.findStatic = function (key) {\r\n        var current = this._root;\r\n        var compare = this._comparator;\r\n        while (current) {\r\n            var cmp = compare(key, current.key);\r\n            if (cmp === 0)\r\n                return current;\r\n            else if (cmp < 0)\r\n                current = current.left;\r\n            else\r\n                current = current.right;\r\n        }\r\n        return null;\r\n    };\r\n    Tree.prototype.find = function (key) {\r\n        if (this._root) {\r\n            this._root = splay(key, this._root, this._comparator);\r\n            if (this._comparator(key, this._root.key) !== 0)\r\n                return null;\r\n        }\r\n        return this._root;\r\n    };\r\n    Tree.prototype.contains = function (key) {\r\n        var current = this._root;\r\n        var compare = this._comparator;\r\n        while (current) {\r\n            var cmp = compare(key, current.key);\r\n            if (cmp === 0)\r\n                return true;\r\n            else if (cmp < 0)\r\n                current = current.left;\r\n            else\r\n                current = current.right;\r\n        }\r\n        return false;\r\n    };\r\n    Tree.prototype.forEach = function (visitor, ctx) {\r\n        var current = this._root;\r\n        var Q = []; /* Initialize stack s */\r\n        var done = false;\r\n        while (!done) {\r\n            if (current !== null) {\r\n                Q.push(current);\r\n                current = current.left;\r\n            }\r\n            else {\r\n                if (Q.length !== 0) {\r\n                    current = Q.pop();\r\n                    visitor.call(ctx, current);\r\n                    current = current.right;\r\n                }\r\n                else\r\n                    done = true;\r\n            }\r\n        }\r\n        return this;\r\n    };\r\n    /**\r\n     * Walk key range from `low` to `high`. Stops if `fn` returns a value.\r\n     */\r\n    Tree.prototype.range = function (low, high, fn, ctx) {\r\n        var Q = [];\r\n        var compare = this._comparator;\r\n        var node = this._root;\r\n        var cmp;\r\n        while (Q.length !== 0 || node) {\r\n            if (node) {\r\n                Q.push(node);\r\n                node = node.left;\r\n            }\r\n            else {\r\n                node = Q.pop();\r\n                cmp = compare(node.key, high);\r\n                if (cmp > 0) {\r\n                    break;\r\n                }\r\n                else if (compare(node.key, low) >= 0) {\r\n                    if (fn.call(ctx, node))\r\n                        return this; // stop if smth is returned\r\n                }\r\n                node = node.right;\r\n            }\r\n        }\r\n        return this;\r\n    };\r\n    /**\r\n     * Returns array of keys\r\n     */\r\n    Tree.prototype.keys = function () {\r\n        var keys = [];\r\n        this.forEach(function (_a) {\r\n            var key = _a.key;\r\n            return keys.push(key);\r\n        });\r\n        return keys;\r\n    };\r\n    /**\r\n     * Returns array of all the data in the nodes\r\n     */\r\n    Tree.prototype.values = function () {\r\n        var values = [];\r\n        this.forEach(function (_a) {\r\n            var data = _a.data;\r\n            return values.push(data);\r\n        });\r\n        return values;\r\n    };\r\n    Tree.prototype.min = function () {\r\n        if (this._root)\r\n            return this.minNode(this._root).key;\r\n        return null;\r\n    };\r\n    Tree.prototype.max = function () {\r\n        if (this._root)\r\n            return this.maxNode(this._root).key;\r\n        return null;\r\n    };\r\n    Tree.prototype.minNode = function (t) {\r\n        if (t === void 0) { t = this._root; }\r\n        if (t)\r\n            while (t.left)\r\n                t = t.left;\r\n        return t;\r\n    };\r\n    Tree.prototype.maxNode = function (t) {\r\n        if (t === void 0) { t = this._root; }\r\n        if (t)\r\n            while (t.right)\r\n                t = t.right;\r\n        return t;\r\n    };\r\n    /**\r\n     * Returns node at given index\r\n     */\r\n    Tree.prototype.at = function (index) {\r\n        var current = this._root;\r\n        var done = false;\r\n        var i = 0;\r\n        var Q = [];\r\n        while (!done) {\r\n            if (current) {\r\n                Q.push(current);\r\n                current = current.left;\r\n            }\r\n            else {\r\n                if (Q.length > 0) {\r\n                    current = Q.pop();\r\n                    if (i === index)\r\n                        return current;\r\n                    i++;\r\n                    current = current.right;\r\n                }\r\n                else\r\n                    done = true;\r\n            }\r\n        }\r\n        return null;\r\n    };\r\n    Tree.prototype.next = function (d) {\r\n        var root = this._root;\r\n        var successor = null;\r\n        if (d.right) {\r\n            successor = d.right;\r\n            while (successor.left)\r\n                successor = successor.left;\r\n            return successor;\r\n        }\r\n        var comparator = this._comparator;\r\n        while (root) {\r\n            var cmp = comparator(d.key, root.key);\r\n            if (cmp === 0)\r\n                break;\r\n            else if (cmp < 0) {\r\n                successor = root;\r\n                root = root.left;\r\n            }\r\n            else\r\n                root = root.right;\r\n        }\r\n        return successor;\r\n    };\r\n    Tree.prototype.prev = function (d) {\r\n        var root = this._root;\r\n        var predecessor = null;\r\n        if (d.left !== null) {\r\n            predecessor = d.left;\r\n            while (predecessor.right)\r\n                predecessor = predecessor.right;\r\n            return predecessor;\r\n        }\r\n        var comparator = this._comparator;\r\n        while (root) {\r\n            var cmp = comparator(d.key, root.key);\r\n            if (cmp === 0)\r\n                break;\r\n            else if (cmp < 0)\r\n                root = root.left;\r\n            else {\r\n                predecessor = root;\r\n                root = root.right;\r\n            }\r\n        }\r\n        return predecessor;\r\n    };\r\n    Tree.prototype.clear = function () {\r\n        this._root = null;\r\n        this._size = 0;\r\n        return this;\r\n    };\r\n    Tree.prototype.toList = function () {\r\n        return toList(this._root);\r\n    };\r\n    /**\r\n     * Bulk-load items. Both array have to be same size\r\n     */\r\n    Tree.prototype.load = function (keys, values, presort) {\r\n        if (values === void 0) { values = []; }\r\n        if (presort === void 0) { presort = false; }\r\n        var size = keys.length;\r\n        var comparator = this._comparator;\r\n        // sort if needed\r\n        if (presort)\r\n            sort(keys, values, 0, size - 1, comparator);\r\n        if (this._root === null) { // empty tree\r\n            this._root = loadRecursive(keys, values, 0, size);\r\n            this._size = size;\r\n        }\r\n        else { // that re-builds the whole tree from two in-order traversals\r\n            var mergedList = mergeLists(this.toList(), createList(keys, values), comparator);\r\n            size = this._size + size;\r\n            this._root = sortedListToBST({ head: mergedList }, 0, size);\r\n        }\r\n        return this;\r\n    };\r\n    Tree.prototype.isEmpty = function () { return this._root === null; };\r\n    Object.defineProperty(Tree.prototype, \"size\", {\r\n        get: function () { return this._size; },\r\n        enumerable: true,\r\n        configurable: true\r\n    });\r\n    Object.defineProperty(Tree.prototype, \"root\", {\r\n        get: function () { return this._root; },\r\n        enumerable: true,\r\n        configurable: true\r\n    });\r\n    Tree.prototype.toString = function (printNode) {\r\n        if (printNode === void 0) { printNode = function (n) { return String(n.key); }; }\r\n        var out = [];\r\n        printRow(this._root, '', true, function (v) { return out.push(v); }, printNode);\r\n        return out.join('');\r\n    };\r\n    Tree.prototype.update = function (key, newKey, newData) {\r\n        var comparator = this._comparator;\r\n        var _a = split(key, this._root, comparator), left = _a.left, right = _a.right;\r\n        if (comparator(key, newKey) < 0) {\r\n            right = insert(newKey, newData, right, comparator);\r\n        }\r\n        else {\r\n            left = insert(newKey, newData, left, comparator);\r\n        }\r\n        this._root = merge(left, right, comparator);\r\n    };\r\n    Tree.prototype.split = function (key) {\r\n        return split(key, this._root, this._comparator);\r\n    };\r\n    Tree.prototype[Symbol.iterator] = function () {\r\n        var n;\r\n        return __generator(this, function (_a) {\r\n            switch (_a.label) {\r\n                case 0:\r\n                    n = this.minNode();\r\n                    _a.label = 1;\r\n                case 1:\r\n                    if (!n) return [3 /*break*/, 3];\r\n                    return [4 /*yield*/, n];\r\n                case 2:\r\n                    _a.sent();\r\n                    n = this.next(n);\r\n                    return [3 /*break*/, 1];\r\n                case 3: return [2 /*return*/];\r\n            }\r\n        });\r\n    };\r\n    return Tree;\r\n}());\r\nfunction loadRecursive(keys, values, start, end) {\r\n    var size = end - start;\r\n    if (size > 0) {\r\n        var middle = start + Math.floor(size / 2);\r\n        var key = keys[middle];\r\n        var data = values[middle];\r\n        var node = new Node(key, data);\r\n        node.left = loadRecursive(keys, values, start, middle);\r\n        node.right = loadRecursive(keys, values, middle + 1, end);\r\n        return node;\r\n    }\r\n    return null;\r\n}\r\nfunction createList(keys, values) {\r\n    var head = new Node(null, null);\r\n    var p = head;\r\n    for (var i = 0; i < keys.length; i++) {\r\n        p = p.next = new Node(keys[i], values[i]);\r\n    }\r\n    p.next = null;\r\n    return head.next;\r\n}\r\nfunction toList(root) {\r\n    var current = root;\r\n    var Q = [];\r\n    var done = false;\r\n    var head = new Node(null, null);\r\n    var p = head;\r\n    while (!done) {\r\n        if (current) {\r\n            Q.push(current);\r\n            current = current.left;\r\n        }\r\n        else {\r\n            if (Q.length > 0) {\r\n                current = p = p.next = Q.pop();\r\n                current = current.right;\r\n            }\r\n            else\r\n                done = true;\r\n        }\r\n    }\r\n    p.next = null; // that'll work even if the tree was empty\r\n    return head.next;\r\n}\r\nfunction sortedListToBST(list, start, end) {\r\n    var size = end - start;\r\n    if (size > 0) {\r\n        var middle = start + Math.floor(size / 2);\r\n        var left = sortedListToBST(list, start, middle);\r\n        var root = list.head;\r\n        root.left = left;\r\n        list.head = list.head.next;\r\n        root.right = sortedListToBST(list, middle + 1, end);\r\n        return root;\r\n    }\r\n    return null;\r\n}\r\nfunction mergeLists(l1, l2, compare) {\r\n    var head = new Node(null, null); // dummy\r\n    var p = head;\r\n    var p1 = l1;\r\n    var p2 = l2;\r\n    while (p1 !== null && p2 !== null) {\r\n        if (compare(p1.key, p2.key) < 0) {\r\n            p.next = p1;\r\n            p1 = p1.next;\r\n        }\r\n        else {\r\n            p.next = p2;\r\n            p2 = p2.next;\r\n        }\r\n        p = p.next;\r\n    }\r\n    if (p1 !== null) {\r\n        p.next = p1;\r\n    }\r\n    else if (p2 !== null) {\r\n        p.next = p2;\r\n    }\r\n    return head.next;\r\n}\r\nfunction sort(keys, values, left, right, compare) {\r\n    if (left >= right)\r\n        return;\r\n    var pivot = keys[(left + right) >> 1];\r\n    var i = left - 1;\r\n    var j = right + 1;\r\n    while (true) {\r\n        do\r\n            i++;\r\n        while (compare(keys[i], pivot) < 0);\r\n        do\r\n            j--;\r\n        while (compare(keys[j], pivot) > 0);\r\n        if (i >= j)\r\n            break;\r\n        var tmp = keys[i];\r\n        keys[i] = keys[j];\r\n        keys[j] = tmp;\r\n        tmp = values[i];\r\n        values[i] = values[j];\r\n        values[j] = tmp;\r\n    }\r\n    sort(keys, values, left, j, compare);\r\n    sort(keys, values, j + 1, right, compare);\r\n}\n\nexport default Tree;\n//# sourceMappingURL=splay.esm.js.map\n", "import SplayTree from 'splaytree';\n\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\n\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}\n\n/**\n * A bounding box has the format:\n *\n *  { ll: { x: xmin, y: ymin }, ur: { x: xmax, y: ymax } }\n *\n */\nvar isInBbox = function isInBbox(bbox, point) {\n  return bbox.ll.x <= point.x && point.x <= bbox.ur.x && bbox.ll.y <= point.y && point.y <= bbox.ur.y;\n};\n/* Returns either null, or a bbox (aka an ordered pair of points)\n * If there is only one point of overlap, a bbox with identical points\n * will be returned */\n\nvar getBboxOverlap = function getBboxOverlap(b1, b2) {\n  // check if the bboxes overlap at all\n  if (b2.ur.x < b1.ll.x || b1.ur.x < b2.ll.x || b2.ur.y < b1.ll.y || b1.ur.y < b2.ll.y) return null; // find the middle two X values\n\n  var lowerX = b1.ll.x < b2.ll.x ? b2.ll.x : b1.ll.x;\n  var upperX = b1.ur.x < b2.ur.x ? b1.ur.x : b2.ur.x; // find the middle two Y values\n\n  var lowerY = b1.ll.y < b2.ll.y ? b2.ll.y : b1.ll.y;\n  var upperY = b1.ur.y < b2.ur.y ? b1.ur.y : b2.ur.y; // put those middle values together to get the overlap\n\n  return {\n    ll: {\n      x: lowerX,\n      y: lowerY\n    },\n    ur: {\n      x: upperX,\n      y: upperY\n    }\n  };\n};\n\n/* Javascript doesn't do integer math. Everything is\n * floating point with percision Number.EPSILON.\n *\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/EPSILON\n */\nvar epsilon = Number.EPSILON; // IE Polyfill\n\nif (epsilon === undefined) epsilon = Math.pow(2, -52);\nvar EPSILON_SQ = epsilon * epsilon;\n/* FLP comparator */\n\nvar cmp = function cmp(a, b) {\n  // check if they're both 0\n  if (-epsilon < a && a < epsilon) {\n    if (-epsilon < b && b < epsilon) {\n      return 0;\n    }\n  } // check if they're flp equal\n\n\n  var ab = a - b;\n\n  if (ab * ab < EPSILON_SQ * a * b) {\n    return 0;\n  } // normal comparison\n\n\n  return a < b ? -1 : 1;\n};\n\n/**\n * This class rounds incoming values sufficiently so that\n * floating points problems are, for the most part, avoided.\n *\n * Incoming points are have their x & y values tested against\n * all previously seen x & y values. If either is 'too close'\n * to a previously seen value, it's value is 'snapped' to the\n * previously seen value.\n *\n * All points should be rounded by this class before being\n * stored in any data structures in the rest of this algorithm.\n */\n\nvar PtRounder = /*#__PURE__*/function () {\n  function PtRounder() {\n    _classCallCheck(this, PtRounder);\n\n    this.reset();\n  }\n\n  _createClass(PtRounder, [{\n    key: \"reset\",\n    value: function reset() {\n      this.xRounder = new CoordRounder();\n      this.yRounder = new CoordRounder();\n    }\n  }, {\n    key: \"round\",\n    value: function round(x, y) {\n      return {\n        x: this.xRounder.round(x),\n        y: this.yRounder.round(y)\n      };\n    }\n  }]);\n\n  return PtRounder;\n}();\n\nvar CoordRounder = /*#__PURE__*/function () {\n  function CoordRounder() {\n    _classCallCheck(this, CoordRounder);\n\n    this.tree = new SplayTree(); // preseed with 0 so we don't end up with values < Number.EPSILON\n\n    this.round(0);\n  } // Note: this can rounds input values backwards or forwards.\n  //       You might ask, why not restrict this to just rounding\n  //       forwards? Wouldn't that allow left endpoints to always\n  //       remain left endpoints during splitting (never change to\n  //       right). No - it wouldn't, because we snap intersections\n  //       to endpoints (to establish independence from the segment\n  //       angle for t-intersections).\n\n\n  _createClass(CoordRounder, [{\n    key: \"round\",\n    value: function round(coord) {\n      var node = this.tree.add(coord);\n      var prevNode = this.tree.prev(node);\n\n      if (prevNode !== null && cmp(node.key, prevNode.key) === 0) {\n        this.tree.remove(coord);\n        return prevNode.key;\n      }\n\n      var nextNode = this.tree.next(node);\n\n      if (nextNode !== null && cmp(node.key, nextNode.key) === 0) {\n        this.tree.remove(coord);\n        return nextNode.key;\n      }\n\n      return coord;\n    }\n  }]);\n\n  return CoordRounder;\n}(); // singleton available by import\n\n\nvar rounder = new PtRounder();\n\n/* Cross Product of two vectors with first point at origin */\n\nvar crossProduct = function crossProduct(a, b) {\n  return a.x * b.y - a.y * b.x;\n};\n/* Dot Product of two vectors with first point at origin */\n\nvar dotProduct = function dotProduct(a, b) {\n  return a.x * b.x + a.y * b.y;\n};\n/* Comparator for two vectors with same starting point */\n\nvar compareVectorAngles = function compareVectorAngles(basePt, endPt1, endPt2) {\n  var v1 = {\n    x: endPt1.x - basePt.x,\n    y: endPt1.y - basePt.y\n  };\n  var v2 = {\n    x: endPt2.x - basePt.x,\n    y: endPt2.y - basePt.y\n  };\n  var kross = crossProduct(v1, v2);\n  return cmp(kross, 0);\n};\nvar length = function length(v) {\n  return Math.sqrt(dotProduct(v, v));\n};\n/* Get the sine of the angle from pShared -> pAngle to pShaed -> pBase */\n\nvar sineOfAngle = function sineOfAngle(pShared, pBase, pAngle) {\n  var vBase = {\n    x: pBase.x - pShared.x,\n    y: pBase.y - pShared.y\n  };\n  var vAngle = {\n    x: pAngle.x - pShared.x,\n    y: pAngle.y - pShared.y\n  };\n  return crossProduct(vAngle, vBase) / length(vAngle) / length(vBase);\n};\n/* Get the cosine of the angle from pShared -> pAngle to pShaed -> pBase */\n\nvar cosineOfAngle = function cosineOfAngle(pShared, pBase, pAngle) {\n  var vBase = {\n    x: pBase.x - pShared.x,\n    y: pBase.y - pShared.y\n  };\n  var vAngle = {\n    x: pAngle.x - pShared.x,\n    y: pAngle.y - pShared.y\n  };\n  return dotProduct(vAngle, vBase) / length(vAngle) / length(vBase);\n};\n/* Get the x coordinate where the given line (defined by a point and vector)\n * crosses the horizontal line with the given y coordiante.\n * In the case of parrallel lines (including overlapping ones) returns null. */\n\nvar horizontalIntersection = function horizontalIntersection(pt, v, y) {\n  if (v.y === 0) return null;\n  return {\n    x: pt.x + v.x / v.y * (y - pt.y),\n    y: y\n  };\n};\n/* Get the y coordinate where the given line (defined by a point and vector)\n * crosses the vertical line with the given x coordiante.\n * In the case of parrallel lines (including overlapping ones) returns null. */\n\nvar verticalIntersection = function verticalIntersection(pt, v, x) {\n  if (v.x === 0) return null;\n  return {\n    x: x,\n    y: pt.y + v.y / v.x * (x - pt.x)\n  };\n};\n/* Get the intersection of two lines, each defined by a base point and a vector.\n * In the case of parrallel lines (including overlapping ones) returns null. */\n\nvar intersection = function intersection(pt1, v1, pt2, v2) {\n  // take some shortcuts for vertical and horizontal lines\n  // this also ensures we don't calculate an intersection and then discover\n  // it's actually outside the bounding box of the line\n  if (v1.x === 0) return verticalIntersection(pt2, v2, pt1.x);\n  if (v2.x === 0) return verticalIntersection(pt1, v1, pt2.x);\n  if (v1.y === 0) return horizontalIntersection(pt2, v2, pt1.y);\n  if (v2.y === 0) return horizontalIntersection(pt1, v1, pt2.y); // General case for non-overlapping segments.\n  // This algorithm is based on Schneider and Eberly.\n  // http://www.cimec.org.ar/~ncalvo/Schneider_Eberly.pdf - pg 244\n\n  var kross = crossProduct(v1, v2);\n  if (kross == 0) return null;\n  var ve = {\n    x: pt2.x - pt1.x,\n    y: pt2.y - pt1.y\n  };\n  var d1 = crossProduct(ve, v1) / kross;\n  var d2 = crossProduct(ve, v2) / kross; // take the average of the two calculations to minimize rounding error\n\n  var x1 = pt1.x + d2 * v1.x,\n      x2 = pt2.x + d1 * v2.x;\n  var y1 = pt1.y + d2 * v1.y,\n      y2 = pt2.y + d1 * v2.y;\n  var x = (x1 + x2) / 2;\n  var y = (y1 + y2) / 2;\n  return {\n    x: x,\n    y: y\n  };\n};\n\nvar SweepEvent = /*#__PURE__*/function () {\n  _createClass(SweepEvent, null, [{\n    key: \"compare\",\n    // for ordering sweep events in the sweep event queue\n    value: function compare(a, b) {\n      // favor event with a point that the sweep line hits first\n      var ptCmp = SweepEvent.comparePoints(a.point, b.point);\n      if (ptCmp !== 0) return ptCmp; // the points are the same, so link them if needed\n\n      if (a.point !== b.point) a.link(b); // favor right events over left\n\n      if (a.isLeft !== b.isLeft) return a.isLeft ? 1 : -1; // we have two matching left or right endpoints\n      // ordering of this case is the same as for their segments\n\n      return Segment.compare(a.segment, b.segment);\n    } // for ordering points in sweep line order\n\n  }, {\n    key: \"comparePoints\",\n    value: function comparePoints(aPt, bPt) {\n      if (aPt.x < bPt.x) return -1;\n      if (aPt.x > bPt.x) return 1;\n      if (aPt.y < bPt.y) return -1;\n      if (aPt.y > bPt.y) return 1;\n      return 0;\n    } // Warning: 'point' input will be modified and re-used (for performance)\n\n  }]);\n\n  function SweepEvent(point, isLeft) {\n    _classCallCheck(this, SweepEvent);\n\n    if (point.events === undefined) point.events = [this];else point.events.push(this);\n    this.point = point;\n    this.isLeft = isLeft; // this.segment, this.otherSE set by factory\n  }\n\n  _createClass(SweepEvent, [{\n    key: \"link\",\n    value: function link(other) {\n      if (other.point === this.point) {\n        throw new Error('Tried to link already linked events');\n      }\n\n      var otherEvents = other.point.events;\n\n      for (var i = 0, iMax = otherEvents.length; i < iMax; i++) {\n        var evt = otherEvents[i];\n        this.point.events.push(evt);\n        evt.point = this.point;\n      }\n\n      this.checkForConsuming();\n    }\n    /* Do a pass over our linked events and check to see if any pair\n     * of segments match, and should be consumed. */\n\n  }, {\n    key: \"checkForConsuming\",\n    value: function checkForConsuming() {\n      // FIXME: The loops in this method run O(n^2) => no good.\n      //        Maintain little ordered sweep event trees?\n      //        Can we maintaining an ordering that avoids the need\n      //        for the re-sorting with getLeftmostComparator in geom-out?\n      // Compare each pair of events to see if other events also match\n      var numEvents = this.point.events.length;\n\n      for (var i = 0; i < numEvents; i++) {\n        var evt1 = this.point.events[i];\n        if (evt1.segment.consumedBy !== undefined) continue;\n\n        for (var j = i + 1; j < numEvents; j++) {\n          var evt2 = this.point.events[j];\n          if (evt2.consumedBy !== undefined) continue;\n          if (evt1.otherSE.point.events !== evt2.otherSE.point.events) continue;\n          evt1.segment.consume(evt2.segment);\n        }\n      }\n    }\n  }, {\n    key: \"getAvailableLinkedEvents\",\n    value: function getAvailableLinkedEvents() {\n      // point.events is always of length 2 or greater\n      var events = [];\n\n      for (var i = 0, iMax = this.point.events.length; i < iMax; i++) {\n        var evt = this.point.events[i];\n\n        if (evt !== this && !evt.segment.ringOut && evt.segment.isInResult()) {\n          events.push(evt);\n        }\n      }\n\n      return events;\n    }\n    /**\n     * Returns a comparator function for sorting linked events that will\n     * favor the event that will give us the smallest left-side angle.\n     * All ring construction starts as low as possible heading to the right,\n     * so by always turning left as sharp as possible we'll get polygons\n     * without uncessary loops & holes.\n     *\n     * The comparator function has a compute cache such that it avoids\n     * re-computing already-computed values.\n     */\n\n  }, {\n    key: \"getLeftmostComparator\",\n    value: function getLeftmostComparator(baseEvent) {\n      var _this = this;\n\n      var cache = new Map();\n\n      var fillCache = function fillCache(linkedEvent) {\n        var nextEvent = linkedEvent.otherSE;\n        cache.set(linkedEvent, {\n          sine: sineOfAngle(_this.point, baseEvent.point, nextEvent.point),\n          cosine: cosineOfAngle(_this.point, baseEvent.point, nextEvent.point)\n        });\n      };\n\n      return function (a, b) {\n        if (!cache.has(a)) fillCache(a);\n        if (!cache.has(b)) fillCache(b);\n\n        var _cache$get = cache.get(a),\n            asine = _cache$get.sine,\n            acosine = _cache$get.cosine;\n\n        var _cache$get2 = cache.get(b),\n            bsine = _cache$get2.sine,\n            bcosine = _cache$get2.cosine; // both on or above x-axis\n\n\n        if (asine >= 0 && bsine >= 0) {\n          if (acosine < bcosine) return 1;\n          if (acosine > bcosine) return -1;\n          return 0;\n        } // both below x-axis\n\n\n        if (asine < 0 && bsine < 0) {\n          if (acosine < bcosine) return -1;\n          if (acosine > bcosine) return 1;\n          return 0;\n        } // one above x-axis, one below\n\n\n        if (bsine < asine) return -1;\n        if (bsine > asine) return 1;\n        return 0;\n      };\n    }\n  }]);\n\n  return SweepEvent;\n}();\n\n// segments and sweep events when all else is identical\n\nvar segmentId = 0;\n\nvar Segment = /*#__PURE__*/function () {\n  _createClass(Segment, null, [{\n    key: \"compare\",\n\n    /* This compare() function is for ordering segments in the sweep\n     * line tree, and does so according to the following criteria:\n     *\n     * Consider the vertical line that lies an infinestimal step to the\n     * right of the right-more of the two left endpoints of the input\n     * segments. Imagine slowly moving a point up from negative infinity\n     * in the increasing y direction. Which of the two segments will that\n     * point intersect first? That segment comes 'before' the other one.\n     *\n     * If neither segment would be intersected by such a line, (if one\n     * or more of the segments are vertical) then the line to be considered\n     * is directly on the right-more of the two left inputs.\n     */\n    value: function compare(a, b) {\n      var alx = a.leftSE.point.x;\n      var blx = b.leftSE.point.x;\n      var arx = a.rightSE.point.x;\n      var brx = b.rightSE.point.x; // check if they're even in the same vertical plane\n\n      if (brx < alx) return 1;\n      if (arx < blx) return -1;\n      var aly = a.leftSE.point.y;\n      var bly = b.leftSE.point.y;\n      var ary = a.rightSE.point.y;\n      var bry = b.rightSE.point.y; // is left endpoint of segment B the right-more?\n\n      if (alx < blx) {\n        // are the two segments in the same horizontal plane?\n        if (bly < aly && bly < ary) return 1;\n        if (bly > aly && bly > ary) return -1; // is the B left endpoint colinear to segment A?\n\n        var aCmpBLeft = a.comparePoint(b.leftSE.point);\n        if (aCmpBLeft < 0) return 1;\n        if (aCmpBLeft > 0) return -1; // is the A right endpoint colinear to segment B ?\n\n        var bCmpARight = b.comparePoint(a.rightSE.point);\n        if (bCmpARight !== 0) return bCmpARight; // colinear segments, consider the one with left-more\n        // left endpoint to be first (arbitrary?)\n\n        return -1;\n      } // is left endpoint of segment A the right-more?\n\n\n      if (alx > blx) {\n        if (aly < bly && aly < bry) return -1;\n        if (aly > bly && aly > bry) return 1; // is the A left endpoint colinear to segment B?\n\n        var bCmpALeft = b.comparePoint(a.leftSE.point);\n        if (bCmpALeft !== 0) return bCmpALeft; // is the B right endpoint colinear to segment A?\n\n        var aCmpBRight = a.comparePoint(b.rightSE.point);\n        if (aCmpBRight < 0) return 1;\n        if (aCmpBRight > 0) return -1; // colinear segments, consider the one with left-more\n        // left endpoint to be first (arbitrary?)\n\n        return 1;\n      } // if we get here, the two left endpoints are in the same\n      // vertical plane, ie alx === blx\n      // consider the lower left-endpoint to come first\n\n\n      if (aly < bly) return -1;\n      if (aly > bly) return 1; // left endpoints are identical\n      // check for colinearity by using the left-more right endpoint\n      // is the A right endpoint more left-more?\n\n      if (arx < brx) {\n        var _bCmpARight = b.comparePoint(a.rightSE.point);\n\n        if (_bCmpARight !== 0) return _bCmpARight;\n      } // is the B right endpoint more left-more?\n\n\n      if (arx > brx) {\n        var _aCmpBRight = a.comparePoint(b.rightSE.point);\n\n        if (_aCmpBRight < 0) return 1;\n        if (_aCmpBRight > 0) return -1;\n      }\n\n      if (arx !== brx) {\n        // are these two [almost] vertical segments with opposite orientation?\n        // if so, the one with the lower right endpoint comes first\n        var ay = ary - aly;\n        var ax = arx - alx;\n        var by = bry - bly;\n        var bx = brx - blx;\n        if (ay > ax && by < bx) return 1;\n        if (ay < ax && by > bx) return -1;\n      } // we have colinear segments with matching orientation\n      // consider the one with more left-more right endpoint to be first\n\n\n      if (arx > brx) return 1;\n      if (arx < brx) return -1; // if we get here, two two right endpoints are in the same\n      // vertical plane, ie arx === brx\n      // consider the lower right-endpoint to come first\n\n      if (ary < bry) return -1;\n      if (ary > bry) return 1; // right endpoints identical as well, so the segments are idential\n      // fall back on creation order as consistent tie-breaker\n\n      if (a.id < b.id) return -1;\n      if (a.id > b.id) return 1; // identical segment, ie a === b\n\n      return 0;\n    }\n    /* Warning: a reference to ringWindings input will be stored,\n     *  and possibly will be later modified */\n\n  }]);\n\n  function Segment(leftSE, rightSE, rings, windings) {\n    _classCallCheck(this, Segment);\n\n    this.id = ++segmentId;\n    this.leftSE = leftSE;\n    leftSE.segment = this;\n    leftSE.otherSE = rightSE;\n    this.rightSE = rightSE;\n    rightSE.segment = this;\n    rightSE.otherSE = leftSE;\n    this.rings = rings;\n    this.windings = windings; // left unset for performance, set later in algorithm\n    // this.ringOut, this.consumedBy, this.prev\n  }\n\n  _createClass(Segment, [{\n    key: \"replaceRightSE\",\n\n    /* When a segment is split, the rightSE is replaced with a new sweep event */\n    value: function replaceRightSE(newRightSE) {\n      this.rightSE = newRightSE;\n      this.rightSE.segment = this;\n      this.rightSE.otherSE = this.leftSE;\n      this.leftSE.otherSE = this.rightSE;\n    }\n  }, {\n    key: \"bbox\",\n    value: function bbox() {\n      var y1 = this.leftSE.point.y;\n      var y2 = this.rightSE.point.y;\n      return {\n        ll: {\n          x: this.leftSE.point.x,\n          y: y1 < y2 ? y1 : y2\n        },\n        ur: {\n          x: this.rightSE.point.x,\n          y: y1 > y2 ? y1 : y2\n        }\n      };\n    }\n    /* A vector from the left point to the right */\n\n  }, {\n    key: \"vector\",\n    value: function vector() {\n      return {\n        x: this.rightSE.point.x - this.leftSE.point.x,\n        y: this.rightSE.point.y - this.leftSE.point.y\n      };\n    }\n  }, {\n    key: \"isAnEndpoint\",\n    value: function isAnEndpoint(pt) {\n      return pt.x === this.leftSE.point.x && pt.y === this.leftSE.point.y || pt.x === this.rightSE.point.x && pt.y === this.rightSE.point.y;\n    }\n    /* Compare this segment with a point.\n     *\n     * A point P is considered to be colinear to a segment if there\n     * exists a distance D such that if we travel along the segment\n     * from one * endpoint towards the other a distance D, we find\n     * ourselves at point P.\n     *\n     * Return value indicates:\n     *\n     *   1: point lies above the segment (to the left of vertical)\n     *   0: point is colinear to segment\n     *  -1: point lies below the segment (to the right of vertical)\n     */\n\n  }, {\n    key: \"comparePoint\",\n    value: function comparePoint(point) {\n      if (this.isAnEndpoint(point)) return 0;\n      var lPt = this.leftSE.point;\n      var rPt = this.rightSE.point;\n      var v = this.vector(); // Exactly vertical segments.\n\n      if (lPt.x === rPt.x) {\n        if (point.x === lPt.x) return 0;\n        return point.x < lPt.x ? 1 : -1;\n      } // Nearly vertical segments with an intersection.\n      // Check to see where a point on the line with matching Y coordinate is.\n\n\n      var yDist = (point.y - lPt.y) / v.y;\n      var xFromYDist = lPt.x + yDist * v.x;\n      if (point.x === xFromYDist) return 0; // General case.\n      // Check to see where a point on the line with matching X coordinate is.\n\n      var xDist = (point.x - lPt.x) / v.x;\n      var yFromXDist = lPt.y + xDist * v.y;\n      if (point.y === yFromXDist) return 0;\n      return point.y < yFromXDist ? -1 : 1;\n    }\n    /**\n     * Given another segment, returns the first non-trivial intersection\n     * between the two segments (in terms of sweep line ordering), if it exists.\n     *\n     * A 'non-trivial' intersection is one that will cause one or both of the\n     * segments to be split(). As such, 'trivial' vs. 'non-trivial' intersection:\n     *\n     *   * endpoint of segA with endpoint of segB --> trivial\n     *   * endpoint of segA with point along segB --> non-trivial\n     *   * endpoint of segB with point along segA --> non-trivial\n     *   * point along segA with point along segB --> non-trivial\n     *\n     * If no non-trivial intersection exists, return null\n     * Else, return null.\n     */\n\n  }, {\n    key: \"getIntersection\",\n    value: function getIntersection(other) {\n      // If bboxes don't overlap, there can't be any intersections\n      var tBbox = this.bbox();\n      var oBbox = other.bbox();\n      var bboxOverlap = getBboxOverlap(tBbox, oBbox);\n      if (bboxOverlap === null) return null; // We first check to see if the endpoints can be considered intersections.\n      // This will 'snap' intersections to endpoints if possible, and will\n      // handle cases of colinearity.\n\n      var tlp = this.leftSE.point;\n      var trp = this.rightSE.point;\n      var olp = other.leftSE.point;\n      var orp = other.rightSE.point; // does each endpoint touch the other segment?\n      // note that we restrict the 'touching' definition to only allow segments\n      // to touch endpoints that lie forward from where we are in the sweep line pass\n\n      var touchesOtherLSE = isInBbox(tBbox, olp) && this.comparePoint(olp) === 0;\n      var touchesThisLSE = isInBbox(oBbox, tlp) && other.comparePoint(tlp) === 0;\n      var touchesOtherRSE = isInBbox(tBbox, orp) && this.comparePoint(orp) === 0;\n      var touchesThisRSE = isInBbox(oBbox, trp) && other.comparePoint(trp) === 0; // do left endpoints match?\n\n      if (touchesThisLSE && touchesOtherLSE) {\n        // these two cases are for colinear segments with matching left\n        // endpoints, and one segment being longer than the other\n        if (touchesThisRSE && !touchesOtherRSE) return trp;\n        if (!touchesThisRSE && touchesOtherRSE) return orp; // either the two segments match exactly (two trival intersections)\n        // or just on their left endpoint (one trivial intersection\n\n        return null;\n      } // does this left endpoint matches (other doesn't)\n\n\n      if (touchesThisLSE) {\n        // check for segments that just intersect on opposing endpoints\n        if (touchesOtherRSE) {\n          if (tlp.x === orp.x && tlp.y === orp.y) return null;\n        } // t-intersection on left endpoint\n\n\n        return tlp;\n      } // does other left endpoint matches (this doesn't)\n\n\n      if (touchesOtherLSE) {\n        // check for segments that just intersect on opposing endpoints\n        if (touchesThisRSE) {\n          if (trp.x === olp.x && trp.y === olp.y) return null;\n        } // t-intersection on left endpoint\n\n\n        return olp;\n      } // trivial intersection on right endpoints\n\n\n      if (touchesThisRSE && touchesOtherRSE) return null; // t-intersections on just one right endpoint\n\n      if (touchesThisRSE) return trp;\n      if (touchesOtherRSE) return orp; // None of our endpoints intersect. Look for a general intersection between\n      // infinite lines laid over the segments\n\n      var pt = intersection(tlp, this.vector(), olp, other.vector()); // are the segments parrallel? Note that if they were colinear with overlap,\n      // they would have an endpoint intersection and that case was already handled above\n\n      if (pt === null) return null; // is the intersection found between the lines not on the segments?\n\n      if (!isInBbox(bboxOverlap, pt)) return null; // round the the computed point if needed\n\n      return rounder.round(pt.x, pt.y);\n    }\n    /**\n     * Split the given segment into multiple segments on the given points.\n     *  * Each existing segment will retain its leftSE and a new rightSE will be\n     *    generated for it.\n     *  * A new segment will be generated which will adopt the original segment's\n     *    rightSE, and a new leftSE will be generated for it.\n     *  * If there are more than two points given to split on, new segments\n     *    in the middle will be generated with new leftSE and rightSE's.\n     *  * An array of the newly generated SweepEvents will be returned.\n     *\n     * Warning: input array of points is modified\n     */\n\n  }, {\n    key: \"split\",\n    value: function split(point) {\n      var newEvents = [];\n      var alreadyLinked = point.events !== undefined;\n      var newLeftSE = new SweepEvent(point, true);\n      var newRightSE = new SweepEvent(point, false);\n      var oldRightSE = this.rightSE;\n      this.replaceRightSE(newRightSE);\n      newEvents.push(newRightSE);\n      newEvents.push(newLeftSE);\n      var newSeg = new Segment(newLeftSE, oldRightSE, this.rings.slice(), this.windings.slice()); // when splitting a nearly vertical downward-facing segment,\n      // sometimes one of the resulting new segments is vertical, in which\n      // case its left and right events may need to be swapped\n\n      if (SweepEvent.comparePoints(newSeg.leftSE.point, newSeg.rightSE.point) > 0) {\n        newSeg.swapEvents();\n      }\n\n      if (SweepEvent.comparePoints(this.leftSE.point, this.rightSE.point) > 0) {\n        this.swapEvents();\n      } // in the point we just used to create new sweep events with was already\n      // linked to other events, we need to check if either of the affected\n      // segments should be consumed\n\n\n      if (alreadyLinked) {\n        newLeftSE.checkForConsuming();\n        newRightSE.checkForConsuming();\n      }\n\n      return newEvents;\n    }\n    /* Swap which event is left and right */\n\n  }, {\n    key: \"swapEvents\",\n    value: function swapEvents() {\n      var tmpEvt = this.rightSE;\n      this.rightSE = this.leftSE;\n      this.leftSE = tmpEvt;\n      this.leftSE.isLeft = true;\n      this.rightSE.isLeft = false;\n\n      for (var i = 0, iMax = this.windings.length; i < iMax; i++) {\n        this.windings[i] *= -1;\n      }\n    }\n    /* Consume another segment. We take their rings under our wing\n     * and mark them as consumed. Use for perfectly overlapping segments */\n\n  }, {\n    key: \"consume\",\n    value: function consume(other) {\n      var consumer = this;\n      var consumee = other;\n\n      while (consumer.consumedBy) {\n        consumer = consumer.consumedBy;\n      }\n\n      while (consumee.consumedBy) {\n        consumee = consumee.consumedBy;\n      }\n\n      var cmp = Segment.compare(consumer, consumee);\n      if (cmp === 0) return; // already consumed\n      // the winner of the consumption is the earlier segment\n      // according to sweep line ordering\n\n      if (cmp > 0) {\n        var tmp = consumer;\n        consumer = consumee;\n        consumee = tmp;\n      } // make sure a segment doesn't consume it's prev\n\n\n      if (consumer.prev === consumee) {\n        var _tmp = consumer;\n        consumer = consumee;\n        consumee = _tmp;\n      }\n\n      for (var i = 0, iMax = consumee.rings.length; i < iMax; i++) {\n        var ring = consumee.rings[i];\n        var winding = consumee.windings[i];\n        var index = consumer.rings.indexOf(ring);\n\n        if (index === -1) {\n          consumer.rings.push(ring);\n          consumer.windings.push(winding);\n        } else consumer.windings[index] += winding;\n      }\n\n      consumee.rings = null;\n      consumee.windings = null;\n      consumee.consumedBy = consumer; // mark sweep events consumed as to maintain ordering in sweep event queue\n\n      consumee.leftSE.consumedBy = consumer.leftSE;\n      consumee.rightSE.consumedBy = consumer.rightSE;\n    }\n    /* The first segment previous segment chain that is in the result */\n\n  }, {\n    key: \"prevInResult\",\n    value: function prevInResult() {\n      if (this._prevInResult !== undefined) return this._prevInResult;\n      if (!this.prev) this._prevInResult = null;else if (this.prev.isInResult()) this._prevInResult = this.prev;else this._prevInResult = this.prev.prevInResult();\n      return this._prevInResult;\n    }\n  }, {\n    key: \"beforeState\",\n    value: function beforeState() {\n      if (this._beforeState !== undefined) return this._beforeState;\n      if (!this.prev) this._beforeState = {\n        rings: [],\n        windings: [],\n        multiPolys: []\n      };else {\n        var seg = this.prev.consumedBy || this.prev;\n        this._beforeState = seg.afterState();\n      }\n      return this._beforeState;\n    }\n  }, {\n    key: \"afterState\",\n    value: function afterState() {\n      if (this._afterState !== undefined) return this._afterState;\n      var beforeState = this.beforeState();\n      this._afterState = {\n        rings: beforeState.rings.slice(0),\n        windings: beforeState.windings.slice(0),\n        multiPolys: []\n      };\n      var ringsAfter = this._afterState.rings;\n      var windingsAfter = this._afterState.windings;\n      var mpsAfter = this._afterState.multiPolys; // calculate ringsAfter, windingsAfter\n\n      for (var i = 0, iMax = this.rings.length; i < iMax; i++) {\n        var ring = this.rings[i];\n        var winding = this.windings[i];\n        var index = ringsAfter.indexOf(ring);\n\n        if (index === -1) {\n          ringsAfter.push(ring);\n          windingsAfter.push(winding);\n        } else windingsAfter[index] += winding;\n      } // calcualte polysAfter\n\n\n      var polysAfter = [];\n      var polysExclude = [];\n\n      for (var _i = 0, _iMax = ringsAfter.length; _i < _iMax; _i++) {\n        if (windingsAfter[_i] === 0) continue; // non-zero rule\n\n        var _ring = ringsAfter[_i];\n        var poly = _ring.poly;\n        if (polysExclude.indexOf(poly) !== -1) continue;\n        if (_ring.isExterior) polysAfter.push(poly);else {\n          if (polysExclude.indexOf(poly) === -1) polysExclude.push(poly);\n\n          var _index = polysAfter.indexOf(_ring.poly);\n\n          if (_index !== -1) polysAfter.splice(_index, 1);\n        }\n      } // calculate multiPolysAfter\n\n\n      for (var _i2 = 0, _iMax2 = polysAfter.length; _i2 < _iMax2; _i2++) {\n        var mp = polysAfter[_i2].multiPoly;\n        if (mpsAfter.indexOf(mp) === -1) mpsAfter.push(mp);\n      }\n\n      return this._afterState;\n    }\n    /* Is this segment part of the final result? */\n\n  }, {\n    key: \"isInResult\",\n    value: function isInResult() {\n      // if we've been consumed, we're not in the result\n      if (this.consumedBy) return false;\n      if (this._isInResult !== undefined) return this._isInResult;\n      var mpsBefore = this.beforeState().multiPolys;\n      var mpsAfter = this.afterState().multiPolys;\n\n      switch (operation.type) {\n        case 'union':\n          {\n            // UNION - included iff:\n            //  * On one side of us there is 0 poly interiors AND\n            //  * On the other side there is 1 or more.\n            var noBefores = mpsBefore.length === 0;\n            var noAfters = mpsAfter.length === 0;\n            this._isInResult = noBefores !== noAfters;\n            break;\n          }\n\n        case 'intersection':\n          {\n            // INTERSECTION - included iff:\n            //  * on one side of us all multipolys are rep. with poly interiors AND\n            //  * on the other side of us, not all multipolys are repsented\n            //    with poly interiors\n            var least;\n            var most;\n\n            if (mpsBefore.length < mpsAfter.length) {\n              least = mpsBefore.length;\n              most = mpsAfter.length;\n            } else {\n              least = mpsAfter.length;\n              most = mpsBefore.length;\n            }\n\n            this._isInResult = most === operation.numMultiPolys && least < most;\n            break;\n          }\n\n        case 'xor':\n          {\n            // XOR - included iff:\n            //  * the difference between the number of multipolys represented\n            //    with poly interiors on our two sides is an odd number\n            var diff = Math.abs(mpsBefore.length - mpsAfter.length);\n            this._isInResult = diff % 2 === 1;\n            break;\n          }\n\n        case 'difference':\n          {\n            // DIFFERENCE included iff:\n            //  * on exactly one side, we have just the subject\n            var isJustSubject = function isJustSubject(mps) {\n              return mps.length === 1 && mps[0].isSubject;\n            };\n\n            this._isInResult = isJustSubject(mpsBefore) !== isJustSubject(mpsAfter);\n            break;\n          }\n\n        default:\n          throw new Error(\"Unrecognized operation type found \".concat(operation.type));\n      }\n\n      return this._isInResult;\n    }\n  }], [{\n    key: \"fromRing\",\n    value: function fromRing(pt1, pt2, ring) {\n      var leftPt, rightPt, winding; // ordering the two points according to sweep line ordering\n\n      var cmpPts = SweepEvent.comparePoints(pt1, pt2);\n\n      if (cmpPts < 0) {\n        leftPt = pt1;\n        rightPt = pt2;\n        winding = 1;\n      } else if (cmpPts > 0) {\n        leftPt = pt2;\n        rightPt = pt1;\n        winding = -1;\n      } else throw new Error(\"Tried to create degenerate segment at [\".concat(pt1.x, \", \").concat(pt1.y, \"]\"));\n\n      var leftSE = new SweepEvent(leftPt, true);\n      var rightSE = new SweepEvent(rightPt, false);\n      return new Segment(leftSE, rightSE, [ring], [winding]);\n    }\n  }]);\n\n  return Segment;\n}();\n\nvar RingIn = /*#__PURE__*/function () {\n  function RingIn(geomRing, poly, isExterior) {\n    _classCallCheck(this, RingIn);\n\n    if (!Array.isArray(geomRing) || geomRing.length === 0) {\n      throw new Error('Input geometry is not a valid Polygon or MultiPolygon');\n    }\n\n    this.poly = poly;\n    this.isExterior = isExterior;\n    this.segments = [];\n\n    if (typeof geomRing[0][0] !== 'number' || typeof geomRing[0][1] !== 'number') {\n      throw new Error('Input geometry is not a valid Polygon or MultiPolygon');\n    }\n\n    var firstPoint = rounder.round(geomRing[0][0], geomRing[0][1]);\n    this.bbox = {\n      ll: {\n        x: firstPoint.x,\n        y: firstPoint.y\n      },\n      ur: {\n        x: firstPoint.x,\n        y: firstPoint.y\n      }\n    };\n    var prevPoint = firstPoint;\n\n    for (var i = 1, iMax = geomRing.length; i < iMax; i++) {\n      if (typeof geomRing[i][0] !== 'number' || typeof geomRing[i][1] !== 'number') {\n        throw new Error('Input geometry is not a valid Polygon or MultiPolygon');\n      }\n\n      var point = rounder.round(geomRing[i][0], geomRing[i][1]); // skip repeated points\n\n      if (point.x === prevPoint.x && point.y === prevPoint.y) continue;\n      this.segments.push(Segment.fromRing(prevPoint, point, this));\n      if (point.x < this.bbox.ll.x) this.bbox.ll.x = point.x;\n      if (point.y < this.bbox.ll.y) this.bbox.ll.y = point.y;\n      if (point.x > this.bbox.ur.x) this.bbox.ur.x = point.x;\n      if (point.y > this.bbox.ur.y) this.bbox.ur.y = point.y;\n      prevPoint = point;\n    } // add segment from last to first if last is not the same as first\n\n\n    if (firstPoint.x !== prevPoint.x || firstPoint.y !== prevPoint.y) {\n      this.segments.push(Segment.fromRing(prevPoint, firstPoint, this));\n    }\n  }\n\n  _createClass(RingIn, [{\n    key: \"getSweepEvents\",\n    value: function getSweepEvents() {\n      var sweepEvents = [];\n\n      for (var i = 0, iMax = this.segments.length; i < iMax; i++) {\n        var segment = this.segments[i];\n        sweepEvents.push(segment.leftSE);\n        sweepEvents.push(segment.rightSE);\n      }\n\n      return sweepEvents;\n    }\n  }]);\n\n  return RingIn;\n}();\nvar PolyIn = /*#__PURE__*/function () {\n  function PolyIn(geomPoly, multiPoly) {\n    _classCallCheck(this, PolyIn);\n\n    if (!Array.isArray(geomPoly)) {\n      throw new Error('Input geometry is not a valid Polygon or MultiPolygon');\n    }\n\n    this.exteriorRing = new RingIn(geomPoly[0], this, true); // copy by value\n\n    this.bbox = {\n      ll: {\n        x: this.exteriorRing.bbox.ll.x,\n        y: this.exteriorRing.bbox.ll.y\n      },\n      ur: {\n        x: this.exteriorRing.bbox.ur.x,\n        y: this.exteriorRing.bbox.ur.y\n      }\n    };\n    this.interiorRings = [];\n\n    for (var i = 1, iMax = geomPoly.length; i < iMax; i++) {\n      var ring = new RingIn(geomPoly[i], this, false);\n      if (ring.bbox.ll.x < this.bbox.ll.x) this.bbox.ll.x = ring.bbox.ll.x;\n      if (ring.bbox.ll.y < this.bbox.ll.y) this.bbox.ll.y = ring.bbox.ll.y;\n      if (ring.bbox.ur.x > this.bbox.ur.x) this.bbox.ur.x = ring.bbox.ur.x;\n      if (ring.bbox.ur.y > this.bbox.ur.y) this.bbox.ur.y = ring.bbox.ur.y;\n      this.interiorRings.push(ring);\n    }\n\n    this.multiPoly = multiPoly;\n  }\n\n  _createClass(PolyIn, [{\n    key: \"getSweepEvents\",\n    value: function getSweepEvents() {\n      var sweepEvents = this.exteriorRing.getSweepEvents();\n\n      for (var i = 0, iMax = this.interiorRings.length; i < iMax; i++) {\n        var ringSweepEvents = this.interiorRings[i].getSweepEvents();\n\n        for (var j = 0, jMax = ringSweepEvents.length; j < jMax; j++) {\n          sweepEvents.push(ringSweepEvents[j]);\n        }\n      }\n\n      return sweepEvents;\n    }\n  }]);\n\n  return PolyIn;\n}();\nvar MultiPolyIn = /*#__PURE__*/function () {\n  function MultiPolyIn(geom, isSubject) {\n    _classCallCheck(this, MultiPolyIn);\n\n    if (!Array.isArray(geom)) {\n      throw new Error('Input geometry is not a valid Polygon or MultiPolygon');\n    }\n\n    try {\n      // if the input looks like a polygon, convert it to a multipolygon\n      if (typeof geom[0][0][0] === 'number') geom = [geom];\n    } catch (ex) {// The input is either malformed or has empty arrays.\n      // In either case, it will be handled later on.\n    }\n\n    this.polys = [];\n    this.bbox = {\n      ll: {\n        x: Number.POSITIVE_INFINITY,\n        y: Number.POSITIVE_INFINITY\n      },\n      ur: {\n        x: Number.NEGATIVE_INFINITY,\n        y: Number.NEGATIVE_INFINITY\n      }\n    };\n\n    for (var i = 0, iMax = geom.length; i < iMax; i++) {\n      var poly = new PolyIn(geom[i], this);\n      if (poly.bbox.ll.x < this.bbox.ll.x) this.bbox.ll.x = poly.bbox.ll.x;\n      if (poly.bbox.ll.y < this.bbox.ll.y) this.bbox.ll.y = poly.bbox.ll.y;\n      if (poly.bbox.ur.x > this.bbox.ur.x) this.bbox.ur.x = poly.bbox.ur.x;\n      if (poly.bbox.ur.y > this.bbox.ur.y) this.bbox.ur.y = poly.bbox.ur.y;\n      this.polys.push(poly);\n    }\n\n    this.isSubject = isSubject;\n  }\n\n  _createClass(MultiPolyIn, [{\n    key: \"getSweepEvents\",\n    value: function getSweepEvents() {\n      var sweepEvents = [];\n\n      for (var i = 0, iMax = this.polys.length; i < iMax; i++) {\n        var polySweepEvents = this.polys[i].getSweepEvents();\n\n        for (var j = 0, jMax = polySweepEvents.length; j < jMax; j++) {\n          sweepEvents.push(polySweepEvents[j]);\n        }\n      }\n\n      return sweepEvents;\n    }\n  }]);\n\n  return MultiPolyIn;\n}();\n\nvar RingOut = /*#__PURE__*/function () {\n  _createClass(RingOut, null, [{\n    key: \"factory\",\n\n    /* Given the segments from the sweep line pass, compute & return a series\n     * of closed rings from all the segments marked to be part of the result */\n    value: function factory(allSegments) {\n      var ringsOut = [];\n\n      for (var i = 0, iMax = allSegments.length; i < iMax; i++) {\n        var segment = allSegments[i];\n        if (!segment.isInResult() || segment.ringOut) continue;\n        var prevEvent = null;\n        var event = segment.leftSE;\n        var nextEvent = segment.rightSE;\n        var events = [event];\n        var startingPoint = event.point;\n        var intersectionLEs = [];\n        /* Walk the chain of linked events to form a closed ring */\n\n        while (true) {\n          prevEvent = event;\n          event = nextEvent;\n          events.push(event);\n          /* Is the ring complete? */\n\n          if (event.point === startingPoint) break;\n\n          while (true) {\n            var availableLEs = event.getAvailableLinkedEvents();\n            /* Did we hit a dead end? This shouldn't happen. Indicates some earlier\n             * part of the algorithm malfunctioned... please file a bug report. */\n\n            if (availableLEs.length === 0) {\n              var firstPt = events[0].point;\n              var lastPt = events[events.length - 1].point;\n              throw new Error(\"Unable to complete output ring starting at [\".concat(firstPt.x, \",\") + \" \".concat(firstPt.y, \"]. Last matching segment found ends at\") + \" [\".concat(lastPt.x, \", \").concat(lastPt.y, \"].\"));\n            }\n            /* Only one way to go, so cotinue on the path */\n\n\n            if (availableLEs.length === 1) {\n              nextEvent = availableLEs[0].otherSE;\n              break;\n            }\n            /* We must have an intersection. Check for a completed loop */\n\n\n            var indexLE = null;\n\n            for (var j = 0, jMax = intersectionLEs.length; j < jMax; j++) {\n              if (intersectionLEs[j].point === event.point) {\n                indexLE = j;\n                break;\n              }\n            }\n            /* Found a completed loop. Cut that off and make a ring */\n\n\n            if (indexLE !== null) {\n              var intersectionLE = intersectionLEs.splice(indexLE)[0];\n              var ringEvents = events.splice(intersectionLE.index);\n              ringEvents.unshift(ringEvents[0].otherSE);\n              ringsOut.push(new RingOut(ringEvents.reverse()));\n              continue;\n            }\n            /* register the intersection */\n\n\n            intersectionLEs.push({\n              index: events.length,\n              point: event.point\n            });\n            /* Choose the left-most option to continue the walk */\n\n            var comparator = event.getLeftmostComparator(prevEvent);\n            nextEvent = availableLEs.sort(comparator)[0].otherSE;\n            break;\n          }\n        }\n\n        ringsOut.push(new RingOut(events));\n      }\n\n      return ringsOut;\n    }\n  }]);\n\n  function RingOut(events) {\n    _classCallCheck(this, RingOut);\n\n    this.events = events;\n\n    for (var i = 0, iMax = events.length; i < iMax; i++) {\n      events[i].segment.ringOut = this;\n    }\n\n    this.poly = null;\n  }\n\n  _createClass(RingOut, [{\n    key: \"getGeom\",\n    value: function getGeom() {\n      // Remove superfluous points (ie extra points along a straight line),\n      var prevPt = this.events[0].point;\n      var points = [prevPt];\n\n      for (var i = 1, iMax = this.events.length - 1; i < iMax; i++) {\n        var _pt = this.events[i].point;\n        var _nextPt = this.events[i + 1].point;\n        if (compareVectorAngles(_pt, prevPt, _nextPt) === 0) continue;\n        points.push(_pt);\n        prevPt = _pt;\n      } // ring was all (within rounding error of angle calc) colinear points\n\n\n      if (points.length === 1) return null; // check if the starting point is necessary\n\n      var pt = points[0];\n      var nextPt = points[1];\n      if (compareVectorAngles(pt, prevPt, nextPt) === 0) points.shift();\n      points.push(points[0]);\n      var step = this.isExteriorRing() ? 1 : -1;\n      var iStart = this.isExteriorRing() ? 0 : points.length - 1;\n      var iEnd = this.isExteriorRing() ? points.length : -1;\n      var orderedPoints = [];\n\n      for (var _i = iStart; _i != iEnd; _i += step) {\n        orderedPoints.push([points[_i].x, points[_i].y]);\n      }\n\n      return orderedPoints;\n    }\n  }, {\n    key: \"isExteriorRing\",\n    value: function isExteriorRing() {\n      if (this._isExteriorRing === undefined) {\n        var enclosing = this.enclosingRing();\n        this._isExteriorRing = enclosing ? !enclosing.isExteriorRing() : true;\n      }\n\n      return this._isExteriorRing;\n    }\n  }, {\n    key: \"enclosingRing\",\n    value: function enclosingRing() {\n      if (this._enclosingRing === undefined) {\n        this._enclosingRing = this._calcEnclosingRing();\n      }\n\n      return this._enclosingRing;\n    }\n    /* Returns the ring that encloses this one, if any */\n\n  }, {\n    key: \"_calcEnclosingRing\",\n    value: function _calcEnclosingRing() {\n      // start with the ealier sweep line event so that the prevSeg\n      // chain doesn't lead us inside of a loop of ours\n      var leftMostEvt = this.events[0];\n\n      for (var i = 1, iMax = this.events.length; i < iMax; i++) {\n        var evt = this.events[i];\n        if (SweepEvent.compare(leftMostEvt, evt) > 0) leftMostEvt = evt;\n      }\n\n      var prevSeg = leftMostEvt.segment.prevInResult();\n      var prevPrevSeg = prevSeg ? prevSeg.prevInResult() : null;\n\n      while (true) {\n        // no segment found, thus no ring can enclose us\n        if (!prevSeg) return null; // no segments below prev segment found, thus the ring of the prev\n        // segment must loop back around and enclose us\n\n        if (!prevPrevSeg) return prevSeg.ringOut; // if the two segments are of different rings, the ring of the prev\n        // segment must either loop around us or the ring of the prev prev\n        // seg, which would make us and the ring of the prev peers\n\n        if (prevPrevSeg.ringOut !== prevSeg.ringOut) {\n          if (prevPrevSeg.ringOut.enclosingRing() !== prevSeg.ringOut) {\n            return prevSeg.ringOut;\n          } else return prevSeg.ringOut.enclosingRing();\n        } // two segments are from the same ring, so this was a penisula\n        // of that ring. iterate downward, keep searching\n\n\n        prevSeg = prevPrevSeg.prevInResult();\n        prevPrevSeg = prevSeg ? prevSeg.prevInResult() : null;\n      }\n    }\n  }]);\n\n  return RingOut;\n}();\nvar PolyOut = /*#__PURE__*/function () {\n  function PolyOut(exteriorRing) {\n    _classCallCheck(this, PolyOut);\n\n    this.exteriorRing = exteriorRing;\n    exteriorRing.poly = this;\n    this.interiorRings = [];\n  }\n\n  _createClass(PolyOut, [{\n    key: \"addInterior\",\n    value: function addInterior(ring) {\n      this.interiorRings.push(ring);\n      ring.poly = this;\n    }\n  }, {\n    key: \"getGeom\",\n    value: function getGeom() {\n      var geom = [this.exteriorRing.getGeom()]; // exterior ring was all (within rounding error of angle calc) colinear points\n\n      if (geom[0] === null) return null;\n\n      for (var i = 0, iMax = this.interiorRings.length; i < iMax; i++) {\n        var ringGeom = this.interiorRings[i].getGeom(); // interior ring was all (within rounding error of angle calc) colinear points\n\n        if (ringGeom === null) continue;\n        geom.push(ringGeom);\n      }\n\n      return geom;\n    }\n  }]);\n\n  return PolyOut;\n}();\nvar MultiPolyOut = /*#__PURE__*/function () {\n  function MultiPolyOut(rings) {\n    _classCallCheck(this, MultiPolyOut);\n\n    this.rings = rings;\n    this.polys = this._composePolys(rings);\n  }\n\n  _createClass(MultiPolyOut, [{\n    key: \"getGeom\",\n    value: function getGeom() {\n      var geom = [];\n\n      for (var i = 0, iMax = this.polys.length; i < iMax; i++) {\n        var polyGeom = this.polys[i].getGeom(); // exterior ring was all (within rounding error of angle calc) colinear points\n\n        if (polyGeom === null) continue;\n        geom.push(polyGeom);\n      }\n\n      return geom;\n    }\n  }, {\n    key: \"_composePolys\",\n    value: function _composePolys(rings) {\n      var polys = [];\n\n      for (var i = 0, iMax = rings.length; i < iMax; i++) {\n        var ring = rings[i];\n        if (ring.poly) continue;\n        if (ring.isExteriorRing()) polys.push(new PolyOut(ring));else {\n          var enclosingRing = ring.enclosingRing();\n          if (!enclosingRing.poly) polys.push(new PolyOut(enclosingRing));\n          enclosingRing.poly.addInterior(ring);\n        }\n      }\n\n      return polys;\n    }\n  }]);\n\n  return MultiPolyOut;\n}();\n\n/**\n * NOTE:  We must be careful not to change any segments while\n *        they are in the SplayTree. AFAIK, there's no way to tell\n *        the tree to rebalance itself - thus before splitting\n *        a segment that's in the tree, we remove it from the tree,\n *        do the split, then re-insert it. (Even though splitting a\n *        segment *shouldn't* change its correct position in the\n *        sweep line tree, the reality is because of rounding errors,\n *        it sometimes does.)\n */\n\nvar SweepLine = /*#__PURE__*/function () {\n  function SweepLine(queue) {\n    var comparator = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : Segment.compare;\n\n    _classCallCheck(this, SweepLine);\n\n    this.queue = queue;\n    this.tree = new SplayTree(comparator);\n    this.segments = [];\n  }\n\n  _createClass(SweepLine, [{\n    key: \"process\",\n    value: function process(event) {\n      var segment = event.segment;\n      var newEvents = []; // if we've already been consumed by another segment,\n      // clean up our body parts and get out\n\n      if (event.consumedBy) {\n        if (event.isLeft) this.queue.remove(event.otherSE);else this.tree.remove(segment);\n        return newEvents;\n      }\n\n      var node = event.isLeft ? this.tree.insert(segment) : this.tree.find(segment);\n      if (!node) throw new Error(\"Unable to find segment #\".concat(segment.id, \" \") + \"[\".concat(segment.leftSE.point.x, \", \").concat(segment.leftSE.point.y, \"] -> \") + \"[\".concat(segment.rightSE.point.x, \", \").concat(segment.rightSE.point.y, \"] \") + 'in SweepLine tree. Please submit a bug report.');\n      var prevNode = node;\n      var nextNode = node;\n      var prevSeg = undefined;\n      var nextSeg = undefined; // skip consumed segments still in tree\n\n      while (prevSeg === undefined) {\n        prevNode = this.tree.prev(prevNode);\n        if (prevNode === null) prevSeg = null;else if (prevNode.key.consumedBy === undefined) prevSeg = prevNode.key;\n      } // skip consumed segments still in tree\n\n\n      while (nextSeg === undefined) {\n        nextNode = this.tree.next(nextNode);\n        if (nextNode === null) nextSeg = null;else if (nextNode.key.consumedBy === undefined) nextSeg = nextNode.key;\n      }\n\n      if (event.isLeft) {\n        // Check for intersections against the previous segment in the sweep line\n        var prevMySplitter = null;\n\n        if (prevSeg) {\n          var prevInter = prevSeg.getIntersection(segment);\n\n          if (prevInter !== null) {\n            if (!segment.isAnEndpoint(prevInter)) prevMySplitter = prevInter;\n\n            if (!prevSeg.isAnEndpoint(prevInter)) {\n              var newEventsFromSplit = this._splitSafely(prevSeg, prevInter);\n\n              for (var i = 0, iMax = newEventsFromSplit.length; i < iMax; i++) {\n                newEvents.push(newEventsFromSplit[i]);\n              }\n            }\n          }\n        } // Check for intersections against the next segment in the sweep line\n\n\n        var nextMySplitter = null;\n\n        if (nextSeg) {\n          var nextInter = nextSeg.getIntersection(segment);\n\n          if (nextInter !== null) {\n            if (!segment.isAnEndpoint(nextInter)) nextMySplitter = nextInter;\n\n            if (!nextSeg.isAnEndpoint(nextInter)) {\n              var _newEventsFromSplit = this._splitSafely(nextSeg, nextInter);\n\n              for (var _i = 0, _iMax = _newEventsFromSplit.length; _i < _iMax; _i++) {\n                newEvents.push(_newEventsFromSplit[_i]);\n              }\n            }\n          }\n        } // For simplicity, even if we find more than one intersection we only\n        // spilt on the 'earliest' (sweep-line style) of the intersections.\n        // The other intersection will be handled in a future process().\n\n\n        if (prevMySplitter !== null || nextMySplitter !== null) {\n          var mySplitter = null;\n          if (prevMySplitter === null) mySplitter = nextMySplitter;else if (nextMySplitter === null) mySplitter = prevMySplitter;else {\n            var cmpSplitters = SweepEvent.comparePoints(prevMySplitter, nextMySplitter);\n            mySplitter = cmpSplitters <= 0 ? prevMySplitter : nextMySplitter;\n          } // Rounding errors can cause changes in ordering,\n          // so remove afected segments and right sweep events before splitting\n\n          this.queue.remove(segment.rightSE);\n          newEvents.push(segment.rightSE);\n\n          var _newEventsFromSplit2 = segment.split(mySplitter);\n\n          for (var _i2 = 0, _iMax2 = _newEventsFromSplit2.length; _i2 < _iMax2; _i2++) {\n            newEvents.push(_newEventsFromSplit2[_i2]);\n          }\n        }\n\n        if (newEvents.length > 0) {\n          // We found some intersections, so re-do the current event to\n          // make sure sweep line ordering is totally consistent for later\n          // use with the segment 'prev' pointers\n          this.tree.remove(segment);\n          newEvents.push(event);\n        } else {\n          // done with left event\n          this.segments.push(segment);\n          segment.prev = prevSeg;\n        }\n      } else {\n        // event.isRight\n        // since we're about to be removed from the sweep line, check for\n        // intersections between our previous and next segments\n        if (prevSeg && nextSeg) {\n          var inter = prevSeg.getIntersection(nextSeg);\n\n          if (inter !== null) {\n            if (!prevSeg.isAnEndpoint(inter)) {\n              var _newEventsFromSplit3 = this._splitSafely(prevSeg, inter);\n\n              for (var _i3 = 0, _iMax3 = _newEventsFromSplit3.length; _i3 < _iMax3; _i3++) {\n                newEvents.push(_newEventsFromSplit3[_i3]);\n              }\n            }\n\n            if (!nextSeg.isAnEndpoint(inter)) {\n              var _newEventsFromSplit4 = this._splitSafely(nextSeg, inter);\n\n              for (var _i4 = 0, _iMax4 = _newEventsFromSplit4.length; _i4 < _iMax4; _i4++) {\n                newEvents.push(_newEventsFromSplit4[_i4]);\n              }\n            }\n          }\n        }\n\n        this.tree.remove(segment);\n      }\n\n      return newEvents;\n    }\n    /* Safely split a segment that is currently in the datastructures\n     * IE - a segment other than the one that is currently being processed. */\n\n  }, {\n    key: \"_splitSafely\",\n    value: function _splitSafely(seg, pt) {\n      // Rounding errors can cause changes in ordering,\n      // so remove afected segments and right sweep events before splitting\n      // removeNode() doesn't work, so have re-find the seg\n      // https://github.com/w8r/splay-tree/pull/5\n      this.tree.remove(seg);\n      var rightSE = seg.rightSE;\n      this.queue.remove(rightSE);\n      var newEvents = seg.split(pt);\n      newEvents.push(rightSE); // splitting can trigger consumption\n\n      if (seg.consumedBy === undefined) this.tree.insert(seg);\n      return newEvents;\n    }\n  }]);\n\n  return SweepLine;\n}();\n\nvar POLYGON_CLIPPING_MAX_QUEUE_SIZE = typeof process !== 'undefined' && process.env.POLYGON_CLIPPING_MAX_QUEUE_SIZE || 1000000;\nvar POLYGON_CLIPPING_MAX_SWEEPLINE_SEGMENTS = typeof process !== 'undefined' && process.env.POLYGON_CLIPPING_MAX_SWEEPLINE_SEGMENTS || 1000000;\nvar Operation = /*#__PURE__*/function () {\n  function Operation() {\n    _classCallCheck(this, Operation);\n  }\n\n  _createClass(Operation, [{\n    key: \"run\",\n    value: function run(type, geom, moreGeoms) {\n      operation.type = type;\n      rounder.reset();\n      /* Convert inputs to MultiPoly objects */\n\n      var multipolys = [new MultiPolyIn(geom, true)];\n\n      for (var i = 0, iMax = moreGeoms.length; i < iMax; i++) {\n        multipolys.push(new MultiPolyIn(moreGeoms[i], false));\n      }\n\n      operation.numMultiPolys = multipolys.length;\n      /* BBox optimization for difference operation\n       * If the bbox of a multipolygon that's part of the clipping doesn't\n       * intersect the bbox of the subject at all, we can just drop that\n       * multiploygon. */\n\n      if (operation.type === 'difference') {\n        // in place removal\n        var subject = multipolys[0];\n        var _i = 1;\n\n        while (_i < multipolys.length) {\n          if (getBboxOverlap(multipolys[_i].bbox, subject.bbox) !== null) _i++;else multipolys.splice(_i, 1);\n        }\n      }\n      /* BBox optimization for intersection operation\n       * If we can find any pair of multipolygons whose bbox does not overlap,\n       * then the result will be empty. */\n\n\n      if (operation.type === 'intersection') {\n        // TODO: this is O(n^2) in number of polygons. By sorting the bboxes,\n        //       it could be optimized to O(n * ln(n))\n        for (var _i2 = 0, _iMax = multipolys.length; _i2 < _iMax; _i2++) {\n          var mpA = multipolys[_i2];\n\n          for (var j = _i2 + 1, jMax = multipolys.length; j < jMax; j++) {\n            if (getBboxOverlap(mpA.bbox, multipolys[j].bbox) === null) return [];\n          }\n        }\n      }\n      /* Put segment endpoints in a priority queue */\n\n\n      var queue = new SplayTree(SweepEvent.compare);\n\n      for (var _i3 = 0, _iMax2 = multipolys.length; _i3 < _iMax2; _i3++) {\n        var sweepEvents = multipolys[_i3].getSweepEvents();\n\n        for (var _j = 0, _jMax = sweepEvents.length; _j < _jMax; _j++) {\n          queue.insert(sweepEvents[_j]);\n\n          if (queue.size > POLYGON_CLIPPING_MAX_QUEUE_SIZE) {\n            // prevents an infinite loop, an otherwise common manifestation of bugs\n            throw new Error('Infinite loop when putting segment endpoints in a priority queue ' + '(queue size too big). Please file a bug report.');\n          }\n        }\n      }\n      /* Pass the sweep line over those endpoints */\n\n\n      var sweepLine = new SweepLine(queue);\n      var prevQueueSize = queue.size;\n      var node = queue.pop();\n\n      while (node) {\n        var evt = node.key;\n\n        if (queue.size === prevQueueSize) {\n          // prevents an infinite loop, an otherwise common manifestation of bugs\n          var seg = evt.segment;\n          throw new Error(\"Unable to pop() \".concat(evt.isLeft ? 'left' : 'right', \" SweepEvent \") + \"[\".concat(evt.point.x, \", \").concat(evt.point.y, \"] from segment #\").concat(seg.id, \" \") + \"[\".concat(seg.leftSE.point.x, \", \").concat(seg.leftSE.point.y, \"] -> \") + \"[\".concat(seg.rightSE.point.x, \", \").concat(seg.rightSE.point.y, \"] from queue. \") + 'Please file a bug report.');\n        }\n\n        if (queue.size > POLYGON_CLIPPING_MAX_QUEUE_SIZE) {\n          // prevents an infinite loop, an otherwise common manifestation of bugs\n          throw new Error('Infinite loop when passing sweep line over endpoints ' + '(queue size too big). Please file a bug report.');\n        }\n\n        if (sweepLine.segments.length > POLYGON_CLIPPING_MAX_SWEEPLINE_SEGMENTS) {\n          // prevents an infinite loop, an otherwise common manifestation of bugs\n          throw new Error('Infinite loop when passing sweep line over endpoints ' + '(too many sweep line segments). Please file a bug report.');\n        }\n\n        var newEvents = sweepLine.process(evt);\n\n        for (var _i4 = 0, _iMax3 = newEvents.length; _i4 < _iMax3; _i4++) {\n          var _evt = newEvents[_i4];\n          if (_evt.consumedBy === undefined) queue.insert(_evt);\n        }\n\n        prevQueueSize = queue.size;\n        node = queue.pop();\n      } // free some memory we don't need anymore\n\n\n      rounder.reset();\n      /* Collect and compile segments we're keeping into a multipolygon */\n\n      var ringsOut = RingOut.factory(sweepLine.segments);\n      var result = new MultiPolyOut(ringsOut);\n      return result.getGeom();\n    }\n  }]);\n\n  return Operation;\n}(); // singleton available by import\n\nvar operation = new Operation();\n\nvar union = function union(geom) {\n  for (var _len = arguments.length, moreGeoms = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    moreGeoms[_key - 1] = arguments[_key];\n  }\n\n  return operation.run('union', geom, moreGeoms);\n};\n\nvar intersection$1 = function intersection(geom) {\n  for (var _len2 = arguments.length, moreGeoms = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n    moreGeoms[_key2 - 1] = arguments[_key2];\n  }\n\n  return operation.run('intersection', geom, moreGeoms);\n};\n\nvar xor = function xor(geom) {\n  for (var _len3 = arguments.length, moreGeoms = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++) {\n    moreGeoms[_key3 - 1] = arguments[_key3];\n  }\n\n  return operation.run('xor', geom, moreGeoms);\n};\n\nvar difference = function difference(subjectGeom) {\n  for (var _len4 = arguments.length, clippingGeoms = new Array(_len4 > 1 ? _len4 - 1 : 0), _key4 = 1; _key4 < _len4; _key4++) {\n    clippingGeoms[_key4 - 1] = arguments[_key4];\n  }\n\n  return operation.run('difference', subjectGeom, clippingGeoms);\n};\n\nvar index = {\n  union: union,\n  intersection: intersection$1,\n  xor: xor,\n  difference: difference\n};\n\nexport default index;\n", "import { multiPolygon, polygon, } from \"@turf/helpers\";\nimport { getGeom } from \"@turf/invariant\";\nimport polygonClipping from \"polygon-clipping\";\n/**\n * Takes two {@link Polygon|polygon} or {@link MultiPolygon|multi-polygon} geometries and\n * finds their polygonal intersection. If they don't intersect, returns null.\n *\n * @name intersect\n * @param {Feature<Polygon | MultiPolygon>} poly1 the first polygon or multipolygon\n * @param {Feature<Polygon | MultiPolygon>} poly2 the second polygon or multipolygon\n * @param {Object} [options={}] Optional Parameters\n * @param {Object} [options.properties={}] Translate GeoJSON Properties to Feature\n * @returns {Feature|null} returns a feature representing the area they share (either a {@link Polygon} or\n * {@link MultiPolygon}). If they do not share any area, returns `null`.\n * @example\n * var poly1 = turf.polygon([[\n *   [-122.801742, 45.48565],\n *   [-122.801742, 45.60491],\n *   [-122.584762, 45.60491],\n *   [-122.584762, 45.48565],\n *   [-122.801742, 45.48565]\n * ]]);\n *\n * var poly2 = turf.polygon([[\n *   [-122.520217, 45.535693],\n *   [-122.64038, 45.553967],\n *   [-122.720031, 45.526554],\n *   [-122.669906, 45.507309],\n *   [-122.723464, 45.446643],\n *   [-122.532577, 45.408574],\n *   [-122.487258, 45.477466],\n *   [-122.520217, 45.535693]\n * ]]);\n *\n * var intersection = turf.intersect(poly1, poly2);\n *\n * //addToMap\n * var addToMap = [poly1, poly2, intersection];\n */\nexport default function intersect(poly1, poly2, options) {\n    if (options === void 0) { options = {}; }\n    var geom1 = getGeom(poly1);\n    var geom2 = getGeom(poly2);\n    var intersection = polygonClipping.intersection(geom1.coordinates, geom2.coordinates);\n    if (intersection.length === 0)\n        return null;\n    if (intersection.length === 1)\n        return polygon(intersection[0], options.properties);\n    return multiPolygon(intersection, options.properties);\n}\n", "const utils = {\r\n  mergeArray(target, source) {\r\n    if (source.length < 5e4) target.push.apply(target, source)\r\n    else for (let i = 0, len = source.length; i < len; i += 1) target.push(source[i])\r\n  },\r\n  now:\r\n    Date.now ||\r\n    function () {\r\n      return new Date().getTime()\r\n    },\r\n  bind(fn, thisArg) {\r\n    return fn.bind\r\n      ? fn.bind(thisArg)\r\n      : function () {\r\n          return fn.apply(thisArg, arguments)\r\n        }\r\n  },\r\n  forEach(array, callback, thisArg) {\r\n    if (array.forEach) return array.forEach(callback, thisArg)\r\n    for (let i = 0, len = array.length; i < len; i++) callback.call(thisArg, array[i], i)\r\n  },\r\n  map(array, callback, thisArg) {\r\n    if (array.map) return array.map(callback, thisArg)\r\n    const newArr = []\r\n    for (let i = 0, len = array.length; i < len; i++) newArr[i] = callback.call(thisArg, array[i], i)\r\n    return newArr\r\n  },\r\n  merge(array1, array2) {\r\n    if (array2.length < 5e4) Array.prototype.push.apply(array1, array2)\r\n    else for (let ii = 0, iilen = array2.length; ii < iilen; ii += 1) array1.push(array2[ii])\r\n  },\r\n  arrayIndexOf(array, searchElement, fromIndex) {\r\n    if (array.indexOf) return array.indexOf(searchElement, fromIndex)\r\n    let k,\r\n      o = array,\r\n      len = o.length >>> 0\r\n    if (0 === len) return -1\r\n    const n = 0 | fromIndex\r\n    if (n >= len) return -1\r\n    k = Math.max(n >= 0 ? n : len - Math.abs(n), 0)\r\n    for (; k < len; ) {\r\n      if (k in o && o[k] === searchElement) return k\r\n      k++\r\n    }\r\n    return -1\r\n  },\r\n  extend(dst) {\r\n    dst || (dst = {})\r\n    return this.extendObjs(dst, Array.prototype.slice.call(arguments, 1))\r\n  },\r\n  extendObjs(dst, objs) {\r\n    dst || (dst = {})\r\n    for (let i = 0, len = objs.length; i < len; i++) {\r\n      const source = objs[i]\r\n      if (source) for (const prop in source) source.hasOwnProperty(prop) && (dst[prop] = source[prop])\r\n    }\r\n    return dst\r\n  },\r\n  debounce(func, wait, immediate) {\r\n    let timeout,\r\n      args,\r\n      context,\r\n      timestamp,\r\n      result,\r\n      later = function () {\r\n        const last = utils.now() - timestamp\r\n        if (last < wait && last >= 0) timeout = setTimeout(later, wait - last)\r\n        else {\r\n          timeout = null\r\n          if (!immediate) {\r\n            result = func.apply(context, args)\r\n            timeout || (context = args = null)\r\n          }\r\n        }\r\n      }\r\n    return function () {\r\n      context = this\r\n      args = arguments\r\n      timestamp = utils.now()\r\n      const callNow = immediate && !timeout\r\n      timeout || (timeout = setTimeout(later, wait))\r\n      if (callNow) {\r\n        result = func.apply(context, args)\r\n        context = args = null\r\n      }\r\n      return result\r\n    }\r\n  },\r\n  throttle(func, wait, options) {\r\n    let context,\r\n      args,\r\n      result,\r\n      timeout = null,\r\n      previous = 0\r\n    options || (options = {})\r\n    const later = function () {\r\n      previous = options.leading === !1 ? 0 : utils.now()\r\n      timeout = null\r\n      result = func.apply(context, args)\r\n      timeout || (context = args = null)\r\n    }\r\n    return function () {\r\n      const now = utils.now()\r\n      previous || options.leading !== !1 || (previous = now)\r\n      const remaining = wait - (now - previous)\r\n      context = this\r\n      args = arguments\r\n      if (remaining <= 0 || remaining > wait) {\r\n        if (timeout) {\r\n          clearTimeout(timeout)\r\n          timeout = null\r\n        }\r\n        previous = now\r\n        result = func.apply(context, args)\r\n        timeout || (context = args = null)\r\n      } else timeout || options.trailing === !1 || (timeout = setTimeout(later, remaining))\r\n      return result\r\n    }\r\n  },\r\n  escapeHtml(text) {\r\n    const map = {\r\n      '&': '&amp;',\r\n      '<': '&lt;',\r\n      '>': '&gt;',\r\n      '\"': '&quot;',\r\n      \"'\": '&#x27;',\r\n      '`': '&#x60;'\r\n    }\r\n    return `${text}`.replace(/[&<>\"']/g, function (m) {\r\n      return map[m]\r\n    })\r\n  }\r\n}\r\nexport { utils as default }\r\n", null, "export default function(x) {\n  return x;\n}\n", "import identity from \"./identity.js\";\n\nexport default function(transform) {\n  if (transform == null) return identity;\n  var x0,\n      y0,\n      kx = transform.scale[0],\n      ky = transform.scale[1],\n      dx = transform.translate[0],\n      dy = transform.translate[1];\n  return function(input, i) {\n    if (!i) x0 = y0 = 0;\n    var j = 2, n = input.length, output = new Array(n);\n    output[0] = (x0 += input[0]) * kx + dx;\n    output[1] = (y0 += input[1]) * ky + dy;\n    while (j < n) output[j] = input[j], ++j;\n    return output;\n  };\n}\n", "export default function(array, n) {\n  var t, j = array.length, i = j - n;\n  while (i < --j) t = array[i], array[i++] = array[j], array[j] = t;\n}\n", "import reverse from \"./reverse.js\";\nimport transform from \"./transform.js\";\n\nexport default function(topology, o) {\n  if (typeof o === \"string\") o = topology.objects[o];\n  return o.type === \"GeometryCollection\"\n      ? {type: \"FeatureCollection\", features: o.geometries.map(function(o) { return feature(topology, o); })}\n      : feature(topology, o);\n}\n\nfunction feature(topology, o) {\n  var id = o.id,\n      bbox = o.bbox,\n      properties = o.properties == null ? {} : o.properties,\n      geometry = object(topology, o);\n  return id == null && bbox == null ? {type: \"Feature\", properties: properties, geometry: geometry}\n      : bbox == null ? {type: \"Feature\", id: id, properties: properties, geometry: geometry}\n      : {type: \"Feature\", id: id, bbox: bbox, properties: properties, geometry: geometry};\n}\n\nexport function object(topology, o) {\n  var transformPoint = transform(topology.transform),\n      arcs = topology.arcs;\n\n  function arc(i, points) {\n    if (points.length) points.pop();\n    for (var a = arcs[i < 0 ? ~i : i], k = 0, n = a.length; k < n; ++k) {\n      points.push(transformPoint(a[k], k));\n    }\n    if (i < 0) reverse(points, n);\n  }\n\n  function point(p) {\n    return transformPoint(p);\n  }\n\n  function line(arcs) {\n    var points = [];\n    for (var i = 0, n = arcs.length; i < n; ++i) arc(arcs[i], points);\n    if (points.length < 2) points.push(points[0]); // This should never happen per the specification.\n    return points;\n  }\n\n  function ring(arcs) {\n    var points = line(arcs);\n    while (points.length < 4) points.push(points[0]); // This may happen if an arc has only two points.\n    return points;\n  }\n\n  function polygon(arcs) {\n    return arcs.map(ring);\n  }\n\n  function geometry(o) {\n    var type = o.type, coordinates;\n    switch (type) {\n      case \"GeometryCollection\": return {type: type, geometries: o.geometries.map(geometry)};\n      case \"Point\": coordinates = point(o.coordinates); break;\n      case \"MultiPoint\": coordinates = o.coordinates.map(point); break;\n      case \"LineString\": coordinates = line(o.arcs); break;\n      case \"MultiLineString\": coordinates = o.arcs.map(line); break;\n      case \"Polygon\": coordinates = polygon(o.arcs); break;\n      case \"MultiPolygon\": coordinates = o.arcs.map(polygon); break;\n      default: return null;\n    }\n    return {type: type, coordinates: coordinates};\n  }\n\n  return geometry(o);\n}\n", null, null, null, null, null, null, null, null, null, null, null, null], "names": ["n", "feature", "SplayTree", "polygonClipping", "f", "x", "h", "d", "y", "a", "m", "c", "I", "L", "g", "b", "w", "p", "i", "l", "_", "u", "M", "r", "F", "k", "v", "E", "D"], "mappings": ";;;;AAQA,MAAeA,GAAS,CAAA,WAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,IAAA,KAAA,CAAA,sFAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,GAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,GAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA;;ACRxB;AACA;AACA;AA4EA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,SAAO,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE;AACnD,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AAC7C,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC;AACnC,IAAI,IAAI,OAAO,CAAC,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,EAAE;AACxC,QAAQ,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC;AAC7B,KAAK;AACL,IAAI,IAAI,OAAO,CAAC,IAAI,EAAE;AACtB,QAAQ,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC;AACjC,KAAK;AACL,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,IAAI,EAAE,CAAC;AACvC,IAAI,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACzB,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AAgGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE;AAC1D,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AAC7C,IAAI,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,aAAa,GAAG,WAAW,EAAE,EAAE,GAAG,aAAa,CAAC,MAAM,EAAE,EAAE,EAAE,EAAE;AACnF,QAAQ,IAAI,IAAI,GAAG,aAAa,CAAC,EAAE,CAAC,CAAC;AACrC,QAAQ,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;AAC7B,YAAY,MAAM,IAAI,KAAK,CAAC,6DAA6D,CAAC,CAAC;AAC3F,SAAS;AACT,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC/D;AACA,YAAY,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACzD,gBAAgB,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAC;AAC/E,aAAa;AACb,SAAS;AACT,KAAK;AACL,IAAI,IAAI,IAAI,GAAG;AACf,QAAQ,IAAI,EAAE,SAAS;AACvB,QAAQ,WAAW,EAAE,WAAW;AAChC,KAAK,CAAC;AACN,IAAI,OAAOA,SAAO,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC9C,CAAC;AAkKD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,YAAY,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE;AAC/D,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AAC7C,IAAI,IAAI,IAAI,GAAG;AACf,QAAQ,IAAI,EAAE,cAAc;AAC5B,QAAQ,WAAW,EAAE,WAAW;AAChC,KAAK,CAAC;AACN,IAAI,OAAOA,SAAO,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;AAC9C;;AClQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,OAAO,CAAC,OAAO,EAAE;AACjC,IAAI,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS,EAAE;AACpC,QAAQ,OAAO,OAAO,CAAC,QAAQ,CAAC;AAChC,KAAK;AACL,IAAI,OAAO,OAAO,CAAC;AACnB;;AChMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,WAAW,CAAC,OAAO,EAAE,IAAI,EAAE;AACpC,IAAI,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrH,IAAI,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,OAAO,MAAM,KAAK,UAAU,KAAK,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,WAAW,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC7J,IAAI,SAAS,IAAI,CAAC,CAAC,EAAE,EAAE,OAAO,UAAU,CAAC,EAAE,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;AACtE,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;AACtB,QAAQ,IAAI,CAAC,EAAE,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC,CAAC;AACtE,QAAQ,OAAO,CAAC,EAAE,IAAI;AACtB,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;AACzK,YAAY,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AACpD,YAAY,QAAQ,EAAE,CAAC,CAAC,CAAC;AACzB,gBAAgB,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM;AAC9C,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;AACxE,gBAAgB,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS;AACjE,gBAAgB,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;AACjE,gBAAgB;AAChB,oBAAoB,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,EAAE;AAChI,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE;AAC1G,oBAAoB,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,EAAE;AACzF,oBAAoB,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,MAAM,EAAE;AACvF,oBAAoB,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,CAAC;AAC1C,oBAAoB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,SAAS;AAC3C,aAAa;AACb,YAAY,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AACvC,SAAS,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;AAClE,QAAQ,IAAI,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;AACzF,KAAK;AACL,CAAC;AACD;AACA,IAAI,IAAI,kBAAkB,YAAY;AACtC,IAAI,SAAS,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE;AAC7B,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;AACvB,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC,EAAE,CAAC,CAAC;AACL;AACA;AACA;AACA;AACA,SAAS,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE;AAC/B,IAAI,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACtC,CAAC;AACD;AACA;AACA;AACA,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;AACjC,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACjC,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC;AACd,IAAI,OAAO,IAAI,EAAE;AACjB,QAAQ,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACvC;AACA,QAAQ,IAAI,GAAG,GAAG,CAAC,EAAE;AACrB,YAAY,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI;AAC/B,gBAAgB,MAAM;AACtB;AACA,YAAY,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAC/C,gBAAgB,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAC/B,gBAAgB,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;AACjC,gBAAgB,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;AAC5B,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACtB,gBAAgB,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI;AACnC,oBAAoB,MAAM;AAC1B,aAAa;AACb,YAAY,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;AACvB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AACvB;AACA,SAAS;AACT,aAAa,IAAI,GAAG,GAAG,CAAC,EAAE;AAC1B,YAAY,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI;AAChC,gBAAgB,MAAM;AACtB;AACA,YAAY,IAAI,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AAChD,gBAAgB,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;AAChC,gBAAgB,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;AACjC,gBAAgB,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC;AAC3B,gBAAgB,CAAC,GAAG,CAAC,CAAC;AACtB,gBAAgB,IAAI,CAAC,CAAC,KAAK,KAAK,IAAI;AACpC,oBAAoB,MAAM;AAC1B,aAAa;AACb,YAAY,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC;AACxB,YAAY,CAAC,GAAG,CAAC,CAAC;AAClB,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;AACxB,SAAS;AACT;AACA,YAAY,MAAM;AAClB,KAAK;AACL;AACA,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;AACrB,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;AACrB,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;AACrB,IAAI,OAAO,CAAC,CAAC;AACb,CAAC;AACD,SAAS,MAAM,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,UAAU,EAAE;AACxC,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AACjC,IAAI,IAAI,CAAC,KAAK,IAAI,EAAE;AACpB,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtC,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;AAChC,IAAI,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACnC,IAAI,IAAI,GAAG,GAAG,CAAC,EAAE;AACjB,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AAC3B,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACvB,QAAQ,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;AACtB,KAAK;AACL,SAAS,IAAI,GAAG,IAAI,CAAC,EAAE;AACvB,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AAC7B,QAAQ,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AACtB,QAAQ,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;AACvB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD,SAAS,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,UAAU,EAAE;AACnC,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC;AACpB,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,EAAE;AACX,QAAQ,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;AACtC,QAAQ,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACzC,QAAQ,IAAI,GAAG,KAAK,CAAC,EAAE;AACvB,YAAY,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AAC1B,YAAY,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AAC5B,SAAS;AACT,aAAa,IAAI,GAAG,GAAG,CAAC,EAAE;AAC1B,YAAY,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AAC5B,YAAY,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;AAC3B,YAAY,IAAI,GAAG,CAAC,CAAC;AACrB,SAAS;AACT,aAAa;AACb,YAAY,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AAC1B,YAAY,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;AAC1B,YAAY,KAAK,GAAG,CAAC,CAAC;AACtB,SAAS;AACT,KAAK;AACL,IAAI,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC;AACxC,CAAC;AACD,SAAS,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE;AACxC,IAAI,IAAI,KAAK,KAAK,IAAI;AACtB,QAAQ,OAAO,IAAI,CAAC;AACpB,IAAI,IAAI,IAAI,KAAK,IAAI;AACrB,QAAQ,OAAO,KAAK,CAAC;AACrB,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAC/C,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;AACtB,IAAI,OAAO,KAAK,CAAC;AACjB,CAAC;AACD;AACA;AACA;AACA,SAAS,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,SAAS,EAAE;AACxD,IAAI,IAAI,IAAI,EAAE;AACd,QAAQ,GAAG,CAAC,EAAE,GAAG,MAAM,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAC/E,QAAQ,IAAI,MAAM,GAAG,MAAM,IAAI,MAAM,GAAG,MAAM,GAAG,MAAM,CAAC,CAAC;AACzD,QAAQ,IAAI,IAAI,CAAC,IAAI;AACrB,YAAY,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;AAC/D,QAAQ,IAAI,IAAI,CAAC,KAAK;AACtB,YAAY,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;AAC/D,KAAK;AACL,CAAC;AACD,IAAI,IAAI,kBAAkB,YAAY;AACtC,IAAI,SAAS,IAAI,CAAC,UAAU,EAAE;AAC9B,QAAQ,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE,EAAE,UAAU,GAAG,eAAe,CAAC,EAAE;AACpE,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACvB,QAAQ,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;AACtC,KAAK;AACL;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,GAAG,EAAE,IAAI,EAAE;AACjD,QAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;AACrB,QAAQ,OAAO,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC5E,KAAK,CAAC;AACN;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,UAAU,GAAG,EAAE,IAAI,EAAE;AAC9C,QAAQ,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACvC,QAAQ,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;AACjC,YAAY,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1C,YAAY,IAAI,CAAC,KAAK,EAAE,CAAC;AACzB,YAAY,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC9B,SAAS;AACT,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ,IAAI,CAAC,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AACnD,QAAQ,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACzC,QAAQ,IAAI,GAAG,KAAK,CAAC;AACrB,YAAY,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AAC3B,aAAa;AACb,YAAY,IAAI,GAAG,GAAG,CAAC,EAAE;AACzB,gBAAgB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;AACnC,gBAAgB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AAC/B,gBAAgB,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;AAC9B,aAAa;AACb,iBAAiB,IAAI,GAAG,GAAG,CAAC,EAAE;AAC9B,gBAAgB,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AACrC,gBAAgB,IAAI,CAAC,IAAI,GAAG,CAAC,CAAC;AAC9B,gBAAgB,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC;AAC/B,aAAa;AACb,YAAY,IAAI,CAAC,KAAK,EAAE,CAAC;AACzB,YAAY,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC9B,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC;AAC1B,KAAK,CAAC;AACN;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,GAAG,EAAE;AAC3C,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACrE,KAAK,CAAC;AACN;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE;AACzD,QAAQ,IAAI,CAAC,CAAC;AACd,QAAQ,IAAI,CAAC,KAAK,IAAI;AACtB,YAAY,OAAO,IAAI,CAAC;AACxB,QAAQ,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,CAAC,CAAC;AACpC,QAAQ,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC;AACvC,QAAQ,IAAI,GAAG,KAAK,CAAC,EAAE;AACvB,YAAY,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;AACjC,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;AAC5B,aAAa;AACb,iBAAiB;AACjB,gBAAgB,CAAC,GAAG,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACjD,gBAAgB,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;AAClC,aAAa;AACb,YAAY,IAAI,CAAC,KAAK,EAAE,CAAC;AACzB,YAAY,OAAO,CAAC,CAAC;AACrB,SAAS;AACT,QAAQ,OAAO,CAAC,CAAC;AACjB,KAAK,CAAC;AACN;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,YAAY;AACrC,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAC9B,QAAQ,IAAI,IAAI,EAAE;AAClB,YAAY,OAAO,IAAI,CAAC,IAAI;AAC5B,gBAAgB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjC,YAAY,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACvE,YAAY,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAC9E,YAAY,OAAO,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;AACtD,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,UAAU,GAAG,EAAE;AAC/C,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AACjC,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,OAAO,OAAO,EAAE;AACxB,YAAY,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AAChD,YAAY,IAAI,GAAG,KAAK,CAAC;AACzB,gBAAgB,OAAO,OAAO,CAAC;AAC/B,iBAAiB,IAAI,GAAG,GAAG,CAAC;AAC5B,gBAAgB,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;AACvC;AACA,gBAAgB,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;AACxC,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,GAAG,EAAE;AACzC,QAAQ,IAAI,IAAI,CAAC,KAAK,EAAE;AACxB,YAAY,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AAClE,YAAY,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC;AAC3D,gBAAgB,OAAO,IAAI,CAAC;AAC5B,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC,KAAK,CAAC;AAC1B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,GAAG,EAAE;AAC7C,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AACjC,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,OAAO,OAAO,EAAE;AACxB,YAAY,IAAI,GAAG,GAAG,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;AAChD,YAAY,IAAI,GAAG,KAAK,CAAC;AACzB,gBAAgB,OAAO,IAAI,CAAC;AAC5B,iBAAiB,IAAI,GAAG,GAAG,CAAC;AAC5B,gBAAgB,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;AACvC;AACA,gBAAgB,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;AACxC,SAAS;AACT,QAAQ,OAAO,KAAK,CAAC;AACrB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,OAAO,EAAE,GAAG,EAAE;AACrD,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AACjC,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;AACnB,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC;AACzB,QAAQ,OAAO,CAAC,IAAI,EAAE;AACtB,YAAY,IAAI,OAAO,KAAK,IAAI,EAAE;AAClC,gBAAgB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,gBAAgB,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;AACvC,aAAa;AACb,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,EAAE;AACpC,oBAAoB,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACtC,oBAAoB,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAC/C,oBAAoB,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;AAC5C,iBAAiB;AACjB;AACA,oBAAoB,IAAI,GAAG,IAAI,CAAC;AAChC,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,GAAG,EAAE;AACzD,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;AACnB,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC;AACvC,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAC9B,QAAQ,IAAI,GAAG,CAAC;AAChB,QAAQ,OAAO,CAAC,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,EAAE;AACvC,YAAY,IAAI,IAAI,EAAE;AACtB,gBAAgB,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC7B,gBAAgB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjC,aAAa;AACb,iBAAiB;AACjB,gBAAgB,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AAC/B,gBAAgB,GAAG,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AAC9C,gBAAgB,IAAI,GAAG,GAAG,CAAC,EAAE;AAC7B,oBAAoB,MAAM;AAC1B,iBAAiB;AACjB,qBAAqB,IAAI,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE;AACtD,oBAAoB,IAAI,EAAE,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC;AAC1C,wBAAwB,OAAO,IAAI,CAAC;AACpC,iBAAiB;AACjB,gBAAgB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAClC,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,YAAY;AACtC,QAAQ,IAAI,IAAI,GAAG,EAAE,CAAC;AACtB,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;AACnC,YAAY,IAAI,GAAG,GAAG,EAAE,CAAC,GAAG,CAAC;AAC7B,YAAY,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAClC,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AACxC,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC;AACxB,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;AACnC,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AAC/B,YAAY,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACrC,SAAS,CAAC,CAAC;AACX,QAAQ,OAAO,MAAM,CAAC;AACtB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,YAAY;AACrC,QAAQ,IAAI,IAAI,CAAC,KAAK;AACtB,YAAY,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AAChD,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,GAAG,YAAY;AACrC,QAAQ,IAAI,IAAI,CAAC,KAAK;AACtB,YAAY,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC;AAChD,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE;AAC1C,QAAQ,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;AAC7C,QAAQ,IAAI,CAAC;AACb,YAAY,OAAO,CAAC,CAAC,IAAI;AACzB,gBAAgB,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AAC3B,QAAQ,OAAO,CAAC,CAAC;AACjB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,UAAU,CAAC,EAAE;AAC1C,QAAQ,IAAI,CAAC,KAAK,KAAK,CAAC,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE;AAC7C,QAAQ,IAAI,CAAC;AACb,YAAY,OAAO,CAAC,CAAC,KAAK;AAC1B,gBAAgB,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC;AAC5B,QAAQ,OAAO,CAAC,CAAC;AACjB,KAAK,CAAC;AACN;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,GAAG,UAAU,KAAK,EAAE;AACzC,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC;AACjC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC;AACzB,QAAQ,IAAI,CAAC,GAAG,CAAC,CAAC;AAClB,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;AACnB,QAAQ,OAAO,CAAC,IAAI,EAAE;AACtB,YAAY,IAAI,OAAO,EAAE;AACzB,gBAAgB,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAChC,gBAAgB,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;AACvC,aAAa;AACb,iBAAiB;AACjB,gBAAgB,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC,oBAAoB,OAAO,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACtC,oBAAoB,IAAI,CAAC,KAAK,KAAK;AACnC,wBAAwB,OAAO,OAAO,CAAC;AACvC,oBAAoB,CAAC,EAAE,CAAC;AACxB,oBAAoB,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;AAC5C,iBAAiB;AACjB;AACA,oBAAoB,IAAI,GAAG,IAAI,CAAC;AAChC,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC,EAAE;AACvC,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAC9B,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC;AAC7B,QAAQ,IAAI,CAAC,CAAC,KAAK,EAAE;AACrB,YAAY,SAAS,GAAG,CAAC,CAAC,KAAK,CAAC;AAChC,YAAY,OAAO,SAAS,CAAC,IAAI;AACjC,gBAAgB,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC;AAC3C,YAAY,OAAO,SAAS,CAAC;AAC7B,SAAS;AACT,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ,OAAO,IAAI,EAAE;AACrB,YAAY,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAClD,YAAY,IAAI,GAAG,KAAK,CAAC;AACzB,gBAAgB,MAAM;AACtB,iBAAiB,IAAI,GAAG,GAAG,CAAC,EAAE;AAC9B,gBAAgB,SAAS,GAAG,IAAI,CAAC;AACjC,gBAAgB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjC,aAAa;AACb;AACA,gBAAgB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAClC,SAAS;AACT,QAAQ,OAAO,SAAS,CAAC;AACzB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,CAAC,EAAE;AACvC,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAC9B,QAAQ,IAAI,WAAW,GAAG,IAAI,CAAC;AAC/B,QAAQ,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,EAAE;AAC7B,YAAY,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC;AACjC,YAAY,OAAO,WAAW,CAAC,KAAK;AACpC,gBAAgB,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC;AAChD,YAAY,OAAO,WAAW,CAAC;AAC/B,SAAS;AACT,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ,OAAO,IAAI,EAAE;AACrB,YAAY,IAAI,GAAG,GAAG,UAAU,CAAC,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;AAClD,YAAY,IAAI,GAAG,KAAK,CAAC;AACzB,gBAAgB,MAAM;AACtB,iBAAiB,IAAI,GAAG,GAAG,CAAC;AAC5B,gBAAgB,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AACjC,iBAAiB;AACjB,gBAAgB,WAAW,GAAG,IAAI,CAAC;AACnC,gBAAgB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;AAClC,aAAa;AACb,SAAS;AACT,QAAQ,OAAO,WAAW,CAAC;AAC3B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,YAAY;AACvC,QAAQ,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC1B,QAAQ,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC;AACvB,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,YAAY;AACxC,QAAQ,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAClC,KAAK,CAAC;AACN;AACA;AACA;AACA,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,UAAU,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE;AAC3D,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC,EAAE,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAC/C,QAAQ,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,KAAK,CAAC,EAAE;AACpD,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;AAC/B,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AAC1C;AACA,QAAQ,IAAI,OAAO;AACnB,YAAY,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,UAAU,CAAC,CAAC;AACxD,QAAQ,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,EAAE;AACjC,YAAY,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AAC9D,YAAY,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AAC9B,SAAS;AACT,aAAa;AACb,YAAY,IAAI,UAAU,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,CAAC,CAAC;AAC7F,YAAY,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACrC,YAAY,IAAI,CAAC,KAAK,GAAG,eAAe,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;AACxE,SAAS;AACT,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,GAAG,YAAY,EAAE,OAAO,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,EAAE,CAAC;AACzE,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE;AAClD,QAAQ,GAAG,EAAE,YAAY,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;AAC/C,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,YAAY,EAAE,IAAI;AAC1B,KAAK,CAAC,CAAC;AACP,IAAI,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM,EAAE;AAClD,QAAQ,GAAG,EAAE,YAAY,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;AAC/C,QAAQ,UAAU,EAAE,IAAI;AACxB,QAAQ,YAAY,EAAE,IAAI;AAC1B,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,UAAU,SAAS,EAAE;AACnD,QAAQ,IAAI,SAAS,KAAK,KAAK,CAAC,EAAE,EAAE,SAAS,GAAG,UAAU,CAAC,EAAE,EAAE,OAAO,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE;AACzF,QAAQ,IAAI,GAAG,GAAG,EAAE,CAAC;AACrB,QAAQ,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC;AACxF,QAAQ,OAAO,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC5B,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,UAAU,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE;AAC5D,QAAQ,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;AAC1C,QAAQ,IAAI,EAAE,GAAG,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,UAAU,CAAC,EAAE,IAAI,GAAG,EAAE,CAAC,IAAI,EAAE,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC;AACtF,QAAQ,IAAI,UAAU,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC,EAAE;AACzC,YAAY,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AAC/D,SAAS;AACT,aAAa;AACb,YAAY,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;AAC7D,SAAS;AACT,QAAQ,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;AACpD,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,UAAU,GAAG,EAAE;AAC1C,QAAQ,OAAO,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;AACxD,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,YAAY;AAClD,QAAQ,IAAI,CAAC,CAAC;AACd,QAAQ,OAAO,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,EAAE;AAC/C,YAAY,QAAQ,EAAE,CAAC,KAAK;AAC5B,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;AACvC,oBAAoB,EAAE,CAAC,KAAK,GAAG,CAAC,CAAC;AACjC,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,IAAI,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;AACpD,oBAAoB,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;AAC5C,gBAAgB,KAAK,CAAC;AACtB,oBAAoB,EAAE,CAAC,IAAI,EAAE,CAAC;AAC9B,oBAAoB,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACrC,oBAAoB,OAAO,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;AAC5C,gBAAgB,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC,YAAY,CAAC;AAC9C,aAAa;AACb,SAAS,CAAC,CAAC;AACX,KAAK,CAAC;AACN,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC,EAAE,CAAC,CAAC;AACL,SAAS,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE;AACjD,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE;AAClB,QAAQ,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAClD,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;AAC/B,QAAQ,IAAI,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;AAClC,QAAQ,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;AACvC,QAAQ,IAAI,CAAC,IAAI,GAAG,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AAC/D,QAAQ,IAAI,CAAC,KAAK,GAAG,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAClE,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD,SAAS,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE;AAClC,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;AACjB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,KAAK;AACL,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;AAClB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,CAAC;AACD,SAAS,MAAM,CAAC,IAAI,EAAE;AACtB,IAAI,IAAI,OAAO,GAAG,IAAI,CAAC;AACvB,IAAI,IAAI,CAAC,GAAG,EAAE,CAAC;AACf,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC;AACrB,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;AACjB,IAAI,OAAO,CAAC,IAAI,EAAE;AAClB,QAAQ,IAAI,OAAO,EAAE;AACrB,YAAY,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC5B,YAAY,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;AACnC,SAAS;AACT,aAAa;AACb,YAAY,IAAI,CAAC,CAAC,MAAM,GAAG,CAAC,EAAE;AAC9B,gBAAgB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AAC/C,gBAAgB,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC;AACxC,aAAa;AACb;AACA,gBAAgB,IAAI,GAAG,IAAI,CAAC;AAC5B,SAAS;AACT,KAAK;AACL,IAAI,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;AAClB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,CAAC;AACD,SAAS,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE;AAC3C,IAAI,IAAI,IAAI,GAAG,GAAG,GAAG,KAAK,CAAC;AAC3B,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE;AAClB,QAAQ,IAAI,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC;AAClD,QAAQ,IAAI,IAAI,GAAG,eAAe,CAAC,IAAI,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACxD,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;AAC7B,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACzB,QAAQ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AACnC,QAAQ,IAAI,CAAC,KAAK,GAAG,eAAe,CAAC,IAAI,EAAE,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;AAC5D,QAAQ,OAAO,IAAI,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC;AAChB,CAAC;AACD,SAAS,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE,OAAO,EAAE;AACrC,IAAI,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,IAAI,IAAI,CAAC,GAAG,IAAI,CAAC;AACjB,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC;AAChB,IAAI,IAAI,EAAE,GAAG,EAAE,CAAC;AAChB,IAAI,OAAO,EAAE,KAAK,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE;AACvC,QAAQ,IAAI,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACzC,YAAY,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC;AACzB,SAAS;AACT,aAAa;AACb,YAAY,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;AACxB,YAAY,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC;AACzB,SAAS;AACT,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;AACnB,KAAK;AACL,IAAI,IAAI,EAAE,KAAK,IAAI,EAAE;AACrB,QAAQ,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;AACpB,KAAK;AACL,SAAS,IAAI,EAAE,KAAK,IAAI,EAAE;AAC1B,QAAQ,CAAC,CAAC,IAAI,GAAG,EAAE,CAAC;AACpB,KAAK;AACL,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC;AACrB,CAAC;AACD,SAAS,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE;AAClD,IAAI,IAAI,IAAI,IAAI,KAAK;AACrB,QAAQ,OAAO;AACf,IAAI,IAAI,KAAK,GAAG,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC;AACrB,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;AACtB,IAAI,OAAO,IAAI,EAAE;AACjB,QAAQ;AACR,YAAY,CAAC,EAAE,CAAC;AAChB,eAAe,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;AAC5C,QAAQ;AACR,YAAY,CAAC,EAAE,CAAC;AAChB,eAAe,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,EAAE;AAC5C,QAAQ,IAAI,CAAC,IAAI,CAAC;AAClB,YAAY,MAAM;AAClB,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1B,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1B,QAAQ,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACtB,QAAQ,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACxB,QAAQ,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC9B,QAAQ,MAAM,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC;AACxB,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;AACzC,IAAI,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,OAAO,CAAC,CAAC;AAC9C;;AC3pBA,SAAS,eAAe,CAAC,QAAQ,EAAE,WAAW,EAAE;AAChD,EAAE,IAAI,EAAE,QAAQ,YAAY,WAAW,CAAC,EAAE;AAC1C,IAAI,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAC;AAC7D,GAAG;AACH,CAAC;AACD;AACA,SAAS,iBAAiB,CAAC,MAAM,EAAE,KAAK,EAAE;AAC1C,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,IAAI,IAAI,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC9B,IAAI,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC,UAAU,IAAI,KAAK,CAAC;AAC3D,IAAI,UAAU,CAAC,YAAY,GAAG,IAAI,CAAC;AACnC,IAAI,IAAI,OAAO,IAAI,UAAU,EAAE,UAAU,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC1D,IAAI,MAAM,CAAC,cAAc,CAAC,MAAM,EAAE,UAAU,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;AAC9D,GAAG;AACH,CAAC;AACD;AACA,SAAS,YAAY,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;AAC5D,EAAE,IAAI,UAAU,EAAE,iBAAiB,CAAC,WAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;AACvE,EAAE,IAAI,WAAW,EAAE,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AAC/D,EAAE,OAAO,WAAW,CAAC;AACrB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,QAAQ,GAAG,SAAS,QAAQ,CAAC,IAAI,EAAE,KAAK,EAAE;AAC9C,EAAE,OAAO,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AACtG,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,IAAI,cAAc,GAAG,SAAS,cAAc,CAAC,EAAE,EAAE,EAAE,EAAE;AACrD;AACA,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;AACpG;AACA,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD;AACA,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD,EAAE,IAAI,MAAM,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACrD;AACA,EAAE,OAAO;AACT,IAAI,EAAE,EAAE;AACR,MAAM,CAAC,EAAE,MAAM;AACf,MAAM,CAAC,EAAE,MAAM;AACf,KAAK;AACL,IAAI,EAAE,EAAE;AACR,MAAM,CAAC,EAAE,MAAM;AACf,MAAM,CAAC,EAAE,MAAM;AACf,KAAK;AACL,GAAG,CAAC;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC;AAC7B;AACA,IAAI,OAAO,KAAK,SAAS,EAAE,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACtD,IAAI,UAAU,GAAG,OAAO,GAAG,OAAO,CAAC;AACnC;AACA;AACA,IAAI,GAAG,GAAG,SAAS,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE;AAC7B;AACA,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,EAAE;AACnC,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,OAAO,EAAE;AACrC,MAAM,OAAO,CAAC,CAAC;AACf,KAAK;AACL,GAAG;AACH;AACA;AACA,EAAE,IAAI,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;AACjB;AACA,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,UAAU,GAAG,CAAC,GAAG,CAAC,EAAE;AACpC,IAAI,OAAO,CAAC,CAAC;AACb,GAAG;AACH;AACA;AACA,EAAE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AACxB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,gBAAgB,YAAY;AACzC,EAAE,SAAS,SAAS,GAAG;AACvB,IAAI,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACrC;AACA,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;AACjB,GAAG;AACH;AACA,EAAE,YAAY,CAAC,SAAS,EAAE,CAAC;AAC3B,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,KAAK,EAAE,SAAS,KAAK,GAAG;AAC5B,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;AACzC,MAAM,IAAI,CAAC,QAAQ,GAAG,IAAI,YAAY,EAAE,CAAC;AACzC,KAAK;AACL,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,KAAK,EAAE,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;AAChC,MAAM,OAAO;AACb,QAAQ,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACjC,QAAQ,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AACjC,OAAO,CAAC;AACR,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,SAAS,CAAC;AACnB,CAAC,EAAE,CAAC;AACJ;AACA,IAAI,YAAY,gBAAgB,YAAY;AAC5C,EAAE,SAAS,YAAY,GAAG;AAC1B,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AACxC;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAIC,IAAS,EAAE,CAAC;AAChC;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAClB,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC;AAC9B,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,KAAK,EAAE,SAAS,KAAK,CAAC,KAAK,EAAE;AACjC,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACtC,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1C;AACA,MAAM,IAAI,QAAQ,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAClE,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAChC,QAAQ,OAAO,QAAQ,CAAC,GAAG,CAAC;AAC5B,OAAO;AACP;AACA,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC1C;AACA,MAAM,IAAI,QAAQ,KAAK,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;AAClE,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAChC,QAAQ,OAAO,QAAQ,CAAC,GAAG,CAAC;AAC5B,OAAO;AACP;AACA,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,YAAY,CAAC;AACtB,CAAC,EAAE,CAAC;AACJ;AACA;AACA,IAAI,OAAO,GAAG,IAAI,SAAS,EAAE,CAAC;AAC9B;AACA;AACA;AACA,IAAI,YAAY,GAAG,SAAS,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE;AAC/C,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACA;AACA,IAAI,UAAU,GAAG,SAAS,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE;AAC3C,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/B,CAAC,CAAC;AACF;AACA;AACA,IAAI,mBAAmB,GAAG,SAAS,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE;AAC/E,EAAE,IAAI,EAAE,GAAG;AACX,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AAC1B,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,IAAI,EAAE,GAAG;AACX,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AAC1B,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,IAAI,KAAK,GAAG,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACnC,EAAE,OAAO,GAAG,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACvB,CAAC,CAAC;AACF,IAAI,MAAM,GAAG,SAAS,MAAM,CAAC,CAAC,EAAE;AAChC,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AACrC,CAAC,CAAC;AACF;AACA;AACA,IAAI,WAAW,GAAG,SAAS,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE;AAC/D,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AAC1B,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AAC3B,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,OAAO,YAAY,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACtE,CAAC,CAAC;AACF;AACA;AACA,IAAI,aAAa,GAAG,SAAS,aAAa,CAAC,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE;AACnE,EAAE,IAAI,KAAK,GAAG;AACd,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AAC1B,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AAC1B,GAAG,CAAC;AACJ,EAAE,IAAI,MAAM,GAAG;AACf,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AAC3B,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC;AAC3B,GAAG,CAAC;AACJ,EAAE,OAAO,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACpE,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,IAAI,sBAAsB,GAAG,SAAS,sBAAsB,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;AACvE,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC;AAC7B,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACpC,IAAI,CAAC,EAAE,CAAC;AACR,GAAG,CAAC;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA,IAAI,oBAAoB,GAAG,SAAS,oBAAoB,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;AACnE,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC;AAC7B,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;AACpC,GAAG,CAAC;AACJ,CAAC,CAAC;AACF;AACA;AACA;AACA,IAAI,YAAY,GAAG,SAAS,YAAY,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,EAAE,EAAE;AAC3D;AACA;AACA;AACA,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,oBAAoB,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9D,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,oBAAoB,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAC9D,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,sBAAsB,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAChE,EAAE,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,sBAAsB,CAAC,GAAG,EAAE,EAAE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;AAChE;AACA;AACA;AACA,EAAE,IAAI,KAAK,GAAG,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AACnC,EAAE,IAAI,KAAK,IAAI,CAAC,EAAE,OAAO,IAAI,CAAC;AAC9B,EAAE,IAAI,EAAE,GAAG;AACX,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACpB,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC;AACpB,GAAG,CAAC;AACJ,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;AACxC,EAAE,IAAI,EAAE,GAAG,YAAY,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,KAAK,CAAC;AACxC;AACA,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC7B,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;AAC5B,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;AAC7B,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACxB,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;AACxB,EAAE,OAAO;AACT,IAAI,CAAC,EAAE,CAAC;AACR,IAAI,CAAC,EAAE,CAAC;AACR,GAAG,CAAC;AACJ,CAAC,CAAC;AACF;AACA,IAAI,UAAU,gBAAgB,YAAY;AAC1C,EAAE,YAAY,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC;AAClC,IAAI,GAAG,EAAE,SAAS;AAClB;AACA,IAAI,KAAK,EAAE,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;AAClC;AACA,MAAM,IAAI,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AAC7D,MAAM,IAAI,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK,CAAC;AACpC;AACA,MAAM,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AACzC;AACA,MAAM,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1D;AACA;AACA,MAAM,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,OAAO,CAAC,CAAC;AACnD,KAAK;AACL;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,eAAe;AACxB,IAAI,KAAK,EAAE,SAAS,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE;AAC5C,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AACnC,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAClC,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AACnC,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AAClC,MAAM,OAAO,CAAC,CAAC;AACf,KAAK;AACL;AACA,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,SAAS,UAAU,CAAC,KAAK,EAAE,MAAM,EAAE;AACrC,IAAI,eAAe,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AACtC;AACA,IAAI,IAAI,KAAK,CAAC,MAAM,KAAK,SAAS,EAAE,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACvF,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,GAAG;AACH;AACA,EAAE,YAAY,CAAC,UAAU,EAAE,CAAC;AAC5B,IAAI,GAAG,EAAE,MAAM;AACf,IAAI,KAAK,EAAE,SAAS,IAAI,CAAC,KAAK,EAAE;AAChC,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,IAAI,CAAC,KAAK,EAAE;AACtC,QAAQ,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;AAC/D,OAAO;AACP;AACA,MAAM,IAAI,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC;AAC3C;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAChE,QAAQ,IAAI,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACjC,QAAQ,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACpC,QAAQ,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;AAC/B,OAAO;AACP;AACA,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAC;AAC/B,KAAK;AACL;AACA;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,mBAAmB;AAC5B,IAAI,KAAK,EAAE,SAAS,iBAAiB,GAAG;AACxC;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;AAC/C;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AAC1C,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACxC,QAAQ,IAAI,IAAI,CAAC,OAAO,CAAC,UAAU,KAAK,SAAS,EAAE,SAAS;AAC5D;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;AAChD,UAAU,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AAC1C,UAAU,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,SAAS;AACtD,UAAU,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS;AAChF,UAAU,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC7C,SAAS;AACT,OAAO;AACP,KAAK;AACL,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,0BAA0B;AACnC,IAAI,KAAK,EAAE,SAAS,wBAAwB,GAAG;AAC/C;AACA,MAAM,IAAI,MAAM,GAAG,EAAE,CAAC;AACtB;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACtE,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACvC;AACA,QAAQ,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE;AAC9E,UAAU,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AAC3B,SAAS;AACT,OAAO;AACP;AACA,MAAM,OAAO,MAAM,CAAC;AACpB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,uBAAuB;AAChC,IAAI,KAAK,EAAE,SAAS,qBAAqB,CAAC,SAAS,EAAE;AACrD,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC;AACvB;AACA,MAAM,IAAI,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;AAC5B;AACA,MAAM,IAAI,SAAS,GAAG,SAAS,SAAS,CAAC,WAAW,EAAE;AACtD,QAAQ,IAAI,SAAS,GAAG,WAAW,CAAC,OAAO,CAAC;AAC5C,QAAQ,KAAK,CAAC,GAAG,CAAC,WAAW,EAAE;AAC/B,UAAU,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC;AAC1E,UAAU,MAAM,EAAE,aAAa,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC;AAC9E,SAAS,CAAC,CAAC;AACX,OAAO,CAAC;AACR;AACA,MAAM,OAAO,UAAU,CAAC,EAAE,CAAC,EAAE;AAC7B,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AACxC,QAAQ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC;AACxC;AACA,QAAQ,IAAI,UAAU,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACrC,YAAY,KAAK,GAAG,UAAU,CAAC,IAAI;AACnC,YAAY,OAAO,GAAG,UAAU,CAAC,MAAM,CAAC;AACxC;AACA,QAAQ,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACtC,YAAY,KAAK,GAAG,WAAW,CAAC,IAAI;AACpC,YAAY,OAAO,GAAG,WAAW,CAAC,MAAM,CAAC;AACzC;AACA;AACA,QAAQ,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;AACtC,UAAU,IAAI,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC,CAAC;AAC1C,UAAU,IAAI,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AAC3C,UAAU,OAAO,CAAC,CAAC;AACnB,SAAS;AACT;AACA;AACA,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,EAAE;AACpC,UAAU,IAAI,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC;AAC3C,UAAU,IAAI,OAAO,GAAG,OAAO,EAAE,OAAO,CAAC,CAAC;AAC1C,UAAU,OAAO,CAAC,CAAC;AACnB,SAAS;AACT;AACA;AACA,QAAQ,IAAI,KAAK,GAAG,KAAK,EAAE,OAAO,CAAC,CAAC,CAAC;AACrC,QAAQ,IAAI,KAAK,GAAG,KAAK,EAAE,OAAO,CAAC,CAAC;AACpC,QAAQ,OAAO,CAAC,CAAC;AACjB,OAAO,CAAC;AACR,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,UAAU,CAAC;AACpB,CAAC,EAAE,CAAC;AACJ;AACA;AACA;AACA,IAAI,SAAS,GAAG,CAAC,CAAC;AAClB;AACA,IAAI,OAAO,gBAAgB,YAAY;AACvC,EAAE,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC;AAC/B,IAAI,GAAG,EAAE,SAAS;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,KAAK,EAAE,SAAS,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE;AAClC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACjC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACjC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC;AACA,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC;AAC9B,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAC/B,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACjC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACjC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC,MAAM,IAAI,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAClC;AACA,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE;AACrB;AACA,QAAQ,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC;AAC7C,QAAQ,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAC9C;AACA,QAAQ,IAAI,SAAS,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACvD,QAAQ,IAAI,SAAS,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AACpC,QAAQ,IAAI,SAAS,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AACrC;AACA,QAAQ,IAAI,UAAU,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACzD,QAAQ,IAAI,UAAU,KAAK,CAAC,EAAE,OAAO,UAAU,CAAC;AAChD;AACA;AACA,QAAQ,OAAO,CAAC,CAAC,CAAC;AAClB,OAAO;AACP;AACA;AACA,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE;AACrB,QAAQ,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAC9C,QAAQ,IAAI,GAAG,GAAG,GAAG,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC;AAC7C;AACA,QAAQ,IAAI,SAAS,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACvD,QAAQ,IAAI,SAAS,KAAK,CAAC,EAAE,OAAO,SAAS,CAAC;AAC9C;AACA,QAAQ,IAAI,UAAU,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACzD,QAAQ,IAAI,UAAU,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AACrC,QAAQ,IAAI,UAAU,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AACtC;AACA;AACA,QAAQ,OAAO,CAAC,CAAC;AACjB,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAC/B,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC;AAC9B;AACA;AACA;AACA,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE;AACrB,QAAQ,IAAI,WAAW,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC1D;AACA,QAAQ,IAAI,WAAW,KAAK,CAAC,EAAE,OAAO,WAAW,CAAC;AAClD,OAAO;AACP;AACA;AACA,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE;AACrB,QAAQ,IAAI,WAAW,GAAG,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC1D;AACA,QAAQ,IAAI,WAAW,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC;AACtC,QAAQ,IAAI,WAAW,GAAG,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC;AACvC,OAAO;AACP;AACA,MAAM,IAAI,GAAG,KAAK,GAAG,EAAE;AACvB;AACA;AACA,QAAQ,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,QAAQ,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,QAAQ,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,QAAQ,IAAI,EAAE,GAAG,GAAG,GAAG,GAAG,CAAC;AAC3B,QAAQ,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC;AACzC,QAAQ,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AAC1C,OAAO;AACP;AACA;AACA;AACA,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC;AAC9B,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAC/B;AACA;AACA;AACA,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;AAC/B,MAAM,IAAI,GAAG,GAAG,GAAG,EAAE,OAAO,CAAC,CAAC;AAC9B;AACA;AACA,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC,CAAC;AACjC,MAAM,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,CAAC;AAChC;AACA,MAAM,OAAO,CAAC,CAAC;AACf,KAAK;AACL;AACA;AACA;AACA,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,SAAS,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;AACrD,IAAI,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACnC;AACA,IAAI,IAAI,CAAC,EAAE,GAAG,EAAE,SAAS,CAAC;AAC1B,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB,IAAI,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AAC1B,IAAI,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AAC7B,IAAI,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;AAC3B,IAAI,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAC3B,IAAI,OAAO,CAAC,OAAO,GAAG,MAAM,CAAC;AAC7B,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B;AACA,GAAG;AACH;AACA,EAAE,YAAY,CAAC,OAAO,EAAE,CAAC;AACzB,IAAI,GAAG,EAAE,gBAAgB;AACzB;AACA;AACA,IAAI,KAAK,EAAE,SAAS,cAAc,CAAC,UAAU,EAAE;AAC/C,MAAM,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC;AAChC,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AAClC,MAAM,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;AACzC,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,KAAK;AACL,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,MAAM;AACf,IAAI,KAAK,EAAE,SAAS,IAAI,GAAG;AAC3B,MAAM,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AACnC,MAAM,IAAI,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AACpC,MAAM,OAAO;AACb,QAAQ,EAAE,EAAE;AACZ,UAAU,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAChC,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC9B,SAAS;AACT,QAAQ,EAAE,EAAE;AACZ,UAAU,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjC,UAAU,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAC9B,SAAS;AACT,OAAO,CAAC;AACR,KAAK;AACL;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,QAAQ;AACjB,IAAI,KAAK,EAAE,SAAS,MAAM,GAAG;AAC7B,MAAM,OAAO;AACb,QAAQ,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrD,QAAQ,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACrD,OAAO,CAAC;AACR,KAAK;AACL,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,cAAc;AACvB,IAAI,KAAK,EAAE,SAAS,YAAY,CAAC,EAAE,EAAE;AACrC,MAAM,OAAO,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;AAC5I,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,cAAc;AACvB,IAAI,KAAK,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE;AACxC,MAAM,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,OAAO,CAAC,CAAC;AAC7C,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;AAClC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AACnC,MAAM,IAAI,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC;AAC5B;AACA,MAAM,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE;AAC3B,QAAQ,IAAI,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;AACxC,QAAQ,OAAO,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AACxC,OAAO;AACP;AACA;AACA;AACA,MAAM,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C,MAAM,IAAI,UAAU,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,UAAU,EAAE,OAAO,CAAC,CAAC;AAC3C;AACA;AACA,MAAM,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC1C,MAAM,IAAI,UAAU,GAAG,GAAG,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC;AAC3C,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,UAAU,EAAE,OAAO,CAAC,CAAC;AAC3C,MAAM,OAAO,KAAK,CAAC,CAAC,GAAG,UAAU,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;AAC3C,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,iBAAiB;AAC1B,IAAI,KAAK,EAAE,SAAS,eAAe,CAAC,KAAK,EAAE;AAC3C;AACA,MAAM,IAAI,KAAK,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;AAC9B,MAAM,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;AAC/B,MAAM,IAAI,WAAW,GAAG,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACrD,MAAM,IAAI,WAAW,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC;AAC5C;AACA;AACA;AACA,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;AAClC,MAAM,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AACnC,MAAM,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AACnC,MAAM,IAAI,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC;AACpC;AACA;AACA;AACA,MAAM,IAAI,eAAe,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjF,MAAM,IAAI,cAAc,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjF,MAAM,IAAI,eAAe,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjF,MAAM,IAAI,cAAc,GAAG,QAAQ,CAAC,KAAK,EAAE,GAAG,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AACjF;AACA,MAAM,IAAI,cAAc,IAAI,eAAe,EAAE;AAC7C;AACA;AACA,QAAQ,IAAI,cAAc,IAAI,CAAC,eAAe,EAAE,OAAO,GAAG,CAAC;AAC3D,QAAQ,IAAI,CAAC,cAAc,IAAI,eAAe,EAAE,OAAO,GAAG,CAAC;AAC3D;AACA;AACA,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP;AACA;AACA,MAAM,IAAI,cAAc,EAAE;AAC1B;AACA,QAAQ,IAAI,eAAe,EAAE;AAC7B,UAAU,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;AAC9D,SAAS;AACT;AACA;AACA,QAAQ,OAAO,GAAG,CAAC;AACnB,OAAO;AACP;AACA;AACA,MAAM,IAAI,eAAe,EAAE;AAC3B;AACA,QAAQ,IAAI,cAAc,EAAE;AAC5B,UAAU,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC;AAC9D,SAAS;AACT;AACA;AACA,QAAQ,OAAO,GAAG,CAAC;AACnB,OAAO;AACP;AACA;AACA,MAAM,IAAI,cAAc,IAAI,eAAe,EAAE,OAAO,IAAI,CAAC;AACzD;AACA,MAAM,IAAI,cAAc,EAAE,OAAO,GAAG,CAAC;AACrC,MAAM,IAAI,eAAe,EAAE,OAAO,GAAG,CAAC;AACtC;AACA;AACA,MAAM,IAAI,EAAE,GAAG,YAAY,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,GAAG,EAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC;AACrE;AACA;AACA,MAAM,IAAI,EAAE,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC;AACnC;AACA,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC,EAAE,OAAO,IAAI,CAAC;AAClD;AACA,MAAM,OAAO,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;AACvC,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,OAAO;AAChB,IAAI,KAAK,EAAE,SAAS,KAAK,CAAC,KAAK,EAAE;AACjC,MAAM,IAAI,SAAS,GAAG,EAAE,CAAC;AACzB,MAAM,IAAI,aAAa,GAAG,KAAK,CAAC,MAAM,KAAK,SAAS,CAAC;AACrD,MAAM,IAAI,SAAS,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAClD,MAAM,IAAI,UAAU,GAAG,IAAI,UAAU,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;AACpD,MAAM,IAAI,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC;AACpC,MAAM,IAAI,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;AACtC,MAAM,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AACjC,MAAM,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;AAChC,MAAM,IAAI,MAAM,GAAG,IAAI,OAAO,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC;AACjG;AACA;AACA;AACA,MAAM,IAAI,UAAU,CAAC,aAAa,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AACnF,QAAQ,MAAM,CAAC,UAAU,EAAE,CAAC;AAC5B,OAAO;AACP;AACA,MAAM,IAAI,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;AAC/E,QAAQ,IAAI,CAAC,UAAU,EAAE,CAAC;AAC1B,OAAO;AACP;AACA;AACA;AACA;AACA,MAAM,IAAI,aAAa,EAAE;AACzB,QAAQ,SAAS,CAAC,iBAAiB,EAAE,CAAC;AACtC,QAAQ,UAAU,CAAC,iBAAiB,EAAE,CAAC;AACvC,OAAO;AACP;AACA,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,YAAY;AACrB,IAAI,KAAK,EAAE,SAAS,UAAU,GAAG;AACjC,MAAM,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC;AAChC,MAAM,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC;AACjC,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AAC3B,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;AAChC,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC;AAClC;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAClE,QAAQ,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;AAC/B,OAAO;AACP,KAAK;AACL;AACA;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,SAAS;AAClB,IAAI,KAAK,EAAE,SAAS,OAAO,CAAC,KAAK,EAAE;AACnC,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC;AAC1B,MAAM,IAAI,QAAQ,GAAG,KAAK,CAAC;AAC3B;AACA,MAAM,OAAO,QAAQ,CAAC,UAAU,EAAE;AAClC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;AACvC,OAAO;AACP;AACA,MAAM,OAAO,QAAQ,CAAC,UAAU,EAAE;AAClC,QAAQ,QAAQ,GAAG,QAAQ,CAAC,UAAU,CAAC;AACvC,OAAO;AACP;AACA,MAAM,IAAI,GAAG,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACpD,MAAM,IAAI,GAAG,KAAK,CAAC,EAAE,OAAO;AAC5B;AACA;AACA;AACA,MAAM,IAAI,GAAG,GAAG,CAAC,EAAE;AACnB,QAAQ,IAAI,GAAG,GAAG,QAAQ,CAAC;AAC3B,QAAQ,QAAQ,GAAG,QAAQ,CAAC;AAC5B,QAAQ,QAAQ,GAAG,GAAG,CAAC;AACvB,OAAO;AACP;AACA;AACA,MAAM,IAAI,QAAQ,CAAC,IAAI,KAAK,QAAQ,EAAE;AACtC,QAAQ,IAAI,IAAI,GAAG,QAAQ,CAAC;AAC5B,QAAQ,QAAQ,GAAG,QAAQ,CAAC;AAC5B,QAAQ,QAAQ,GAAG,IAAI,CAAC;AACxB,OAAO;AACP;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACnE,QAAQ,IAAI,IAAI,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACrC,QAAQ,IAAI,OAAO,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC3C,QAAQ,IAAI,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AACjD;AACA,QAAQ,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAC1B,UAAU,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpC,UAAU,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC1C,SAAS,MAAM,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC;AACnD,OAAO;AACP;AACA,MAAM,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;AAC5B,MAAM,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC;AAC/B,MAAM,QAAQ,CAAC,UAAU,GAAG,QAAQ,CAAC;AACrC;AACA,MAAM,QAAQ,CAAC,MAAM,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAC;AACnD,MAAM,QAAQ,CAAC,OAAO,CAAC,UAAU,GAAG,QAAQ,CAAC,OAAO,CAAC;AACrD,KAAK;AACL;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,cAAc;AACvB,IAAI,KAAK,EAAE,SAAS,YAAY,GAAG;AACnC,MAAM,IAAI,IAAI,CAAC,aAAa,KAAK,SAAS,EAAE,OAAO,IAAI,CAAC,aAAa,CAAC;AACtE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,EAAE,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC;AACnK,MAAM,OAAO,IAAI,CAAC,aAAa,CAAC;AAChC,KAAK;AACL,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,aAAa;AACtB,IAAI,KAAK,EAAE,SAAS,WAAW,GAAG;AAClC,MAAM,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC;AACpE,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,YAAY,GAAG;AAC1C,QAAQ,KAAK,EAAE,EAAE;AACjB,QAAQ,QAAQ,EAAE,EAAE;AACpB,QAAQ,UAAU,EAAE,EAAE;AACtB,OAAO,CAAC,KAAK;AACb,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC;AACpD,QAAQ,IAAI,CAAC,YAAY,GAAG,GAAG,CAAC,UAAU,EAAE,CAAC;AAC7C,OAAO;AACP,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;AAC/B,KAAK;AACL,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,YAAY;AACrB,IAAI,KAAK,EAAE,SAAS,UAAU,GAAG;AACjC,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC;AAClE,MAAM,IAAI,WAAW,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;AAC3C,MAAM,IAAI,CAAC,WAAW,GAAG;AACzB,QAAQ,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC,QAAQ,QAAQ,EAAE,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;AAC/C,QAAQ,UAAU,EAAE,EAAE;AACtB,OAAO,CAAC;AACR,MAAM,IAAI,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;AAC9C,MAAM,IAAI,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;AACpD,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;AACjD;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC/D,QAAQ,IAAI,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AACjC,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvC,QAAQ,IAAI,KAAK,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;AAC7C;AACA,QAAQ,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AAC1B,UAAU,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAChC,UAAU,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtC,SAAS,MAAM,aAAa,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC;AAC/C,OAAO;AACP;AACA;AACA,MAAM,IAAI,UAAU,GAAG,EAAE,CAAC;AAC1B,MAAM,IAAI,YAAY,GAAG,EAAE,CAAC;AAC5B;AACA,MAAM,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE,EAAE;AACpE,QAAQ,IAAI,aAAa,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,SAAS;AAC9C;AACA,QAAQ,IAAI,KAAK,GAAG,UAAU,CAAC,EAAE,CAAC,CAAC;AACnC,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC;AAC9B,QAAQ,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,SAAS;AACxD,QAAQ,IAAI,KAAK,CAAC,UAAU,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK;AACzD,UAAU,IAAI,YAAY,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACzE;AACA,UAAU,IAAI,MAAM,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AACtD;AACA,UAAU,IAAI,MAAM,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAC1D,SAAS;AACT,OAAO;AACP;AACA;AACA,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE;AACzE,QAAQ,IAAI,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,SAAS,CAAC;AAC3C,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAC3D,OAAO;AACP;AACA,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC;AAC9B,KAAK;AACL;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,YAAY;AACrB,IAAI,KAAK,EAAE,SAAS,UAAU,GAAG;AACjC;AACA,MAAM,IAAI,IAAI,CAAC,UAAU,EAAE,OAAO,KAAK,CAAC;AACxC,MAAM,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC;AAClE,MAAM,IAAI,SAAS,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,UAAU,CAAC;AACpD,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC;AAClD;AACA,MAAM,QAAQ,SAAS,CAAC,IAAI;AAC5B,QAAQ,KAAK,OAAO;AACpB,UAAU;AACV;AACA;AACA;AACA,YAAY,IAAI,SAAS,GAAG,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC;AACnD,YAAY,IAAI,QAAQ,GAAG,QAAQ,CAAC,MAAM,KAAK,CAAC,CAAC;AACjD,YAAY,IAAI,CAAC,WAAW,GAAG,SAAS,KAAK,QAAQ,CAAC;AACtD,YAAY,MAAM;AAClB,WAAW;AACX;AACA,QAAQ,KAAK,cAAc;AAC3B,UAAU;AACV;AACA;AACA;AACA;AACA,YAAY,IAAI,KAAK,CAAC;AACtB,YAAY,IAAI,IAAI,CAAC;AACrB;AACA,YAAY,IAAI,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE;AACpD,cAAc,KAAK,GAAG,SAAS,CAAC,MAAM,CAAC;AACvC,cAAc,IAAI,GAAG,QAAQ,CAAC,MAAM,CAAC;AACrC,aAAa,MAAM;AACnB,cAAc,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC;AACtC,cAAc,IAAI,GAAG,SAAS,CAAC,MAAM,CAAC;AACtC,aAAa;AACb;AACA,YAAY,IAAI,CAAC,WAAW,GAAG,IAAI,KAAK,SAAS,CAAC,aAAa,IAAI,KAAK,GAAG,IAAI,CAAC;AAChF,YAAY,MAAM;AAClB,WAAW;AACX;AACA,QAAQ,KAAK,KAAK;AAClB,UAAU;AACV;AACA;AACA;AACA,YAAY,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC;AACpE,YAAY,IAAI,CAAC,WAAW,GAAG,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9C,YAAY,MAAM;AAClB,WAAW;AACX;AACA,QAAQ,KAAK,YAAY;AACzB,UAAU;AACV;AACA;AACA,YAAY,IAAI,aAAa,GAAG,SAAS,aAAa,CAAC,GAAG,EAAE;AAC5D,cAAc,OAAO,GAAG,CAAC,MAAM,KAAK,CAAC,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;AAC1D,aAAa,CAAC;AACd;AACA,YAAY,IAAI,CAAC,WAAW,GAAG,aAAa,CAAC,SAAS,CAAC,KAAK,aAAa,CAAC,QAAQ,CAAC,CAAC;AACpF,YAAY,MAAM;AAClB,WAAW;AACX;AACA,QAAQ;AACR,UAAU,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC;AACvF,OAAO;AACP;AACA,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC,EAAE,CAAC;AACP,IAAI,GAAG,EAAE,UAAU;AACnB,IAAI,KAAK,EAAE,SAAS,QAAQ,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE;AAC7C,MAAM,IAAI,MAAM,EAAE,OAAO,EAAE,OAAO,CAAC;AACnC;AACA,MAAM,IAAI,MAAM,GAAG,UAAU,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;AACtD;AACA,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE;AACtB,QAAQ,MAAM,GAAG,GAAG,CAAC;AACrB,QAAQ,OAAO,GAAG,GAAG,CAAC;AACtB,QAAQ,OAAO,GAAG,CAAC,CAAC;AACpB,OAAO,MAAM,IAAI,MAAM,GAAG,CAAC,EAAE;AAC7B,QAAQ,MAAM,GAAG,GAAG,CAAC;AACrB,QAAQ,OAAO,GAAG,GAAG,CAAC;AACtB,QAAQ,OAAO,GAAG,CAAC,CAAC,CAAC;AACrB,OAAO,MAAM,MAAM,IAAI,KAAK,CAAC,yCAAyC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC;AAC/G;AACA,MAAM,IAAI,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AAChD,MAAM,IAAI,OAAO,GAAG,IAAI,UAAU,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AACnD,MAAM,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;AAC7D,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC,EAAE,CAAC;AACJ;AACA,IAAI,MAAM,gBAAgB,YAAY;AACtC,EAAE,SAAS,MAAM,CAAC,QAAQ,EAAE,IAAI,EAAE,UAAU,EAAE;AAC9C,IAAI,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAClC;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3D,MAAM,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AAC/E,KAAK;AACL;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,IAAI,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;AACjC,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB;AACA,IAAI,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AAClF,MAAM,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AAC/E,KAAK;AACL;AACA,IAAI,IAAI,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACnE,IAAI,IAAI,CAAC,IAAI,GAAG;AAChB,MAAM,EAAE,EAAE;AACV,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC;AACvB,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC;AACvB,OAAO;AACP,MAAM,EAAE,EAAE;AACV,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC;AACvB,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC;AACvB,OAAO;AACP,KAAK,CAAC;AACN,IAAI,IAAI,SAAS,GAAG,UAAU,CAAC;AAC/B;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC3D,MAAM,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAI,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;AACpF,QAAQ,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AACjF,OAAO;AACP;AACA,MAAM,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAChE;AACA,MAAM,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE,SAAS;AACvE,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AACnE,MAAM,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAC7D,MAAM,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAC7D,MAAM,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAC7D,MAAM,IAAI,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;AAC7D,MAAM,SAAS,GAAG,KAAK,CAAC;AACxB,KAAK;AACL;AACA;AACA,IAAI,IAAI,UAAU,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,KAAK,SAAS,CAAC,CAAC,EAAE;AACtE,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC,CAAC;AACxE,KAAK;AACL,GAAG;AACH;AACA,EAAE,YAAY,CAAC,MAAM,EAAE,CAAC;AACxB,IAAI,GAAG,EAAE,gBAAgB;AACzB,IAAI,KAAK,EAAE,SAAS,cAAc,GAAG;AACrC,MAAM,IAAI,WAAW,GAAG,EAAE,CAAC;AAC3B;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAClE,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AACvC,QAAQ,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACzC,QAAQ,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1C,OAAO;AACP;AACA,MAAM,OAAO,WAAW,CAAC;AACzB,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC,EAAE,CAAC;AACJ,IAAI,MAAM,gBAAgB,YAAY;AACtC,EAAE,SAAS,MAAM,CAAC,QAAQ,EAAE,SAAS,EAAE;AACvC,IAAI,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AAClC;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;AAClC,MAAM,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AAC/E,KAAK;AACL;AACA,IAAI,IAAI,CAAC,YAAY,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;AAC5D;AACA,IAAI,IAAI,CAAC,IAAI,GAAG;AAChB,MAAM,EAAE,EAAE;AACV,QAAQ,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,QAAQ,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,OAAO;AACP,MAAM,EAAE,EAAE;AACV,QAAQ,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,QAAQ,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AACtC,OAAO;AACP,KAAK,CAAC;AACN,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC5B;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC3D,MAAM,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AACtD,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpC,KAAK;AACL;AACA,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,YAAY,CAAC,MAAM,EAAE,CAAC;AACxB,IAAI,GAAG,EAAE,gBAAgB;AACzB,IAAI,KAAK,EAAE,SAAS,cAAc,GAAG;AACrC,MAAM,IAAI,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE,CAAC;AAC3D;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACvE,QAAQ,IAAI,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;AACrE;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACtE,UAAU,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,SAAS;AACT,OAAO;AACP;AACA,MAAM,OAAO,WAAW,CAAC;AACzB,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC,EAAE,CAAC;AACJ,IAAI,WAAW,gBAAgB,YAAY;AAC3C,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE;AACxC,IAAI,eAAe,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;AACvC;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;AAC9B,MAAM,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAC;AAC/E,KAAK;AACL;AACA,IAAI,IAAI;AACR;AACA,MAAM,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;AAC3D,KAAK,CAAC,OAAO,EAAE,EAAE;AACjB;AACA,KAAK;AACL;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,EAAE,CAAC;AACpB,IAAI,IAAI,CAAC,IAAI,GAAG;AAChB,MAAM,EAAE,EAAE;AACV,QAAQ,CAAC,EAAE,MAAM,CAAC,iBAAiB;AACnC,QAAQ,CAAC,EAAE,MAAM,CAAC,iBAAiB;AACnC,OAAO;AACP,MAAM,EAAE,EAAE;AACV,QAAQ,CAAC,EAAE,MAAM,CAAC,iBAAiB;AACnC,QAAQ,CAAC,EAAE,MAAM,CAAC,iBAAiB;AACnC,OAAO;AACP,KAAK,CAAC;AACN;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACvD,MAAM,IAAI,IAAI,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;AAC3C,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,MAAM,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;AAC3E,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,KAAK;AACL;AACA,IAAI,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC/B,GAAG;AACH;AACA,EAAE,YAAY,CAAC,WAAW,EAAE,CAAC;AAC7B,IAAI,GAAG,EAAE,gBAAgB;AACzB,IAAI,KAAK,EAAE,SAAS,cAAc,GAAG;AACrC,MAAM,IAAI,WAAW,GAAG,EAAE,CAAC;AAC3B;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC/D,QAAQ,IAAI,eAAe,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;AAC7D;AACA,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACtE,UAAU,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;AAC/C,SAAS;AACT,OAAO;AACP;AACA,MAAM,OAAO,WAAW,CAAC;AACzB,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,WAAW,CAAC;AACrB,CAAC,EAAE,CAAC;AACJ;AACA,IAAI,OAAO,gBAAgB,YAAY;AACvC,EAAE,YAAY,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC;AAC/B,IAAI,GAAG,EAAE,SAAS;AAClB;AACA;AACA;AACA,IAAI,KAAK,EAAE,SAAS,OAAO,CAAC,WAAW,EAAE;AACzC,MAAM,IAAI,QAAQ,GAAG,EAAE,CAAC;AACxB;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,WAAW,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAChE,QAAQ,IAAI,OAAO,GAAG,WAAW,CAAC,CAAC,CAAC,CAAC;AACrC,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE,SAAS;AAC/D,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC;AAC7B,QAAQ,IAAI,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC;AACnC,QAAQ,IAAI,SAAS,GAAG,OAAO,CAAC,OAAO,CAAC;AACxC,QAAQ,IAAI,MAAM,GAAG,CAAC,KAAK,CAAC,CAAC;AAC7B,QAAQ,IAAI,aAAa,GAAG,KAAK,CAAC,KAAK,CAAC;AACxC,QAAQ,IAAI,eAAe,GAAG,EAAE,CAAC;AACjC;AACA;AACA,QAAQ,OAAO,IAAI,EAAE;AACrB,UAAU,SAAS,GAAG,KAAK,CAAC;AAC5B,UAAU,KAAK,GAAG,SAAS,CAAC;AAC5B,UAAU,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAC7B;AACA;AACA,UAAU,IAAI,KAAK,CAAC,KAAK,KAAK,aAAa,EAAE,MAAM;AACnD;AACA,UAAU,OAAO,IAAI,EAAE;AACvB,YAAY,IAAI,YAAY,GAAG,KAAK,CAAC,wBAAwB,EAAE,CAAC;AAChE;AACA;AACA;AACA,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3C,cAAc,IAAI,OAAO,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC5C,cAAc,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;AAC3D,cAAc,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,wCAAwC,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;AAC5N,aAAa;AACb;AACA;AACA;AACA,YAAY,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE;AAC3C,cAAc,SAAS,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AAClD,cAAc,MAAM;AACpB,aAAa;AACb;AACA;AACA;AACA,YAAY,IAAI,OAAO,GAAG,IAAI,CAAC;AAC/B;AACA,YAAY,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC1E,cAAc,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,EAAE;AAC5D,gBAAgB,OAAO,GAAG,CAAC,CAAC;AAC5B,gBAAgB,MAAM;AACtB,eAAe;AACf,aAAa;AACb;AACA;AACA;AACA,YAAY,IAAI,OAAO,KAAK,IAAI,EAAE;AAClC,cAAc,IAAI,cAAc,GAAG,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE,cAAc,IAAI,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;AACnE,cAAc,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;AACxD,cAAc,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,CAAC;AAC/D,cAAc,SAAS;AACvB,aAAa;AACb;AACA;AACA;AACA,YAAY,eAAe,CAAC,IAAI,CAAC;AACjC,cAAc,KAAK,EAAE,MAAM,CAAC,MAAM;AAClC,cAAc,KAAK,EAAE,KAAK,CAAC,KAAK;AAChC,aAAa,CAAC,CAAC;AACf;AACA;AACA,YAAY,IAAI,UAAU,GAAG,KAAK,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;AACpE,YAAY,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC;AACjE,YAAY,MAAM;AAClB,WAAW;AACX,SAAS;AACT;AACA,QAAQ,QAAQ,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC;AAC3C,OAAO;AACP;AACA,MAAM,OAAO,QAAQ,CAAC;AACtB,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,SAAS,OAAO,CAAC,MAAM,EAAE;AAC3B,IAAI,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACnC;AACA,IAAI,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;AACzB;AACA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACzD,MAAM,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC;AACvC,KAAK;AACL;AACA,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACrB,GAAG;AACH;AACA,EAAE,YAAY,CAAC,OAAO,EAAE,CAAC;AACzB,IAAI,GAAG,EAAE,SAAS;AAClB,IAAI,KAAK,EAAE,SAAS,OAAO,GAAG;AAC9B;AACA,MAAM,IAAI,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACxC,MAAM,IAAI,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;AAC5B;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACpE,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AACvC,QAAQ,IAAI,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC;AAC/C,QAAQ,IAAI,mBAAmB,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,EAAE,SAAS;AACtE,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;AACzB,QAAQ,MAAM,GAAG,GAAG,CAAC;AACrB,OAAO;AACP;AACA;AACA,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC;AAC3C;AACA,MAAM,IAAI,EAAE,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AACzB,MAAM,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,mBAAmB,CAAC,EAAE,EAAE,MAAM,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC;AACxE,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7B,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;AAChD,MAAM,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC;AACjE,MAAM,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;AAC5D,MAAM,IAAI,aAAa,GAAG,EAAE,CAAC;AAC7B;AACA,MAAM,KAAK,IAAI,EAAE,GAAG,MAAM,EAAE,EAAE,IAAI,IAAI,EAAE,EAAE,IAAI,IAAI,EAAE;AACpD,QAAQ,aAAa,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD,OAAO;AACP;AACA,MAAM,OAAO,aAAa,CAAC;AAC3B,KAAK;AACL,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,gBAAgB;AACzB,IAAI,KAAK,EAAE,SAAS,cAAc,GAAG;AACrC,MAAM,IAAI,IAAI,CAAC,eAAe,KAAK,SAAS,EAAE;AAC9C,QAAQ,IAAI,SAAS,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AAC7C,QAAQ,IAAI,CAAC,eAAe,GAAG,SAAS,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,GAAG,IAAI,CAAC;AAC9E,OAAO;AACP;AACA,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC;AAClC,KAAK;AACL,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,eAAe;AACxB,IAAI,KAAK,EAAE,SAAS,aAAa,GAAG;AACpC,MAAM,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE;AAC7C,QAAQ,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACxD,OAAO;AACP;AACA,MAAM,OAAO,IAAI,CAAC,cAAc,CAAC;AACjC,KAAK;AACL;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,oBAAoB;AAC7B,IAAI,KAAK,EAAE,SAAS,kBAAkB,GAAG;AACzC;AACA;AACA,MAAM,IAAI,WAAW,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACvC;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAChE,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;AACjC,QAAQ,IAAI,UAAU,CAAC,OAAO,CAAC,WAAW,EAAE,GAAG,CAAC,GAAG,CAAC,EAAE,WAAW,GAAG,GAAG,CAAC;AACxE,OAAO;AACP;AACA,MAAM,IAAI,OAAO,GAAG,WAAW,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;AACvD,MAAM,IAAI,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC;AAChE;AACA,MAAM,OAAO,IAAI,EAAE;AACnB;AACA,QAAQ,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC;AAClC;AACA;AACA,QAAQ,IAAI,CAAC,WAAW,EAAE,OAAO,OAAO,CAAC,OAAO,CAAC;AACjD;AACA;AACA;AACA,QAAQ,IAAI,WAAW,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,EAAE;AACrD,UAAU,IAAI,WAAW,CAAC,OAAO,CAAC,aAAa,EAAE,KAAK,OAAO,CAAC,OAAO,EAAE;AACvE,YAAY,OAAO,OAAO,CAAC,OAAO,CAAC;AACnC,WAAW,MAAM,OAAO,OAAO,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;AACxD,SAAS;AACT;AACA;AACA;AACA,QAAQ,OAAO,GAAG,WAAW,CAAC,YAAY,EAAE,CAAC;AAC7C,QAAQ,WAAW,GAAG,OAAO,GAAG,OAAO,CAAC,YAAY,EAAE,GAAG,IAAI,CAAC;AAC9D,OAAO;AACP,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC,EAAE,CAAC;AACJ,IAAI,OAAO,gBAAgB,YAAY;AACvC,EAAE,SAAS,OAAO,CAAC,YAAY,EAAE;AACjC,IAAI,eAAe,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AACnC;AACA,IAAI,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;AACrC,IAAI,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC;AAC7B,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;AAC5B,GAAG;AACH;AACA,EAAE,YAAY,CAAC,OAAO,EAAE,CAAC;AACzB,IAAI,GAAG,EAAE,aAAa;AACtB,IAAI,KAAK,EAAE,SAAS,WAAW,CAAC,IAAI,EAAE;AACtC,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACpC,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;AACvB,KAAK;AACL,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,SAAS;AAClB,IAAI,KAAK,EAAE,SAAS,OAAO,GAAG;AAC9B,MAAM,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,CAAC;AAC/C;AACA,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,OAAO,IAAI,CAAC;AACxC;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACvE,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AACvD;AACA,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE,SAAS;AACxC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5B,OAAO;AACP;AACA,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,OAAO,CAAC;AACjB,CAAC,EAAE,CAAC;AACJ,IAAI,YAAY,gBAAgB,YAAY;AAC5C,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE;AAC/B,IAAI,eAAe,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;AACxC;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AAC3C,GAAG;AACH;AACA,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC;AAC9B,IAAI,GAAG,EAAE,SAAS;AAClB,IAAI,KAAK,EAAE,SAAS,OAAO,GAAG;AAC9B,MAAM,IAAI,IAAI,GAAG,EAAE,CAAC;AACpB;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC/D,QAAQ,IAAI,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,CAAC;AAC/C;AACA,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE,SAAS;AACxC,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5B,OAAO;AACP;AACA,MAAM,OAAO,IAAI,CAAC;AAClB,KAAK;AACL,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,eAAe;AACxB,IAAI,KAAK,EAAE,SAAS,aAAa,CAAC,KAAK,EAAE;AACzC,MAAM,IAAI,KAAK,GAAG,EAAE,CAAC;AACrB;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC1D,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;AAC5B,QAAQ,IAAI,IAAI,CAAC,IAAI,EAAE,SAAS;AAChC,QAAQ,IAAI,IAAI,CAAC,cAAc,EAAE,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;AACtE,UAAU,IAAI,aAAa,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;AACnD,UAAU,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC;AAC1E,UAAU,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;AAC/C,SAAS;AACT,OAAO;AACP;AACA,MAAM,OAAO,KAAK,CAAC;AACnB,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,YAAY,CAAC;AACtB,CAAC,EAAE,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAI,SAAS,gBAAgB,YAAY;AACzC,EAAE,SAAS,SAAS,CAAC,KAAK,EAAE;AAC5B,IAAI,IAAI,UAAU,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,SAAS,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC;AACzG;AACA,IAAI,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACrC;AACA,IAAI,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACvB,IAAI,IAAI,CAAC,IAAI,GAAG,IAAIA,IAAS,CAAC,UAAU,CAAC,CAAC;AAC1C,IAAI,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;AACvB,GAAG;AACH;AACA,EAAE,YAAY,CAAC,SAAS,EAAE,CAAC;AAC3B,IAAI,GAAG,EAAE,SAAS;AAClB,IAAI,KAAK,EAAE,SAAS,OAAO,CAAC,KAAK,EAAE;AACnC,MAAM,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;AAClC,MAAM,IAAI,SAAS,GAAG,EAAE,CAAC;AACzB;AACA;AACA,MAAM,IAAI,KAAK,CAAC,UAAU,EAAE;AAC5B,QAAQ,IAAI,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAC1F,QAAQ,OAAO,SAAS,CAAC;AACzB,OAAO;AACP;AACA,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACpF,MAAM,IAAI,CAAC,IAAI,EAAE,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,MAAM,CAAC,OAAO,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,gDAAgD,CAAC,CAAC;AAC7S,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC;AAC1B,MAAM,IAAI,QAAQ,GAAG,IAAI,CAAC;AAC1B,MAAM,IAAI,OAAO,GAAG,SAAS,CAAC;AAC9B,MAAM,IAAI,OAAO,GAAG,SAAS,CAAC;AAC9B;AACA,MAAM,OAAO,OAAO,KAAK,SAAS,EAAE;AACpC,QAAQ,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5C,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,GAAG,CAAC,UAAU,KAAK,SAAS,EAAE,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC;AACrH,OAAO;AACP;AACA;AACA,MAAM,OAAO,OAAO,KAAK,SAAS,EAAE;AACpC,QAAQ,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAC5C,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC,KAAK,IAAI,QAAQ,CAAC,GAAG,CAAC,UAAU,KAAK,SAAS,EAAE,OAAO,GAAG,QAAQ,CAAC,GAAG,CAAC;AACrH,OAAO;AACP;AACA,MAAM,IAAI,KAAK,CAAC,MAAM,EAAE;AACxB;AACA,QAAQ,IAAI,cAAc,GAAG,IAAI,CAAC;AAClC;AACA,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,IAAI,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AAC3D;AACA,UAAU,IAAI,SAAS,KAAK,IAAI,EAAE;AAClC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,cAAc,GAAG,SAAS,CAAC;AAC7E;AACA,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;AAClD,cAAc,IAAI,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC7E;AACA,cAAc,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,kBAAkB,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC/E,gBAAgB,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC,CAAC;AACtD,eAAe;AACf,aAAa;AACb,WAAW;AACX,SAAS;AACT;AACA;AACA,QAAQ,IAAI,cAAc,GAAG,IAAI,CAAC;AAClC;AACA,QAAQ,IAAI,OAAO,EAAE;AACrB,UAAU,IAAI,SAAS,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AAC3D;AACA,UAAU,IAAI,SAAS,KAAK,IAAI,EAAE;AAClC,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE,cAAc,GAAG,SAAS,CAAC;AAC7E;AACA,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;AAClD,cAAc,IAAI,mBAAmB,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;AAC9E;AACA,cAAc,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,KAAK,GAAG,mBAAmB,CAAC,MAAM,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE,EAAE;AACrF,gBAAgB,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC,CAAC;AACxD,eAAe;AACf,aAAa;AACb,WAAW;AACX,SAAS;AACT;AACA;AACA;AACA;AACA,QAAQ,IAAI,cAAc,KAAK,IAAI,IAAI,cAAc,KAAK,IAAI,EAAE;AAChE,UAAU,IAAI,UAAU,GAAG,IAAI,CAAC;AAChC,UAAU,IAAI,cAAc,KAAK,IAAI,EAAE,UAAU,GAAG,cAAc,CAAC,KAAK,IAAI,cAAc,KAAK,IAAI,EAAE,UAAU,GAAG,cAAc,CAAC,KAAK;AACtI,YAAY,IAAI,YAAY,GAAG,UAAU,CAAC,aAAa,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;AACxF,YAAY,UAAU,GAAG,YAAY,IAAI,CAAC,GAAG,cAAc,GAAG,cAAc,CAAC;AAC7E,WAAW;AACX;AACA;AACA,UAAU,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC7C,UAAU,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1C;AACA,UAAU,IAAI,oBAAoB,GAAG,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;AAC/D;AACA,UAAU,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,oBAAoB,CAAC,MAAM,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE;AACvF,YAAY,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC;AACtD,WAAW;AACX,SAAS;AACT;AACA,QAAQ,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;AAClC;AACA;AACA;AACA,UAAU,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACpC,UAAU,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;AAChC,SAAS,MAAM;AACf;AACA,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACtC,UAAU,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC;AACjC,SAAS;AACT,OAAO,MAAM;AACb;AACA;AACA;AACA,QAAQ,IAAI,OAAO,IAAI,OAAO,EAAE;AAChC,UAAU,IAAI,KAAK,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;AACvD;AACA,UAAU,IAAI,KAAK,KAAK,IAAI,EAAE;AAC9B,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;AAC9C,cAAc,IAAI,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3E;AACA,cAAc,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,oBAAoB,CAAC,MAAM,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE;AAC3F,gBAAgB,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1D,eAAe;AACf,aAAa;AACb;AACA,YAAY,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE;AAC9C,cAAc,IAAI,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;AAC3E;AACA,cAAc,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,oBAAoB,CAAC,MAAM,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE;AAC3F,gBAAgB,SAAS,CAAC,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1D,eAAe;AACf,aAAa;AACb,WAAW;AACX,SAAS;AACT;AACA,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AAClC,OAAO;AACP;AACA,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL;AACA;AACA;AACA,GAAG,EAAE;AACL,IAAI,GAAG,EAAE,cAAc;AACvB,IAAI,KAAK,EAAE,SAAS,YAAY,CAAC,GAAG,EAAE,EAAE,EAAE;AAC1C;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC5B,MAAM,IAAI,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC;AAChC,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;AACjC,MAAM,IAAI,SAAS,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;AACpC,MAAM,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAC9B;AACA,MAAM,IAAI,GAAG,CAAC,UAAU,KAAK,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;AAC9D,MAAM,OAAO,SAAS,CAAC;AACvB,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,SAAS,CAAC;AACnB,CAAC,EAAE,CAAC;AACJ;AACA,IAAI,+BAA+B,GAAG,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,OAAO,CAAC;AAC/H,IAAI,uCAAuC,GAAG,OAAO,OAAO,KAAK,WAAW,IAAI,OAAO,CAAC,GAAG,CAAC,uCAAuC,IAAI,OAAO,CAAC;AAC/I,IAAI,SAAS,gBAAgB,YAAY;AACzC,EAAE,SAAS,SAAS,GAAG;AACvB,IAAI,eAAe,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;AACrC,GAAG;AACH;AACA,EAAE,YAAY,CAAC,SAAS,EAAE,CAAC;AAC3B,IAAI,GAAG,EAAE,KAAK;AACd,IAAI,KAAK,EAAE,SAAS,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,SAAS,EAAE;AAC/C,MAAM,SAAS,CAAC,IAAI,GAAG,IAAI,CAAC;AAC5B,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;AACtB;AACA;AACA,MAAM,IAAI,UAAU,GAAG,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;AACrD;AACA,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AAC9D,QAAQ,UAAU,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AAC9D,OAAO;AACP;AACA,MAAM,SAAS,CAAC,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC;AAClD;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,SAAS,CAAC,IAAI,KAAK,YAAY,EAAE;AAC3C;AACA,QAAQ,IAAI,OAAO,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;AACpC,QAAQ,IAAI,EAAE,GAAG,CAAC,CAAC;AACnB;AACA,QAAQ,OAAO,EAAE,GAAG,UAAU,CAAC,MAAM,EAAE;AACvC,UAAU,IAAI,cAAc,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,EAAE,CAAC,KAAK,UAAU,CAAC,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;AAC7G,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,SAAS,CAAC,IAAI,KAAK,cAAc,EAAE;AAC7C;AACA;AACA,QAAQ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,KAAK,EAAE,GAAG,EAAE,EAAE;AACzE,UAAU,IAAI,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC;AACpC;AACA,UAAU,KAAK,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE;AACzE,YAAY,IAAI,cAAc,CAAC,GAAG,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,OAAO,EAAE,CAAC;AACjF,WAAW;AACX,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA,MAAM,IAAI,KAAK,GAAG,IAAIA,IAAS,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;AACpD;AACA,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,UAAU,CAAC,MAAM,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE;AACzE,QAAQ,IAAI,WAAW,GAAG,UAAU,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,CAAC;AAC3D;AACA,QAAQ,KAAK,IAAI,EAAE,GAAG,CAAC,EAAE,KAAK,GAAG,WAAW,CAAC,MAAM,EAAE,EAAE,GAAG,KAAK,EAAE,EAAE,EAAE,EAAE;AACvE,UAAU,KAAK,CAAC,MAAM,CAAC,WAAW,CAAC,EAAE,CAAC,CAAC,CAAC;AACxC;AACA,UAAU,IAAI,KAAK,CAAC,IAAI,GAAG,+BAA+B,EAAE;AAC5D;AACA,YAAY,MAAM,IAAI,KAAK,CAAC,mEAAmE,GAAG,iDAAiD,CAAC,CAAC;AACrJ,WAAW;AACX,SAAS;AACT,OAAO;AACP;AACA;AACA;AACA,MAAM,IAAI,SAAS,GAAG,IAAI,SAAS,CAAC,KAAK,CAAC,CAAC;AAC3C,MAAM,IAAI,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC;AACrC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;AAC7B;AACA,MAAM,OAAO,IAAI,EAAE;AACnB,QAAQ,IAAI,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AAC3B;AACA,QAAQ,IAAI,KAAK,CAAC,IAAI,KAAK,aAAa,EAAE;AAC1C;AACA,UAAU,IAAI,GAAG,GAAG,GAAG,CAAC,OAAO,CAAC;AAChC,UAAU,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,GAAG,MAAM,GAAG,OAAO,EAAE,cAAc,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,GAAG,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE,gBAAgB,CAAC,GAAG,2BAA2B,CAAC,CAAC;AAC/X,SAAS;AACT;AACA,QAAQ,IAAI,KAAK,CAAC,IAAI,GAAG,+BAA+B,EAAE;AAC1D;AACA,UAAU,MAAM,IAAI,KAAK,CAAC,uDAAuD,GAAG,iDAAiD,CAAC,CAAC;AACvI,SAAS;AACT;AACA,QAAQ,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,GAAG,uCAAuC,EAAE;AACjF;AACA,UAAU,MAAM,IAAI,KAAK,CAAC,uDAAuD,GAAG,2DAA2D,CAAC,CAAC;AACjJ,SAAS;AACT;AACA,QAAQ,IAAI,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;AAC/C;AACA,QAAQ,KAAK,IAAI,GAAG,GAAG,CAAC,EAAE,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,GAAG,GAAG,MAAM,EAAE,GAAG,EAAE,EAAE;AAC1E,UAAU,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;AACpC,UAAU,IAAI,IAAI,CAAC,UAAU,KAAK,SAAS,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAChE,SAAS;AACT;AACA,QAAQ,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC;AACnC,QAAQ,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC;AAC3B,OAAO;AACP;AACA;AACA,MAAM,OAAO,CAAC,KAAK,EAAE,CAAC;AACtB;AACA;AACA,MAAM,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;AACzD,MAAM,IAAI,MAAM,GAAG,IAAI,YAAY,CAAC,QAAQ,CAAC,CAAC;AAC9C,MAAM,OAAO,MAAM,CAAC,OAAO,EAAE,CAAC;AAC9B,KAAK;AACL,GAAG,CAAC,CAAC,CAAC;AACN;AACA,EAAE,OAAO,SAAS,CAAC;AACnB,CAAC,EAAE,CAAC;AACJ;AACA,IAAI,SAAS,GAAG,IAAI,SAAS,EAAE,CAAC;AAChC;AACA,IAAI,KAAK,GAAG,SAAS,KAAK,CAAC,IAAI,EAAE;AACjC,EAAE,KAAK,IAAI,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,SAAS,GAAG,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,EAAE,EAAE;AACnH,IAAI,SAAS,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC;AAC1C,GAAG;AACH;AACA,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AACjD,CAAC,CAAC;AACF;AACA,IAAI,cAAc,GAAG,SAAS,YAAY,CAAC,IAAI,EAAE;AACjD,EAAE,KAAK,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,SAAS,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;AAC1H,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AAC5C,GAAG;AACH;AACA,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AACxD,CAAC,CAAC;AACF;AACA,IAAI,GAAG,GAAG,SAAS,GAAG,CAAC,IAAI,EAAE;AAC7B,EAAE,KAAK,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,SAAS,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;AAC1H,IAAI,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AAC5C,GAAG;AACH;AACA,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;AAC/C,CAAC,CAAC;AACF;AACA,IAAI,UAAU,GAAG,SAAS,UAAU,CAAC,WAAW,EAAE;AAClD,EAAE,KAAK,IAAI,KAAK,GAAG,SAAS,CAAC,MAAM,EAAE,aAAa,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,KAAK,EAAE,KAAK,EAAE,EAAE;AAC9H,IAAI,aAAa,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;AAChD,GAAG;AACH;AACA,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,YAAY,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC;AACjE,CAAC,CAAC;AACF;AACA,IAAI,KAAK,GAAG;AACZ,EAAE,KAAK,EAAE,KAAK;AACd,EAAE,YAAY,EAAE,cAAc;AAC9B,EAAE,GAAG,EAAE,GAAG;AACV,EAAE,UAAU,EAAE,UAAU;AACxB,CAAC;;ACtxDD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAAS,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE;AACzD,IAAI,IAAI,OAAO,KAAK,KAAK,CAAC,EAAE,EAAE,OAAO,GAAG,EAAE,CAAC,EAAE;AAC7C,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/B,IAAI,IAAI,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/B,IAAI,IAAI,YAAY,GAAGC,KAAe,CAAC,YAAY,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,WAAW,CAAC,CAAC;AAC1F,IAAI,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC;AACjC,QAAQ,OAAO,IAAI,CAAC;AACpB,IAAI,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC;AACjC,QAAQ,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AAC5D,IAAI,OAAO,YAAY,CAAC,YAAY,EAAE,OAAO,CAAC,UAAU,CAAC,CAAC;AAC1D;;ACjDA,MAAMC,GAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,OAAO,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAE,CAAC,OAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAACA,GAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,CAAC,CAAC,OAAO,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAACA,GAAC,CAAC,GAAG,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,GAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,EAAC,CAAC,CAAC,OAAO,UAAU,CAAC,MAAM,CAAC,CAACA,GAAC,CAAC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,OAAO,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,QAAQ,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,OAAM,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;ACAjuD,UAAe,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA;;ACAA,iBAAQ,CAAC,CAAC,EAAE;AAC3B,EAAE,OAAO,CAAC,CAAC;AACX;;ACAe,kBAAQ,CAAC,SAAS,EAAE;AACnC,EAAE,IAAI,SAAS,IAAI,IAAI,EAAE,OAAO,QAAQ,CAAC;AACzC,EAAE,IAAI,EAAE;AACR,MAAM,EAAE;AACR,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7B,MAAM,EAAE,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;AAC7B,MAAM,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;AACjC,MAAM,EAAE,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;AAClC,EAAE,OAAO,SAAS,KAAK,EAAE,CAAC,EAAE;AAC5B,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AACxB,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;AACvD,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAC3C,IAAI,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,KAAK,CAAC,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC;AAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AAC5C,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC;AACJ;;AClBe,gBAAQ,CAAC,KAAK,EAAE,CAAC,EAAE;AAClC,EAAE,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACrC,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;AACpE;;ACAe,UAAQ,CAAC,QAAQ,EAAE,CAAC,EAAE;AACrC,EAAE,IAAI,OAAO,CAAC,KAAK,QAAQ,EAAE,CAAC,GAAG,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;AACrD,EAAE,OAAO,CAAC,CAAC,IAAI,KAAK,oBAAoB;AACxC,QAAQ,CAAC,IAAI,EAAE,mBAAmB,EAAE,QAAQ,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,EAAE,OAAO,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;AAC7G,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AAC7B,CAAC;AACD;AACA,SAAS,OAAO,CAAC,QAAQ,EAAE,CAAC,EAAE;AAC9B,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC,EAAE;AACf,MAAM,IAAI,GAAG,CAAC,CAAC,IAAI;AACnB,MAAM,UAAU,GAAG,CAAC,CAAC,UAAU,IAAI,IAAI,GAAG,EAAE,GAAG,CAAC,CAAC,UAAU;AAC3D,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;AACrC,EAAE,OAAO,EAAE,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC;AACnG,QAAQ,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC;AAC5F,QAAQ,CAAC,IAAI,EAAE,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC1F,CAAC;AACD;AACO,SAAS,MAAM,CAAC,QAAQ,EAAE,CAAC,EAAE;AACpC,EAAE,IAAI,cAAc,GAAG,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC;AACpD,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;AAC3B;AACA,EAAE,SAAS,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE;AAC1B,IAAI,IAAI,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,EAAE,CAAC;AACpC,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE;AACxE,MAAM,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AAC3C,KAAK;AACL,IAAI,IAAI,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;AAClC,GAAG;AACH;AACA,EAAE,SAAS,KAAK,CAAC,CAAC,EAAE;AACpB,IAAI,OAAO,cAAc,CAAC,CAAC,CAAC,CAAC;AAC7B,GAAG;AACH;AACA,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE;AACtB,IAAI,IAAI,MAAM,GAAG,EAAE,CAAC;AACpB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;AACtE,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AAClD,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA,EAAE,SAAS,IAAI,CAAC,IAAI,EAAE;AACtB,IAAI,IAAI,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;AAC5B,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG;AACH;AACA,EAAE,SAAS,OAAO,CAAC,IAAI,EAAE;AACzB,IAAI,OAAO,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;AAC1B,GAAG;AACH;AACA,EAAE,SAAS,QAAQ,CAAC,CAAC,EAAE;AACvB,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,WAAW,CAAC;AACnC,IAAI,QAAQ,IAAI;AAChB,MAAM,KAAK,oBAAoB,EAAE,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;AAC7F,MAAM,KAAK,OAAO,EAAE,WAAW,GAAG,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,MAAM;AAC9D,MAAM,KAAK,YAAY,EAAE,WAAW,GAAG,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM;AACvE,MAAM,KAAK,YAAY,EAAE,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM;AAC3D,MAAM,KAAK,iBAAiB,EAAE,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM;AACpE,MAAM,KAAK,SAAS,EAAE,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM;AAC3D,MAAM,KAAK,cAAc,EAAE,WAAW,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM;AACpE,MAAM,SAAS,OAAO,IAAI,CAAC;AAC3B,KAAK;AACL,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,CAAC,CAAC;AAClD,GAAG;AACH;AACA,EAAE,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC;AACrB;;ACrEA,SAASC,GAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAC,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAE,CAAW,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAAC,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAAC,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAAD,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAAC,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAAC,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAAC,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAAD,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,SAAAE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,SAAAJ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAAC,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,0BAAA,CAAA,CAAA,CAAA,cAAA,CAAAE,GAAA,CAAA,cAAA,CAAA,CAAA,CAAA,WAAA,CAAAJ,GAAA,CAAA;;ACArB,MAAAM,GAAA,CAAAL,GAAA,CAAA,OAAA,CAAAF,GAAA,CAAA,EAAA,CAAA,SAAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,IAAA,KAAA,CAAA,CAAA,g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sBAAA,CAAA,CAAA,CAAA,YAAA,CAAAD,GAAA,CAAA;;ACKb,MAAO,CAAU,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,OAAA,qBAAA,EAAA,CAAA,OAAA,IAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,MAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,OAAA,EAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,CAAA;;ACLU,SAAAE,GAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,IAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,SAAAN,GAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,QAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,EAAA,EAAA,EAAA,MAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,QAAA,CAAA,EAAA,CAAA,SAAAP,GAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAAc,GAAA,CAAA,YAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAAD,GAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,GAAA,EAAAN,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,MAAA,CAAA,IAAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,QAAA,CAAA,SAAA,CAAAX,GAAA,CAAA;;ACAzC,MAAMO,OACJ,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,GAAA,CAAa,KAAW,CAAA,IAAA,CAAA,EACxB,CAAOQ,GAAA,CAAA,IAAA,CAAA,EAAG,IAAI,CAAC,EAAE,KAAM,CAAA,YACbb,GAAA,CAAA,CAAG,CAAG,CAAA,OAAOK,GAAA,CAAA,CACvB,QAAM,CAAG,CAAA,CAAA,GAAA,CAAI,IAAG,CAAA,GAAI,CACpB,SAAO,CAAG,CAAA,CAAA,CAAA,SAAU,CAAC,EAAE,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,GAAA,CAAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAAN,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAAH,GAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,UAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAAL,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,SAAAE,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAAV,GAAA,CAAA,CAAA,CAAAD,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAAD,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAAK,GAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,OAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,QAAA,CAAAJ,GAAA,CAAA,oBAAA,CAAAC,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,aAAA,CAAAU,GAAA,CAAA;;ACLyB,MAAAN,GAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,aAAA,EAAA,CAAA,OAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,UAAA,CAAA,IAAA,CAAA,OAAA,kBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,MAAA,CAAA,IAAA,CAAA,OAAA,sBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA,WAAA,CAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,aAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,EAAA,CAAA,YAAA,EAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,4BAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,eAAA,CAAA,CAAA,CAAA,UAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,EAAA,CAAA,MAAA,CAAA,EAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,yBAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,4BAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,4BAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,yBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,iCAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,cAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,iCAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAAL,GAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAAO,GAAA,CAAA,sBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,+BAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,MAAA,IAAA,KAAA,CAAA,CAAA,iBAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,+BAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,GAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,QAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAAM,GAAA,CAAA,0BAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,+BAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,QAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAAA,GAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAAA,GAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,+BAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAAZ,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAAA,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,2BAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,8BAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,2BAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,mCAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,8BAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,uBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,IAAA,cAAA,CAAA,OAAA,IAAA,CAAA,mCAAA,CAAA,CAAA,CAAA,CAAA,QAAA,MAAA,IAAA,KAAA,CAAA,CAAA,aAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,MAAA,CAAA,MAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,WAAA,CAAA,IAAA,CAAA,uBAAA,CAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,CAAA,CAAA,QAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,SAAA,EAAA,CAAA,OAAA,IAAA,CAAA,QAAA,CAAA,QAAA,CAAA,CAAA,OAAA,EAAA,CAAA,OAAA,IAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,cAAA,EAAA,CAAA,OAAA,IAAA,CAAA,QAAA,CAAA,aAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAAI,GAAA,CAAA,iBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA,YAAA,GAAA,CAAA,CAAA,YAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,uBAAA,EAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,MAAA,CAAA,cAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,OAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,aAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,EAAA,CAAA,OAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA,aAAA,CAAA,CAAA,qBAAA,EAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,SAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,YAAA,CAAA,IAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,YAAA,CAAA;;ACAtB,MAAA,CAAA,SAAAL,GAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,MAAA,CAAA,CAAA,WAAA,CAAA,8DAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,IAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,uBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,EAAA,GAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,OAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,wBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,GAAA,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA,CAAA,wBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,EAAA,IAAA,CAAA,KAAA,CAAA,WAAA,CAAA,eAAA,CAAA,CAAA,CAAA,EAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAAC,GAAA,CAAA,OAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,EAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,EAAA,EAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,UAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,KAAA,IAAA,CAAA,SAAA,CAAA,IAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,IAAA,KAAA,CAAA,CAAA,gBAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAAa,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,qBAAA,GAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,0BAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,cAAA,CAAA,OAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA;;ACAsB,MAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,OAAA,CAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,IAAAhB,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAAiB,GAAA,CAAA,MAAA,CAAA,CAAA,UAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA,MAAA,CAAA,CAAA,GAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,UAAA,EAAA,CAAA,EAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAAhB,GAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,OAAA,IAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,OAAA,CAAA,IAAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,CAAA,OAAA,EAAA,CAAA,OAAA,IAAA,CAAA,WAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,OAAA,OAAA,CAAA,IAAA,CAAA,CAAA,mBAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,OAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA,WAAA,EAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,cAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,OAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,qBAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,OAAA,CAAA,CAAA,OAAA,IAAA,CAAA,WAAA,EAAA,CAAA,OAAA,IAAA,CAAA,kBAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,YAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,OAAA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAAS,SAAA,CAAAN,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,SAAA,EAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,KAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,UAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,YAAA,EAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,IAAA,IAAA,EAAA,CAAA,GAAA,KAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,eAAA,CAAA,OAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,IAAA,KAAA,CAAA,CAAA,qBAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA,CAAA,CAAA,QAAA,EAAAU,GAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,MAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,GAAA,EAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,0BAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,EAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,OAAA,EAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,OAAA,EAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,IAAA,CAAA,OAAA,CAAA,EAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,KAAA,EAAA,CAAA;;ACAhC,SAAS,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAAV,GAAA,CAAA,MAAA,CAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,GAAA,CAAA,SAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,IAAA,CAAA,uBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,KAAA,CAAA,CAAA,EAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,EAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,CAAA,uBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,WAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,QAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,QAAA,GAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,IAAA,KAAA,CAAA,CAAA,mBAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,WAAA,EAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,EAAA,GAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,EAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,UAAA,CAAA,UAAA,CAAA,MAAA,CAAA,KAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,EAAAA,GAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,SAAA,EAAA,CAAA,SAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,EAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,SAAA,EAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,SAAA,EAAA,CAAA,QAAA,CAAA,CAAA,CAAA,EAAA,CAAA,OAAA,EAAA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA;;ACA3B,IAAkD,CAAA,CAAA,MAAA,CAAA,cAAA,CAAAI,GAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,yBAAA,CAAA,IAAAD,GAAA,CAAA,MAAA,CAAA,qBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,cAAA,CAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,IAAAV,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAAa,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAAb,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAAU,GAAA,CAAA,IAAA,IAAA,CAAA,IAAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAAV,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAAK,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,UAAA,SAAAO,GAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,EAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAAX,GAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA,0BAAA,CAAA,GAAA,CAAA,oCAAA,CAAA,GAAA,CAAA,gCAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,yBAAA,CAAA,CAAA,CAAA,CAAA,2BAAA,CAAA,CAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,OAAA,CAAA,CAAA,WAAA,CAAA,mBAAA,CAAA,aAAA,CAAA,EAAA,CAAA,YAAA,CAAA,CAAA,CAAA,SAAA,CAAA,kBAAA,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,WAAA,CAAA,mBAAA,CAAA,aAAA,CAAA,EAAA,CAAA,YAAA,CAAA,CAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,WAAA,CAAA,mBAAA,CAAA,aAAA,CAAA,EAAA,CAAA,YAAA,CAAA,CAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA,QAAA,CAAA,CAAA,WAAA,CAAA,mBAAA,CAAA,aAAA,CAAA,EAAA,CAAA,YAAA,CAAA,CAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,WAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,IAAA,CAAA,YAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,CAAA,YAAA,EAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,IAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,EAAA,EAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,aAAA,EAAA,CAAA,OAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,gBAAA,EAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,EAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,OAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAAY,GAAA,CAAA,CAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAAA,GAAA,CAAA,YAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA,IAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,IAAA,CAAA,0BAAA,CAAAD,GAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,aAAA,GAAA,CAAA,cAAA,EAAA,CAAA,GAAA,IAAA,CAAA,gBAAA,EAAA,CAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,SAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,EAAA,CAAA,IAAA,CAAA,eAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,EAAA,CAAA,kBAAA,EAAA,CAAA,OAAA,IAAA,CAAA,gBAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,GAAA,IAAA,CAAA,gBAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,0BAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,EAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,GAAA,IAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,OAAA,CAAA,CAAA,iBAAA,EAAA,CAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,iBAAA,EAAA,CAAA,EAAA,CAAA,CAAA,iBAAA,CAAA,OAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,MAAA,GAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,OAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,0BAAA,CAAA,OAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,gCAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA,OAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,GAAA,CAAA,CAAA,MAAA,GAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,oCAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA,CAAA,CAAA,QAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA,EAAA,IAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,MAAA,IAAA,KAAA,CAAA,CAAA,mBAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,EAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,EAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,EAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,cAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA,iCAAA,CAAA,CAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,UAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA,wBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,EAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,yBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,EAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,GAAA,IAAA,CAAA,SAAA,GAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,UAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,mBAAA,GAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,cAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,EAAA,CAAA,aAAA,CAAA,CAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,yBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,EAAA,CAAA,mBAAA,EAAA,CAAA,IAAA,CAAA,cAAA,EAAA,CAAA,IAAA,CAAA,iBAAA,GAAA,CAAA,iBAAA,EAAA,CAAA,IAAA,CAAA,0BAAA,GAAA,CAAA,0BAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,EAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,UAAA,CAAA,GAAA,CAAA,GAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,OAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,mBAAA,GAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAAZ,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,qBAAA,EAAA,IAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,OAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,WAAA,CAAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,UAAA,CAAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,yBAAA,EAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAAA,GAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAAM,GAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,OAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,2BAAA,EAAA,IAAA,CAAA,IAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,OAAA,CAAA,UAAA,CAAA,MAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,EAAA,CAAA,GAAA,KAAA,CAAA,EAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,EAAA,CAAA,GAAA,KAAA,CAAA,EAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA,CAAA,cAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,aAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,KAAA,CAAA,kBAAA,EAAA,CAAA,iBAAA,EAAA,CAAA,IAAA,CAAA,SAAA,GAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,IAAA,CAAA,SAAA,GAAA,CAAA,EAAA,IAAA,CAAA,gBAAA,CAAA,CAAA,EAAA,CAAA,kBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,iBAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,IAAA,CAAA,iBAAA,EAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,iBAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,yBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,cAAA,EAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,UAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,MAAA,CAAA,MAAA,CAAA,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,GAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,aAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,uBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,EAAA,CAAA,OAAA,IAAA,IAAA,CAAA,OAAA,CAAAP,GAAA,CAAAO,GAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,QAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,GAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,EAAA,EAAA,EAAA,CAAA,OAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,KAAA,CAAA,uCAAA,CAAA,IAAA,CAAA,sCAAA,CAAA,SAAA,CAAA,iCAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,QAAA,CAAA,aAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,OAAA,EAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,QAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,cAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,SAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,OAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,eAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAAN,GAAA,CAAA,UAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,MAAA,CAAA,IAAA,IAAA,CAAA,MAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,IAAA,IAAA,CAAA,KAAA,CAAA,CAAA,EAAA,CAAA,CAAA,EAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA,OAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,uBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAAA,GAAA,CAAA,MAAA,CAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,cAAA,GAAA,IAAA,CAAA,cAAA,CAAA,UAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,EAAA,GAAA,CAAA,EAAA,CAAA,WAAA,EAAA,CAAA,OAAA,IAAA,CAAA,YAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,cAAA,GAAA,YAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,WAAA,EAAA,CAAA,IAAA,CAAA,cAAA,GAAA,YAAA,CAAA,IAAA,CAAA,cAAA,CAAA,CAAA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,QAAA,CAAA,OAAA,CAAA,IAAA,CAAA,cAAA,CAAA,IAAA,CAAA,CAAA,CAAA,EAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,UAAA,EAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,EAAA,CAAA,GAAA,KAAA,CAAA,EAAA,CAAA,CAAA,IAAA,GAAA,CAAA,IAAA,EAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,EAAA,CAAA,GAAA,KAAA,CAAA,EAAA,CAAA,CAAA,IAAA,GAAA,CAAA,KAAA,EAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,IAAA,IAAA,EAAA,CAAA,GAAA,KAAA,CAAA,EAAA,CAAA,CAAA,aAAA,EAAA,CAAA,IAAA,CAAA,aAAA,CAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,GAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,QAAA,EAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,KAAA,CAAA,OAAA,EAAA,CAAA,IAAA,CAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,gBAAA,CAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA;;ACApC,MAAO,CAAS,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,EAAA,CAAA;;ACA9B,IAAkD,CAAA,CAAA,MAAA,CAAA,cAAA,CAAA,CAAA,CAAA,MAAA,CAAA,gBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,yBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,qBAAA,CAAA,IAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,cAAA,CAAA,CAAA,CAAA,MAAA,CAAA,SAAA,CAAA,oBAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,IAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,SAAAH,GAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,WAAA,CAAAa,GAAA,CAAA,IAAA,CAAAA,GAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,WAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,OAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAA,CAAA,GAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,eAAA,CAAA,IAAA,CAAA,MAAA,CAAA,EAAA,CAAA,aAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAAA,GAAA,CAAA,MAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,IAAA,CAAA,QAAA,CAAA,IAAAI,CAAA,CAAA,CAAA,UAAA,CAAA,IAAA,CAAA,KAAA,CAAA,UAAA,CAAA,eAAA,CAAA,IAAA,CAAA,KAAA,CAAA,eAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAAC,CAAA,CAAA,CAAA,OAAA,CAAA,IAAA,CAAA,QAAA,CAAA,kBAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,EAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAAV,UAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,aAAA,CAAA,CAAA,CAAA,MAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,EAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,GAAA,CAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,EAAA,IAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,oBAAA,GAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAA,IAAA,CAAA,WAAA,CAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,OAAA,EAAA,CAAA,MAAA,CAAA,CAAA,4BAAA,CAAA,GAAA,QAAA,CAAA,cAAA,CAAA,CAAA,CAAA,CAAA,OAAA,MAAA,CAAA,CAAA,urFAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,aAAA,CAAA,CAAA,CAAA,YAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,oBAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA,EAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,YAAA,CAAA,mBAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,QAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,mBAAA,EAAA,CAAA,OAAA,IAAA,CAAA,QAAA,CAAA,WAAA,EAAA,CAAA,SAAA,EAAA,CAAA,OAAA,IAAA,CAAA,YAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,qBAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,WAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,WAAA,CAAA,CAAA,EAAA,CAAA,MAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,MAAA,GAAA,CAAA,WAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,WAAA,GAAA,CAAA,UAAA,EAAA,CAAA,OAAA,IAAA,CAAA,QAAA,CAAA,UAAA,EAAA,CAAA,IAAA,CAAA,OAAA,CAAA,eAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,IAAA,CAAA,YAAA,CAAA,SAAA,EAAA,CAAA,IAAA,CAAA,OAAA,CAAA,cAAA,EAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,EAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,IAAA,IAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA,aAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,OAAA,IAAA,CAAA,GAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA,QAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,WAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,UAAA,EAAA,CAAA,IAAA,CAAA,OAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAAC,CAAA,CAAA,qBAAA,EAAA,CAAA,IAAA,CAAA,eAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,OAAA,CAAA,cAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,CAAA,CAAA,MAAA,EAAA,IAAA,CAAA,KAAA,CAAA,cAAA,EAAA,IAAA,CAAA,UAAA,GAAA,CAAA,OAAA,EAAA,CAAA,OAAA,IAAA,CAAA,QAAA,CAAA,OAAA,EAAA,EAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,UAAA,EAAA,CAAA,MAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,MAAA,EAAA,CAAA,CAAA,CAAA,IAAA,IAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA,CAAA,EAAA,CAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA,cAAA,EAAA,CAAA,OAAA,IAAA,CAAA,YAAA,CAAA,MAAA,EAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,QAAA,EAAA,CAAA,OAAA,IAAA,CAAA,YAAA,CAAA,QAAA,EAAA,CAAA,QAAA,EAAA,CAAA,OAAA,CAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,IAAA,EAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,IAAA,EAAA,CAAA,IAAA,EAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,IAAA,EAAA,CAAA,OAAA,EAAA,CAAA,IAAA,CAAA,oBAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,OAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,OAAA,EAAA,CAAA,IAAA,CAAA,QAAA,CAAA,OAAA,EAAA,CAAA,IAAA,CAAA,YAAA,CAAA,IAAA,CAAA,IAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,KAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,KAAA,EAAA,CAAA,SAAA,EAAA,CAAA,OAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,SAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA,IAAA,CAAA,SAAA,EAAA,CAAA,SAAA,CAAA,CAAA,EAAA,CAAA;;;;"}