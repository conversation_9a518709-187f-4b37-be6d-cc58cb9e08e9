'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var index = require('./packages/amap/index.js');
var index$1 = require('./packages/control/ControlBar/index.js');
var index$2 = require('./packages/control/HawkEye/index.js');
var index$3 = require('./packages/control/MapType/index.js');
var index$4 = require('./packages/control/Scale/index.js');
var index$5 = require('./packages/control/ToolBar/index.js');
var index$6 = require('./packages/control/SearchBox/index.js');
var index$7 = require('./packages/infoWindow/InfoWindow/index.js');
var index$8 = require('./packages/layer/data/Canvas/index.js');
var index$9 = require('./packages/layer/data/Custom/index.js');
var index$a = require('./packages/layer/data/Flexible/index.js');
var index$b = require('./packages/layer/data/GLCustom/index.js');
var index$c = require('./packages/layer/data/HeatMap/index.js');
var index$d = require('./packages/layer/data/Image/index.js');
var index$e = require('./packages/layer/data/Labels/index.js');
var index$f = require('./packages/layer/data/Vector/index.js');
var index$g = require('./packages/layer/official/Buildings/index.js');
var index$h = require('./packages/layer/official/DefaultLayer/index.js');
var index$i = require('./packages/layer/official/DistrictLayer/index.js');
var index$j = require('./packages/layer/official/IndoorMap/index.js');
var index$k = require('./packages/layer/official/RoadNet/index.js');
var index$l = require('./packages/layer/official/Satellite/index.js');
var index$m = require('./packages/layer/official/TileLayer/index.js');
var index$n = require('./packages/layer/official/Traffic/index.js');
var index$o = require('./packages/layer/standard/MapboxVectorTileLayer/index.js');
var index$p = require('./packages/layer/standard/WMS/index.js');
var index$q = require('./packages/layer/standard/WMTS/index.js');
var index$r = require('./packages/marker/ElasticMarker/index.js');
var index$s = require('./packages/marker/LabelMarker/index.js');
var index$t = require('./packages/marker/Marker/index.js');
var index$u = require('./packages/marker/MarkerCluster/index.js');
var index$v = require('./packages/marker/MassMarks/index.js');
var index$w = require('./packages/marker/Text/index.js');
var index$x = require('./packages/vector/BezierCurve/index.js');
var index$y = require('./packages/vector/Circle/index.js');
var index$z = require('./packages/vector/Ellipse/index.js');
var index$A = require('./packages/vector/GeoJSON/index.js');
var index$B = require('./packages/vector/Polygon/index.js');
var index$C = require('./packages/vector/Polyline/index.js');
var index$D = require('./packages/vector/Rectangle/index.js');
var index$E = require('./packages/layer/data/Tiles3D/index.js');
var index$F = require('./packages/control/Geolocation/index.js');
var index$G = require('./packages/marker/CircleMarker/index.js');
var index$H = require('./packages/layer/data/Video/index.js');
var index$I = require('./packages/util/MouseTool/index.js');
var index$J = require('./packages/layer/data/DistrictCluster/index.js');
var index$K = require('./packages/layer/data/CustomXyz/index.js');

var Components = [
  index.ElAmap,
  index$1.ElAmapControlControlBar,
  index$2.ElAmapControlHawkEye,
  index$3.ElAmapControlMapType,
  index$4.ElAmapControlScale,
  index$5.ElAmapControlToolBar,
  index$6.ElAmapSearchBox,
  index$7.ElAmapInfoWindow,
  index$8.ElAmapLayerCanvas,
  index$9.ElAmapLayerCustom,
  index$a.ElAmapLayerFlexible,
  index$b.ElAmapLayerGlCustom,
  index$c.ElAmapLayerHeatMap,
  index$d.ElAmapLayerImage,
  index$e.ElAmapLayerLabels,
  index$f.ElAmapLayerVector,
  index$g.ElAmapLayerBuildings,
  index$h.ElAmapLayerDefault,
  index$i.ElAmapLayerDistrict,
  index$j.ElAmapLayerIndoorMap,
  index$k.ElAmapLayerRoadNet,
  index$l.ElAmapLayerSatellite,
  index$m.ElAmapLayerTile,
  index$n.ElAmapLayerTraffic,
  index$o.ElAmapLayerMapboxVectorTile,
  index$p.ElAmapLayerWms,
  index$q.ElAmapLayerWmts,
  index$r.ElAmapElasticMarker,
  index$s.ElAmapLabelMarker,
  index$t.ElAmapMarker,
  index$u.ElAmapMarkerCluster,
  index$v.ElAmapMassMarks,
  index$w.ElAmapText,
  index$x.ElAmapBezierCurve,
  index$y.ElAmapCircle,
  index$z.ElAmapEllipse,
  index$A.ElAmapGeojson,
  index$B.ElAmapPolygon,
  index$C.ElAmapPolyline,
  index$D.ElAmapRectangle,
  index$E.ElAmapLayerTiles3d,
  index$F.ElAmapControlGeolocation,
  index$G.ElAmapCircleMarker,
  index$H.ElAmapLayerVideo,
  index$I.ElAmapMouseTool,
  index$J.ElAmapLayerDistrictCluster,
  index$K.ElAmapLayerCustomXyz
];

exports.default = Components;
//# sourceMappingURL=component.js.map
