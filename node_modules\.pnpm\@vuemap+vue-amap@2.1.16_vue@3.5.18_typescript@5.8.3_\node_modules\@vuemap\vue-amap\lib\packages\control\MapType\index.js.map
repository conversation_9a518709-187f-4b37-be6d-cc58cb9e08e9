{"version": 3, "file": "index.js", "sources": ["../../../../../packages/control/MapType/index.ts"], "sourcesContent": ["import MapType from './MapType.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nMapType.install = (app: App) => {\r\n  app.component(MapType.name, MapType);\r\n  return app;\r\n};\r\nexport const ElAmapControlMapType = MapType as typeof MapType & Plugin;\r\nexport default ElAmapControlMapType;\r\n\r\nexport type ElAmapControlMapTypeInstance = InstanceType<typeof MapType>\r\n"], "names": ["MapType"], "mappings": ";;;;;;;AAEAA,mDAAQ,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AAC9B,EAAI,GAAA,CAAA,SAAA,CAAUA,mDAAQ,CAAA,IAAA,EAAMA,mDAAO,CAAA,CAAA;AACnC,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AACO,MAAM,oBAAuB,GAAAA;;;;;"}