{"version": 3, "file": "props.js", "sources": ["../../../../../packages/control/HawkEye/props.ts"], "sourcesContent": ["import {buildProps} from \"../../../utils/buildHelper\";\r\n\r\nexport const propsTypes = buildProps({\r\n  autoMove: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 是否随主图视口变化移动\r\n  showRectangle: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 是否显示视口矩形\r\n  showButton: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 是否显示打开关闭的按钮\r\n  isOpen: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 默认是否展开\r\n  mapStyle: {\r\n    type: String\r\n  }, // 缩略图要显示的地图自定义样式，如'amap://styles/dark'\r\n  layers: {\r\n    type: Array\r\n  }, // 缩略图要显示的图层类型，默认为普通矢量地图\r\n  width: {\r\n    type: String\r\n  }, // 缩略图的宽度，同CSS，如'200px'\r\n  height: {\r\n    type: String\r\n  }, // 缩略图的高度，同CSS，如'200px'\r\n  offset: {\r\n    type: Array\r\n  }, // 缩略图距离地图右下角的像素距离，如 [2,2]\r\n  borderStyle: {\r\n    type: String\r\n  }, // 缩略图的边框样式，同CSS，如\"double solid solid double\"\r\n  borderColor: {\r\n    type: String\r\n  }, // 缩略图的边框颜色，同CSS，如'silver'\r\n  borderRadius: {\r\n    type: String\r\n  }, // 缩略图的边框角度，同CSS，如'5px'\r\n  borderWidth: {\r\n    type: String\r\n  }, // 缩略图的边框宽度，同CSS，如'2px'\r\n  buttonSize: {\r\n    type: String\r\n  } // 箭头按钮的像素尺寸，同CSS，如'12px'\r\n});"], "names": ["buildProps"], "mappings": ";;;;AAEO,MAAM,aAAaA,sBAAW,CAAA;AAAA,EACnC,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,aAAe,EAAA;AAAA,IACb,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,KAAA;AAAA,GACR;AAAA;AAAA,EACA,KAAO,EAAA;AAAA,IACL,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,KAAA;AAAA,GACR;AAAA;AAAA,EACA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,YAAc,EAAA;AAAA,IACZ,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AACF,CAAC;;;;"}