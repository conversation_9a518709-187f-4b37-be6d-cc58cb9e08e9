import type { PropType } from "vue";
export declare const propsTypes: {
    path: {
        type: ArrayConstructor;
        required: true;
    };
    bubble: {
        type: BooleanConstructor;
        default: boolean;
    };
    cursor: {
        type: StringConstructor;
    };
    strokeColor: {
        type: StringConstructor;
    };
    strokeOpacity: {
        type: NumberConstructor;
    };
    strokeWeight: {
        type: NumberConstructor;
    };
    borderWeight: {
        type: NumberConstructor;
    };
    isOutline: {
        type: BooleanConstructor;
        default: boolean;
    };
    outlineColor: {
        type: StringConstructor;
    };
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    extData: {
        type: ObjectConstructor;
        default: () => null;
    };
    strokeStyle: {
        type: PropType<"solid" | "dashed">;
        validator: (value: string) => boolean;
    };
    strokeDasharray: {
        type: ArrayConstructor;
    };
    lineJoin: {
        type: PropType<"miter" | "round" | "bevel">;
        validator: (value: string) => boolean;
    };
    lineCap: {
        type: PropType<"round" | "butt" | "square">;
        validator: (value: string) => boolean;
    };
    geodesic: {
        type: BooleanConstructor;
        default: boolean;
    };
    showDir: {
        type: BooleanConstructor;
        default: boolean;
    };
    editable: {
        type: BooleanConstructor;
        default: boolean;
    };
    editOptions: {
        type: ObjectConstructor;
    };
} & {
    visible: import("../../../utils/buildHelper").IPropOptions<boolean>;
    zIndex: import("../../../utils/buildHelper").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils/buildHelper").IPropOptions<boolean>;
    extraOptions: import("../../../utils/buildHelper").IPropOptions<any>;
};
