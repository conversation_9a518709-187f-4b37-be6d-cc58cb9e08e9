import { ElAmap } from './packages/amap/index.mjs';
import { ElAmapControlControlBar } from './packages/control/ControlBar/index.mjs';
import { ElAmapControlHawkEye } from './packages/control/HawkEye/index.mjs';
import { ElAmapControlMapType } from './packages/control/MapType/index.mjs';
import { ElAmapControlScale } from './packages/control/Scale/index.mjs';
import { ElAmapControlToolBar } from './packages/control/ToolBar/index.mjs';
import { ElAmapSearchBox } from './packages/control/SearchBox/index.mjs';
import { ElAmapInfoWindow } from './packages/infoWindow/InfoWindow/index.mjs';
import { ElAmapLayerCanvas } from './packages/layer/data/Canvas/index.mjs';
import { ElAmapLayerCustom } from './packages/layer/data/Custom/index.mjs';
import { ElAmapLayerFlexible } from './packages/layer/data/Flexible/index.mjs';
import { ElAmapLayerGlCustom } from './packages/layer/data/GLCustom/index.mjs';
import { ElAmapLayerHeatMap } from './packages/layer/data/HeatMap/index.mjs';
import { ElAmapLayerImage } from './packages/layer/data/Image/index.mjs';
import { ElAmapLayerLabels } from './packages/layer/data/Labels/index.mjs';
import { ElAmapLayerVector } from './packages/layer/data/Vector/index.mjs';
import { ElAmapLayerBuildings } from './packages/layer/official/Buildings/index.mjs';
import { ElAmapLayerDefault } from './packages/layer/official/DefaultLayer/index.mjs';
import { ElAmapLayerDistrict } from './packages/layer/official/DistrictLayer/index.mjs';
import { ElAmapLayerIndoorMap } from './packages/layer/official/IndoorMap/index.mjs';
import { ElAmapLayerRoadNet } from './packages/layer/official/RoadNet/index.mjs';
import { ElAmapLayerSatellite } from './packages/layer/official/Satellite/index.mjs';
import { ElAmapLayerTile } from './packages/layer/official/TileLayer/index.mjs';
import { ElAmapLayerTraffic } from './packages/layer/official/Traffic/index.mjs';
import { ElAmapLayerMapboxVectorTile } from './packages/layer/standard/MapboxVectorTileLayer/index.mjs';
import { ElAmapLayerWms } from './packages/layer/standard/WMS/index.mjs';
import { ElAmapLayerWmts } from './packages/layer/standard/WMTS/index.mjs';
import { ElAmapElasticMarker } from './packages/marker/ElasticMarker/index.mjs';
import { ElAmapLabelMarker } from './packages/marker/LabelMarker/index.mjs';
import { ElAmapMarker } from './packages/marker/Marker/index.mjs';
import { ElAmapMarkerCluster } from './packages/marker/MarkerCluster/index.mjs';
import { ElAmapMassMarks } from './packages/marker/MassMarks/index.mjs';
import { ElAmapText } from './packages/marker/Text/index.mjs';
import { ElAmapBezierCurve } from './packages/vector/BezierCurve/index.mjs';
import { ElAmapCircle } from './packages/vector/Circle/index.mjs';
import { ElAmapEllipse } from './packages/vector/Ellipse/index.mjs';
import { ElAmapGeojson } from './packages/vector/GeoJSON/index.mjs';
import { ElAmapPolygon } from './packages/vector/Polygon/index.mjs';
import { ElAmapPolyline } from './packages/vector/Polyline/index.mjs';
import { ElAmapRectangle } from './packages/vector/Rectangle/index.mjs';
import { ElAmapLayerTiles3d } from './packages/layer/data/Tiles3D/index.mjs';
import { ElAmapControlGeolocation } from './packages/control/Geolocation/index.mjs';
import { ElAmapCircleMarker } from './packages/marker/CircleMarker/index.mjs';
import { ElAmapLayerVideo } from './packages/layer/data/Video/index.mjs';
import { ElAmapMouseTool } from './packages/util/MouseTool/index.mjs';
import { ElAmapLayerDistrictCluster } from './packages/layer/data/DistrictCluster/index.mjs';
import { ElAmapLayerCustomXyz } from './packages/layer/data/CustomXyz/index.mjs';

var Components = [
  ElAmap,
  ElAmapControlControlBar,
  ElAmapControlHawkEye,
  ElAmapControlMapType,
  ElAmapControlScale,
  ElAmapControlToolBar,
  ElAmapSearchBox,
  ElAmapInfoWindow,
  ElAmapLayerCanvas,
  ElAmapLayerCustom,
  ElAmapLayerFlexible,
  ElAmapLayerGlCustom,
  ElAmapLayerHeatMap,
  ElAmapLayerImage,
  ElAmapLayerLabels,
  ElAmapLayerVector,
  ElAmapLayerBuildings,
  ElAmapLayerDefault,
  ElAmapLayerDistrict,
  ElAmapLayerIndoorMap,
  ElAmapLayerRoadNet,
  ElAmapLayerSatellite,
  ElAmapLayerTile,
  ElAmapLayerTraffic,
  ElAmapLayerMapboxVectorTile,
  ElAmapLayerWms,
  ElAmapLayerWmts,
  ElAmapElasticMarker,
  ElAmapLabelMarker,
  ElAmapMarker,
  ElAmapMarkerCluster,
  ElAmapMassMarks,
  ElAmapText,
  ElAmapBezierCurve,
  ElAmapCircle,
  ElAmapEllipse,
  ElAmapGeojson,
  ElAmapPolygon,
  ElAmapPolyline,
  ElAmapRectangle,
  ElAmapLayerTiles3d,
  ElAmapControlGeolocation,
  ElAmapCircleMarker,
  ElAmapLayerVideo,
  ElAmapMouseTool,
  ElAmapLayerDistrictCluster,
  ElAmapLayerCustomXyz
];

export { Components as default };
//# sourceMappingURL=component.mjs.map
