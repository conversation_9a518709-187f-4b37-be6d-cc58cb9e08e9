/// <reference types="@vuemap/amap-loca-types" />
declare const _default: import("vue").DefineComponent<{
    color: {
        type: StringConstructor;
    };
    intensity: {
        type: NumberConstructor;
    };
} & {
    visible: import("@vuemap/vue-amap").IPropOptions<boolean>;
    zIndex: import("@vuemap/vue-amap").IPropOptions<number>;
    reEventWhenUpdate: import("@vuemap/vue-amap").IPropOptions<boolean>;
    extraOptions: import("@vuemap/vue-amap").IPropOptions<any>;
}, {
    emits: (event: "init", ...args: any[]) => void;
    $amapComponent: any;
    $$getInstance: () => Loca.AmbientLight;
    parentInstance: import("@vuemap/vue-amap").IProvideType | undefined;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, "init"[], "init", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    color: {
        type: StringConstructor;
    };
    intensity: {
        type: NumberConstructor;
    };
} & {
    visible: import("@vuemap/vue-amap").IPropOptions<boolean>;
    zIndex: import("@vuemap/vue-amap").IPropOptions<number>;
    reEventWhenUpdate: import("@vuemap/vue-amap").IPropOptions<boolean>;
    extraOptions: import("@vuemap/vue-amap").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
}, {}, {}>;
export default _default;
