"use strict";Object.defineProperty(exports,"__esModule",{value:!0});class Y{constructor(){this._listeners={}}on(t,e,r){this._listeners[t]?this._listeners[t].push({callback:e,isOnce:r}):this._listeners[t]=[{callback:e,isOnce:r}]}off(t,e){if(!e)throw new Error("\u53D6\u6D88\u4E8B\u4EF6\u65F6\u9700\u8981\u4F20\u5165\u539F\u56DE\u8C03\u51FD\u6570");const r=this._listeners[t];if(r&&r.length>0){for(let i=0;i<r.length;i++)if(r[i].callback===e){r.splice(i,1);break}}}emit(t,...e){const r=this._listeners[t];if(r&&r.length>0)for(let n=0;n<r.length;n++){const s=r[n];s.callback.call(this,...e),s.isOnce&&(r.splice(n,1),n--)}const i=this._listeners["*"];if(i&&i.length>0)for(let n=0;n<i.length;n++){const s=i[n];s.callback.call(this,t,...e),s.isOnce&&(i.splice(n,1),n--)}}trigger(t,...e){this.emit(t,e)}}function dt(o,t,e){e===void 0&&(e={});var r={type:"Feature"};return(e.id===0||e.id)&&(r.id=e.id),e.bbox&&(r.bbox=e.bbox),r.properties=t||{},r.geometry=o,r}function H(o,t,e){e===void 0&&(e={});for(var r=0,i=o;r<i.length;r++){var n=i[r];if(n.length<4)throw new Error("Each LinearRing of a Polygon must have 4 or more Positions.");for(var s=0;s<n[n.length-1].length;s++)if(n[n.length-1][s]!==n[0][s])throw new Error("First and last Position are not equivalent.")}var a={type:"Polygon",coordinates:o};return dt(a,t,e)}function Bt(o,t,e){e===void 0&&(e={});var r={type:"MultiPolygon",coordinates:o};return dt(r,t,e)}function ft(o){return o.type==="Feature"?o.geometry:o}/**
 * splaytree v3.1.1
 * Fast Splay tree for Node and browser
 *
 * <AUTHOR> Milevski <<EMAIL>>
 * @license MIT
 * @preserve
 *//*! *****************************************************************************
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the Apache License, Version 2.0 (the "License"); you may not use
this file except in compliance with the License. You may obtain a copy of the
License at http://www.apache.org/licenses/LICENSE-2.0

THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
MERCHANTABLITY OR NON-INFRINGEMENT.

See the Apache Version 2.0 License for specific language governing permissions
and limitations under the License.
***************************************************************************** */function $t(o,t){var e={label:0,sent:function(){if(n[0]&1)throw n[1];return n[1]},trys:[],ops:[]},r,i,n,s;return s={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(l){return function(h){return u([l,h])}}function u(l){if(r)throw new TypeError("Generator is already executing.");for(;e;)try{if(r=1,i&&(n=l[0]&2?i.return:l[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,l[1])).done)return n;switch(i=0,n&&(l=[l[0]&2,n.value]),l[0]){case 0:case 1:n=l;break;case 4:return e.label++,{value:l[1],done:!1};case 5:e.label++,i=l[1],l=[0];continue;case 7:l=e.ops.pop(),e.trys.pop();continue;default:if(n=e.trys,!(n=n.length>0&&n[n.length-1])&&(l[0]===6||l[0]===2)){e=0;continue}if(l[0]===3&&(!n||l[1]>n[0]&&l[1]<n[3])){e.label=l[1];break}if(l[0]===6&&e.label<n[1]){e.label=n[1],n=l;break}if(n&&e.label<n[2]){e.label=n[2],e.ops.push(l);break}n[2]&&e.ops.pop(),e.trys.pop();continue}l=t.call(o,e)}catch(h){l=[6,h],i=0}finally{r=n=0}if(l[0]&5)throw l[1];return{value:l[0]?l[1]:void 0,done:!0}}}var A=function(){function o(t,e){this.next=null,this.key=t,this.data=e,this.left=null,this.right=null}return o}();function zt(o,t){return o>t?1:o<t?-1:0}function P(o,t,e){for(var r=new A(null,null),i=r,n=r;;){var s=e(o,t.key);if(s<0){if(t.left===null)break;if(e(o,t.left.key)<0){var a=t.left;if(t.left=a.right,a.right=t,t=a,t.left===null)break}n.left=t,n=t,t=t.left}else if(s>0){if(t.right===null)break;if(e(o,t.right.key)>0){var a=t.right;if(t.right=a.left,a.left=t,t=a,t.right===null)break}i.right=t,i=t,t=t.right}else break}return i.right=t.left,n.left=t.right,t.left=r.right,t.right=r.left,t}function X(o,t,e,r){var i=new A(o,t);if(e===null)return i.left=i.right=null,i;e=P(o,e,r);var n=r(o,e.key);return n<0?(i.left=e.left,i.right=e,e.left=null):n>=0&&(i.right=e.right,i.left=e,e.right=null),i}function pt(o,t,e){var r=null,i=null;if(t){t=P(o,t,e);var n=e(t.key,o);n===0?(r=t.left,i=t.right):n<0?(i=t.right,t.right=null,r=t):(r=t.left,t.left=null,i=t)}return{left:r,right:i}}function Gt(o,t,e){return t===null?o:(o===null||(t=P(o.key,t,e),t.left=o),t)}function Q(o,t,e,r,i){if(o){r(""+t+(e?"\u2514\u2500\u2500 ":"\u251C\u2500\u2500 ")+i(o)+`
`);var n=t+(e?"    ":"\u2502   ");o.left&&Q(o.left,n,!1,r,i),o.right&&Q(o.right,n,!0,r,i)}}var J=function(){function o(t){t===void 0&&(t=zt),this._root=null,this._size=0,this._comparator=t}return o.prototype.insert=function(t,e){return this._size++,this._root=X(t,e,this._root,this._comparator)},o.prototype.add=function(t,e){var r=new A(t,e);this._root===null&&(r.left=r.right=null,this._size++,this._root=r);var i=this._comparator,n=P(t,this._root,i),s=i(t,n.key);return s===0?this._root=n:(s<0?(r.left=n.left,r.right=n,n.left=null):s>0&&(r.right=n.right,r.left=n,n.right=null),this._size++,this._root=r),this._root},o.prototype.remove=function(t){this._root=this._remove(t,this._root,this._comparator)},o.prototype._remove=function(t,e,r){var i;if(e===null)return null;e=P(t,e,r);var n=r(t,e.key);return n===0?(e.left===null?i=e.right:(i=P(t,e.left,r),i.right=e.right),this._size--,i):e},o.prototype.pop=function(){var t=this._root;if(t){for(;t.left;)t=t.left;return this._root=P(t.key,this._root,this._comparator),this._root=this._remove(t.key,this._root,this._comparator),{key:t.key,data:t.data}}return null},o.prototype.findStatic=function(t){for(var e=this._root,r=this._comparator;e;){var i=r(t,e.key);if(i===0)return e;i<0?e=e.left:e=e.right}return null},o.prototype.find=function(t){return this._root&&(this._root=P(t,this._root,this._comparator),this._comparator(t,this._root.key)!==0)?null:this._root},o.prototype.contains=function(t){for(var e=this._root,r=this._comparator;e;){var i=r(t,e.key);if(i===0)return!0;i<0?e=e.left:e=e.right}return!1},o.prototype.forEach=function(t,e){for(var r=this._root,i=[],n=!1;!n;)r!==null?(i.push(r),r=r.left):i.length!==0?(r=i.pop(),t.call(e,r),r=r.right):n=!0;return this},o.prototype.range=function(t,e,r,i){for(var n=[],s=this._comparator,a=this._root,u;n.length!==0||a;)if(a)n.push(a),a=a.left;else{if(a=n.pop(),u=s(a.key,e),u>0)break;if(s(a.key,t)>=0&&r.call(i,a))return this;a=a.right}return this},o.prototype.keys=function(){var t=[];return this.forEach(function(e){var r=e.key;return t.push(r)}),t},o.prototype.values=function(){var t=[];return this.forEach(function(e){var r=e.data;return t.push(r)}),t},o.prototype.min=function(){return this._root?this.minNode(this._root).key:null},o.prototype.max=function(){return this._root?this.maxNode(this._root).key:null},o.prototype.minNode=function(t){if(t===void 0&&(t=this._root),t)for(;t.left;)t=t.left;return t},o.prototype.maxNode=function(t){if(t===void 0&&(t=this._root),t)for(;t.right;)t=t.right;return t},o.prototype.at=function(t){for(var e=this._root,r=!1,i=0,n=[];!r;)if(e)n.push(e),e=e.left;else if(n.length>0){if(e=n.pop(),i===t)return e;i++,e=e.right}else r=!0;return null},o.prototype.next=function(t){var e=this._root,r=null;if(t.right){for(r=t.right;r.left;)r=r.left;return r}for(var i=this._comparator;e;){var n=i(t.key,e.key);if(n===0)break;n<0?(r=e,e=e.left):e=e.right}return r},o.prototype.prev=function(t){var e=this._root,r=null;if(t.left!==null){for(r=t.left;r.right;)r=r.right;return r}for(var i=this._comparator;e;){var n=i(t.key,e.key);if(n===0)break;n<0?e=e.left:(r=e,e=e.right)}return r},o.prototype.clear=function(){return this._root=null,this._size=0,this},o.prototype.toList=function(){return Vt(this._root)},o.prototype.load=function(t,e,r){e===void 0&&(e=[]),r===void 0&&(r=!1);var i=t.length,n=this._comparator;if(r&&et(t,e,0,i-1,n),this._root===null)this._root=K(t,e,0,i),this._size=i;else{var s=qt(this.toList(),Zt(t,e),n);i=this._size+i,this._root=tt({head:s},0,i)}return this},o.prototype.isEmpty=function(){return this._root===null},Object.defineProperty(o.prototype,"size",{get:function(){return this._size},enumerable:!0,configurable:!0}),Object.defineProperty(o.prototype,"root",{get:function(){return this._root},enumerable:!0,configurable:!0}),o.prototype.toString=function(t){t===void 0&&(t=function(r){return String(r.key)});var e=[];return Q(this._root,"",!0,function(r){return e.push(r)},t),e.join("")},o.prototype.update=function(t,e,r){var i=this._comparator,n=pt(t,this._root,i),s=n.left,a=n.right;i(t,e)<0?a=X(e,r,a,i):s=X(e,r,s,i),this._root=Gt(s,a,i)},o.prototype.split=function(t){return pt(t,this._root,this._comparator)},o.prototype[Symbol.iterator]=function(){var t;return $t(this,function(e){switch(e.label){case 0:t=this.minNode(),e.label=1;case 1:return t?[4,t]:[3,3];case 2:return e.sent(),t=this.next(t),[3,1];case 3:return[2]}})},o}();function K(o,t,e,r){var i=r-e;if(i>0){var n=e+Math.floor(i/2),s=o[n],a=t[n],u=new A(s,a);return u.left=K(o,t,e,n),u.right=K(o,t,n+1,r),u}return null}function Zt(o,t){for(var e=new A(null,null),r=e,i=0;i<o.length;i++)r=r.next=new A(o[i],t[i]);return r.next=null,e.next}function Vt(o){for(var t=o,e=[],r=!1,i=new A(null,null),n=i;!r;)t?(e.push(t),t=t.left):e.length>0?(t=n=n.next=e.pop(),t=t.right):r=!0;return n.next=null,i.next}function tt(o,t,e){var r=e-t;if(r>0){var i=t+Math.floor(r/2),n=tt(o,t,i),s=o.head;return s.left=n,o.head=o.head.next,s.right=tt(o,i+1,e),s}return null}function qt(o,t,e){for(var r=new A(null,null),i=r,n=o,s=t;n!==null&&s!==null;)e(n.key,s.key)<0?(i.next=n,n=n.next):(i.next=s,s=s.next),i=i.next;return n!==null?i.next=n:s!==null&&(i.next=s),r.next}function et(o,t,e,r,i){if(!(e>=r)){for(var n=o[e+r>>1],s=e-1,a=r+1;;){do s++;while(i(o[s],n)<0);do a--;while(i(o[a],n)>0);if(s>=a)break;var u=o[s];o[s]=o[a],o[a]=u,u=t[s],t[s]=t[a],t[a]=u}et(o,t,e,a,i),et(o,t,a+1,r,i)}}function S(o,t){if(!(o instanceof t))throw new TypeError("Cannot call a class as a function")}function gt(o,t){for(var e=0;e<t.length;e++){var r=t[e];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(o,r.key,r)}}function b(o,t,e){return t&&gt(o.prototype,t),e&&gt(o,e),o}var z=function(t,e){return t.ll.x<=e.x&&e.x<=t.ur.x&&t.ll.y<=e.y&&e.y<=t.ur.y},rt=function(t,e){if(e.ur.x<t.ll.x||t.ur.x<e.ll.x||e.ur.y<t.ll.y||t.ur.y<e.ll.y)return null;var r=t.ll.x<e.ll.x?e.ll.x:t.ll.x,i=t.ur.x<e.ur.x?t.ur.x:e.ur.x,n=t.ll.y<e.ll.y?e.ll.y:t.ll.y,s=t.ur.y<e.ur.y?t.ur.y:e.ur.y;return{ll:{x:r,y:n},ur:{x:i,y:s}}},N=Number.EPSILON;N===void 0&&(N=Math.pow(2,-52));var jt=N*N,it=function(t,e){if(-N<t&&t<N&&-N<e&&e<N)return 0;var r=t-e;return r*r<jt*t*e?0:t<e?-1:1},Ut=function(){function o(){S(this,o),this.reset()}return b(o,[{key:"reset",value:function(){this.xRounder=new yt,this.yRounder=new yt}},{key:"round",value:function(e,r){return{x:this.xRounder.round(e),y:this.yRounder.round(r)}}}]),o}(),yt=function(){function o(){S(this,o),this.tree=new J,this.round(0)}return b(o,[{key:"round",value:function(e){var r=this.tree.add(e),i=this.tree.prev(r);if(i!==null&&it(r.key,i.key)===0)return this.tree.remove(e),i.key;var n=this.tree.next(r);return n!==null&&it(r.key,n.key)===0?(this.tree.remove(e),n.key):e}}]),o}(),G=new Ut,Z=function(t,e){return t.x*e.y-t.y*e.x},vt=function(t,e){return t.x*e.x+t.y*e.y},_t=function(t,e,r){var i={x:e.x-t.x,y:e.y-t.y},n={x:r.x-t.x,y:r.y-t.y},s=Z(i,n);return it(s,0)},V=function(t){return Math.sqrt(vt(t,t))},Wt=function(t,e,r){var i={x:e.x-t.x,y:e.y-t.y},n={x:r.x-t.x,y:r.y-t.y};return Z(n,i)/V(n)/V(i)},Yt=function(t,e,r){var i={x:e.x-t.x,y:e.y-t.y},n={x:r.x-t.x,y:r.y-t.y};return vt(n,i)/V(n)/V(i)},mt=function(t,e,r){return e.y===0?null:{x:t.x+e.x/e.y*(r-t.y),y:r}},xt=function(t,e,r){return e.x===0?null:{x:r,y:t.y+e.y/e.x*(r-t.x)}},Ht=function(t,e,r,i){if(e.x===0)return xt(r,i,t.x);if(i.x===0)return xt(t,e,r.x);if(e.y===0)return mt(r,i,t.y);if(i.y===0)return mt(t,e,r.y);var n=Z(e,i);if(n==0)return null;var s={x:r.x-t.x,y:r.y-t.y},a=Z(s,e)/n,u=Z(s,i)/n,l=t.x+u*e.x,h=r.x+a*i.x,c=t.y+u*e.y,d=r.y+a*i.y,f=(l+h)/2,g=(c+d)/2;return{x:f,y:g}},I=function(){b(o,null,[{key:"compare",value:function(e,r){var i=o.comparePoints(e.point,r.point);return i!==0?i:(e.point!==r.point&&e.link(r),e.isLeft!==r.isLeft?e.isLeft?1:-1:q.compare(e.segment,r.segment))}},{key:"comparePoints",value:function(e,r){return e.x<r.x?-1:e.x>r.x?1:e.y<r.y?-1:e.y>r.y?1:0}}]);function o(t,e){S(this,o),t.events===void 0?t.events=[this]:t.events.push(this),this.point=t,this.isLeft=e}return b(o,[{key:"link",value:function(e){if(e.point===this.point)throw new Error("Tried to link already linked events");for(var r=e.point.events,i=0,n=r.length;i<n;i++){var s=r[i];this.point.events.push(s),s.point=this.point}this.checkForConsuming()}},{key:"checkForConsuming",value:function(){for(var e=this.point.events.length,r=0;r<e;r++){var i=this.point.events[r];if(i.segment.consumedBy===void 0)for(var n=r+1;n<e;n++){var s=this.point.events[n];s.consumedBy===void 0&&i.otherSE.point.events===s.otherSE.point.events&&i.segment.consume(s.segment)}}}},{key:"getAvailableLinkedEvents",value:function(){for(var e=[],r=0,i=this.point.events.length;r<i;r++){var n=this.point.events[r];n!==this&&!n.segment.ringOut&&n.segment.isInResult()&&e.push(n)}return e}},{key:"getLeftmostComparator",value:function(e){var r=this,i=new Map,n=function(a){var u=a.otherSE;i.set(a,{sine:Wt(r.point,e.point,u.point),cosine:Yt(r.point,e.point,u.point)})};return function(s,a){i.has(s)||n(s),i.has(a)||n(a);var u=i.get(s),l=u.sine,h=u.cosine,c=i.get(a),d=c.sine,f=c.cosine;return l>=0&&d>=0?h<f?1:h>f?-1:0:l<0&&d<0?h<f?-1:h>f?1:0:d<l?-1:d>l?1:0}}}]),o}(),Xt=0,q=function(){b(o,null,[{key:"compare",value:function(e,r){var i=e.leftSE.point.x,n=r.leftSE.point.x,s=e.rightSE.point.x,a=r.rightSE.point.x;if(a<i)return 1;if(s<n)return-1;var u=e.leftSE.point.y,l=r.leftSE.point.y,h=e.rightSE.point.y,c=r.rightSE.point.y;if(i<n){if(l<u&&l<h)return 1;if(l>u&&l>h)return-1;var d=e.comparePoint(r.leftSE.point);if(d<0)return 1;if(d>0)return-1;var f=r.comparePoint(e.rightSE.point);return f!==0?f:-1}if(i>n){if(u<l&&u<c)return-1;if(u>l&&u>c)return 1;var g=r.comparePoint(e.leftSE.point);if(g!==0)return g;var p=e.comparePoint(r.rightSE.point);return p<0?1:p>0?-1:1}if(u<l)return-1;if(u>l)return 1;if(s<a){var y=r.comparePoint(e.rightSE.point);if(y!==0)return y}if(s>a){var m=e.comparePoint(r.rightSE.point);if(m<0)return 1;if(m>0)return-1}if(s!==a){var x=h-u,w=s-i,_=c-l,k=a-n;if(x>w&&_<k)return 1;if(x<w&&_>k)return-1}return s>a?1:s<a||h<c?-1:h>c?1:e.id<r.id?-1:e.id>r.id?1:0}}]);function o(t,e,r,i){S(this,o),this.id=++Xt,this.leftSE=t,t.segment=this,t.otherSE=e,this.rightSE=e,e.segment=this,e.otherSE=t,this.rings=r,this.windings=i}return b(o,[{key:"replaceRightSE",value:function(e){this.rightSE=e,this.rightSE.segment=this,this.rightSE.otherSE=this.leftSE,this.leftSE.otherSE=this.rightSE}},{key:"bbox",value:function(){var e=this.leftSE.point.y,r=this.rightSE.point.y;return{ll:{x:this.leftSE.point.x,y:e<r?e:r},ur:{x:this.rightSE.point.x,y:e>r?e:r}}}},{key:"vector",value:function(){return{x:this.rightSE.point.x-this.leftSE.point.x,y:this.rightSE.point.y-this.leftSE.point.y}}},{key:"isAnEndpoint",value:function(e){return e.x===this.leftSE.point.x&&e.y===this.leftSE.point.y||e.x===this.rightSE.point.x&&e.y===this.rightSE.point.y}},{key:"comparePoint",value:function(e){if(this.isAnEndpoint(e))return 0;var r=this.leftSE.point,i=this.rightSE.point,n=this.vector();if(r.x===i.x)return e.x===r.x?0:e.x<r.x?1:-1;var s=(e.y-r.y)/n.y,a=r.x+s*n.x;if(e.x===a)return 0;var u=(e.x-r.x)/n.x,l=r.y+u*n.y;return e.y===l?0:e.y<l?-1:1}},{key:"getIntersection",value:function(e){var r=this.bbox(),i=e.bbox(),n=rt(r,i);if(n===null)return null;var s=this.leftSE.point,a=this.rightSE.point,u=e.leftSE.point,l=e.rightSE.point,h=z(r,u)&&this.comparePoint(u)===0,c=z(i,s)&&e.comparePoint(s)===0,d=z(r,l)&&this.comparePoint(l)===0,f=z(i,a)&&e.comparePoint(a)===0;if(c&&h)return f&&!d?a:!f&&d?l:null;if(c)return d&&s.x===l.x&&s.y===l.y?null:s;if(h)return f&&a.x===u.x&&a.y===u.y?null:u;if(f&&d)return null;if(f)return a;if(d)return l;var g=Ht(s,this.vector(),u,e.vector());return g===null||!z(n,g)?null:G.round(g.x,g.y)}},{key:"split",value:function(e){var r=[],i=e.events!==void 0,n=new I(e,!0),s=new I(e,!1),a=this.rightSE;this.replaceRightSE(s),r.push(s),r.push(n);var u=new o(n,a,this.rings.slice(),this.windings.slice());return I.comparePoints(u.leftSE.point,u.rightSE.point)>0&&u.swapEvents(),I.comparePoints(this.leftSE.point,this.rightSE.point)>0&&this.swapEvents(),i&&(n.checkForConsuming(),s.checkForConsuming()),r}},{key:"swapEvents",value:function(){var e=this.rightSE;this.rightSE=this.leftSE,this.leftSE=e,this.leftSE.isLeft=!0,this.rightSE.isLeft=!1;for(var r=0,i=this.windings.length;r<i;r++)this.windings[r]*=-1}},{key:"consume",value:function(e){for(var r=this,i=e;r.consumedBy;)r=r.consumedBy;for(;i.consumedBy;)i=i.consumedBy;var n=o.compare(r,i);if(n!==0){if(n>0){var s=r;r=i,i=s}if(r.prev===i){var a=r;r=i,i=a}for(var u=0,l=i.rings.length;u<l;u++){var h=i.rings[u],c=i.windings[u],d=r.rings.indexOf(h);d===-1?(r.rings.push(h),r.windings.push(c)):r.windings[d]+=c}i.rings=null,i.windings=null,i.consumedBy=r,i.leftSE.consumedBy=r.leftSE,i.rightSE.consumedBy=r.rightSE}}},{key:"prevInResult",value:function(){return this._prevInResult!==void 0?this._prevInResult:(this.prev?this.prev.isInResult()?this._prevInResult=this.prev:this._prevInResult=this.prev.prevInResult():this._prevInResult=null,this._prevInResult)}},{key:"beforeState",value:function(){if(this._beforeState!==void 0)return this._beforeState;if(!this.prev)this._beforeState={rings:[],windings:[],multiPolys:[]};else{var e=this.prev.consumedBy||this.prev;this._beforeState=e.afterState()}return this._beforeState}},{key:"afterState",value:function(){if(this._afterState!==void 0)return this._afterState;var e=this.beforeState();this._afterState={rings:e.rings.slice(0),windings:e.windings.slice(0),multiPolys:[]};for(var r=this._afterState.rings,i=this._afterState.windings,n=this._afterState.multiPolys,s=0,a=this.rings.length;s<a;s++){var u=this.rings[s],l=this.windings[s],h=r.indexOf(u);h===-1?(r.push(u),i.push(l)):i[h]+=l}for(var c=[],d=[],f=0,g=r.length;f<g;f++)if(i[f]!==0){var p=r[f],y=p.poly;if(d.indexOf(y)===-1)if(p.isExterior)c.push(y);else{d.indexOf(y)===-1&&d.push(y);var m=c.indexOf(p.poly);m!==-1&&c.splice(m,1)}}for(var x=0,w=c.length;x<w;x++){var _=c[x].multiPoly;n.indexOf(_)===-1&&n.push(_)}return this._afterState}},{key:"isInResult",value:function(){if(this.consumedBy)return!1;if(this._isInResult!==void 0)return this._isInResult;var e=this.beforeState().multiPolys,r=this.afterState().multiPolys;switch(L.type){case"union":{var i=e.length===0,n=r.length===0;this._isInResult=i!==n;break}case"intersection":{var s,a;e.length<r.length?(s=e.length,a=r.length):(s=r.length,a=e.length),this._isInResult=a===L.numMultiPolys&&s<a;break}case"xor":{var u=Math.abs(e.length-r.length);this._isInResult=u%2===1;break}case"difference":{var l=function(c){return c.length===1&&c[0].isSubject};this._isInResult=l(e)!==l(r);break}default:throw new Error("Unrecognized operation type found ".concat(L.type))}return this._isInResult}}],[{key:"fromRing",value:function(e,r,i){var n,s,a,u=I.comparePoints(e,r);if(u<0)n=e,s=r,a=1;else if(u>0)n=r,s=e,a=-1;else throw new Error("Tried to create degenerate segment at [".concat(e.x,", ").concat(e.y,"]"));var l=new I(n,!0),h=new I(s,!1);return new o(l,h,[i],[a])}}]),o}(),bt=function(){function o(t,e,r){if(S(this,o),!Array.isArray(t)||t.length===0)throw new Error("Input geometry is not a valid Polygon or MultiPolygon");if(this.poly=e,this.isExterior=r,this.segments=[],typeof t[0][0]!="number"||typeof t[0][1]!="number")throw new Error("Input geometry is not a valid Polygon or MultiPolygon");var i=G.round(t[0][0],t[0][1]);this.bbox={ll:{x:i.x,y:i.y},ur:{x:i.x,y:i.y}};for(var n=i,s=1,a=t.length;s<a;s++){if(typeof t[s][0]!="number"||typeof t[s][1]!="number")throw new Error("Input geometry is not a valid Polygon or MultiPolygon");var u=G.round(t[s][0],t[s][1]);u.x===n.x&&u.y===n.y||(this.segments.push(q.fromRing(n,u,this)),u.x<this.bbox.ll.x&&(this.bbox.ll.x=u.x),u.y<this.bbox.ll.y&&(this.bbox.ll.y=u.y),u.x>this.bbox.ur.x&&(this.bbox.ur.x=u.x),u.y>this.bbox.ur.y&&(this.bbox.ur.y=u.y),n=u)}(i.x!==n.x||i.y!==n.y)&&this.segments.push(q.fromRing(n,i,this))}return b(o,[{key:"getSweepEvents",value:function(){for(var e=[],r=0,i=this.segments.length;r<i;r++){var n=this.segments[r];e.push(n.leftSE),e.push(n.rightSE)}return e}}]),o}(),Qt=function(){function o(t,e){if(S(this,o),!Array.isArray(t))throw new Error("Input geometry is not a valid Polygon or MultiPolygon");this.exteriorRing=new bt(t[0],this,!0),this.bbox={ll:{x:this.exteriorRing.bbox.ll.x,y:this.exteriorRing.bbox.ll.y},ur:{x:this.exteriorRing.bbox.ur.x,y:this.exteriorRing.bbox.ur.y}},this.interiorRings=[];for(var r=1,i=t.length;r<i;r++){var n=new bt(t[r],this,!1);n.bbox.ll.x<this.bbox.ll.x&&(this.bbox.ll.x=n.bbox.ll.x),n.bbox.ll.y<this.bbox.ll.y&&(this.bbox.ll.y=n.bbox.ll.y),n.bbox.ur.x>this.bbox.ur.x&&(this.bbox.ur.x=n.bbox.ur.x),n.bbox.ur.y>this.bbox.ur.y&&(this.bbox.ur.y=n.bbox.ur.y),this.interiorRings.push(n)}this.multiPoly=e}return b(o,[{key:"getSweepEvents",value:function(){for(var e=this.exteriorRing.getSweepEvents(),r=0,i=this.interiorRings.length;r<i;r++)for(var n=this.interiorRings[r].getSweepEvents(),s=0,a=n.length;s<a;s++)e.push(n[s]);return e}}]),o}(),wt=function(){function o(t,e){if(S(this,o),!Array.isArray(t))throw new Error("Input geometry is not a valid Polygon or MultiPolygon");try{typeof t[0][0][0]=="number"&&(t=[t])}catch(s){}this.polys=[],this.bbox={ll:{x:Number.POSITIVE_INFINITY,y:Number.POSITIVE_INFINITY},ur:{x:Number.NEGATIVE_INFINITY,y:Number.NEGATIVE_INFINITY}};for(var r=0,i=t.length;r<i;r++){var n=new Qt(t[r],this);n.bbox.ll.x<this.bbox.ll.x&&(this.bbox.ll.x=n.bbox.ll.x),n.bbox.ll.y<this.bbox.ll.y&&(this.bbox.ll.y=n.bbox.ll.y),n.bbox.ur.x>this.bbox.ur.x&&(this.bbox.ur.x=n.bbox.ur.x),n.bbox.ur.y>this.bbox.ur.y&&(this.bbox.ur.y=n.bbox.ur.y),this.polys.push(n)}this.isSubject=e}return b(o,[{key:"getSweepEvents",value:function(){for(var e=[],r=0,i=this.polys.length;r<i;r++)for(var n=this.polys[r].getSweepEvents(),s=0,a=n.length;s<a;s++)e.push(n[s]);return e}}]),o}(),Jt=function(){b(o,null,[{key:"factory",value:function(e){for(var r=[],i=0,n=e.length;i<n;i++){var s=e[i];if(!(!s.isInResult()||s.ringOut)){for(var a=null,u=s.leftSE,l=s.rightSE,h=[u],c=u.point,d=[];a=u,u=l,h.push(u),u.point!==c;)for(;;){var f=u.getAvailableLinkedEvents();if(f.length===0){var g=h[0].point,p=h[h.length-1].point;throw new Error("Unable to complete output ring starting at [".concat(g.x,",")+" ".concat(g.y,"]. Last matching segment found ends at")+" [".concat(p.x,", ").concat(p.y,"]."))}if(f.length===1){l=f[0].otherSE;break}for(var y=null,m=0,x=d.length;m<x;m++)if(d[m].point===u.point){y=m;break}if(y!==null){var w=d.splice(y)[0],_=h.splice(w.index);_.unshift(_[0].otherSE),r.push(new o(_.reverse()));continue}d.push({index:h.length,point:u.point});var k=u.getLeftmostComparator(a);l=f.sort(k)[0].otherSE;break}r.push(new o(h))}}return r}}]);function o(t){S(this,o),this.events=t;for(var e=0,r=t.length;e<r;e++)t[e].segment.ringOut=this;this.poly=null}return b(o,[{key:"getGeom",value:function(){for(var e=this.events[0].point,r=[e],i=1,n=this.events.length-1;i<n;i++){var s=this.events[i].point,a=this.events[i+1].point;_t(s,e,a)!==0&&(r.push(s),e=s)}if(r.length===1)return null;var u=r[0],l=r[1];_t(u,e,l)===0&&r.shift(),r.push(r[0]);for(var h=this.isExteriorRing()?1:-1,c=this.isExteriorRing()?0:r.length-1,d=this.isExteriorRing()?r.length:-1,f=[],g=c;g!=d;g+=h)f.push([r[g].x,r[g].y]);return f}},{key:"isExteriorRing",value:function(){if(this._isExteriorRing===void 0){var e=this.enclosingRing();this._isExteriorRing=e?!e.isExteriorRing():!0}return this._isExteriorRing}},{key:"enclosingRing",value:function(){return this._enclosingRing===void 0&&(this._enclosingRing=this._calcEnclosingRing()),this._enclosingRing}},{key:"_calcEnclosingRing",value:function(){for(var e=this.events[0],r=1,i=this.events.length;r<i;r++){var n=this.events[r];I.compare(e,n)>0&&(e=n)}for(var s=e.segment.prevInResult(),a=s?s.prevInResult():null;;){if(!s)return null;if(!a)return s.ringOut;if(a.ringOut!==s.ringOut)return a.ringOut.enclosingRing()!==s.ringOut?s.ringOut:s.ringOut.enclosingRing();s=a.prevInResult(),a=s?s.prevInResult():null}}}]),o}(),St=function(){function o(t){S(this,o),this.exteriorRing=t,t.poly=this,this.interiorRings=[]}return b(o,[{key:"addInterior",value:function(e){this.interiorRings.push(e),e.poly=this}},{key:"getGeom",value:function(){var e=[this.exteriorRing.getGeom()];if(e[0]===null)return null;for(var r=0,i=this.interiorRings.length;r<i;r++){var n=this.interiorRings[r].getGeom();n!==null&&e.push(n)}return e}}]),o}(),Kt=function(){function o(t){S(this,o),this.rings=t,this.polys=this._composePolys(t)}return b(o,[{key:"getGeom",value:function(){for(var e=[],r=0,i=this.polys.length;r<i;r++){var n=this.polys[r].getGeom();n!==null&&e.push(n)}return e}},{key:"_composePolys",value:function(e){for(var r=[],i=0,n=e.length;i<n;i++){var s=e[i];if(!s.poly)if(s.isExteriorRing())r.push(new St(s));else{var a=s.enclosingRing();a.poly||r.push(new St(a)),a.poly.addInterior(s)}}return r}}]),o}(),te=function(){function o(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:q.compare;S(this,o),this.queue=t,this.tree=new J(e),this.segments=[]}return b(o,[{key:"process",value:function(e){var r=e.segment,i=[];if(e.consumedBy)return e.isLeft?this.queue.remove(e.otherSE):this.tree.remove(r),i;var n=e.isLeft?this.tree.insert(r):this.tree.find(r);if(!n)throw new Error("Unable to find segment #".concat(r.id," ")+"[".concat(r.leftSE.point.x,", ").concat(r.leftSE.point.y,"] -> ")+"[".concat(r.rightSE.point.x,", ").concat(r.rightSE.point.y,"] ")+"in SweepLine tree. Please submit a bug report.");for(var s=n,a=n,u=void 0,l=void 0;u===void 0;)s=this.tree.prev(s),s===null?u=null:s.key.consumedBy===void 0&&(u=s.key);for(;l===void 0;)a=this.tree.next(a),a===null?l=null:a.key.consumedBy===void 0&&(l=a.key);if(e.isLeft){var h=null;if(u){var c=u.getIntersection(r);if(c!==null&&(r.isAnEndpoint(c)||(h=c),!u.isAnEndpoint(c)))for(var d=this._splitSafely(u,c),f=0,g=d.length;f<g;f++)i.push(d[f])}var p=null;if(l){var y=l.getIntersection(r);if(y!==null&&(r.isAnEndpoint(y)||(p=y),!l.isAnEndpoint(y)))for(var m=this._splitSafely(l,y),x=0,w=m.length;x<w;x++)i.push(m[x])}if(h!==null||p!==null){var _=null;if(h===null)_=p;else if(p===null)_=h;else{var k=I.comparePoints(h,p);_=k<=0?h:p}this.queue.remove(r.rightSE),i.push(r.rightSE);for(var D=r.split(_),M=0,R=D.length;M<R;M++)i.push(D[M])}i.length>0?(this.tree.remove(r),i.push(e)):(this.segments.push(r),r.prev=u)}else{if(u&&l){var E=u.getIntersection(l);if(E!==null){if(!u.isAnEndpoint(E))for(var T=this._splitSafely(u,E),F=0,U=T.length;F<U;F++)i.push(T[F]);if(!l.isAnEndpoint(E))for(var B=this._splitSafely(l,E),$=0,W=B.length;$<W;$++)i.push(B[$])}}this.tree.remove(r)}return i}},{key:"_splitSafely",value:function(e,r){this.tree.remove(e);var i=e.rightSE;this.queue.remove(i);var n=e.split(r);return n.push(i),e.consumedBy===void 0&&this.tree.insert(e),n}}]),o}(),Et=typeof process!="undefined"&&process.env.POLYGON_CLIPPING_MAX_QUEUE_SIZE||1e6,ee=typeof process!="undefined"&&process.env.POLYGON_CLIPPING_MAX_SWEEPLINE_SEGMENTS||1e6,re=function(){function o(){S(this,o)}return b(o,[{key:"run",value:function(e,r,i){L.type=e,G.reset();for(var n=[new wt(r,!0)],s=0,a=i.length;s<a;s++)n.push(new wt(i[s],!1));if(L.numMultiPolys=n.length,L.type==="difference")for(var u=n[0],l=1;l<n.length;)rt(n[l].bbox,u.bbox)!==null?l++:n.splice(l,1);if(L.type==="intersection"){for(var h=0,c=n.length;h<c;h++)for(var d=n[h],f=h+1,g=n.length;f<g;f++)if(rt(d.bbox,n[f].bbox)===null)return[]}for(var p=new J(I.compare),y=0,m=n.length;y<m;y++)for(var x=n[y].getSweepEvents(),w=0,_=x.length;w<_;w++)if(p.insert(x[w]),p.size>Et)throw new Error("Infinite loop when putting segment endpoints in a priority queue (queue size too big). Please file a bug report.");for(var k=new te(p),D=p.size,M=p.pop();M;){var R=M.key;if(p.size===D){var E=R.segment;throw new Error("Unable to pop() ".concat(R.isLeft?"left":"right"," SweepEvent ")+"[".concat(R.point.x,", ").concat(R.point.y,"] from segment #").concat(E.id," ")+"[".concat(E.leftSE.point.x,", ").concat(E.leftSE.point.y,"] -> ")+"[".concat(E.rightSE.point.x,", ").concat(E.rightSE.point.y,"] from queue. ")+"Please file a bug report.")}if(p.size>Et)throw new Error("Infinite loop when passing sweep line over endpoints (queue size too big). Please file a bug report.");if(k.segments.length>ee)throw new Error("Infinite loop when passing sweep line over endpoints (too many sweep line segments). Please file a bug report.");for(var T=k.process(R),F=0,U=T.length;F<U;F++){var B=T[F];B.consumedBy===void 0&&p.insert(B)}D=p.size,M=p.pop()}G.reset();var $=Jt.factory(k.segments),W=new Kt($);return W.getGeom()}}]),o}(),L=new re,ie=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];return L.run("union",t,r)},ne=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];return L.run("intersection",t,r)},oe=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];return L.run("xor",t,r)},se=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];return L.run("difference",t,r)},ae={union:ie,intersection:ne,xor:oe,difference:se};function ue(o,t,e){e===void 0&&(e={});var r=ft(o),i=ft(t),n=ae.intersection(r.coordinates,i.coordinates);return n.length===0?null:n.length===1?H(n[0],e.properties):Bt(n,e.properties)}const v={mergeArray(o,t){if(t.length<5e4)o.push.apply(o,t);else for(let e=0,r=t.length;e<r;e+=1)o.push(t[e])},now:Date.now||function(){return new Date().getTime()},bind(o,t){return o.bind?o.bind(t):function(){return o.apply(t,arguments)}},forEach(o,t,e){if(o.forEach)return o.forEach(t,e);for(let r=0,i=o.length;r<i;r++)t.call(e,o[r],r)},map(o,t,e){if(o.map)return o.map(t,e);const r=[];for(let i=0,n=o.length;i<n;i++)r[i]=t.call(e,o[i],i);return r},merge(o,t){if(t.length<5e4)Array.prototype.push.apply(o,t);else for(let e=0,r=t.length;e<r;e+=1)o.push(t[e])},arrayIndexOf(o,t,e){if(o.indexOf)return o.indexOf(t,e);let r,i=o,n=i.length>>>0;if(n===0)return-1;const s=0|e;if(s>=n)return-1;for(r=Math.max(s>=0?s:n-Math.abs(s),0);r<n;){if(r in i&&i[r]===t)return r;r++}return-1},extend(o){return o||(o={}),this.extendObjs(o,Array.prototype.slice.call(arguments,1))},extendObjs(o,t){o||(o={});for(let e=0,r=t.length;e<r;e++){const i=t[e];if(i)for(const n in i)i.hasOwnProperty(n)&&(o[n]=i[n])}return o},debounce(o,t,e){let r,i,n,s,a,u=function(){const l=v.now()-s;l<t&&l>=0?r=setTimeout(u,t-l):(r=null,e||(a=o.apply(n,i),r||(n=i=null)))};return function(){n=this,i=arguments,s=v.now();const l=e&&!r;return r||(r=setTimeout(u,t)),l&&(a=o.apply(n,i),n=i=null),a}},throttle(o,t,e){let r,i,n,s=null,a=0;e||(e={});const u=function(){a=e.leading===!1?0:v.now(),s=null,n=o.apply(r,i),s||(r=i=null)};return function(){const l=v.now();a||e.leading!==!1||(a=l);const h=t-(l-a);return r=this,i=arguments,h<=0||h>t?(s&&(clearTimeout(s),s=null),a=l,n=o.apply(r,i),s||(r=i=null)):s||e.trailing===!1||(s=setTimeout(u,h)),n}},escapeHtml(o){const t={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;"};return`${o}`.replace(/[&<>"']/g,function(e){return t[e]})}};var nt={BBRFLAG:{I:1,S:2},ADCODES:{COUNTRY:1e5}};function le(o){return o}function he(o){if(o==null)return le;var t,e,r=o.scale[0],i=o.scale[1],n=o.translate[0],s=o.translate[1];return function(a,u){u||(t=e=0);var l=2,h=a.length,c=new Array(h);for(c[0]=(t+=a[0])*r+n,c[1]=(e+=a[1])*i+s;l<h;)c[l]=a[l],++l;return c}}function ce(o,t){for(var e,r=o.length,i=r-t;i<--r;)e=o[i],o[i++]=o[r],o[r]=e}function de(o,t){return typeof t=="string"&&(t=o.objects[t]),t.type==="GeometryCollection"?{type:"FeatureCollection",features:t.geometries.map(function(e){return Lt(o,e)})}:Lt(o,t)}function Lt(o,t){var e=t.id,r=t.bbox,i=t.properties==null?{}:t.properties,n=fe(o,t);return e==null&&r==null?{type:"Feature",properties:i,geometry:n}:r==null?{type:"Feature",id:e,properties:i,geometry:n}:{type:"Feature",id:e,bbox:r,properties:i,geometry:n}}function fe(o,t){var e=he(o.transform),r=o.arcs;function i(h,c){c.length&&c.pop();for(var d=r[h<0?~h:h],f=0,g=d.length;f<g;++f)c.push(e(d[f],f));h<0&&ce(c,g)}function n(h){return e(h)}function s(h){for(var c=[],d=0,f=h.length;d<f;++d)i(h[d],c);return c.length<2&&c.push(c[0]),c}function a(h){for(var c=s(h);c.length<4;)c.push(c[0]);return c}function u(h){return h.map(a)}function l(h){var c=h.type,d;switch(c){case"GeometryCollection":return{type:c,geometries:h.geometries.map(l)};case"Point":d=n(h.coordinates);break;case"MultiPoint":d=h.coordinates.map(n);break;case"LineString":d=s(h.arcs);break;case"MultiLineString":d=h.arcs.map(s);break;case"Polygon":d=u(h.arcs);break;case"MultiPolygon":d=h.arcs.map(u);break;default:return null}return{type:c,coordinates:d}}return l(t)}function pe(o,t){let e,r,i,n,s=o;e=t[t.length-2];for(let a=0,u=t.length-1;a<u;a++){r=t[a];const l=s;s=[],i=l[l.length-1];for(let h=0,c=l.length;h<c;h++)n=l[h],ot(n,e,r)?(ot(i,e,r)||s.push(kt(e,r,i,n)),s.push(n)):ot(i,e,r)&&s.push(kt(e,r,i,n)),i=n;e=r}return s.length<3?[]:(s.push(s[0]),s)}function ge(o,t,e){const r=(e[1]-t[1])/(e[0]-t[0])*(o[0]-t[0])+t[1];return Math.abs(r-o[1])<1e-6&&o[0]>=t[0]&&o[0]<=e[0]}function ye(o,t){for(let e=0,r=t.length;e<r-1;e++)if(ge(o,t[e],t[e+1]))return!0;return!1}function ve(o,t){let e=!1;for(let r=o[0],i=o[1],n=0,s=t.length,a=s-1;n<s;a=n++){const u=t[n][0],l=t[n][1],h=t[a][0],c=t[a][1];l>i!=c>i&&r<(h-u)*(i-l)/(c-l)+u&&(e=!e)}return e}function _e(o,t,e){let r,i=t[0],n=t[1];const s=e[0]-i,a=e[1]-n,u=s*s+a*a;return u>0&&(r=((o[0]-i)*s+(o[1]-n)*a)/u,r>1?(i=e[0],n=e[1]):r>0&&(i+=s*r,n+=a*r)),[i,n]}function me(o,t,e){const r=_e(o,t,e),i=o[0]-r[0],n=o[1]-r[1];return i*i+n*n}function xe(o,t){let e=Number.MAX_VALUE;for(let r=0,i=t.length;r<i-1;r++){const n=me(o,t[r],t[r+1]);n<e&&(e=n)}return e}function ot(o,t,e){return(e[0]-t[0])*(o[1]-t[1])>(e[1]-t[1])*(o[0]-t[0])}function kt(o,t,e,r){const i=[o[0]-t[0],o[1]-t[1]],n=[e[0]-r[0],e[1]-r[1]],s=o[0]*t[1]-o[1]*t[0],a=e[0]*r[1]-e[1]*r[0],u=1/(i[0]*n[1]-i[1]*n[0]);return[(s*n[0]-a*i[0])*u,(s*n[1]-a*i[1])*u]}var j={sqClosestDistanceToPolygon:xe,pointOnPolygon:ye,pointInPolygon:ve,polygonClip:pe};const st=nt.BBRFLAG,at=[];function be(o,t){const e=[];for(let r=0,i=o.length;r<i;r++){const n=o[r].split("-");let s=n[0],a=n.length>1?n[1]:s;s=parseInt(s,t),a=parseInt(a,t);for(let u=s;u<=a;u++)e.push(u)}return e}function It(o,t,e){if(o[t])throw new Error(`Alreay exists:  ${o[t]}`);o[t]=e}function we(o){return at[o]||(at[o]=[st.I,o]),at[o]}function Se(o,t,e){if(o)for(let r=o.split(":"),i=parseInt(r[0],t),n=be(r[1].split(","),t),s=we(i),a=0,u=n.length;a<u;a++)It(e,n[a],s)}function Ee(o,t,e){if(o){const r=[];let i=o.split(":"),n=parseInt(i[0],t);const s=i[1].split(";");for(let a=0,u=s.length;a<u;a++){i=s[a].split(",");const l=[parseInt(i[0],t),0];i.length>1&&(l[1]=parseInt(i[1],t)),r.push(l)}It(e,n,[st.S,r])}}function At(o,t){if(!o)return null;const e=o.split(","),r=[];for(let i=0,n=e.length;i<n;i++){if(parseInt(e[i],t)<0)return null;r.push(parseInt(e[i],t))}return r}function Le(o,t){if(!o)return null;const e=o.split(";"),r=[];for(let i=0,n=e.length;i<n;i++)r.push(At(e[i],t));return r}function ke(o){let t,e;const r=o.r,i=[],n=o.idx.i.split("|");for(o.idx.i=null,t=0,e=n.length;t<e;t++)Se(n[t],r,i);n.length=0;const s=o.idx.s.split("|");for(o.idx.s=null,t=0,e=s.length;t<e;t++)Ee(s[t],r,i);s.length=0,o.idx=null,o.idxList=i,o.mxr&&(o.maxRect=At(o.mxr,r),o.mxr=null),o.mxsr&&(o.maxSubRect=Le(o.mxsr,r),o.mxsr=null)}function Ie(o,t,e){for(let r=o.geoData.sub.features,i=0,n=e.length;i<n;i++){const s=e[i],a=r[s[0]],u=a.geometry.coordinates[s[1]][0],l=j.polygonClip(u,t);!l||l.length<4?console.warn(`Cliped ring length werid: ${l}`):s[2]=l}return!0}function Ae(o,t,e){const r=o.bbIndex,i=r.s;(t<0||e<0||e>=r.h||t>=r.w)&&console.warn("Wrong x,y",t,e,r);const n=e*r.w+t,s=r.idxList[n];if(s[0]!==st.S)return!1;const a=s[1];if(a[0].length>2)return!1;const u=t*i+r.l,l=e*i+r.t;return Ie(o,[[u,l],[u+i,l],[u+i,l+i],[u,l+i],[u,l]],a),!0}var Pt={prepareGridFeatureClip:Ae,buildIdxList:ke};class C{constructor(t,e,r,i){this.x=t,this.y=e,this.width=r,this.height=i}static getBoundsItemToExpand(){return new C(Number.MAX_VALUE,Number.MAX_VALUE,-1,-1)}static boundsIntersect(t,e){return t.x<=e.x+e.width&&e.x<=t.x+t.width&&t.y<=e.y+e.height&&e.y<=t.y+t.height}isEmpty(){return this.width<0}expandByPoint(t,e){let r,i,n,s;this.isEmpty()?(r=n=t,i=s=e):(r=this.x,i=this.y,n=this.x+this.width,s=this.y+this.height,t<r?r=t:t>n&&(n=t),e<i?i=e:e>s&&(s=e)),this.x=r,this.y=i,this.width=n-r,this.height=s-i}}function Pe(o){const t={},e=o.objects;for(const r in e)t[r]=de(o,e[r]);return t}function Ne(o){for(let t=o.sub?o.sub.features:[],e=o.parent.properties,r=(e.acroutes||[]).concat([e.adcode]),i=0,n=t.length;i<n;i++)t[i].properties.subFeatureIndex=i,t[i].properties.acroutes=r}function Me(o){if(!o._isBuiled){Pt.buildIdxList(o.bbIndex),o.geoData=Pe(o.topo),o.geoData.sub&&Ne(o.geoData);const t=o.topo.bbox;o.bounds=new C(t[0],t[1],t[2]-t[0],t[3]-t[1]),o.topo=null,o._isBuiled=!0}return o}var Re={buildData:Me};const ut={},O=Math.PI/180,Nt=180/Math.PI,Fe=Math.PI/4,Mt=.5/Math.PI;function lt(o){return ut[o]||(ut[o]=256*Math.pow(2,o)),ut[o]}function Oe(o){let t=o[1],e=o[0]*O,r=t*O;return r=Math.log(Math.tan(Fe+r/2)),[e,r]}function Ce(o,t){t=t||1;const e=Mt,r=.5,i=-e,n=.5;return[t*(e*o[0]+r),t*(i*o[1]+n)]}function De(o){const t=o[0]*Nt,e=(2*Math.atan(Math.exp(o[1]))-Math.PI/2)*Nt;return[parseFloat(t.toFixed(6)),parseFloat(e.toFixed(6))]}function Te(o,t){const e=Mt,r=.5,i=-e,n=.5;return[(o[0]/t-r)/e,(o[1]/t-n)/i]}function Rt(o,t,e){const r=Ce(Oe(o),t);return e&&(r[0]=Math.round(r[0]),r[1]=Math.round(r[1])),r}function Be(o,t,e){return Rt(o,lt(t),e)}function $e(o,t){const e=lt(t),r=Te(o,e);return De(r)}function ze(o,t){const e=Math.cos,r=o[1]*O,i=o[0]*O,n=t[1]*O,s=t[0]*O,a=n-r,u=s-i,l=(1-e(a)+(1-e(u))*e(r)*e(n))/2;return 12756274*Math.asin(Math.sqrt(l))}var ht={haversineDistance:ze,getScale:lt,lngLatToPointByScale:Rt,pointToLngLat:$e,lngLatToPoint:Be};class ct{constructor(t,e,r){this.adcode=t,this._data=e,this._sqScaleFactor=e.scale*e.scale,this._opts=Object.assign({nearTolerance:2},r),this.setNearTolerance(this._opts.nearTolerance)}static getPropsOfFeature(t){return t&&t.properties?t.properties:null}static getAdcodeOfFeature(t){return t?t.properties.adcode:null}static doesFeatureHasChildren(t){return!!t&&t.properties.childrenNum>0}setNearTolerance(t){this._opts.nearTolerance=t,this._sqNearTolerance=t*t}getIdealZoom(){return this._data.idealZoom}_getEmptySubFeatureGroupItem(t){return{subFeatureIndex:t,subFeature:this.getSubFeatureByIndex(t),pointsIndexes:[],points:[]}}groupByPosition(t,e){let r,i,n={},s=null;for(r=0,i=t.length;r<i;r++){const u=this.getLocatedSubFeatureIndex(e.call(null,t[r],r));n[u]||(n[u]=this._getEmptySubFeatureGroupItem(u)),n[u].pointsIndexes.push(r),n[u].points.push(t[r]),u<0&&(s=n[u])}const a=[];if(this._data.geoData.sub)for(r=0,i=this._data.geoData.sub.features.length;r<i;r++)a.push(n[r]||this._getEmptySubFeatureGroupItem(r));return s&&a.push(s),n=null,a}getLocatedSubFeatureIndex(t){return this._getLocatedSubFeatureIndexByPixel(this.lngLatToPixel(t))}getSubFeatureByIndex(t){return t>=0?this.getSubFeatures()[t]:null}_getLocatedSubFeatureIndexByPixel(t){if(!this._data.geoData.sub)return-1;const e=this._data,r=e.bbIndex,i=t[0]-r.l,n=t[1]-r.t,s=Math.floor(n/r.s),a=Math.floor(i/r.s);if(a<0||s<0||s>=r.h||a>=r.w)return-1;const u=s*r.w+a,l=r.idxList[u];if(!l)return-1;const h=nt.BBRFLAG;switch(l[0]){case h.I:return l[1];case h.S:return Pt.prepareGridFeatureClip(e,a,s),this._calcLocatedFeatureIndexOfSList(t,l[1]);default:throw new Error(`Unknown BBRFLAG: ${l[0]}`)}}_calcNearestFeatureIndexOfSList(t,e){let r=[];this._data.geoData.sub&&(r=this._data.geoData.sub.features);const i={sq:Number.MAX_VALUE,idx:-1};for(let n=0,s=e.length;n<s;n++){const a=e[n],u=r[a[0]],l=a[2]||u.geometry.coordinates[a[1]][0],h=j.sqClosestDistanceToPolygon(t,l);h<i.sq&&(i.sq=h,i.idx=a[0])}return i.sq/this._sqScaleFactor<this._sqNearTolerance?i.idx:-1}_calcLocatedFeatureIndexOfSList(t,e){for(let r=this._data.geoData.sub.features,i=0,n=e.length;i<n;i++){const s=e[i],a=r[s[0]],u=s[2]||a.geometry.coordinates[s[1]][0];if(j.pointInPolygon(t,u)||j.pointOnPolygon(t,u))return s[0]}return this._calcNearestFeatureIndexOfSList(t,e)}pixelToLngLat(t,e){return ht.pointToLngLat([t,e],this._data.pz)}lngLatToPixel(t){t instanceof AMap.LngLat&&(t=[t.getLng(),t.getLat()]);const e=ht.lngLatToPoint(t,this._data.pz);return[Math.round(e[0]),Math.round(e[1])]}_convertRingCoordsToLngLats(t){const e=[];for(let r=0,i=t.length;r<i;r++)e[r]=this.pixelToLngLat(t[r][0],t[r][1]);return e}_convertPolygonCoordsToLngLats(t){const e=[];for(let r=0,i=t.length;r<i;r++)e[r]=this._convertRingCoordsToLngLats(t[r]);return e}_convertMultiPolygonCoordsToLngLats(t){const e=[];for(let r=0,i=t.length;r<i;r++)e[r]=this._convertPolygonCoordsToLngLats(t[r]);return e}_convertCoordsToLngLats(t,e){switch(t){case"MultiPolygon":return this._convertMultiPolygonCoordsToLngLats(e);default:throw new Error(`Unknown type ${t}`)}}_createLngLatFeature(t,e){const r=Object.assign({},t);return e&&Object.assign(r.properties,e),r.geometry=Object.assign({},r.geometry),r.geometry.coordinates=this._convertCoordsToLngLats(r.geometry.type,r.geometry.coordinates),r}getAdcode(){return this.getProps("adcode")}getName(){return this.getProps("name")}getChildrenNum(){return this.getProps("childrenNum")}getProps(t){const e=ct.getPropsOfFeature(this._data.geoData.parent);return e?t?e[t]:e:null}getParentFeature(){const t=this._data.geoData;return t.lngLatParent||(t.lngLatParent=this._createLngLatFeature(t.parent)),t.lngLatParent}getParentFeatureInPixel(){return this._data.geoData.parent}getSubFeatures(){const t=this._data.geoData;if(!t.sub)return[];if(!t.lngLatSubList){const e=[];for(let r=t.sub.features,i=0,n=r.length;i<n;i++)e[i]=this._createLngLatFeature(r[i]);t.lngLatSubList=e}return[].concat(t.lngLatSubList)}getSubFeaturesInPixel(){return this._data.geoData.sub?[].concat(this._data.geoData.sub.features):[]}getBounds(){const t=this._data;if(!t.lngLatBounds){const e=this._data.bounds;t.lngLatBounds=new AMap.Bounds(this.pixelToLngLat(e.x,e.y+e.height),this.pixelToLngLat(e.x+e.width,e.y))}return t.lngLatBounds}}class Ge extends Y{constructor(t){super(),this._opts=Object.assign({distDataLoc:"//webapi.amap.com/ui/1.1/ui/geo/DistrictExplorer/assets/d_v2"},t),this._areaNodesForLocating=null,this._areaNodeCache={},this._opts.preload&&this.loadMultiAreaNodes(this._opts.preload)}setAreaNodesForLocating(t){t?Array.isArray(t)||(t=[t]):t=[],this._areaNodesForLocating=t||[]}_loadJson(t,e){const r=this;return fetch(t,{headers:{Accept:"application/json"}}).then(i=>i.json()).then(i=>{e&&e.call(r,null,i)}).catch(i=>{if(!e)throw i;e(i)})}_getAreaNodeDataFileName(t){return`an_${t}.json`}_getAreaNodeDataSrc(t){return`${this._opts.distDataLoc}/${this._getAreaNodeDataFileName(t)}`}loadAreaTree(t){this._loadJson(`${this._opts.distDataLoc}/area_tree.json`,t)}loadCountryNode(t){this.loadAreaNode(nt.ADCODES.COUNTRY,t)}loadMultiAreaNodes(t,e){let r=[],i=!1,n;function s(a){return function(u,l){i||(n--,u?(e&&e(u),i=!0):(r[a]=l,n===0&&e&&e(null,r)))}}if(t&&t.length){const a=t.length;for(let u=0;u<a;u++)this.loadAreaNode(t[u],e?s(u):null)}else e&&e(null,[])}loadAreaNode(t,e,r,i){if(r=r||this,this._areaNodeCache[t]){if(e){const n=this._areaNodeCache[t];i?e.call(r,null,n,!0):setTimeout(function(){e.call(r,null,n)},0)}}else this._loadJson(this._getAreaNodeDataSrc(t),(n,s)=>{n?e&&e.call(r,n):(this._buildAreaNode(t,s),e&&e.call(r,null,this._areaNodeCache[t]))})}getLocalAreaNode(t){return this._areaNodeCache[t]||null}_buildAreaNode(t,e){if(!this._areaNodeCache[t]){if(!e)throw new Error(`Empty distData: ${t}`);const r=new ct(t,Re.buildData(e),this._opts);this._areaNodeCache[t]=r,this._areaNodesForLocating||(this._areaNodesForLocating=[r])}}clearAreaNodeCacheByAdcode(t){const e=this._areaNodeCache;return delete e[t],!0}destroy(){this._areaNodesForLocating=null,this._areaNodeCache=null,this._opts=null}}class Ze{constructor(t){this.isDistReady=!1,this.nodeMap={},this.waitFnList=[],this.singleDistExplorer=new Ge({}),this._opts=v.extend({topAdcodes:[1e5]},t),this._touchMap={},this.singleDistExplorer.loadAreaTree((e,r)=>{if(e)throw e;if(this.filterAreaTree(r),this.singleCountryNode=r,this.isDistReady=!0,this.waitFnList.length){for(let i=0,n=this.waitFnList.length;i<n;i++)this.waitFnList[i][0].call(this.waitFnList[i][1]);this.waitFnList.length=0}this.singleDistExplorer.loadMultiAreaNodes(this._opts.topAdcodes)})}pixelToLngLat(t,e,r){return ht.pointToLngLat([t,e],r)}getBounds(t){const e=t.bbounds;return new AMap.Bounds(this.pixelToLngLat(e.x,e.y+e.height,20),this.pixelToLngLat(e.x+e.width,e.y,20))}filterAreaTree(t){const e=[t];do{const r=e.pop();this.nodeMap[r.adcode]=r;const i=r.bbox;if(r.bbounds=new C(i[0],i[1],i[2],i[3]),r.bbox=this.getBounds(r),r.children)for(let n=r.children,s=0,a=n.length;s<a;s++)n[s].childIdx=s,e.push(n[s])}while(e.length)}isReady(){return this.isDistReady}getParentAdcode(t,e){if(!e){const r=this.getNodeByAdcode(t);if(!r)return console.warn(`Can not find node: ${t}`),null;e=r.acroutes}return e&&e.length?e[e.length-1]:null}getSubIdx(t){return this.getNodeByAdcode(t).childIdx}getChildrenNum(t){const e=this.getNodeByAdcode(t);return this.getChildrenNumOfNode(e)}getChildrenNumOfNode(t){return t.children?t.children.length:t.childrenNum||0}getNodeByAdcode(t){const e=this.nodeMap[t];if(!e){let r=this.singleDistExplorer.getLocalAreaNode(`${`${t}`.substr(0,4)}00`);if(r||(r=this.singleDistExplorer.getLocalAreaNode(`${`${t}`.substr(0,2)}0000`)),!r)return null;for(let i=r.getSubFeatures(),n=0,s=i.length;n<s;n++)if(i[n].properties.adcode===t)return i[n].properties}return e}getNodeChildren(t){const e=this.getNodeByAdcode(t);if(!e)return null;if(e.children)return e.children;if(e.childrenNum>=0){const r=this.singleDistExplorer.getLocalAreaNode(t);if(!r)return null;const i=[],n=r.getSubFeaturesInPixel();for(let s=0,a=n.length;s<a;s++)i.push(n[s].properties);return i}return null}getExplorer(){return this.singleDistExplorer}traverseCountry(t,e,r,i,n){this.traverseNode(this.singleCountryNode,t,e,r,i,n,[])}getNodeBoundsSize(t,e){const r=this.getPixelZoom(),i=Math.pow(2,r-e);return[t.bbounds.width/i,t.bbounds.height/i]}doesRingRingIntersect(t,e){const r=[t.getNorthWest().toArray(),t.getNorthEast().toArray(),t.getSouthEast().toArray(),t.getSouthWest().toArray(),t.getNorthWest().toArray()],i=[e.getNorthWest().toArray(),e.getNorthEast().toArray(),e.getSouthEast().toArray(),e.getSouthWest().toArray(),e.getNorthWest().toArray()];return!!ue(H([r]),H([i]))}traverseNode(t,e,r,i,n,s,a,u){if(!(a&&a.indexOf(t.adcode)>=0)){if(this.doesRingRingIntersect(e,t.bbox)){const l=t.children,h=l&&l.length>0;if(r>t.idealZoom&&h)for(let c=0,d=l.length;c<d;c++)this.traverseNode(l[c],e,r,i,null,s,a);else i.call(s,t)}n&&(u?(u.count++,u.count>=u.total&&n.call(s)):n.call(s))}}onReady(t,e,r){this.isDistReady?r?t.call(e):setTimeout(function(){t.call(e)},0):this.waitFnList.push([t,e])}getPixelZoom(){var t;return(t=this.singleCountryNode)===null||t===void 0?void 0:t.pz}loadAreaNode(t,e,r,i){this.singleDistExplorer.loadAreaNode(t,e,r,i)}isExcludedAdcode(t){const e=this._opts.excludedAdcodes;return e&&e.indexOf(t)>=0}traverseTopNodes(t,e,r,i,n){const s=this._opts.topAdcodes,a=this._opts.excludedAdcodes,u={total:s.length,count:0};for(let l=0,h=s.length;l<h;l++){const c=this.getNodeByAdcode(s[l]);if(!c)throw new Error(`Can not find adcode: ${s[l]}`);this.traverseNode(c,t,e,r,i,n,a,u)}}tryClearCache(t,e){if(!(e<0)){const r=[this.singleCountryNode],i=[],n=this._touchMap;do{const a=r.pop();a.children&&v.mergeArray(r,a.children);const u=n[a.adcode];u&&u!==t&&i.push(a.adcode)}while(r.length);i.sort(function(a,u){const l=n[a]-n[u];return l===0?a-u:l});const s=i.length-e;if(!(s<=0))for(let a=0;a<s;a++)this.singleDistExplorer.clearAreaNodeCacheByAdcode(i[a])&&this.touchAdcode(i[a],null)}}touchAdcode(t,e){this._touchMap[t]=e}destroy(){this.singleDistExplorer.destroy(),this._touchMap={},this.nodeMap={},this.singleDistExplorer=void 0,this._opts=void 0,this.waitFnList=[],this.singleCountryNode=void 0}}function Ve(o){return[o.x,o.y]}class qe{constructor(t){this._data=[],this._pointsMap={},this._opts=v.extend({topAdcode:1e5},t),this.clearData()}clearData(){this._data=[],this._pointsMap={}}setData(t){this.clearData(),this._data=t,this._updatePointsMap(this._opts.topAdcode,"all",t)}_updatePointsMap(t,e,r){let i=this._pointsMap[t];i||(i=this._pointsMap[t]={}),i[e]=r,i[`${e}_pack`]=this._buildPackItemsByAdcode(t,r)}getPointsByAdcode(t,e){return this._pointsMap[t]?this._pointsMap[t][e||"all"]:[]}getPackItemsByAdcode(t,e){return this._pointsMap[t]?this._pointsMap[t][`${e||"all"}_pack`]:[]}_buildPackItemsByAdcode(t,e){const r=this._opts.pointPacker,i=[];for(let n=0,s=e.length;n<s;n++)i[n]=r.call(this._opts.pointPackerThisArg,e[n]);return i}calcDistGroup(t,e,r,i){const n=this._opts.distMgr.getNodeByAdcode(t);let s=n.acroutes||[1e5];e&&n.acroutes&&(s=[].concat(s),s.push(t)),this._calcGroupWithRoutes(s,0,r,i)}_calcGroupWithRoutes(t,e,r,i){const n=()=>{e<t.length-1?this._calcGroupWithRoutes(t,e+1,r,i):r&&r.call(i)},s=t[e];if(this.getPointsByAdcode(s,"__done"))n.call(this);else{const a=this.getPointsByAdcode(s);if(!a)throw new Error(`Not points found:  ${s}`);this._opts.distMgr.getExplorer().loadAreaNode(s,(u,l)=>{this._groupByAreaNode(l,a),n.call(this)},this,!0)}}_groupByAreaNode(t,e){const r=t.groupByPosition(e,Ve),i=t.getAdcode()===this._opts.topAdcode,n=[];for(let s=0,a=r.length;s<a;s++){const u=r[s];u.subFeature?(this._updatePointsMap(u.subFeature.properties.adcode,"all",u.points),i&&v.mergeArray(n,u.points)):this._updatePointsMap(t.getAdcode(),"hanging",u.points)}i&&this._updatePointsMap(t.getAdcode(),"all",n),this._updatePointsMap(t.getAdcode(),"__done",!0)}destroy(){this.clearData(),this._opts=null}}var je=Object.defineProperty,Ue=Object.defineProperties,We=Object.getOwnPropertyDescriptors,Ft=Object.getOwnPropertySymbols,Ye=Object.prototype.hasOwnProperty,He=Object.prototype.propertyIsEnumerable,Ot=(o,t,e)=>t in o?je(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,Ct=(o,t)=>{for(var e in t||(t={}))Ye.call(t,e)&&Ot(o,e,t[e]);if(Ft)for(var e of Ft(t))He.call(t,e)&&Ot(o,e,t[e]);return o},Xe=(o,t)=>Ue(o,We(t));class Qe extends Y{constructor(t,e){super(),this.baseId=1,this._currentZoom=2,this._currentFeatures=[],this._loadLeft=0,this._polygonCache=[],this._markerCache=[],this._opts=v.extend({engine:"default",areaNodeCacheLimit:-1,minHeightToShowSubFeatures:630,minSiblingAvgHeightToShowSubFeatures:600,minSubAvgHeightToShowSubFeatures:300,zooms:[2,30],clusterMarkerEventSupport:!0,clusterMarkerClickToShowSub:!0,featureEventSupport:!0,featureClickToShowSub:!1,featureStyleByLevel:{country:{strokeColor:"rgb(31, 119, 180)",strokeOpacity:.9,strokeWeight:2,fillColor:"rgb(49, 163, 84)",fillOpacity:.8},province:{strokeColor:"rgb(31, 119, 180)",strokeOpacity:.9,strokeWeight:2,fillColor:"rgb(116, 196, 118)",fillOpacity:.7},city:{strokeColor:"rgb(31, 119, 180)",strokeOpacity:.9,strokeWeight:2,fillColor:"rgb(161, 217, 155)",fillOpacity:.6},district:{strokeColor:"rgb(31, 119, 180)",strokeOpacity:.9,strokeWeight:2,fillColor:"rgb(199, 233, 192)",fillOpacity:.5}}},e),this._map=this._opts.map,this._createLayer(),this._ins=t,this._isRendering=!1,this._loadLeft=0,this._currentFeatures=[]}_createLayer(){this.markerGroup=new AMap.OverlayGroup,this._map.add(this.markerGroup),this.layer=new AMap.VectorLayer({zIndex:this._opts.zIndex||10,visible:this._opts.visible||!0}),this._map.addLayer(this.layer)}zoomToShowSubFeatures(t,e){const r=this.getMinZoomToShowSub(t);if(r>=3){const i=this._ins.getMap();i&&(e||(e=this._ins._distMgr.getNodeByAdcode(t).center),i.setZoomAndCenter(r,e))}}getPixelRatio(){return Math.min(2,Math.round(window.devicePixelRatio||1))}refreshViewState(){if(!this._ins._distMgr.isReady())return!1;const t=this._ins;if(!t.isReady())return!1;const e=t.getMap(),r=e.getBounds(),i=e.getSize(),n=e.getZoom(3),s=this._opts.zooms[1],a=Math.pow(2,s-n),u=r.getNorthWest(),l=e.lngLatToCoords([u.getLng(),u.getLat()]),h=new C(l[0],l[1],i.width*a,i.height*a);this._currentZoom=n,this._currentScaleFactor=a,this._currentViewBounds=h,this._currentViewBoundsInLngLat=r,this._currentPixelRatio=this.getPixelRatio()}renderViewport(){if(this.refreshViewState(),!this._currentViewBounds)return!1;this._currentRenderId=this.baseId++,this._loadLeft=0,this._currentFeatures=[],this._renderViewDist(this._currentRenderId),this._isRendering=!1}getCurrentRenderId(){return this._currentRenderId}isRenderIdStillValid(t){return t===this._currentRenderId}_renderViewDist(t){const e=[];if(this._currentZoom<this._opts.zooms[0]||this._currentZoom>this._opts.zooms[1]){this.isRenderIdStillValid(t)&&this._prepareFeatures(t,e);return}this._ins.getDistMgr().traverseTopNodes(this._currentViewBoundsInLngLat,this._currentZoom,r=>{e.push(r.adcode)},()=>{this.isRenderIdStillValid(t)&&this._prepareFeatures(t,e)},this)}getMinZoomToShowSub(t){const e=this._ins._distMgr.getNodeByAdcode(t);if(!e||!e.idealZoom)return-1;if(!e._minZoomToShowSub){const r=this._ins.getZooms();for(let i=r[0];i<=r[1];i++)if(this.shouldShowSubOnZoom(e,i)){e._minZoomToShowSub=i;break}}return e._minZoomToShowSub||-1}shouldShowSubOnZoom(t,e){if(!t.idealZoom)return!1;if(t._minZoomToShowSub&&e>=t._minZoomToShowSub)return!0;let r=this._ins._distMgr.getNodeBoundsSize(t,e);if(t.adcode===1e5&&r[1]>400)return!0;if(r[1]<this._opts.minHeightToShowSubFeatures)return!1;let i,n,s;if(t.children){const u=t.children;if(s=0,n=u.length,n){for(i=0;i<n;i++)r=this._ins._distMgr.getNodeBoundsSize(u[i],e),s+=r[1];if(s/n<this._opts.minSubAvgHeightToShowSubFeatures)return!1}}const a=this._ins._distMgr.getParentAdcode(t.adcode,t.acroutes);if(a){const u=this._ins._distMgr.getNodeByAdcode(a),l=u.children;if(l||console.error("No children bound",t,u),n=l.length,n>1){for(s=0,i=0;i<n;i++)l[i].adcode!==t.adcode&&(r=this._ins._distMgr.getNodeBoundsSize(l[i],e),s+=r[1]);if(s/(n-1)<this._opts.minSiblingAvgHeightToShowSubFeatures)return!1}}return!0}_shouldShowSub(t){return!(!t.children||!t.children.length)&&this.shouldShowSubOnZoom(t,this._currentZoom)}_prepareFeatures(t,e){const r=[],i=[];for(let n=0,s=e.length;n<s;n++){const a=this._ins._distMgr.getNodeByAdcode(e[n]);if(!a)throw new Error(`Can not find node: ${e[n]}`);this._shouldShowSub(a)?i.push(e[n]):r.push(e[n])}this._prepareSelfFeatures(t,r),this._prepareSubFeatures(t,i),this._checkLoadFinish(t)}_prepareSelfFeatures(t,e){let r;const i=this._currentZoom;for(let n=0,s=e.length;n<s;n++){const a=this._ins._distMgr.getNodeByAdcode(e[n]);if(r=null,a.acroutes){const u=this._ins._distMgr.getNodeByAdcode(a.acroutes[a.acroutes.length-1]);(!a.idealZoom||i<a.idealZoom-1||Math.abs(i-u.idealZoom)<=Math.abs(a.idealZoom-i))&&(r=u.adcode)}this._loadAndRenderSelf(t,r||e[n],e[n])}}_prepareSubFeatures(t,e){let r,i;for(r=0,i=e.length;r<i;r++)this._loadAndRenderSub(t,e[r])}_renderSelf(t,e,r){let i;if(e===r.getAdcode())i=r.getParentFeature();else{const n=r.getSubFeatures(),s=this._ins._distMgr.getSubIdx(e);if(i=n[s],!i){console.warn("Werid, can not find sub feature",r.getAdcode(),e);return}if(i.properties.adcode!==e){console.warn("Sub adcode not match!!",n,s);return}}this._ins.getDistCounter().calcDistGroup(e,!1,()=>{this.isRenderIdStillValid(t)&&this._prepRenderFeatureInPixel(t,i)},this)}_checkLoadFinish(t){if(this._loadLeft===0){const e=this;setTimeout(function(){e.isRenderIdStillValid(t)&&e._handleRenderFinish()},0)}}_renderSub(t,e){const r=e.getSubFeatures();this._ins.getDistCounter().calcDistGroup(e.getAdcode(),!0,()=>{if(this.isRenderIdStillValid(t))for(let i=0,n=r.length;i<n;i++)this._prepRenderFeatureInPixel(t,r[i])},this)}_handleRenderFinish(){this._tryFreeMemery(),this._renderAllFeature()}_renderAllFeature(){this._renderAllFeatureByDefault()}_renderAllFeatureByDefault(){var t,e;const r=[],i=[],n=[],s=[];for(let a=0;a<this._polygonCache.length;a++){const u=this._polygonCache[a],l=u.getExtData()._data.adcode;let h=!1;for(let c=0;c<this._currentFeatures.length;c++){const d=this._currentFeatures[c].feature.properties;if(l===d.adcode){h=!0,this._currentFeatures.splice(c,1);break}}h||(i.push(u),this._polygonCache.splice(a,1),s.push(this._markerCache[a]),this._markerCache.splice(a,1),a--)}this._currentFeatures.forEach(a=>{const u=this._createPolygonFeature(a.feature,a.dataItems);this._opts.featureEventSupport&&(u.on("click",v.bind(h=>{this.emit("featureClick",h,a.feature),this._opts.featureClickToShowSub&&this._ins.zoomToShowSubFeatures(a.feature.properties.adcode)},this)),u.on("mouseover",v.bind(h=>{this.emit("featureMouseover",h,a.feature)},this)),u.on("mouseout",v.bind(h=>{this.emit("featureMouseout",h,a.feature)},this)));const l=this._createClusterMarker(a.feature,a.dataItems);this._opts.clusterMarkerEventSupport&&l.on("click",v.bind(h=>{this.emit("clusterMarkerClick",h,Ct({adcode:a.feature.properties.adcode},a)),this._opts.clusterMarkerClickToShowSub&&this._ins.zoomToShowSubFeatures(a.feature.properties.adcode)},this)),r.push(u),n.push(l)}),this.layer.remove(i),(t=this.markerGroup)===null||t===void 0||t.removeOverlays(s),this.layer.add(r),this._polygonCache.push(...r),r.length=0,(e=this.markerGroup)===null||e===void 0||e.addOverlays(n),this._markerCache.push(...n),n.length=0}_tryFreeMemery(){this._ins.getDistMgr().tryClearCache(this._currentRenderId,this._opts.areaNodeCacheLimit)}_increaseLoadLeft(){this._loadLeft++}_decreaseLoadLeft(t){this._loadLeft--,this._loadLeft===0&&this._checkLoadFinish(t)}_loadAndRenderSelf(t,e,r){this._ins.getDistMgr().touchAdcode(e,t);const i=this._ins._distMgr.getExplorer(),n=i.getLocalAreaNode(e);n?this._renderSelf(t,r,n):(this._increaseLoadLeft(),i.loadAreaNode(e,(s,a)=>{this.isRenderIdStillValid(t)&&(s?console.error(s):this._renderSelf(t,r,a),this._decreaseLoadLeft(t))},this))}_loadAndRenderSub(t,e){this._ins.getDistMgr().touchAdcode(e,t);const r=this._ins._distMgr.getExplorer(),i=r.getLocalAreaNode(e);i?this._renderSub(t,i):(this._increaseLoadLeft(),r.loadAreaNode(e,(n,s)=>{this.isRenderIdStillValid(t)&&(n?console.error(n):this._renderSub(t,s),this._decreaseLoadLeft(t))},this))}_prepRenderFeatureInPixel(t,e){if(!this._ins.getDistMgr().isExcludedAdcode(e.properties.adcode)){const r=this._ins.getDistCounter().getPackItemsByAdcode(e.properties.adcode);this._currentFeatures.push({feature:e,dataItems:r})}}_createPolygonFeature(t,e){const r=Object.assign({},t.properties);if(r.dataItems=e,this._opts.renderPolygon){const n=this._opts.renderPolygon(t,e),s=n.getExtData()||{};return s._data=r,n.setExtData(s),n}const i=this._getFeatureStyleOptions(t,e)||{};return new AMap.Polygon(Xe(Ct({path:t.geometry.coordinates},i),{extData:{_data:r}}))}_createClusterMarker(t,e){const r=t.properties;if(r.dataItems=e,this._opts.renderClusterMarker){const h=this._opts.renderClusterMarker(t,e),c=h.getExtData()||{};return c._data=r,h.setExtData(c),h}const i={title:"amap-ui-district-cluster-marker-title",body:"amap-ui-district-cluster-marker-body",container:"amap-ui-district-cluster-marker"},n=document.createElement("div"),s=document.createElement("span");s.className=i.title;const a=document.createElement("span");a.className=i.body,n.appendChild(s),n.appendChild(a);const u=[],l=[i.container,`level_${r.level}`,`adcode_${r.adcode}`];if(r.acroutes)for(let h=r.acroutes,c=0,d=h.length;c<d;c++)l.push(`descendant_of_${h[c]}`),c===d-1&&l.push(`child_of_${h[c]}`),c>0&&u.push(this._ins._distMgr.getNodeByAdcode(h[c]).name);return n.className=l.join(" "),u.length>0?(u.push(r.name),n.setAttribute("title",u.join(">"))):n.removeAttribute("title"),s.innerHTML=v.escapeHtml(r.name),a.innerHTML=e.length,new AMap.Marker({topWhenClick:!0,offset:new AMap.Pixel(-20,-30),content:n,position:r.center,extData:{_data:r}})}_getFeatureStyleOptions(t,e){const r=this._opts.getFeatureStyle,i=this._opts.featureStyleByLevel[t.properties.level];if(!r)return i;const n=r.call(null,t,e);return n?v.extend({},this._opts.featureStyleByLevel[t.properties.level],n):i}renderLater(t){this._renderLaterId||(this._renderLaterId=setTimeout(()=>{this.render()},t||100))}isRendering(){return this._isRendering}render(){this._renderLaterId&&(clearTimeout(this._renderLaterId),this._renderLaterId=null),this._isRendering=!0,this._ins._distMgr.onReady(this.renderViewport,this,!0)}forceRender(){this._renderLaterId&&(clearTimeout(this._renderLaterId),this._renderLaterId=null),this._isRendering=!0,this.clear(),this._ins._distMgr.onReady(this.renderViewport,this,!0)}getOption(t){return this._opts[t]}getOptions(){return this._opts}show(){var t;this.layer.show(),(t=this.markerGroup)===null||t===void 0||t.show()}hide(){var t;this.layer.hide(),(t=this.markerGroup)===null||t===void 0||t.hide()}clear(){var t;this.layer.clear(),(t=this.markerGroup)===null||t===void 0||t.clearOverlays(),this._polygonCache=[],this._markerCache=[]}setzIndex(t){this.layer.setzIndex(t)}getZooms(){return this._opts.zooms}destroy(){this._map.removeLayer(this.layer),this._map.remove(this.markerGroup),this._currentFeatures=[],this.clear(),this.layer=null,this._map=null,this._ins=null}}class Je{constructor(t,e,r){this.x=t,this.y=e,this.idx=r}}var Ke=Object.defineProperty,tr=Object.defineProperties,er=Object.getOwnPropertyDescriptors,Dt=Object.getOwnPropertySymbols,rr=Object.prototype.hasOwnProperty,ir=Object.prototype.propertyIsEnumerable,Tt=(o,t,e)=>t in o?Ke(o,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):o[t]=e,nr=(o,t)=>{for(var e in t||(t={}))rr.call(t,e)&&Tt(o,e,t[e]);if(Dt)for(var e of Dt(t))ir.call(t,e)&&Tt(o,e,t[e]);return o},or=(o,t)=>tr(o,er(t));class sr extends Y{constructor(t){super(),this._data={list:[],bounds:null,source:null},this._mouseEvent=v.bind(v.debounce(()=>{this.renderLater()},50),this),this.initCSS();const e={autoSetFitView:!0,topAdcodes:[1e5],visible:!0,excludedAdcodes:null,zIndex:10,renderOptions:{}};this._opts=v.extend({},e,t),this.map=t.map,this._distMgr=new Ze({topAdcodes:this._opts.topAdcodes,excludedAdcodes:this._opts.excludedAdcodes}),this._distCounter=new qe({distMgr:this._distMgr,pointPackerThisArg:this,pointPacker:r=>this._packDataItem(r)}),this.renderEngine=new Qe(this,or(nr({},t.renderOptions),{zIndex:this._opts.zIndex,visible:this._opts.visible,map:t.map})),this.renderEngine.on("*",(r,...i)=>{this.emit(r,...i)}),this._opts.data&&this.setData(this._opts.data),this.bindOrUnbindMapEvent()}bindOrUnbindMapEvent(t=!0){const e=t?"on":"off";this.map[e]("moveend",this._mouseEvent),this.map[e]("zoomend",this._mouseEvent),this.map[e]("resize",this._mouseEvent),this.map[e]("rotateend",this._mouseEvent),this.map[e]("dragend",this._mouseEvent)}initCSS(){const t="_amap_district_cluster_css";if(document.getElementById(t))return;const e=".amap-ui-district-cluster-container{cursor:default;-webkit-backface-visibility:hidden;-webkit-transform:translateZ(0) scale(1,1)}.amap-ui-district-cluster-container canvas{position:absolute}.amap-ui-district-cluster-container .amap-ui-hide{display:none!important}.amap-ui-district-cluster-container .overlay-title,.amap-ui-district-cluster-marker{color:#555;background-color:#fffeef;font-size:12px;white-space:nowrap;position:absolute}.amap-ui-district-cluster-container .overlay-title{padding:2px 6px;display:inline-block;z-index:99999;border:1px solid #7e7e7e;border-radius:2px}.amap-ui-district-cluster-container .overlay-title:after,.amap-ui-district-cluster-container .overlay-title:before{content:'';display:block;position:absolute;margin:auto;width:0;height:0;border:solid transparent;border-width:5px}.amap-ui-district-cluster-container .overlay-title.left{transform:translate(10px,-50%)}.amap-ui-district-cluster-container .overlay-title.left:before{top:5px}.amap-ui-district-cluster-container .overlay-title.left:after{left:-9px;top:5px;border-right-color:#fffeef}.amap-ui-district-cluster-container .overlay-title.left:before{left:-10px;border-right-color:#7e7e7e}.amap-ui-district-cluster-container .overlay-title.top{transform:translate(-50%,-130%)}.amap-ui-district-cluster-container .overlay-title.top:before{left:0;right:0}.amap-ui-district-cluster-container .overlay-title.top:after{bottom:-9px;left:0;right:0;border-top-color:#fffeef}.amap-ui-district-cluster-container .overlay-title.top:before{bottom:-10px;border-top-color:#7e7e7e}.amap-ui-district-cluster-marker{border:1px solid #8e8e8e;width:auto;height:22px;border-radius:5px 5px 5px 0;left:0;top:0}.amap-ui-district-cluster-marker:after,.amap-ui-district-cluster-marker:before{content:'';display:block;position:absolute;width:0;height:0;border:solid rgba(0,0,0,0);border-width:6px;left:13px}.amap-ui-district-cluster-marker:after{bottom:-12px;border-top-color:#fffeef}.amap-ui-district-cluster-marker:before{bottom:-13px;border-top-color:#8e8e8e}.amap-ui-district-cluster-marker span{vertical-align:middle;padding:3px 5px;display:inline-block;height:16px;line-height:16px}.amap-ui-district-cluster-marker-title{border-radius:5px 0 0 0}.amap-ui-district-cluster-marker-body{background-color:#dc3912;color:#fff;border-radius:0 5px 5px 0}.amap-ui-district-cluster-marker.level_country .amap-ui-district-cluster-marker-body{background-color:#36c}.amap-ui-district-cluster-marker.level_province .amap-ui-district-cluster-marker-body{background-color:#dc3912}.amap-ui-district-cluster-marker.level_city .amap-ui-district-cluster-marker-body{background-color:#909}.amap-ui-district-cluster-marker.level_district .amap-ui-district-cluster-marker-body{background-color:#d47}",r=document,i="appendChild",n="styleSheet",s=r.createElement("style");s.id=t,s.type="text/css",r.getElementsByTagName("head")[0][i](s),s[n]?s[n].cssText=e:s[i](r.createTextNode(e))}getMinZoomToShowSub(t){return this.renderEngine.getMinZoomToShowSub(t)}getAreaNodeProps(t){return this._distMgr.getNodeByAdcode(t)}getDistrictExplorer(){return this._distMgr.getExplorer()}getRender(){return this.renderEngine}zoomToShowSubFeatures(t,e){this.renderEngine.zoomToShowSubFeatures(t,e)}renderLater(t){this.renderEngine.renderLater(t)}render(){this.renderEngine.render()}forceRender(){this.renderEngine.forceRender()}getDistMgr(){return this._distMgr}_clearData(){this.trigger("willClearData"),this._data?this._data.list.length=0:this._data={list:[],bounds:null},this._data.source=null,this._data.bounds=null,this._data.kdTree=null,this._distCounter.clearData(),this.trigger("didClearData")}_buildDataItems(t){const e=this._opts,r=e.getPosition,i=this._data.list,n=this._data.bounds;for(let s=0,a=t.length;s<a;s++){let u=t[s],l=r.call(this,u,s);l&&(l.getLng&&(l=[l.getLng(),l.getLat()]),i[s]=new Je(l[0],l[1],s),n.expandByPoint(l[0],l[1]))}}getDataItemsByBounds(t){const e=this._data.kdTree;if(!e)return null;const r=t.getSouthWest(),i=t.getNorthEast(),n=this._data.list,s=e.range(r.getLng(),r.getLat(),i.getLng(),i.getLat()),a=[];for(let u=0,l=s.length;u<l;u++)a[u]=this._packDataItem(n[s[u]]);return a}_packDataItem(t){if(!t)return null;if(!t._packedItem){const e=t.idx,r=[t.x,t.y];t._packedItem={dataIndex:e,dataItem:this._data.source[e],position:r}}return t._packedItem}_buildData(t){this._clearData(),this.trigger("willBuildData",t),this._data.source=t,this._data.bounds=C.getBoundsItemToExpand(),this._buildDataItems(t),this._distCounter.setData(this._data.list),this.trigger("didBuildData",t)}setData(t){t||(t=[]),this._buildData(t),this.forceRender(),t.length&&this._opts.autoSetFitView&&this.setFitView()}isReady(){return this._distMgr.isReady()&&!!this._data}setFitView(){const t=this._data.bounds,e=this.getMap(),r=new AMap.Bounds([t.x,t.y],[t.x+t.width,t.y+t.height]);e&&e.setBounds(r)}getDistCounter(){return this._distCounter}getMap(){return this._opts.map}getZooms(){return this.renderEngine.getZooms()}isHidden(){return!this._opts.visible}show(){return this._opts.visible=!0,this.getRender().show()}hide(){return this._opts.visible=!1,this.getRender().hide()}destroy(){this.bindOrUnbindMapEvent(!1),this.getRender().destroy(),this._distCounter.destroy(),this._distMgr.destroy(),this.renderEngine=null,this._data={list:[],bounds:null},this._distMgr=null,this.map=void 0,this._opts=void 0}getzIndex(){return this._opts.zIndex}setzIndex(t){this._opts.zIndex=t,this.getRender().setzIndex(t)}}exports.DistrictCluster=sr;
//# sourceMappingURL=index-cjs.js.map
