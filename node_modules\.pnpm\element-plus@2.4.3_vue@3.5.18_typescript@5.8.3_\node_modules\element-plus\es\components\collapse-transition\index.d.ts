import type { SFCWithInstall } from 'element-plus/es/utils';
declare const _CollapseTransition: SFCWithInstall<import("vue").DefineComponent<{}, {
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    reset: (el: import("vue").RendererElement) => void;
    on: {
        beforeEnter(el: import("vue").RendererElement): void;
        enter(el: import("vue").RendererElement): void;
        afterEnter(el: import("vue").RendererElement): void;
        enterCancelled(el: import("vue").RendererElement): void;
        beforeLeave(el: import("vue").RendererElement): void;
        leave(el: import("vue").RendererElement): void;
        afterLeave(el: import("vue").RendererElement): void;
        leaveCancelled(el: import("vue").RendererElement): void;
    };
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>>;
export default _CollapseTransition;
export declare const ElCollapseTransition: SFCWithInstall<import("vue").DefineComponent<{}, {
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    reset: (el: import("vue").RendererElement) => void;
    on: {
        beforeEnter(el: import("vue").RendererElement): void;
        enter(el: import("vue").RendererElement): void;
        afterEnter(el: import("vue").RendererElement): void;
        enterCancelled(el: import("vue").RendererElement): void;
        beforeLeave(el: import("vue").RendererElement): void;
        leave(el: import("vue").RendererElement): void;
        afterLeave(el: import("vue").RendererElement): void;
        leaveCancelled(el: import("vue").RendererElement): void;
    };
}, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, import("vue").EmitsOptions, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{}>>, {}>>;
