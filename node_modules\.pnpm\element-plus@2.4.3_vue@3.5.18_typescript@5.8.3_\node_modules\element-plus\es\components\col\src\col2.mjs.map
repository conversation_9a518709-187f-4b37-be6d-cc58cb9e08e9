{"version": 3, "file": "col2.mjs", "sources": ["../../../../../../packages/components/col/src/col.vue"], "sourcesContent": ["<template>\n  <component :is=\"tag\" :class=\"colKls\" :style=\"style\">\n    <slot />\n  </component>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, inject } from 'vue'\nimport { isNumber, isObject } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { rowContextKey } from '@element-plus/components/row'\nimport { colProps } from './col'\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElCol',\n})\n\nconst props = defineProps(colProps)\n\nconst { gutter } = inject(rowContextKey, { gutter: computed(() => 0) })\nconst ns = useNamespace('col')\n\nconst style = computed(() => {\n  const styles: CSSProperties = {}\n  if (gutter.value) {\n    styles.paddingLeft = styles.paddingRight = `${gutter.value / 2}px`\n  }\n  return styles\n})\n\nconst colKls = computed(() => {\n  const classes: string[] = []\n  const pos = ['span', 'offset', 'pull', 'push'] as const\n\n  pos.forEach((prop) => {\n    const size = props[prop]\n    if (isNumber(size)) {\n      if (prop === 'span') classes.push(ns.b(`${props[prop]}`))\n      else if (size > 0) classes.push(ns.b(`${prop}-${props[prop]}`))\n    }\n  })\n\n  const sizes = ['xs', 'sm', 'md', 'lg', 'xl'] as const\n  sizes.forEach((size) => {\n    if (isNumber(props[size])) {\n      classes.push(ns.b(`${size}-${props[size]}`))\n    } else if (isObject(props[size])) {\n      Object.entries(props[size]).forEach(([prop, sizeProp]) => {\n        classes.push(\n          prop !== 'span'\n            ? ns.b(`${size}-${prop}-${sizeProp}`)\n            : ns.b(`${size}-${sizeProp}`)\n        )\n      })\n    }\n  })\n\n  // this is for the fix\n  if (gutter.value) {\n    classes.push(ns.is('guttered'))\n  }\n  return [ns.b(), classes]\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;mCAcc,CAAA;AAAA,EACZ,IAAM,EAAA,OAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAE,MAAW,EAAA,GAAA,MAAA,CAAO,aAAe,EAAA,EAAE,QAAQ,QAAS,CAAA,MAAM,CAAC,CAAA,EAAG,CAAA,CAAA;AACtE,IAAM,MAAA,EAAA,GAAK,aAAa,KAAK,CAAA,CAAA;AAE7B,IAAM,MAAA,KAAA,GAAQ,SAAS,MAAM;AAC3B,MAAA,MAAM,SAAwB,EAAC,CAAA;AAC/B,MAAA,IAAI,OAAO,KAAO,EAAA;AAChB,QAAA,MAAA,CAAO,WAAc,GAAA,MAAA,CAAO,YAAe,GAAA,CAAA,EAAG,OAAO,KAAQ,GAAA,CAAA,CAAA,EAAA,CAAA,CAAA;AAAA,OAC/D;AACA,MAAO,OAAA,MAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,MAAA,GAAS,SAAS,MAAM;AAC5B,MAAA,MAAM,UAAoB,EAAC,CAAA;AAC3B,MAAA,MAAM,GAAM,GAAA,CAAC,MAAQ,EAAA,QAAA,EAAU,QAAQ,MAAM,CAAA,CAAA;AAE7C,MAAI,GAAA,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AACpB,QAAA,MAAM,OAAO,KAAM,CAAA,IAAA,CAAA,CAAA;AACnB,QAAI,IAAA,QAAA,CAAS,IAAI,CAAG,EAAA;AAClB,UAAA,IAAI,IAAS,KAAA,MAAA;AAAQ,YAAA,OAAA,CAAQ,KAAK,EAAG,CAAA,CAAA,CAAE,CAAG,EAAA,KAAA,CAAM,OAAO,CAAC,CAAA,CAAA;AAAA,eAAA,IAC/C,IAAO,GAAA,CAAA;AAAG,YAAA,OAAA,CAAQ,KAAK,EAAG,CAAA,CAAA,CAAE,GAAG,IAAQ,CAAA,CAAA,EAAA,KAAA,CAAM,OAAO,CAAC,CAAA,CAAA;AAAA,SAChE;AAAA,OACD,CAAA,CAAA;AAED,MAAA,MAAM,QAAQ,CAAC,IAAA,EAAM,IAAM,EAAA,IAAA,EAAM,MAAM,IAAI,CAAA,CAAA;AAC3C,MAAM,KAAA,CAAA,OAAA,CAAQ,CAAC,IAAS,KAAA;AACtB,QAAI,IAAA,QAAA,CAAS,KAAM,CAAA,IAAA,CAAK,CAAG,EAAA;AACzB,UAAA,OAAA,CAAQ,KAAK,EAAG,CAAA,CAAA,CAAE,GAAG,IAAQ,CAAA,CAAA,EAAA,KAAA,CAAM,OAAO,CAAC,CAAA,CAAA;AAAA,SAClC,MAAA,IAAA,QAAA,CAAS,KAAM,CAAA,IAAA,CAAK,CAAG,EAAA;AAChC,UAAO,MAAA,CAAA,OAAA,CAAQ,MAAM,IAAK,CAAA,CAAA,CAAE,QAAQ,CAAC,CAAC,MAAM,QAAc,CAAA,KAAA;AACxD,YAAA,OAAA,CAAQ,IACN,CAAA,IAAA,KAAS,MACL,GAAA,EAAA,CAAG,EAAE,CAAG,EAAA,IAAA,CAAA,CAAA,EAAQ,IAAQ,CAAA,CAAA,EAAA,QAAA,CAAA,CAAU,IAClC,EAAG,CAAA,CAAA,CAAE,CAAG,EAAA,IAAA,CAAA,CAAA,EAAQ,UAAU,CAChC,CAAA,CAAA;AAAA,WACD,CAAA,CAAA;AAAA,SACH;AAAA,OACD,CAAA,CAAA;AAGD,MAAA,IAAI,OAAO,KAAO,EAAA;AAChB,QAAA,OAAA,CAAQ,IAAK,CAAA,EAAA,CAAG,EAAG,CAAA,UAAU,CAAC,CAAA,CAAA;AAAA,OAChC;AACA,MAAA,OAAO,CAAC,EAAA,CAAG,CAAE,EAAA,EAAG,OAAO,CAAA,CAAA;AAAA,KACxB,CAAA,CAAA;;;;;;;;;;;;;;;;;;"}