export declare const propsType: {
    vid: {
        type: StringConstructor;
    };
    center: {
        type: ArrayConstructor;
    };
    zoom: {
        type: NumberConstructor;
    };
    rotation: {
        type: NumberConstructor;
    };
    pitch: {
        type: NumberConstructor;
    };
    viewMode: {
        type: StringConstructor;
    };
    features: {
        type: ArrayConstructor;
    };
    layers: {
        type: ArrayConstructor;
    };
    zooms: {
        type: ArrayConstructor;
    };
    resizeEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    dragEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    zoomEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    jogEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    pitchEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    rotateEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    animateEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    keyboardEnable: {
        type: BooleanConstructor;
        default: boolean;
    };
    doubleClickZoom: {
        type: BooleanConstructor;
        default: boolean;
    };
    scrollWheel: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchZoom: {
        type: BooleanConstructor;
        default: boolean;
    };
    touchZoomCenter: {
        type: NumberConstructor;
    };
    showLabel: {
        type: BooleanConstructor;
        default: boolean;
    };
    defaultCursor: {
        type: StringConstructor;
    };
    isHotspot: {
        type: BooleanConstructor;
    };
    mapStyle: {
        type: StringConstructor;
    };
    wallColor: {
        type: (StringConstructor | ArrayConstructor)[];
    };
    roofColor: {
        type: (StringConstructor | ArrayConstructor)[];
    };
    showBuildingBlock: {
        type: BooleanConstructor;
        default: boolean;
    };
    showIndoorMap: {
        type: BooleanConstructor;
        default: boolean;
    };
    skyColor: {
        type: (StringConstructor | ArrayConstructor)[];
    };
    labelRejectMask: {
        type: BooleanConstructor;
        default: boolean;
    };
    mask: {
        type: ArrayConstructor;
    };
    WebGLParams: {
        type: ObjectConstructor;
    };
    terrain: {
        type: BooleanConstructor;
        default: boolean;
    };
} & {
    visible: import("../../utils/buildHelper").IPropOptions<boolean>;
    zIndex: import("../../utils/buildHelper").IPropOptions<number>;
    reEventWhenUpdate: import("../../utils/buildHelper").IPropOptions<boolean>;
    extraOptions: import("../../utils/buildHelper").IPropOptions<any>;
};
