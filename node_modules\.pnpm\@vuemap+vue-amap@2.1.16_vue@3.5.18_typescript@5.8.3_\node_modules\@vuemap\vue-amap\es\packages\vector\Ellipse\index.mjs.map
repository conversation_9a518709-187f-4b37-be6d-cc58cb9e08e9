{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/vector/Ellipse/index.ts"], "sourcesContent": ["import Ellipse from './Ellipse.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nEllipse.install = (app: App) => {\r\n  app.component(Ellipse.name, Ellipse);\r\n  return app;\r\n};\r\nexport const ElAmapEllipse = Ellipse as typeof Ellipse & Plugin;\r\nexport default ElAmapEllipse;\r\n\r\nexport type ElAmapEllipseInstance = InstanceType<typeof Ellipse>\r\n"], "names": ["Ellipse"], "mappings": ";;;AAEAA,MAAQ,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AAC9B,EAAI,GAAA,CAAA,SAAA,CAAUA,MAAQ,CAAA,IAAA,EAAMA,MAAO,CAAA,CAAA;AACnC,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AACO,MAAM,aAAgB,GAAAA;;;;"}