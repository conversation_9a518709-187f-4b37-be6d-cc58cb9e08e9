/// <reference types="@vuemap/amap-jsapi-types" />
declare const _default: import("vue").DefineComponent<{
    path: {
        type: ArrayConstructor;
        required: true;
    };
    bubble: {
        type: BooleanConstructor;
        default: boolean;
    };
    cursor: {
        type: StringConstructor;
    };
    strokeColor: {
        type: StringConstructor;
    };
    strokeOpacity: {
        type: NumberConstructor;
    };
    strokeWeight: {
        type: NumberConstructor;
    };
    borderWeight: {
        type: NumberConstructor;
    };
    isOutline: {
        type: BooleanConstructor;
        default: boolean;
    };
    outlineColor: {
        type: StringConstructor;
    };
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    extData: {
        type: ObjectConstructor;
        default: () => null;
    };
    strokeStyle: {
        type: import("vue").PropType<"solid" | "dashed">;
        validator: (value: string) => boolean;
    };
    strokeDasharray: {
        type: ArrayConstructor;
    };
    lineJoin: {
        type: import("vue").PropType<"miter" | "round" | "bevel">;
        validator: (value: string) => boolean;
    };
    lineCap: {
        type: import("vue").PropType<"round" | "butt" | "square">;
        validator: (value: string) => boolean;
    };
    geodesic: {
        type: BooleanConstructor;
        default: boolean;
    };
    showDir: {
        type: BooleanConstructor;
        default: boolean;
    };
    editable: {
        type: BooleanConstructor;
        default: boolean;
    };
    editOptions: {
        type: ObjectConstructor;
    };
} & {
    visible: import("../../../utils").IPropOptions<boolean>;
    zIndex: import("../../../utils").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils").IPropOptions<boolean>;
    extraOptions: import("../../../utils").IPropOptions<any>;
}, {
    props: import("@vue/shared").LooseRequired<{
        readonly draggable: boolean;
        readonly bubble: boolean;
        readonly extData: Record<string, any>;
        readonly path: unknown[];
        readonly isOutline: boolean;
        readonly geodesic: boolean;
        readonly showDir: boolean;
        readonly editable: boolean;
        readonly visible?: boolean | undefined;
        readonly zIndex?: number | undefined;
        readonly reEventWhenUpdate?: boolean | undefined;
        readonly extraOptions?: any;
        readonly cursor?: string | undefined;
        readonly strokeColor?: string | undefined;
        readonly strokeOpacity?: number | undefined;
        readonly strokeWeight?: number | undefined;
        readonly borderWeight?: number | undefined;
        readonly outlineColor?: string | undefined;
        readonly strokeStyle?: "solid" | "dashed" | undefined;
        readonly strokeDasharray?: unknown[] | undefined;
        readonly lineJoin?: "miter" | "round" | "bevel" | undefined;
        readonly lineCap?: "round" | "butt" | "square" | undefined;
        readonly editOptions?: Record<string, any> | undefined;
        readonly onInit?: ((...args: any[]) => any) | undefined;
        readonly "onUpdate:path"?: ((...args: any[]) => any) | undefined;
    } & {}>;
    emits: (event: "init" | "update:path", ...args: any[]) => void;
    $amapComponent: AMap.Polyline;
    destroying: boolean;
    $$getInstance: () => AMap.Polyline;
    parentInstance: import("../../../mixins").IProvideType | undefined;
    resetEditor: import("lodash").DebouncedFunc<() => void>;
    bindModelEvents: () => void;
    emitModel: (target: AMap.Polyline) => void;
    editor: AMap.PolylineEditor;
    attrs: {
        [x: string]: unknown;
    };
    createEditor: () => Promise<void>;
    bindEditorModelEvents: () => void;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, ("init" | "update:path")[], "init" | "update:path", import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    path: {
        type: ArrayConstructor;
        required: true;
    };
    bubble: {
        type: BooleanConstructor;
        default: boolean;
    };
    cursor: {
        type: StringConstructor;
    };
    strokeColor: {
        type: StringConstructor;
    };
    strokeOpacity: {
        type: NumberConstructor;
    };
    strokeWeight: {
        type: NumberConstructor;
    };
    borderWeight: {
        type: NumberConstructor;
    };
    isOutline: {
        type: BooleanConstructor;
        default: boolean;
    };
    outlineColor: {
        type: StringConstructor;
    };
    draggable: {
        type: BooleanConstructor;
        default: boolean;
    };
    extData: {
        type: ObjectConstructor;
        default: () => null;
    };
    strokeStyle: {
        type: import("vue").PropType<"solid" | "dashed">;
        validator: (value: string) => boolean;
    };
    strokeDasharray: {
        type: ArrayConstructor;
    };
    lineJoin: {
        type: import("vue").PropType<"miter" | "round" | "bevel">;
        validator: (value: string) => boolean;
    };
    lineCap: {
        type: import("vue").PropType<"round" | "butt" | "square">;
        validator: (value: string) => boolean;
    };
    geodesic: {
        type: BooleanConstructor;
        default: boolean;
    };
    showDir: {
        type: BooleanConstructor;
        default: boolean;
    };
    editable: {
        type: BooleanConstructor;
        default: boolean;
    };
    editOptions: {
        type: ObjectConstructor;
    };
} & {
    visible: import("../../../utils").IPropOptions<boolean>;
    zIndex: import("../../../utils").IPropOptions<number>;
    reEventWhenUpdate: import("../../../utils").IPropOptions<boolean>;
    extraOptions: import("../../../utils").IPropOptions<any>;
}>> & {
    onInit?: ((...args: any[]) => any) | undefined;
    "onUpdate:path"?: ((...args: any[]) => any) | undefined;
}, {
    draggable: boolean;
    bubble: boolean;
    extData: Record<string, any>;
    isOutline: boolean;
    geodesic: boolean;
    showDir: boolean;
    editable: boolean;
}, {}>;
export default _default;
