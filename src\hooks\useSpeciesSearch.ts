import { ref } from 'vue';
import { getExistSpecies } from '/@/api/screen';
import { createQuery } from '/@/screen/utils/query';
import { useExistSpeciesStore } from '/@/stores/existSpecies';

export interface Species {
	name: string;
	alias?: string;
	[key: string]: any;
}

/**
 * 物种搜索 Hook
 * 提供物种联想搜索功能
 */
export function useSpeciesSearch() {
	const speciesOptions = ref<Species[]>([]);
	const searchLoading = ref(false);
	const searchError = ref<string | null>(null);

	/**
	 * 搜索物种
	 * @param name 物种名称关键字
	 * @param eventTypeOrTypes 事件类型 - 可以是单个值、数组，或不传
	 * @returns Promise<Species[]> 搜索结果
	 *
	 * @example
	 * // 基础搜索
	 * searchSpecies('老虎')
	 *
	 * // 单个事件类型
	 * searchSpecies('老虎', 1)
	 *
	 * // 多个事件类型
	 * searchSpecies('老虎', [1, 2])
	 *
	 * // 'all' 会被忽略
	 * searchSpecies('老虎', 'all')
	 * searchSpecies('老虎', ['all'])
	 */
	const searchSpecies = async (
		name: string,
		eventTypeOrTypes?: number | string | (number | string)[]
	): Promise<Species[]> => {
		if (!name || name.trim().length === 0) {
			return [];
		}

		searchLoading.value = true;
		searchError.value = null;

		try {
			const query = createQuery({ name: name.trim() });

			// 智能处理 eventType 参数
			if (eventTypeOrTypes !== undefined) {
				let processedEventTypes: number[] | undefined;

				if (Array.isArray(eventTypeOrTypes)) {
					// 处理数组：过滤掉 'all' 和非数字值
					const numericTypes = eventTypeOrTypes.filter(
						(type): type is number => typeof type === 'number'
					);
					if (numericTypes.length > 0 && !eventTypeOrTypes.includes('all')) {
						processedEventTypes = numericTypes;
					}
				} else if (eventTypeOrTypes !== 'all' && typeof eventTypeOrTypes === 'number') {
					// 处理单个数值
					processedEventTypes = [eventTypeOrTypes];
				}
				// 如果是 'all' 或其他情况，不设置 eventTypes

				if (processedEventTypes && processedEventTypes.length > 0) {
					query.eventTypes = processedEventTypes;
				}
			}

			const response = await getExistSpecies();
			const results = response.payload || [];

			// 更新选项列表
			speciesOptions.value = results;

			return results;
		} catch (error) {
			console.error('搜索物种失败:', error);
			searchError.value = '搜索物种失败';
			speciesOptions.value = [];
			return [];
		} finally {
			searchLoading.value = false;
		}
	};

	/**
	 * 基于 existSpecies store 的物种搜索
	 * @param name 物种名称关键字
	 * @param eventTypeOrTypes 事件类型 - 可以是单个值、数组，或不传
	 * @returns Promise<Species[]> 搜索结果
	 */
	const searchExistSpecies = async (
		name: string,
		eventTypeOrTypes?: number | string | (number | string)[]
	): Promise<Species[]> => {
		if (!name || name.trim().length === 0) {
			return [];
		}

		searchLoading.value = true;
		searchError.value = null;

		try {
			// 使用 store 获取物种数据
			const existSpeciesStore = useExistSpeciesStore();

			// 确保数据存在（智能缓存）
			await existSpeciesStore.ensureSpecies();

			// 使用 store 的过滤方法
			const results = existSpeciesStore.getFilteredSpecies(name, eventTypeOrTypes);

			// 更新选项列表
			speciesOptions.value = results;

			return results;
		} catch (error) {
			console.error('搜索存在物种失败:', error);
			searchError.value = '搜索存在物种失败';
			speciesOptions.value = [];
			return [];
		} finally {
			searchLoading.value = false;
		}
	};

	/**
	 * 清空搜索结果
	 */
	const clearSearchResults = (): void => {
		speciesOptions.value = [];
		searchError.value = null;
	};

	/**
	 * 重置搜索状态
	 */
	const resetSearchState = (): void => {
		speciesOptions.value = [];
		searchLoading.value = false;
		searchError.value = null;
	};

	return {
		// 状态
		speciesOptions,
		searchLoading,
		searchError,

		// 方法
		searchSpecies,
		searchExistSpecies,
		clearSearchResults,
		resetSearchState,
	};
}
