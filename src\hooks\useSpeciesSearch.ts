import { ref, computed } from 'vue';
import { getSpecies } from '/@/api/screen';

// 防抖函数
function debounce<T extends (...args: any[]) => any>(
	func: T,
	wait: number
): (...args: Parameters<T>) => Promise<ReturnType<T>> {
	let timeout: NodeJS.Timeout;
	return (...args: Parameters<T>): Promise<ReturnType<T>> => {
		return new Promise((resolve) => {
			clearTimeout(timeout);
			timeout = setTimeout(() => resolve(func(...args)), wait);
		});
	};
}

export interface Species {
	name: string;
	alias?: string;
	[key: string]: any;
}

/**
 * 物种搜索 Hook
 * 提供全部物种的搜索功能，用于人工鉴定
 * 基于对照表物种数据进行本地搜索和过滤
 */
export function useSpeciesSearch() {
	// 状态管理
	const allSpecies = ref<Species[]>([]);
	const searchLoading = ref(false);
	const searchError = ref<string | null>(null);
	const lastFetchTime = ref(0);
	const cacheExpireTime = 5 * 60 * 1000; // 5分钟缓存过期时间

	// 性能优化相关状态
	const searchCache = ref<Map<string, Species[]>>(new Map());
	const searchIndex = ref<Map<string, Species[]>>(new Map());
	const maxResults = 50; // 限制搜索结果数量

	// 计算属性：判断缓存是否过期
	const isCacheExpired = computed(() => {
		const now = Date.now();
		return now - lastFetchTime.value > cacheExpireTime;
	});

	// 计算属性：判断是否有有效数据
	const hasValidData = computed(() => {
		return allSpecies.value.length > 0;
	});

	/**
	 * 构建搜索索引以提高搜索性能
	 */
	const buildSearchIndex = () => {
		const index = new Map<string, Species[]>();

		allSpecies.value.forEach((species) => {
			// 按名称首字符建立索引
			if (species.name) {
				const firstChar = species.name.charAt(0).toLowerCase();
				if (!index.has(firstChar)) {
					index.set(firstChar, []);
				}
				index.get(firstChar)!.push(species);
			}

			// 按别名首字符建立索引
			if (species.alias) {
				const firstChar = species.alias.charAt(0).toLowerCase();
				if (!index.has(firstChar)) {
					index.set(firstChar, []);
				}
				index.get(firstChar)!.push(species);
			}
		});

		searchIndex.value = index;
		console.log(`搜索索引构建完成，共 ${index.size} 个索引键`);
	};

	/**
	 * 获取全部物种数据
	 * @returns Promise<Species[]> 全部物种列表
	 */
	const fetchAllSpecies = async (): Promise<Species[]> => {
		searchLoading.value = true;
		searchError.value = null;

		try {
			const response = await getSpecies();
			const results = response.payload || [];

			// 更新缓存数据
			allSpecies.value = results;
			lastFetchTime.value = Date.now();

			// 构建搜索索引
			buildSearchIndex();

			// 清空搜索缓存
			searchCache.value.clear();

			console.log(`物种对照表数据已更新，共 ${results.length} 条记录`);
			return results;
		} catch (error) {
			console.error('获取物种对照表失败:', error);
			searchError.value = '获取物种对照表失败';
			allSpecies.value = [];
			return [];
		} finally {
			searchLoading.value = false;
		}
	};

	/**
	 * 确保物种数据存在（智能缓存）
	 * 如果没有数据或缓存过期，则自动获取
	 */
	const ensureSpeciesData = async (): Promise<void> => {
		// 如果正在加载，直接返回
		if (searchLoading.value) {
			return;
		}

		// 如果有有效数据且缓存未过期，直接返回
		if (hasValidData.value && !isCacheExpired.value) {
			return;
		}

		// 获取数据
		await fetchAllSpecies();
	};

	/**
	 * 搜索物种（本地搜索）
	 * @param name 物种名称关键字
	 * @returns Promise<Species[]> 搜索结果
	 *
	 * @example
	 * // 基础搜索
	 * searchSpecies('老虎')
	 * searchSpecies('鸟')
	 */
	const searchSpecies = async (name: string): Promise<Species[]> => {
		if (!name || name.trim().length === 0) {
			return [];
		}

		// 确保数据存在
		await ensureSpeciesData();

		// 本地搜索过滤
		const searchTerm = name.trim().toLowerCase();
		const filteredResults = allSpecies.value.filter((species) => {
			// 按名称搜索
			const nameMatch = species.name?.toLowerCase().includes(searchTerm);
			const aliasMatch = species.alias?.toLowerCase().includes(searchTerm);

			console.log('[ nameMatch ] >', nameMatch, aliasMatch);
			return nameMatch || aliasMatch;
		});

		return filteredResults;
	};

	/**
	 * 获取所有物种名称列表（去重）
	 * @returns Promise<string[]> 物种名称列表
	 */
	const getAllSpeciesNames = async (): Promise<string[]> => {
		await ensureSpeciesData();

		const names = allSpecies.value.map((item) => item.name).filter(Boolean);

		return [...new Set(names)];
	};

	/**
	 * 清空搜索结果和缓存
	 */
	const clearCache = (): void => {
		allSpecies.value = [];
		searchError.value = null;
		lastFetchTime.value = 0;
	};

	/**
	 * 重置搜索状态
	 */
	const resetSearchState = (): void => {
		searchLoading.value = false;
		searchError.value = null;
	};

	/**
	 * 强制刷新数据
	 */
	const refreshData = async (): Promise<Species[]> => {
		return await fetchAllSpecies();
	};

	return {
		// 状态
		allSpecies: computed(() => allSpecies.value),
		searchLoading: computed(() => searchLoading.value),
		searchError: computed(() => searchError.value),
		hasValidData,
		isCacheExpired,

		// 方法
		searchSpecies,
		fetchAllSpecies,
		ensureSpeciesData,
		getAllSpeciesNames,
		clearCache,
		resetSearchState,
		refreshData,

		// 兼容性别名（为了保持与 useExistSpeciesSearch 的接口一致）
		speciesOptions: computed(() => allSpecies.value),
		searchExistSpecies: searchSpecies,
		clearSearchResults: clearCache,
	};
}
