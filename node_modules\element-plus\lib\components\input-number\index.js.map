{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/input-number/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport InputNumber from './src/input-number.vue'\n\nexport const ElInputNumber = withInstall(InputNumber)\n\nexport default ElInputNumber\nexport * from './src/input-number'\n"], "names": ["withInstall", "InputNumber"], "mappings": ";;;;;;;;;AAEY,MAAC,aAAa,GAAGA,mBAAW,CAACC,wBAAW;;;;;;;"}