/**
 * 天气插件hook返回值
 */
export interface WeatherPromise {
    /**
     * 通过城市名称、区域编码（如杭州市、330100），查询目标城市/区域的实时天气状况
     * @param {string} city 城市名称或者区域编码（如：“杭州市”/“330100”）
     * @return {Promise<WeatherLiveResult>}
     */
    getLive: (city: string) => Promise<AMap.WeatherLiveResult>;
    /**
     * 通过城市名称、区域编码（如北京市、110000），查询目标城市/区域的天气预报状况。
     * @param {string} city 城市名称或者区域编码（如：“杭州市”/“330100”）
     * @return {Promise<WeatherForecastResult>}
     */
    getForecast: (city: string) => Promise<AMap.WeatherForecastResult>;
}
/**
 * 天气插件hook，返回天气插件里的方法，全部Promise处理
 */
export declare function useWeather(): Promise<WeatherPromise>;
