/*! @vuemap/vue-amap-loca v2.1.2 */(function(v,l){typeof exports=="object"&&typeof module!="undefined"?l(exports,require("vue")):typeof define=="function"&&define.amd?define(["exports","vue"],l):(v=typeof globalThis!="undefined"?globalThis:v||self,l(v.VueAMap={},v.Vue))})(this,function(v,l){"use strict";const _e=(o=[])=>{const d=[];return{install:m=>{d.includes(m)||(d.push(m),o.forEach(n=>m.use(n)))}}};function Ae(o){if(!o||o.length<4)return o;const d=o.substring(3,o.length);return o[2].toLowerCase()+d}const Be=/^on[A-Z]+/;function Ee(o){return o&&o.charAt(0).toUpperCase()+o.slice(1)}function Ie(o,d,m){!o||!o.on||o.on(d,m)}function we(o,d,m){!o||!o.off||o.off(d,m)}const re={visible:{type:Boolean,default:!0},zIndex:{type:Number},reEventWhenUpdate:{type:Boolean,default:!1},extraOptions:{type:Object}},te=o=>Object.assign({},re,o),ie="parentInstance",$=(o,d)=>{let m=l.getCurrentInstance(),{props:n,attrs:s}=m,r=l.inject(ie,void 0);const a=d.emits;let p=!1,c;l.onMounted(()=>{r?r.$amapComponent?f():r.addChildComponent(f):d.isRoot&&f()}),l.onBeforeUnmount(()=>{c&&(P(),z(),d.destroyComponent?d.destroyComponent():C(),d.provideData&&(d.provideData.isDestroy=!0),r=void 0,n=void 0,s=void 0,m=void 0,c=void 0)}),l.onBeforeUpdate(()=>{n.reEventWhenUpdate&&p&&c&&P()}),l.onUpdated(()=>{n.reEventWhenUpdate&&p&&c&&b()});const f=()=>{const u=y();o(u,r==null?void 0:r.$amapComponent).then(k=>{c=k,b(),g(),j(),Object.assign(m.ctx,m.exposed),a("init",c,m.ctx),l.nextTick(()=>{oe()}).then(),p=!0})},g=()=>{["editable","visible","zooms"].forEach(u=>{if(n[u]!==void 0){const k=W(u);k&&k.call(c,L(S(u,n[u])))}})},h=d.propsRedirect||{},y=()=>{const u={};return n.extraOptions&&Object.assign(u,n.extraOptions),Object.keys(n).forEach(k=>{let _=k;const x=S(_,n[_]);x!==void 0&&(h&&h[k]&&(_=h[_]),u[_]=x)}),u},t=d.converts||{},S=(u,k)=>t&&t[u]?t[u].call(void 0,k):k,L=u=>l.isProxy(u)?l.toRaw(u):l.unref(u);let i=[],e=Object.assign({__visible:u=>{c&&c.show&&c.hide&&(u?c.show():c.hide())},__zIndex(u){c&&c.setzIndex&&c.setzIndex(u)}},d.watchRedirectFn||{});const j=()=>{Object.keys(n).forEach(u=>{let k=u;h&&h[u]&&(k=h[u]);const _=W(k);if(!_)return;const x={deep:!1},ke=Object.prototype.toString.call(n[u]);(ke==="[object Object]"||ke==="[object Array]")&&(x.deep=!0);const Oe=l.watch(()=>n[u],je=>{_.call(c,L(S(u,je)))},x);i.push(Oe)})},z=()=>{i.forEach(u=>u()),i=[],e=void 0},W=u=>e[`__${u}`]?e[`__${u}`]:c?c[`set${Ee(u)}`]:null,O={},b=()=>{Object.keys(s).forEach(u=>{if(Be.test(u)){const k=Ae(u);Ie(c,k,s[u]),O[k]=s[u]}})},P=()=>{Object.keys(O).forEach(u=>{we(c,u,O[u]),delete O[u]})},oe=()=>{const u=d.needInitComponents||[];for(;u.length>0;)u[0](),u.splice(0,1)},C=()=>{c&&(c.setMap&&c.setMap(null),c.close&&c.close(),c.editor&&c.editor.close())};function w(){return c}return{$$getInstance:w,parentInstance:r,isMounted:p}},A=o=>Object.assign({},re,{sourceUrl:{type:String},sourceData:{type:Object},geoBufferSource:{type:[ArrayBuffer,String],default(){return null}},layerStyle:{type:Object},defaultStyleValue:{type:Object,default(){return{}}},zooms:{type:Array},opacity:{type:Number},initEvents:{type:Boolean,default:!0},visibleDuration:{type:Number,default:0},onClick:{type:Function,default:null},onMousemove:{type:Function,default:null},onRightclick:{type:Function,default:null}},o),B=["init","mousemove","click","rightclick"];function E(o){return{__layerStyle(d){l.nextTick(()=>{var m;(m=o.$amapComponent())!=null&&m.setStyle&&o.$amapComponent().setStyle(d)}).then()},__sourceUrl(){l.nextTick(()=>{o.setSource()}).then()},__sourceData(){l.nextTick(()=>{o.setSource()}).then()},__geoBufferSource(){l.nextTick(()=>{o.setSource()}).then()},__visible(d){const m=o.$amapComponent();m!=null&&m.show&&m!=null&&m.hide&&(d?m.show(o.props.visibleDuration):m.hide(o.props.visibleDuration))}}}function I(o){let d=!1,m=!1,n;const{parentInstance:s,$amapComponent:r,emits:a,props:p,setStyle:c}=o,f=()=>{n&&(n.destroy(),n=null),p.geoBufferSource?typeof p.geoBufferSource=="string"?n=new Loca.GeoBufferSource({url:p.geoBufferSource}):n=new Loca.GeoBufferSource({data:p.geoBufferSource}):p.sourceUrl?n=new Loca.GeoJSONSource({url:p.sourceUrl}):p.sourceData?n=new Loca.GeoJSONSource({data:p.sourceData}):n=new Loca.GeoJSONSource({}),r.setSource(n)},g=()=>{p.initEvents&&h()},h=()=>{if(s){const b=s.getMap();p.onClick!==null&&b.on("click",y),p.onMousemove!==null&&(b.on("mousemove",S),b.on("dragstart",i),b.on("dragend",e),b.on("rotatestart",z),b.on("rotateend",W),b.on("mouseout",j)),p.onRightclick!==null&&b.on("rightclick",t)}},y=b=>{const P=L(b);a("click",P,b)},t=b=>{const P=L(b);a("rightclick",P,b)},S=b=>{if(d||m)return;const P=L(b);a("mousemove",P,b)},L=b=>r.queryFeature(b.pixel.toArray()),i=()=>{d=!0},e=()=>{d=!1},j=()=>{d=!1,m=!1},z=()=>{m=!0},W=()=>{m=!1},O=()=>{if(s){const b=s.getMap();b.off("click",y),b.off("rightclick",t),b.off("mousemove",S),b.off("dragstart",i),b.off("dragend",e),b.off("rotatestart",z),b.off("rotateend",W),b.off("mouseout",j)}};return f(),c(),s==null||s.$amapComponent.add(r),g(),{_destroyComponent:()=>{O(),s!=null&&s.isDestroy||(s==null||s.$amapComponent.remove(r),r.destroy()),n&&(n.destroy(),n=null)},setSource:f}}var F=l.defineComponent({name:"ElAmapLocaGrid",inheritAttrs:!1,__name:"GridLayer",props:A({cullface:{type:String},acceptLight:{type:Boolean,default:!0},shininess:{type:Number},hasSide:{type:Boolean,default:!0},depth:{type:Boolean,default:!0}}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.GridLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{topColor:"#fff",sideTopColor:"#fff",sideBottomColor:"#fff",altitude:0,height:0,radius:1e3,gap:0,unit:"meter"},n.defaultStyleValue),S={topColor:(i,e)=>(e.properties=e.properties||{},e.properties.topColor===void 0?t.topColor:e.properties.topColor),sideTopColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideTopColor===void 0?t.sideTopColor:e.properties.sideTopColor),sideBottomColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideBottomColor===void 0?t.sideBottomColor:e.properties.sideBottomColor),altitude:(i,e)=>(e.properties=e.properties||{},e.properties.altitude===void 0?t.altitude:e.properties.altitude),height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),radius:t.radius,gap:t.gap,unit:t.unit},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});F.__file="src/vue-amap-loca/packages/GridLayer/GridLayer.vue",F.install=o=>(o.component(F.name,F),o);const ne=F;var T=l.defineComponent({name:"ElAmapLocaHeatmap",inheritAttrs:!1,__name:"HeatMapLayer",props:A({depth:{type:Boolean,default:!0}}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.HeatMapLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{radius:20,value:10,gradient:{.5:"blue",.65:"rgb(117,211,248)",.7:"rgb(0, 255, 0)",.9:"#ffea00",1:"red"},opacity:[0,1],height:100,heightBezier:[.4,.2,.4,.8],max:null,min:null,unit:"px",difference:!1},n.defaultStyleValue),S={radius:(i,e)=>(e.properties=e.properties||{},e.properties.radius===void 0?t.radius:e.properties.radius),value:(i,e)=>(e.properties=e.properties||{},e.properties.value===void 0?t.value:e.properties.value),gradient:t.gradient,opacity:t.opacity,height:t.height,heightBezier:t.heightBezier,max:t.max,min:t.min,unit:t.unit,difference:t.difference},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});T.__file="src/vue-amap-loca/packages/HeatMapLayer/HeatMapLayer.vue",T.install=o=>(o.component(T.name,T),o);const pe=T;var M=l.defineComponent({name:"ElAmapLocaHexagon",inheritAttrs:!1,__name:"HexagonLayer",props:A({cullface:{type:String},acceptLight:{type:Boolean,default:!0},shininess:{type:Number},hasSide:{type:Boolean,default:!0},depth:{type:Boolean,default:!0}}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.HexagonLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{topColor:"#fff",sideTopColor:"#fff",sideBottomColor:"#fff",altitude:0,height:0,radius:1e3,gap:0,unit:"meter"},n.defaultStyleValue),S={topColor:(i,e)=>(e.properties=e.properties||{},e.properties.topColor===void 0?t.topColor:e.properties.topColor),sideTopColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideTopColor===void 0?t.sideTopColor:e.properties.sideTopColor),sideBottomColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideBottomColor===void 0?t.sideBottomColor:e.properties.sideBottomColor),altitude:(i,e)=>(e.properties=e.properties||{},e.properties.altitude===void 0?t.altitude:e.properties.altitude),height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),radius:t.radius,gap:t.gap,unit:t.unit},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});M.__file="src/vue-amap-loca/packages/HexagonLayer/HexagonLayer.vue",M.install=o=>(o.component(M.name,M),o);const se=M;var R=l.defineComponent({name:"ElAmapLocaIcon",inheritAttrs:!1,__name:"IconLayer",props:A({}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.IconLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{unit:"px",icon:"",iconSize:[20,20],rotation:0,opacity:1,offset:[0,0]},n.defaultStyleValue),S={unit:t.unit,icon:(i,e)=>(e.properties=e.properties||{},e.properties.icon===void 0?t.icon:e.properties.icon),iconSize:(i,e)=>(e.properties=e.properties||{},e.properties.iconSize===void 0?t.iconSize:e.properties.iconSize),rotation:(i,e)=>(e.properties=e.properties||{},e.properties.rotation===void 0?t.rotation:e.properties.rotation),opacity:(i,e)=>(e.properties=e.properties||{},e.properties.opacity===void 0?t.opacity:e.properties.opacity),offset:(i,e)=>(e.properties=e.properties||{},e.properties.offset===void 0?t.offset:e.properties.offset)},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});R.__file="src/vue-amap-loca/packages/IconLayer/IconLayer.vue",R.install=o=>(o.component(R.name,R),o);const ae=R;var D=l.defineComponent({name:"ElAmapLocaLine",inheritAttrs:!1,__name:"LineLayer",props:A({}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.LineLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{color:"#fff",lineWidth:2,altitude:0,borderWidth:0,borderColor:"#fff",dashArray:[10,0,10,0]},n.defaultStyleValue),S=Object.assign({},{color:(L,i)=>(i.properties=i.properties||{},i.properties.color===void 0?t.color:i.properties.color),lineWidth:(L,i)=>(i.properties=i.properties||{},i.properties.lineWidth===void 0?t.lineWidth:i.properties.lineWidth),altitude:(L,i)=>(i.properties=i.properties||{},i.properties.altitude===void 0?t.altitude:i.properties.altitude),borderWidth:(L,i)=>(i.properties=i.properties||{},i.properties.borderWidth===void 0?t.borderWidth:i.properties.borderWidth),borderColor:(L,i)=>(i.properties=i.properties||{},i.properties.borderColor===void 0?t.borderColor:i.properties.borderColor),dashArray:(L,i)=>(i.properties=i.properties||{},i.properties.dashArray===void 0?t.dashArray:i.properties.dashArray)},n.layerStyle);r.setStyle(S)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});D.__file="src/vue-amap-loca/packages/LineLayer/LineLayer.vue",D.install=o=>(o.component(D.name,D),o);const le=D;var H=l.defineComponent({name:"ElAmapLocaLink",inheritAttrs:!1,__name:"LinkLayer",props:A({}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.LinkLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{lineColors:["rgba(255,255,255,1)","rgba(255,255,255,0)"],height:100,smoothSteps:100},n.defaultStyleValue),S=Object.assign({},{lineColors:(L,i)=>(i.properties=i.properties||{},i.properties.lineColors===void 0?t.lineColors:i.properties.lineColors),height:(L,i)=>(i.properties=i.properties||{},i.properties.height===void 0?t.height:i.properties.height),smoothSteps:(L,i)=>(i.properties=i.properties||{},i.properties.smoothSteps===void 0?t.smoothSteps:i.properties.smoothSteps)},n.layerStyle);r.setStyle(S)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});H.__file="src/vue-amap-loca/packages/LinkLayer/LinkLayer.vue",H.install=o=>(o.component(H.name,H),o);const ce=H;var N=l.defineComponent({name:"ElAmapLoca",inheritAttrs:!1,__name:"Loca",props:A({ambLight:{type:Object},dirLight:{type:Object},pointLight:{type:Object},onClick:{type:Function,default:null},onMousemove:{type:Function,default:null},onRightclick:{type:Function,default:null},eventOptions:{type:Object,default:()=>({hitFirst:!0})}}),emits:B,setup(o,{expose:d,emit:m}){const n=[],s={$amapComponent:void 0,addChildComponent(C){n.push(C)},isDestroy:!1,getMap:()=>t()};l.provide(ie,s);const r=o,a=m;let p,c=!1,f=!1,g=!0;const{$$getInstance:h,parentInstance:y}=$((C,w)=>new Promise(u=>{p=new Loca.Container({map:w}),s.$amapComponent=p,C.ambLight&&(p.ambLight=C.ambLight),C.dirLight&&(p.dirLight=C.dirLight),C.pointLight&&(p.pointLight=C.pointLight),g=C.eventOptions.hitFirst,S(),u(p)}),{emits:a,needInitComponents:n,provideData:s,destroyComponent(){p&&(oe(),p.animate&&p.animate.stop&&p.animate.stop(),p.destroy(),p=null)}}),t=()=>y==null?void 0:y.$amapComponent,S=()=>{if(y){const C=t();r.onClick!==null&&C.on("click",L),r.onMousemove!==null&&(C.on("mousemove",e),C.on("dragstart",z),C.on("dragend",W),C.on("rotatestart",b),C.on("rotateend",P),C.on("mouseout",O)),r.onRightclick!==null&&C.on("rightclick",i)}},L=C=>{const w=j(C);a("click",w,C)},i=C=>{const w=j(C);a("rightclick",w,C)},e=C=>{if(c||f)return;const w=j(C);a("mousemove",w,C)},j=C=>{const w=[];if(p.layers){const u=[];p.layers.forEach(_=>{u.push(_)}),u.sort((_,x)=>x.zIndex-_.zIndex);const k=u.length;for(let _=0;_<k;_++){const x=u[_].queryFeature(C.pixel.toArray());if(x&&(w.push(x),g))break}}return w},z=()=>{c=!0},W=()=>{c=!1},O=()=>{c=!1,f=!1},b=()=>{f=!0},P=()=>{f=!1},oe=()=>{if(y){const C=t();C.off("click",L),C.off("rightclick",i),C.off("mousemove",e),C.off("dragstart",z),C.off("dragend",W),C.off("rotatestart",b),C.off("rotateend",P),C.off("mouseout",O)}};return d({$$getInstance:h}),(C,w)=>(l.openBlock(),l.createElementBlock("div",null,[l.renderSlot(C.$slots,"default")]))}});N.__file="src/vue-amap-loca/packages/Loca/Loca.vue",N.install=o=>(o.component(N.name,N),o);const de=N;var V=l.defineComponent({name:"ElAmapLocaPoint",inheritAttrs:!1,__name:"PointLayer",props:A({blend:{type:String}}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.PointLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{radius:20,color:"#fff",unit:"px",borderWidth:10,borderColor:"#fff",blurWidth:-1},n.defaultStyleValue),S={radius:(i,e)=>(e.properties=e.properties||{},e.properties.radius===void 0?t.radius:e.properties.radius),color:(i,e)=>(e.properties=e.properties||{},e.properties.color===void 0?t.color:e.properties.color),unit:t.unit,borderWidth:(i,e)=>(e.properties=e.properties||{},e.properties.borderWidth===void 0?t.borderWidth:e.properties.borderWidth),borderColor:(i,e)=>(e.properties=e.properties||{},e.properties.borderColor===void 0?t.borderColor:e.properties.borderColor),blurWidth:(i,e)=>(e.properties=e.properties||{},e.properties.blurWidth===void 0?t.blurWidth:e.properties.blurWidth)},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});V.__file="src/vue-amap-loca/packages/PointLayer/PointLayer.vue",V.install=o=>(o.component(V.name,V),o);const me=V;var G=l.defineComponent({name:"ElAmapLocaPolygon",inheritAttrs:!1,__name:"PolygonLayer",props:A({cullface:{type:String},acceptLight:{type:Boolean,default:!0},shininess:{type:Number},hasSide:{type:Boolean,default:!0},hasBottom:{type:Boolean,default:!1},blockHide:{type:Boolean,default:!0},depth:{type:Boolean,default:!0}}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.PolygonLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{topColor:"#fff",sideTopColor:"#fff",sideBottomColor:"#fff",altitude:0,height:0,texture:null,textureSize:[20,3],label:void 0,labelAltitude:0},n.defaultStyleValue),S={topColor:(i,e)=>(e.properties=e.properties||{},e.properties.topColor===void 0?t.topColor:e.properties.topColor),sideTopColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideTopColor===void 0?t.sideTopColor:e.properties.sideTopColor),sideBottomColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideBottomColor===void 0?t.sideBottomColor:e.properties.sideBottomColor),altitude:(i,e)=>(e.properties=e.properties||{},e.properties.altitude===void 0?t.altitude:e.properties.altitude),height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),texture:t.texture,textureSize:(i,e)=>(e.properties=e.properties||{},e.properties.textureSize===void 0?t.textureSize:e.properties.textureSize),label:(i,e)=>(e.properties=e.properties||{},e.properties.label===void 0?t.label:e.properties.label),labelAltitude:(i,e)=>(e.properties=e.properties||{},e.properties.labelAltitude===void 0?t.labelAltitude:e.properties.labelAltitude)},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});G.__file="src/vue-amap-loca/packages/PolygonLayer/PolygonLayer.vue",G.install=o=>(o.component(G.name,G),o);const ue=G;var U=l.defineComponent({name:"ElAmapLocaPrism",inheritAttrs:!1,__name:"PrismLayer",props:A({cullface:{type:String},acceptLight:{type:Boolean,default:!0},shininess:{type:Number},hasSide:{type:Boolean,default:!0},depth:{type:Boolean,default:!0}}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.PrismLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{radius:20,unit:"px",sideNumber:3,rotation:0,altitude:0,height:100,topColor:"#fff",sideTopColor:"#fff",sideBottomColor:"#fff"},n.defaultStyleValue),S={radius:t.radius,unit:t.unit,sideNumber:t.sideNumber,rotation:(i,e)=>(e.properties=e.properties||{},e.properties.rotation===void 0?t.rotation:e.properties.rotation),altitude:t.altitude,height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),topColor:(i,e)=>(e.properties=e.properties||{},e.properties.topColor===void 0?t.topColor:e.properties.topColor),sideTopColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideTopColor===void 0?t.sideTopColor:e.properties.sideTopColor),sideBottomColor:(i,e)=>(e.properties=e.properties||{},e.properties.sideBottomColor===void 0?t.sideBottomColor:e.properties.sideBottomColor)},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});U.__file="src/vue-amap-loca/packages/PrismLayer/PrismLayer.vue",U.install=o=>(o.component(U.name,U),o);const he=U;var Z=l.defineComponent({name:"ElAmapLocaPulseLine",inheritAttrs:!1,__name:"PulseLineLayer",props:A({depth:{type:Boolean,default:!0}}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.PulseLineLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{lineWidth:1,headColor:"rgba(0, 0, 0, 0.75)",trailColor:"rgba(0, 0, 0, 0.25)",altitude:0,interval:1,duration:2e3},n.defaultStyleValue),S={lineWidth:(i,e)=>(e.properties=e.properties||{},e.properties.lineWidth===void 0?t.lineWidth:e.properties.lineWidth),headColor:(i,e)=>(e.properties=e.properties||{},e.properties.headColor===void 0?t.headColor:e.properties.headColor),trailColor:(i,e)=>(e.properties=e.properties||{},e.properties.trailColor===void 0?t.trailColor:e.properties.trailColor),altitude:t.altitude,interval:t.interval,duration:t.duration},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});Z.__file="src/vue-amap-loca/packages/PulseLineLayer/PulseLineLayer.vue",Z.install=o=>(o.component(Z.name,Z),o);const ye=Z;var q=l.defineComponent({name:"ElAmapLocaPulseLink",inheritAttrs:!1,__name:"PulseLinkLayer",props:A({depth:{type:Boolean,default:!0}}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.PulseLinkLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{lineColors:["#fff"],height:100,maxHeightScale:0,smoothSteps:50,lineWidth:[1,1],unit:"px",dash:[4e3,0,4e3,0],speed:100,headColor:"rgba(0, 0, 0, 0.75)",trailColor:"rgba(0, 0, 0, 0.25)",flowLength:100},n.defaultStyleValue),S={lineColors:(i,e)=>(e.properties=e.properties||{},e.properties.lineColors===void 0?t.lineColors:e.properties.lineColors),height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),maxHeightScale:(i,e)=>(e.properties=e.properties||{},e.properties.maxHeightScale===void 0?t.maxHeightScale:e.properties.maxHeightScale),smoothSteps:(i,e)=>(e.properties=e.properties||{},e.properties.smoothSteps===void 0?t.smoothSteps:e.properties.smoothSteps),lineWidth:(i,e)=>(e.properties=e.properties||{},e.properties.lineWidth===void 0?t.lineWidth:e.properties.lineWidth),unit:t.unit,dash:(i,e)=>(e.properties=e.properties||{},e.properties.dash===void 0?t.dash:e.properties.dash),speed:(i,e)=>(e.properties=e.properties||{},e.properties.speed===void 0?t.speed:e.properties.speed),headColor:(i,e)=>(e.properties=e.properties||{},e.properties.headColor===void 0?t.headColor:e.properties.headColor),trailColor:(i,e)=>(e.properties=e.properties||{},e.properties.trailColor===void 0?t.trailColor:e.properties.trailColor),flowLength:t.flowLength},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});q.__file="src/vue-amap-loca/packages/PulseLinkLayer/PulseLinkLayer.vue",q.install=o=>(o.component(q.name,q),o);const ge=q;var J=l.defineComponent({name:"ElAmapLocaScatter",inheritAttrs:!1,__name:"ScatterLayer",props:A({}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.ScatterLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{size:[20,20],rotation:0,color:"rgba(200,200,200,1)",altitude:0,borderWidth:0,borderColor:"rgba(250,250,250,1)",texture:null,unit:"px",animate:!1,duration:0},n.defaultStyleValue),S={size:(i,e)=>(e.properties=e.properties||{},e.properties.size===void 0?t.size:e.properties.size),rotation:(i,e)=>(e.properties=e.properties||{},e.properties.rotation===void 0?t.rotation:e.properties.rotation),color:(i,e)=>(e.properties=e.properties||{},e.properties.color===void 0?t.color:e.properties.color),altitude:(i,e)=>(e.properties=e.properties||{},e.properties.altitude===void 0?t.altitude:e.properties.altitude),borderWidth:(i,e)=>(e.properties=e.properties||{},e.properties.borderWidth===void 0?t.borderWidth:e.properties.borderWidth),borderColor:(i,e)=>(e.properties=e.properties||{},e.properties.borderColor===void 0?t.borderColor:e.properties.borderColor),texture:t.texture,unit:t.unit,animate:t.animate,duration:t.duration},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});J.__file="src/vue-amap-loca/packages/ScatterLayer/ScatterLayer.vue",J.install=o=>(o.component(J.name,J),o);const fe=J;var K=l.defineComponent({name:"ElAmapLocaZMarker",inheritAttrs:!1,__name:"ZMarkerLayer",props:A({}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.ZMarkerLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{unit:"px",content:"",size:[20,20],rotation:0,alwaysFront:!1,altitude:0},n.defaultStyleValue),S={unit:t.unit,content:(i,e)=>(e.properties=e.properties||{},e.properties.content===void 0?t.content:e.properties.content),size:(i,e)=>(e.properties=e.properties||{},e.properties.size===void 0?t.size:e.properties.size),rotation:(i,e)=>(e.properties=e.properties||{},e.properties.rotation===void 0?t.rotation:e.properties.rotation),alwaysFront:t.alwaysFront,altitude:(i,e)=>(e.properties=e.properties||{},e.properties.altitude===void 0?t.altitude:e.properties.altitude)},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});K.__file="src/vue-amap-loca/packages/ZMarkerLayer/ZMarkerLayer.vue",K.install=o=>(o.component(K.name,K),o);const Le=K;var Q=l.defineComponent({name:"ElAmapLocaAmbientLight",inheritAttrs:!1,__name:"AmbientLight",props:te({color:{type:String},intensity:{type:Number}}),emits:["init"],setup(o,{expose:d,emit:m}){const n=m;let s;const{$$getInstance:r,parentInstance:a}=$((p,c)=>new Promise(f=>{s=new Loca.AmbientLight(p),c.addLight(s),f(s)}),{emits:n,destroyComponent(){s&&a!=null&&a.$amapComponent&&(a.isDestroy||a.$amapComponent.removeLight(s),s=null)}});return d({$$getInstance:r}),(p,c)=>(l.openBlock(),l.createElementBlock("div"))}});Q.__file="src/vue-amap-loca/packages/AmbientLight/AmbientLight.vue",Q.install=o=>(o.component(Q.name,Q),o);const Ce=Q;var X=l.defineComponent({name:"ElAmapLocaDirectionalLight",inheritAttrs:!1,__name:"DirectionalLight",props:te({color:{type:String},intensity:{type:Number},position:{type:Array,required:!0},target:{type:Array}}),emits:["init"],setup(o,{expose:d,emit:m}){const n=m;let s;const{$$getInstance:r,parentInstance:a}=$((p,c)=>new Promise(f=>{s=new Loca.DirectionalLight(p),c.addLight(s),f(s)}),{emits:n,destroyComponent(){s&&a!=null&&a.$amapComponent&&(a.isDestroy||a.$amapComponent.removeLight(s),s=null)}});return d({$$getInstance:r}),(p,c)=>(l.openBlock(),l.createElementBlock("div"))}});X.__file="src/vue-amap-loca/packages/DirectionalLight/DirectionalLight.vue",X.install=o=>(o.component(X.name,X),o);const Se=X;var Y=l.defineComponent({name:"ElAmapLocaPointLight",inheritAttrs:!1,__name:"PointLight",props:te({color:{type:String},intensity:{type:Number},position:{type:Array,required:!0},distance:{type:Number}}),emits:["init"],setup(o,{expose:d,emit:m}){const n=m;let s;const{$$getInstance:r,parentInstance:a}=$((p,c)=>new Promise(f=>{s=new Loca.PointLight(p),c.addLight(s),f(s)}),{emits:n,destroyComponent(){s&&a!=null&&a.$amapComponent&&(a.isDestroy||a.$amapComponent.removeLight(s),s=null)}});return d({$$getInstance:r}),(p,c)=>(l.openBlock(),l.createElementBlock("div"))}});Y.__file="src/vue-amap-loca/packages/PointLight/PointLight.vue",Y.install=o=>(o.component(Y.name,Y),o);const be=Y;var ee=l.defineComponent({name:"ElAmapLocaLaser",inheritAttrs:!1,__name:"LaserLayer",props:A({depth:{type:Boolean,default:!0}}),emits:B,setup(o,{expose:d,emit:m}){const n=o,s=m;let r,a,p;const{$$getInstance:c,parentInstance:f}=$(g=>new Promise(h=>{r=new Loca.LaserLayer(g);const y=I({parentInstance:f,$amapComponent:r,emits:s,props:n,setStyle(){const t=Object.assign({},{unit:"px",height:200,color:"rgba(255,255,0,0.5)",angle:0,lineWidth:2,trailLength:30,duration:2e3,interval:0,delay:0,repeat:void 0},n.defaultStyleValue),S={unit:t.unit,height:(i,e)=>(e.properties=e.properties||{},e.properties.height===void 0?t.height:e.properties.height),color:(i,e)=>(e.properties=e.properties||{},e.properties.color===void 0?t.color:e.properties.color),angle:t.angle,lineWidth:t.lineWidth,trailLength:t.trailLength,duration:(i,e)=>(e.properties=e.properties||{},e.properties.duration===void 0?t.duration:e.properties.duration),interval:(i,e)=>(e.properties=e.properties||{},e.properties.interval===void 0?t.interval:e.properties.interval),delay:(i,e)=>(e.properties=e.properties||{},e.properties.delay===void 0?t.delay:e.properties.delay),repeat:t.repeat},L=Object.assign({},S,n.layerStyle);r.setStyle(L)}});a=y._destroyComponent,p=y.setSource,h(r)}),{emits:s,watchRedirectFn:E({setSource(){p&&p()},$amapComponent:()=>r,props:n}),destroyComponent(){a&&a(),r=null}});return d({$$getInstance:c}),(g,h)=>(l.openBlock(),l.createElementBlock("div"))}});ee.__file="src/vue-amap-loca/packages/LaserLayer/LaserLayer.vue",ee.install=o=>(o.component(ee.name,ee),o);const ve=ee;var Pe=[ne,pe,se,ae,le,ce,de,me,ue,he,ye,ge,fe,Le,Ce,Se,be,ve],$e=_e([...Pe]);const xe=$e.install;v.ElAmapLoca=de,v.ElAmapLocaAmbientLight=Ce,v.ElAmapLocaDirectionalLight=Se,v.ElAmapLocaGrid=ne,v.ElAmapLocaHeatmap=pe,v.ElAmapLocaHexagon=se,v.ElAmapLocaIcon=ae,v.ElAmapLocaLaser=ve,v.ElAmapLocaLine=le,v.ElAmapLocaLink=ce,v.ElAmapLocaPoint=me,v.ElAmapLocaPointLight=be,v.ElAmapLocaPolygon=ue,v.ElAmapLocaPrism=he,v.ElAmapLocaPulseLine=ye,v.ElAmapLocaPulseLink=ge,v.ElAmapLocaScatter=fe,v.ElAmapLocaZMarker=Le,v.default=$e,v.install=xe,Object.defineProperty(v,"__esModule",{value:!0})});
//# sourceMappingURL=index.min.js.map
