{"version": 3, "file": "amap-api-loader.mjs", "sources": ["../../../services/amap-api-loader.ts"], "sourcesContent": ["import AMapLoader from '@vuemap/amap-jsapi-loader';\r\nimport {merge} from 'lodash-es';\r\n\r\nconst DEFAULT_AMP_CONFIG = {\r\n  'key': '', // 申请好的Web端开发者Key，首次调用 load 时必填\r\n  'version': '2.0', // 指定要加载的 JSAPI 的版本，缺省时默认为 1.4.15\r\n  'plugins': [], // 需要使用的的插件列表，如比例尺'AMap.Scale'等\r\n  // 'Loca': { // 是否加载 Loca， 缺省不加载\r\n  //   'version': '2.0.0' // Loca 版本，缺省 1.3.2\r\n  // },\r\n  serviceHost: '',\r\n  securityJsCode: ''\r\n};\r\n\r\nexport default function (config : any = {}) {\r\n  config = merge({}, DEFAULT_AMP_CONFIG, config);\r\n  if (config.serviceHost) {\r\n    (window as any)._AMapSecurityConfig = {\r\n      serviceHost: config.serviceHost\r\n    };\r\n  } else if (config.securityJsCode) {\r\n    (window as any)._AMapSecurityConfig = {\r\n      securityJsCode: config.securityJsCode\r\n    };\r\n  }\r\n  return AMapLoader.load(config);\r\n}\r\n\r\nexport const resetJsApi = AMapLoader.reset;"], "names": [], "mappings": ";;;AAGA,MAAM,kBAAqB,GAAA;AAAA,EACzB,KAAO,EAAA,EAAA;AAAA;AAAA,EACP,SAAW,EAAA,KAAA;AAAA;AAAA,EACX,WAAW,EAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAIZ,WAAa,EAAA,EAAA;AAAA,EACb,cAAgB,EAAA,EAAA;AAClB,CAAA,CAAA;AAEyB,sBAAA,CAAA,MAAA,GAAe,EAAI,EAAA;AAC1C,EAAA,MAAA,GAAS,KAAM,CAAA,EAAI,EAAA,kBAAA,EAAoB,MAAM,CAAA,CAAA;AAC7C,EAAA,IAAI,OAAO,WAAa,EAAA;AACtB,IAAC,OAAe,mBAAsB,GAAA;AAAA,MACpC,aAAa,MAAO,CAAA,WAAA;AAAA,KACtB,CAAA;AAAA,GACF,MAAA,IAAW,OAAO,cAAgB,EAAA;AAChC,IAAC,OAAe,mBAAsB,GAAA;AAAA,MACpC,gBAAgB,MAAO,CAAA,cAAA;AAAA,KACzB,CAAA;AAAA,GACF;AACA,EAAO,OAAA,UAAA,CAAW,KAAK,MAAM,CAAA,CAAA;AAC/B,CAAA;AAEO,MAAM,aAAa,UAAW,CAAA;;;;"}