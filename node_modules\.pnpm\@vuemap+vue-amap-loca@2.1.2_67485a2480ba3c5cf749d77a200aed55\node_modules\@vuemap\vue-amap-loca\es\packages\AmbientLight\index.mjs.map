{"version": 3, "file": "index.mjs", "sources": ["../../../../packages/AmbientLight/index.ts"], "sourcesContent": ["import AmbientLight from './AmbientLight.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nAmbientLight.install = (app: App) => {\r\n  app.component(AmbientLight.name, AmbientLight);\r\n  return app;\r\n};\r\nexport const ElAmapLocaAmbientLight = AmbientLight as typeof AmbientLight & Plugin;\r\nexport default ElAmapLocaAmbientLight;\r\n\r\nexport type ElAmapLocaAmbientLightInstance = InstanceType<typeof AmbientLight>\r\n"], "names": ["AmbientLight"], "mappings": ";;;AAEAA,MAAa,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AACnC,EAAI,GAAA,CAAA,SAAA,CAAUA,MAAa,CAAA,IAAA,EAAMA,MAAY,CAAA,CAAA;AAC7C,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AACO,MAAM,sBAAyB,GAAAA;;;;"}