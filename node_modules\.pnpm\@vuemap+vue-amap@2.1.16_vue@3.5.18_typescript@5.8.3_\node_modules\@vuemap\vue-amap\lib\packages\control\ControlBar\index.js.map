{"version": 3, "file": "index.js", "sources": ["../../../../../packages/control/ControlBar/index.ts"], "sourcesContent": ["import ControlBar from './ControlBar.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nControlBar.install = (app: App) => {\r\n  app.component(ControlBar.name, ControlBar);\r\n  return app;\r\n};\r\n\r\nexport const ElAmapControlControlBar = ControlBar as typeof ControlBar & Plugin;\r\nexport default ElAmapControlControlBar;\r\n\r\nexport type ElAmapControlControlBarInstance = InstanceType<typeof ControlBar>\r\n"], "names": ["ControlBar"], "mappings": ";;;;;;;AAEAA,sDAAW,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AACjC,EAAI,GAAA,CAAA,SAAA,CAAUA,sDAAW,CAAA,IAAA,EAAMA,sDAAU,CAAA,CAAA;AACzC,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AAEO,MAAM,uBAA0B,GAAAA;;;;;"}