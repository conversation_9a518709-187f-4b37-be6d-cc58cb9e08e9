{"version": 3, "file": "props.js", "sources": ["../../../../../packages/control/Geolocation/props.ts"], "sourcesContent": ["import {buildProps} from \"../../../utils/buildHelper\";\r\nimport type {PropType} from \"vue\";\r\n\r\nexport const propsType = buildProps({\r\n  //悬停位置，默认为\"RB\"，即右下角.'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角\r\n  position: {\r\n    type: String as PropType<'LT'|'RT'|'LB'|'RB'>\r\n  },\r\n  //缩略图距离悬停位置的像素距离，如 [2,2]\r\n  offset: {\r\n    type: Array\r\n  },\r\n  //按钮边框颜色值，同CSS，如'silver'\r\n  borderColor: {\r\n    type: String\r\n  },\r\n  borderRadius: {\r\n    type: String\r\n  },//按钮圆角边框值，同CSS，如'5px'\r\n  buttonSize: {\r\n    type: String\r\n  },//箭头按钮的像素尺寸，同CSS，如'12px'\r\n  convert: {\r\n    type: Boolean,\r\n    default: true\r\n  },//是否将定位结果转换为高德坐标\r\n  enableHighAccuracy: {\r\n    type: Boolean,\r\n    default: false\r\n  },//进行浏览器原生定位的时候是否尝试获取较高精度，可能影响定位效率，默认为false\r\n  timeout: {\r\n    type: Number\r\n  },//定位的超时时间，毫秒\r\n  maximumAge: {\r\n    type: Number\r\n  },//浏览器原生定位的缓存时间，毫秒\r\n  showButton: {\r\n    type: Boolean,\r\n    default: true\r\n  },//是否显示定位按钮，默认为true\r\n  showCircle: {\r\n    type: Boolean,\r\n    default: true\r\n  },//是否显示定位精度圆，默认为true\r\n  showMarker: {\r\n    type: Boolean,\r\n    default: true\r\n  },//是否显示定位点，默认为true\r\n  markerOptions: {\r\n    type: Object\r\n  },//定位点的样式\r\n  circleOptions: {\r\n    type: Object\r\n  },//CircleOptions\r\n  panToLocation: {\r\n    type: Boolean,\r\n    default: true\r\n  },//定位成功后是否自动移动到响应位置\r\n  zoomToAccuracy: {\r\n    type: Boolean,\r\n    default: true\r\n  },//定位成功后是否自动调整级别\r\n  GeoLocationFirst: {\r\n    type: Boolean,\r\n    default: true\r\n  },//优先使用H5定位，默认false\r\n  noIpLocate: {\r\n    type: Number\r\n  },//是否禁用IP精确定位，默认为0，0:都用 1:手机上不用 2:PC上不用 3:都不用\r\n  noGeoLocation: {\r\n    type: Number\r\n  },//是否禁用浏览器原生定位，默认为0，0:都用 1:手机上不用 2:PC上不用 3:都不用\r\n  useNative: {\r\n    type: Boolean,\r\n    default: false\r\n  },//是否与高德定位SDK能力结合，需要同时使用安卓版高德定位sdk，否则无效\r\n  getCityWhenFail: {\r\n    type: Boolean,\r\n    default: false\r\n  },//定位失败之后是否返回基本城市定位信息\r\n  needAddress: {\r\n    type: Boolean,\r\n    default: false\r\n  },//是否需要将定位结果进行逆地理编码操作\r\n  extensions: {\r\n    type: String as PropType<'base' | 'all'>,\r\n    validator: (value : string): boolean => {\r\n      // 这个值必须匹配下列字符串中的一个\r\n      return ['base', 'all'].indexOf(value) !== -1;\r\n    }\r\n  },//是否需要详细的逆地理编码信息，默认为'base'只返回基本信息，可选'all'\r\n});"], "names": ["buildProps"], "mappings": ";;;;AAGO,MAAM,YAAYA,sBAAW,CAAA;AAAA;AAAA,EAElC,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,KAAA;AAAA,GACR;AAAA;AAAA,EAEA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,GACR;AAAA,EACA,YAAc,EAAA;AAAA,IACZ,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,OAAS,EAAA;AAAA,IACP,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,kBAAoB,EAAA;AAAA,IAClB,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AAAA,EACA,OAAS,EAAA;AAAA,IACP,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,aAAe,EAAA;AAAA,IACb,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,aAAe,EAAA;AAAA,IACb,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,aAAe,EAAA;AAAA,IACb,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,cAAgB,EAAA;AAAA,IACd,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,gBAAkB,EAAA;AAAA,IAChB,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,aAAe,EAAA;AAAA,IACb,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,SAAW,EAAA;AAAA,IACT,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AAAA,EACA,eAAiB,EAAA;AAAA,IACf,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AAAA,EACA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,MAAA;AAAA,IACN,SAAA,EAAW,CAAC,KAA4B,KAAA;AAEtC,MAAA,OAAO,CAAC,MAAQ,EAAA,KAAK,CAAE,CAAA,OAAA,CAAQ,KAAK,CAAM,KAAA,CAAA,CAAA,CAAA;AAAA,KAC5C;AAAA,GACF;AAAA;AACF,CAAC;;;;"}