{"version": 3, "file": "menu-item-group.js", "sources": ["../../../../../../packages/components/menu/src/menu-item-group.ts"], "sourcesContent": ["import type { ExtractPropTypes } from 'vue'\n\nexport const menuItemGroupProps = {\n  title: String,\n} as const\nexport type MenuItemGroupProps = ExtractPropTypes<typeof menuItemGroupProps>\n"], "names": [], "mappings": ";;;;AAAY,MAAC,kBAAkB,GAAG;AAClC,EAAE,KAAK,EAAE,MAAM;AACf;;;;"}