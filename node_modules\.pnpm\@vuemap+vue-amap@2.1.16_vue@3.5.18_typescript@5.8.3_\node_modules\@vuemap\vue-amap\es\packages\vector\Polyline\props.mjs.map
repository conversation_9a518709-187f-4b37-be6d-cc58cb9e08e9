{"version": 3, "file": "props.mjs", "sources": ["../../../../../packages/vector/Polyline/props.ts"], "sourcesContent": ["import {buildProps} from \"../../../utils/buildHelper\";\r\nimport type {PropType} from \"vue\";\r\n\r\nexport const propsTypes = buildProps({\r\n  // polyline 路径，支持 lineString 和 MultiLineString\r\n  path: {\r\n    type: Array,\r\n    required: true\r\n  }, \r\n  // 是否将覆盖物的鼠标或touch等事件冒泡到地图上\r\n  bubble: {\r\n    type: Boolean,\r\n    default: false\r\n  }, \r\n  // 指定鼠标悬停时的鼠标样式，自定义cursor，IE仅支持cur/ani/ico格式，Opera不支持自定义cursor\r\n  cursor: {\r\n    type: String\r\n  }, \r\n  // 线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC\r\n  strokeColor: {\r\n    type: String\r\n  }, \r\n  // 轮廓线透明度，取值范围 [0,1] ，0表示完全透明，1表示不透明。默认为0.9\r\n  strokeOpacity: {\r\n    type: Number\r\n  }, \r\n  // 轮廓线宽度。默认 2\r\n  strokeWeight: {\r\n    type: Number\r\n  }, \r\n  // 描边线宽度\r\n  borderWeight: {\r\n    type: Number\r\n  }, \r\n  // 是否显示描边,默认false\r\n  isOutline: {\r\n    type: Boolean,\r\n    default: false\r\n  }, \r\n  // 线条描边颜色，此项仅在isOutline为true时有效，默认：#00B2D5\r\n  outlineColor: {\r\n    type: String\r\n  }, \r\n  // 设置多边形是否可拖拽移动，默认为false\r\n  draggable: {\r\n    type: Boolean,\r\n    default: false\r\n  }, \r\n  extData: {\r\n    type: Object,\r\n    default: () => null\r\n  },\r\n  // 轮廓线样式，实线:solid，虚线:dashed\r\n  strokeStyle: {\r\n    type: String as PropType<'solid' | 'dashed'>,\r\n    validator: (value: string): boolean => {\r\n      // 这个值必须匹配下列字符串中的一个\r\n      return ['solid', 'dashed'].indexOf(value) !== -1;\r\n    }\r\n  }, \r\n  // 勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线\r\n  strokeDasharray: {\r\n    type: Array\r\n  }, \r\n  // 折线拐点的绘制样式，默认值为'miter'尖角，其他可选值：'round'圆角、'bevel'斜角\r\n  lineJoin: {\r\n    type: String as PropType<'miter' | 'round' | 'bevel'>,\r\n    validator: (value : string): boolean => {\r\n      // 这个值必须匹配下列字符串中的一个\r\n      return ['miter', 'round', 'bevel'].indexOf(value) !== -1;\r\n    }\r\n  }, \r\n  // 折线两端线帽的绘制样式，默认值为'butt'无头，其他可选值：'round'圆头、'square'方头\r\n  lineCap: {\r\n    type: String as PropType<'butt' | 'round' | 'square'>,\r\n    validator: (value : string):boolean => {\r\n      // 这个值必须匹配下列字符串中的一个\r\n      return ['butt', 'round', 'square'].indexOf(value) !== -1;\r\n    }\r\n  },\r\n  // 是否绘制成大地线，默认false\r\n  geodesic: {\r\n    type: Boolean,\r\n    default: false\r\n  }, \r\n  // 是否延路径显示白色方向箭头,默认false。建议折线宽度大于6时使用,\r\n  showDir: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  editable: {\r\n    type: Boolean,\r\n    default: false\r\n  },\r\n  editOptions: {\r\n    type: Object\r\n  }\r\n});"], "names": [], "mappings": ";;AAGO,MAAM,aAAa,UAAW,CAAA;AAAA;AAAA,EAEnC,IAAM,EAAA;AAAA,IACJ,IAAM,EAAA,KAAA;AAAA,IACN,QAAU,EAAA,IAAA;AAAA,GACZ;AAAA;AAAA,EAEA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AAAA,EAEA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,aAAe,EAAA;AAAA,IACb,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,YAAc,EAAA;AAAA,IACZ,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,YAAc,EAAA;AAAA,IACZ,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,SAAW,EAAA;AAAA,IACT,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AAAA,EAEA,YAAc,EAAA;AAAA,IACZ,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EAEA,SAAW,EAAA;AAAA,IACT,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA,EACA,OAAS,EAAA;AAAA,IACP,IAAM,EAAA,MAAA;AAAA,IACN,SAAS,MAAM,IAAA;AAAA,GACjB;AAAA;AAAA,EAEA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,IACN,SAAA,EAAW,CAAC,KAA2B,KAAA;AAErC,MAAA,OAAO,CAAC,OAAS,EAAA,QAAQ,CAAE,CAAA,OAAA,CAAQ,KAAK,CAAM,KAAA,CAAA,CAAA,CAAA;AAAA,KAChD;AAAA,GACF;AAAA;AAAA,EAEA,eAAiB,EAAA;AAAA,IACf,IAAM,EAAA,KAAA;AAAA,GACR;AAAA;AAAA,EAEA,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,MAAA;AAAA,IACN,SAAA,EAAW,CAAC,KAA4B,KAAA;AAEtC,MAAA,OAAO,CAAC,OAAS,EAAA,OAAA,EAAS,OAAO,CAAE,CAAA,OAAA,CAAQ,KAAK,CAAM,KAAA,CAAA,CAAA,CAAA;AAAA,KACxD;AAAA,GACF;AAAA;AAAA,EAEA,OAAS,EAAA;AAAA,IACP,IAAM,EAAA,MAAA;AAAA,IACN,SAAA,EAAW,CAAC,KAA2B,KAAA;AAErC,MAAA,OAAO,CAAC,MAAQ,EAAA,OAAA,EAAS,QAAQ,CAAE,CAAA,OAAA,CAAQ,KAAK,CAAM,KAAA,CAAA,CAAA,CAAA;AAAA,KACxD;AAAA,GACF;AAAA;AAAA,EAEA,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AAAA,EAEA,OAAS,EAAA;AAAA,IACP,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA,EACA,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA,EACA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,GACR;AACF,CAAC;;;;"}