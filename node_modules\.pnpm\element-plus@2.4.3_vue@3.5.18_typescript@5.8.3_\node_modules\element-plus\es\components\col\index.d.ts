export declare const ElCol: import("element-plus/es/utils").SFCWithInstall<import("vue").DefineComponent<{
    readonly tag: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
    readonly span: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 24, boolean>;
    readonly offset: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly pull: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly push: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly xs: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly sm: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly md: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly lg: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly xl: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
}, {
    props: Readonly<import("@vue/shared").LooseRequired<Readonly<import("vue").ExtractPropTypes<{
        readonly tag: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
        readonly span: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 24, boolean>;
        readonly offset: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
        readonly pull: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
        readonly push: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
        readonly xs: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
        readonly sm: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
        readonly md: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
        readonly lg: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
        readonly xl: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    }>> & {
        [x: string & `on${string}`]: ((...args: any[]) => any) | ((...args: unknown[]) => any) | undefined;
    }>>;
    gutter: import("vue").ComputedRef<number>;
    ns: {
        namespace: import("vue").ComputedRef<string>;
        b: (blockSuffix?: string) => string;
        e: (element?: string | undefined) => string;
        m: (modifier?: string | undefined) => string;
        be: (blockSuffix?: string | undefined, element?: string | undefined) => string;
        em: (element?: string | undefined, modifier?: string | undefined) => string;
        bm: (blockSuffix?: string | undefined, modifier?: string | undefined) => string;
        bem: (blockSuffix?: string | undefined, element?: string | undefined, modifier?: string | undefined) => string;
        is: {
            (name: string, state: boolean | undefined): string;
            (name: string): string;
        };
        cssVar: (object: Record<string, string>) => Record<string, string>;
        cssVarName: (name: string) => string;
        cssVarBlock: (object: Record<string, string>) => Record<string, string>;
        cssVarBlockName: (name: string) => string;
    };
    style: import("vue").ComputedRef<import("vue").CSSProperties>;
    colKls: import("vue").ComputedRef<(string | string[])[]>;
}, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, Record<string, any>, string, import("vue").VNodeProps & import("vue").AllowedComponentProps & import("vue").ComponentCustomProps, Readonly<import("vue").ExtractPropTypes<{
    readonly tag: import("element-plus/es/utils").EpPropFinalized<StringConstructor, unknown, unknown, "div", boolean>;
    readonly span: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 24, boolean>;
    readonly offset: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly pull: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly push: import("element-plus/es/utils").EpPropFinalized<NumberConstructor, unknown, unknown, 0, boolean>;
    readonly xs: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly sm: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly md: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly lg: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
    readonly xl: import("element-plus/es/utils").EpPropFinalized<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown, () => import("element-plus/es/utils").Mutable<{}>, boolean>;
}>>, {
    readonly offset: number;
    readonly push: number;
    readonly tag: string;
    readonly span: number;
    readonly pull: number;
    readonly xs: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown>;
    readonly sm: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown>;
    readonly md: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown>;
    readonly lg: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown>;
    readonly xl: import("element-plus/es/utils").EpPropMergeType<(new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize) | ((new (...args: any[]) => import("./src/col").ColSize & {}) | (() => import("./src/col").ColSize))[], unknown, unknown>;
}>> & Record<string, any>;
export default ElCol;
export * from './src/col';
