---
name: 💥 Proposal
about: Propose a non-trivial change to Clipboard.js
labels: 'proposal, needs triage'
---

## 💥 Proposal

**Is your feature request related to a problem? Please describe**
A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]

**Describe the solution you'd like**
A clear and concise description of what you want to happen.

**Describe alternatives you've considered**
A clear and concise description of any alternative solutions or features you've considered.

**Are you able to assist to bring the feature to reality?**
no | yes, I can...

**Additional context**
Add any other context or screenshots about the feature request here.

### Have you read the [Contributing Guidelines on issues](https://github.com/zenorocha/clipboard.js/blob/master/contributing.md)?

(Write your answer here.)
