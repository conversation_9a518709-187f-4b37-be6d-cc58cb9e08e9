{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/result/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Result from './src/result.vue'\n\nexport const ElResult = withInstall(Result)\n\nexport default ElResult\n\nexport * from './src/result'\n"], "names": ["withInstall", "Result"], "mappings": ";;;;;;;;;AAEY,MAAC,QAAQ,GAAGA,mBAAW,CAACC,mBAAM;;;;;;;;"}