{"version": 3, "file": "hue-slider.mjs", "sources": ["../../../../../../../packages/components/color-picker/src/components/hue-slider.vue"], "sourcesContent": ["<template>\n  <div :class=\"[ns.b(), ns.is('vertical', vertical)]\">\n    <div ref=\"bar\" :class=\"ns.e('bar')\" @click=\"handleClick\" />\n    <div\n      ref=\"thumb\"\n      :class=\"ns.e('thumb')\"\n      :style=\"{\n        left: thumbLeft + 'px',\n        top: thumbTop + 'px',\n      }\"\n    />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  onMounted,\n  ref,\n  watch,\n} from 'vue'\nimport { getClientXY } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { draggable } from '../utils/draggable'\n\nimport type { PropType } from 'vue'\nimport type Color from '../utils/color'\n\nexport default defineComponent({\n  name: 'ElColorHueSlider',\n\n  props: {\n    color: {\n      type: Object as PropType<Color>,\n      required: true,\n    },\n\n    vertical: <PERSON><PERSON><PERSON>,\n  },\n  setup(props) {\n    const ns = useNamespace('color-hue-slider')\n    const instance = getCurrentInstance()!\n    // ref\n    const thumb = ref<HTMLElement>()\n    const bar = ref<HTMLElement>()\n    // data\n    const thumbLeft = ref(0)\n    const thumbTop = ref(0)\n    // computed\n    const hueValue = computed(() => {\n      return props.color.get('hue')\n    })\n    // watch\n    watch(\n      () => hueValue.value,\n      () => {\n        update()\n      }\n    )\n\n    // methods\n    function handleClick(event: MouseEvent | TouchEvent) {\n      const target = event.target\n\n      if (target !== thumb.value) {\n        handleDrag(event)\n      }\n    }\n\n    function handleDrag(event: MouseEvent | TouchEvent) {\n      if (!bar.value || !thumb.value) return\n\n      const el = instance.vnode.el as HTMLElement\n      const rect = el.getBoundingClientRect()\n      const { clientX, clientY } = getClientXY(event)\n      let hue\n\n      if (!props.vertical) {\n        let left = clientX - rect.left\n        left = Math.min(left, rect.width - thumb.value.offsetWidth / 2)\n        left = Math.max(thumb.value.offsetWidth / 2, left)\n\n        hue = Math.round(\n          ((left - thumb.value.offsetWidth / 2) /\n            (rect.width - thumb.value.offsetWidth)) *\n            360\n        )\n      } else {\n        let top = clientY - rect.top\n\n        top = Math.min(top, rect.height - thumb.value.offsetHeight / 2)\n        top = Math.max(thumb.value.offsetHeight / 2, top)\n        hue = Math.round(\n          ((top - thumb.value.offsetHeight / 2) /\n            (rect.height - thumb.value.offsetHeight)) *\n            360\n        )\n      }\n      props.color.set('hue', hue)\n    }\n\n    function getThumbLeft() {\n      if (!thumb.value) return 0\n\n      const el = instance.vnode.el\n\n      if (props.vertical) return 0\n      const hue = props.color.get('hue')\n\n      if (!el) return 0\n      return Math.round(\n        (hue * (el.offsetWidth - thumb.value.offsetWidth / 2)) / 360\n      )\n    }\n\n    function getThumbTop() {\n      if (!thumb.value) return 0\n\n      const el = instance.vnode.el as HTMLElement\n      if (!props.vertical) return 0\n      const hue = props.color.get('hue')\n\n      if (!el) return 0\n      return Math.round(\n        (hue * (el.offsetHeight - thumb.value.offsetHeight / 2)) / 360\n      )\n    }\n\n    function update() {\n      thumbLeft.value = getThumbLeft()\n      thumbTop.value = getThumbTop()\n    }\n\n    // mounded\n    onMounted(() => {\n      if (!bar.value || !thumb.value) return\n\n      const dragConfig = {\n        drag: (event: MouseEvent | TouchEvent) => {\n          handleDrag(event)\n        },\n        end: (event: MouseEvent | TouchEvent) => {\n          handleDrag(event)\n        },\n      }\n\n      draggable(bar.value, dragConfig)\n      draggable(thumb.value, dragConfig)\n      update()\n    })\n\n    return {\n      bar,\n      thumb,\n      thumbLeft,\n      thumbTop,\n      hueValue,\n      handleClick,\n      update,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["_createElementBlock", "_normalizeClass", "_createElementVNode", "_normalizeStyle"], "mappings": ";;;;;;;;AA8BA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,kBAAA;AAAA,EAEN,KAAO,EAAA;AAAA,IACL,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IAEA,QAAU,EAAA,OAAA;AAAA,GACZ;AAAA,EACA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAK,aAAa,kBAAkB,CAAA,CAAA;AAC1C,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AAEpC,IAAA,MAAM,QAAQ,GAAiB,EAAA,CAAA;AAC/B,IAAA,MAAM,MAAM,GAAiB,EAAA,CAAA;AAE7B,IAAM,MAAA,SAAA,GAAY,IAAI,CAAC,CAAA,CAAA;AACvB,IAAM,MAAA,QAAA,GAAW,IAAI,CAAC,CAAA,CAAA;AAEtB,IAAM,MAAA,QAAA,GAAW,SAAS,MAAM;AAC9B,MAAO,OAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,KAAK,CAAA,CAAA;AAAA,KAC7B,CAAA,CAAA;AAED,IACE,KAAA,CAAA,MAAM,QAAS,CAAA,KAAA,EACf,MAAM;AACJ,MAAO,MAAA,EAAA,CAAA;AAAA,KAEX,CAAA,CAAA;AAGA,IAAA,SAAA,WAAA,CAAqB,KAAgC,EAAA;AACnD,MAAA,MAAM,SAAS,KAAM,CAAA,MAAA,CAAA;AAErB,MAAI,IAAA,MAAA,KAAW,MAAM,KAAO,EAAA;AAC1B,QAAA,UAAA,CAAW,KAAK,CAAA,CAAA;AAAA,OAClB;AAAA,KACF;AAEA,IAAA,SAAA,UAAA,CAAoB,KAAgC,EAAA;AAClD,MAAA,IAAI,CAAC,GAAA,CAAI,KAAS,IAAA,CAAC,KAAM,CAAA,KAAA;AAAO,QAAA,OAAA;AAEhC,MAAM,MAAA,EAAA,GAAK,SAAS,KAAM,CAAA,EAAA,CAAA;AAC1B,MAAM,MAAA,IAAA,GAAO,GAAG,qBAAsB,EAAA,CAAA;AACtC,MAAA,MAAM,EAAE,OAAA,EAAS,OAAY,EAAA,GAAA,WAAA,CAAY,KAAK,CAAA,CAAA;AAC9C,MAAI,IAAA,GAAA,CAAA;AAEJ,MAAI,IAAA,CAAC,MAAM,QAAU,EAAA;AACnB,QAAI,IAAA,IAAA,GAAO,UAAU,IAAK,CAAA,IAAA,CAAA;AAC1B,QAAO,IAAA,GAAA,IAAA,CAAK,IAAI,IAAM,EAAA,IAAA,CAAK,QAAQ,KAAM,CAAA,KAAA,CAAM,cAAc,CAAC,CAAA,CAAA;AAC9D,QAAA,IAAA,GAAO,KAAK,GAAI,CAAA,KAAA,CAAM,KAAM,CAAA,WAAA,GAAc,GAAG,IAAI,CAAA,CAAA;AAEjD,QAAA,GAAA,GAAM,IAAK,CAAA,KAAA,CACP,CAAO,IAAA,GAAA,KAAA,CAAM,KAAM,CAAA,WAAA,GAAc,CAChC,KAAA,IAAA,CAAK,KAAQ,GAAA,KAAA,CAAM,KAAM,CAAA,WAAA,CAAA,GAC1B,GACJ,CAAA,CAAA;AAAA,OACK,MAAA;AACL,QAAI,IAAA,GAAA,GAAM,UAAU,IAAK,CAAA,GAAA,CAAA;AAEzB,QAAM,GAAA,GAAA,IAAA,CAAK,IAAI,GAAK,EAAA,IAAA,CAAK,SAAS,KAAM,CAAA,KAAA,CAAM,eAAe,CAAC,CAAA,CAAA;AAC9D,QAAA,GAAA,GAAM,KAAK,GAAI,CAAA,KAAA,CAAM,KAAM,CAAA,YAAA,GAAe,GAAG,GAAG,CAAA,CAAA;AAChD,QAAA,GAAA,GAAM,IAAK,CAAA,KAAA,CACP,CAAM,GAAA,GAAA,KAAA,CAAM,KAAM,CAAA,YAAA,GAAe,CAChC,KAAA,IAAA,CAAK,MAAS,GAAA,KAAA,CAAM,KAAM,CAAA,YAAA,CAAA,GAC3B,GACJ,CAAA,CAAA;AAAA,OACF;AACA,MAAM,KAAA,CAAA,KAAA,CAAM,GAAI,CAAA,KAAA,EAAO,GAAG,CAAA,CAAA;AAAA,KAC5B;AAEA,IAAwB,SAAA,YAAA,GAAA;AACtB,MAAA,IAAI,CAAC,KAAM,CAAA,KAAA;AAAO,QAAO,OAAA,CAAA,CAAA;AAEzB,MAAM,MAAA,EAAA,GAAK,SAAS,KAAM,CAAA,EAAA,CAAA;AAE1B,MAAA,IAAI,KAAM,CAAA,QAAA;AAAU,QAAO,OAAA,CAAA,CAAA;AAC3B,MAAA,MAAM,GAAM,GAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,KAAK,CAAA,CAAA;AAEjC,MAAA,IAAI,CAAC,EAAA;AAAI,QAAO,OAAA,CAAA,CAAA;AAChB,MAAO,OAAA,IAAA,CAAK,MACT,GAAO,IAAA,EAAA,CAAG,cAAc,KAAM,CAAA,KAAA,CAAM,WAAc,GAAA,CAAA,CAAA,GAAM,GAC3D,CAAA,CAAA;AAAA,KACF;AAEA,IAAuB,SAAA,WAAA,GAAA;AACrB,MAAA,IAAI,CAAC,KAAM,CAAA,KAAA;AAAO,QAAO,OAAA,CAAA,CAAA;AAEzB,MAAM,MAAA,EAAA,GAAK,SAAS,KAAM,CAAA,EAAA,CAAA;AAC1B,MAAA,IAAI,CAAC,KAAM,CAAA,QAAA;AAAU,QAAO,OAAA,CAAA,CAAA;AAC5B,MAAA,MAAM,GAAM,GAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,KAAK,CAAA,CAAA;AAEjC,MAAA,IAAI,CAAC,EAAA;AAAI,QAAO,OAAA,CAAA,CAAA;AAChB,MAAO,OAAA,IAAA,CAAK,MACT,GAAO,IAAA,EAAA,CAAG,eAAe,KAAM,CAAA,KAAA,CAAM,YAAe,GAAA,CAAA,CAAA,GAAM,GAC7D,CAAA,CAAA;AAAA,KACF;AAEA,IAAkB,SAAA,MAAA,GAAA;AAChB,MAAA,SAAA,CAAU,QAAQ,YAAa,EAAA,CAAA;AAC/B,MAAA,QAAA,CAAS,QAAQ,WAAY,EAAA,CAAA;AAAA,KAC/B;AAGA,IAAA,SAAA,CAAU,MAAM;AACd,MAAA,IAAI,CAAC,GAAA,CAAI,KAAS,IAAA,CAAC,KAAM,CAAA,KAAA;AAAO,QAAA,OAAA;AAEhC,MAAA,MAAM,UAAa,GAAA;AAAA,QACjB,IAAA,EAAM,CAAC,KAAmC,KAAA;AACxC,UAAA,UAAA,CAAW,KAAK,CAAA,CAAA;AAAA,SAClB;AAAA,QACA,GAAA,EAAK,CAAC,KAAmC,KAAA;AACvC,UAAA,UAAA,CAAW,KAAK,CAAA,CAAA;AAAA,SAClB;AAAA,OACF,CAAA;AAEA,MAAU,SAAA,CAAA,GAAA,CAAI,OAAO,UAAU,CAAA,CAAA;AAC/B,MAAU,SAAA,CAAA,KAAA,CAAM,OAAO,UAAU,CAAA,CAAA;AACjC,MAAO,MAAA,EAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAO,OAAA;AAAA,MACL,GAAA;AAAA,MACA,KAAA;AAAA,MACA,SAAA;AAAA,MACA,QAAA;AAAA,MACA,QAAA;AAAA,MACA,WAAA;AAAA,MACA,MAAA;AAAA,MACA,EAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;sBAnKCA,kBAUM,CAAA,KAAA,EAAA;AAAA,IAVA,KAAA,EAAKC,gBAAG,IAAG,CAAA,EAAA,CAAA,CAAA,IAAK,IAAG,CAAA,EAAA,CAAA,EAAA,CAAE,YAAa,IAAQ,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,GAAA,EAAA;IAC9CC,kBAA2D,CAAA,KAAA,EAAA;AAAA,MAAtD,GAAI,EAAA,KAAA;AAAA,MAAO,KAAA,EAAKD,cAAE,CAAA,IAAA,CAAA,EAAA,CAAG,CAAC,CAAA,KAAA,CAAA,CAAA;AAAA,MAAU,SAAK,MAAE,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,WAAA,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;IAC5CC,kBAOE,CAAA,KAAA,EAAA;AAAA,MANA,GAAI,EAAA,OAAA;AAAA,MACH,KAAA,EAAKD,cAAE,CAAA,IAAA,CAAA,EAAA,CAAG,CAAC,CAAA,OAAA,CAAA,CAAA;AAAA,MACX,KAAK,EAAAE,cAAA,CAAA;AAAA,QAAA,IAAA,EAAkB,IAAS,CAAA,SAAA,GAAA,IAAA;AAAA,QAAA,GAAA,EAAsB,IAAQ,CAAA,QAAA,GAAA,IAAA;AAAA,OAAA,CAAA;;;;;;;;"}