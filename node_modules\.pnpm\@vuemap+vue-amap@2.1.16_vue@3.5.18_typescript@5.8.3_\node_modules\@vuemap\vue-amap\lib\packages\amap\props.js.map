{"version": 3, "file": "props.js", "sources": ["../../../../packages/amap/props.ts"], "sourcesContent": ["import {buildProps} from \"../../utils/buildHelper\";\r\n\r\nexport const propsType = buildProps({\r\n  vid: {\r\n    type: String\r\n  }, // 地图ID\r\n  center: {\r\n    type: Array\r\n  }, // 初始中心经纬度\r\n  zoom: {\r\n    type: Number\r\n  }, // 地图显示的缩放级别，可以设置为浮点数；若center与level未赋值，地图初始化默认显示用户所在城市范围。\r\n  rotation: {\r\n    type: Number\r\n  }, // 地图顺时针旋转角度，取值范围 [0-360] ，默认值：0\r\n  pitch: {\r\n    type: Number\r\n  }, // 俯仰角度，默认 0，最大值根据地图当前 zoom 级别不断增大，2D地图下无效 。\r\n  viewMode: {\r\n    type: String\r\n  }, // 地图视图模式, 默认为‘2D’，可选’3D’，选择‘3D’会显示 3D 地图效果。\r\n  features: {\r\n    type: Array\r\n  }, // 设置地图上显示的元素种类, 支持'bg'（地图背景）、'point'（POI点）、'road'（道路）、'building'（建筑物），默认值：['bg','point','road','building']\r\n  layers: {\r\n    type: Array\r\n  }, // 地图图层数组，数组可以是图层 中的一个或多个，默认为普通二维地图。 当叠加多个 图层 时，普通二维地图需通过实例化一个TileLayer类实现。 如果你希望创建一个默认底图图层，使用 AMap.createDefaultLayer()\r\n  zooms: {\r\n    type: Array\r\n  }, // 图显示的缩放级别范围, 默认为 [2, 20] ，取值范围 [2 ~ 30]\r\n  resizeEnable: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 是否监控地图容器尺寸变化，默认值为false。此属性可被 setStatus/getStatus 方法控制\r\n  dragEnable: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 地图是否可通过鼠标拖拽平移, 默认为 true。此属性可被 setStatus/getStatus 方法控制\r\n  zoomEnable: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 地图是否可缩放，默认值为 true。此属性可被 setStatus/getStatus 方法控制\r\n  jogEnable: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 地图是否使用缓动效果，默认值为true。此属性可被setStatus/getStatus 方法控制\r\n  pitchEnable: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 是否允许设置俯仰角度, 3D 视图下为 true, 2D 视图下无效。。此属性可被setStatus/getStatus 方法控制\r\n  rotateEnable: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 地图是否可旋转, 图默认为true。此属性可被setStatus/getStatus 方法控制\r\n  animateEnable: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 地图平移过程中是否使用动画（如调用panBy、panTo、setCenter、setZoomAndCenter等函数, 将对地图产生平移操作, 是否使用动画平移的效果）, 默认为true, 即使用动画\r\n  keyboardEnable: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 地图是否可通过键盘控制, 默认为true, 方向键控制地图平移，\"+\"和\"-\"可以控制地图的缩放, Ctrl+“→”顺时针旋转，Ctrl+“←”逆时针旋转。此属性可被setStatus/getStatus 方法控制\r\n  doubleClickZoom: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 地图是否可通过双击鼠标放大地图, 默认为true。此属性可被setStatus/getStatus 方法控制\r\n  scrollWheel: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 地图是否可通过鼠标滚轮缩放浏览，默认为true。此属性可被setStatus/getStatus 方法控制\r\n  touchZoom: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 地图在移动终端上是否可通过多点触控缩放浏览地图，默认为true。关闭手势缩放地图，请设置为false。\r\n  touchZoomCenter: {\r\n    type: Number\r\n  }, // 可缺省，当touchZoomCenter=1的时候，手机端双指缩放的以地图中心为中心，否则默认以双指中间点为中心。默认：1\r\n  showLabel: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 是否展示地图文字和 POI 信息。默认 true\r\n  defaultCursor: {\r\n    type: String\r\n  }, // 地图默认鼠标样式。参数 defaultCursor 应符合 CSS 的 cursor 属性规范。\r\n  isHotspot: {\r\n    type: Boolean\r\n  }, // 是否开启地图热点和标注的 hover 效果。PC端默认是true, 移动端默认是 false。\r\n  mapStyle: {\r\n    type: String\r\n  }, // 设置地图的显示样式，目前支持两种地图样式： 第一种：自定义地图样式，如 \"amap://styles/d6bf8c1d69cea9f5c696185ad4ac4c86\" 可前往地图自定义平台定制自己的个性地图样式； 第二种：官方样式模版,如\"amap://styles/grey\"。 其他模版样式及自定义地图的使用说明见开发指南\r\n  wallColor: {\r\n    type: [String, Array]\r\n  }, // 地图楼块的侧面颜色\r\n  roofColor: {\r\n    type: [String, Array]\r\n  }, // 地图楼块的顶面颜色\r\n  showBuildingBlock: {\r\n    type: Boolean,\r\n    default: true\r\n  }, // 是否展示地图 3D 楼块，默认 true\r\n  showIndoorMap: {\r\n    type: Boolean,\r\n    default: false\r\n  }, // 是否自动展示室内地图，默认是 false\r\n  skyColor: {\r\n    type: [String, Array]\r\n  }, // 天空颜色，3D 模式下带有俯仰角时会显示\r\n  labelRejectMask: {\r\n    type: Boolean,\r\n    default: false\r\n  }, // 文字是否拒绝掩模图层进行掩模\r\n  mask: {\r\n    type: Array\r\n  }, // 为 Map 实例指定掩模的路径，各图层将只显示路径范围内图像，3D视图下有效。 格式为一个经纬度的一维、二维或三维数组。\r\n  WebGLParams: {\r\n    type: Object\r\n  },// 额外配置的WebGL参数 eg: preserveDrawingBuffer\r\n  terrain: {\r\n    type: Boolean,\r\n    default: false\r\n  }, //是否开启地形，默认不开启\r\n});"], "names": ["buildProps"], "mappings": ";;;;AAEO,MAAM,YAAYA,sBAAW,CAAA;AAAA,EAClC,GAAK,EAAA;AAAA,IACH,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,KAAA;AAAA,GACR;AAAA;AAAA,EACA,IAAM,EAAA;AAAA,IACJ,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,KAAO,EAAA;AAAA,IACL,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,KAAA;AAAA,GACR;AAAA;AAAA,EACA,MAAQ,EAAA;AAAA,IACN,IAAM,EAAA,KAAA;AAAA,GACR;AAAA;AAAA,EACA,KAAO,EAAA;AAAA,IACL,IAAM,EAAA,KAAA;AAAA,GACR;AAAA;AAAA,EACA,YAAc,EAAA;AAAA,IACZ,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,UAAY,EAAA;AAAA,IACV,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,SAAW,EAAA;AAAA,IACT,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,YAAc,EAAA;AAAA,IACZ,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,aAAe,EAAA;AAAA,IACb,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,cAAgB,EAAA;AAAA,IACd,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,eAAiB,EAAA;AAAA,IACf,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,SAAW,EAAA;AAAA,IACT,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,eAAiB,EAAA;AAAA,IACf,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,SAAW,EAAA;AAAA,IACT,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,aAAe,EAAA;AAAA,IACb,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,SAAW,EAAA;AAAA,IACT,IAAM,EAAA,OAAA;AAAA,GACR;AAAA;AAAA,EACA,QAAU,EAAA;AAAA,IACR,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,SAAW,EAAA;AAAA,IACT,IAAA,EAAM,CAAC,MAAA,EAAQ,KAAK,CAAA;AAAA,GACtB;AAAA;AAAA,EACA,SAAW,EAAA;AAAA,IACT,IAAA,EAAM,CAAC,MAAA,EAAQ,KAAK,CAAA;AAAA,GACtB;AAAA;AAAA,EACA,iBAAmB,EAAA;AAAA,IACjB,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,IAAA;AAAA,GACX;AAAA;AAAA,EACA,aAAe,EAAA;AAAA,IACb,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AAAA,EACA,QAAU,EAAA;AAAA,IACR,IAAA,EAAM,CAAC,MAAA,EAAQ,KAAK,CAAA;AAAA,GACtB;AAAA;AAAA,EACA,eAAiB,EAAA;AAAA,IACf,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AAAA,EACA,IAAM,EAAA;AAAA,IACJ,IAAM,EAAA,KAAA;AAAA,GACR;AAAA;AAAA,EACA,WAAa,EAAA;AAAA,IACX,IAAM,EAAA,MAAA;AAAA,GACR;AAAA;AAAA,EACA,OAAS,EAAA;AAAA,IACP,IAAM,EAAA,OAAA;AAAA,IACN,OAAS,EAAA,KAAA;AAAA,GACX;AAAA;AACF,CAAC;;;;"}