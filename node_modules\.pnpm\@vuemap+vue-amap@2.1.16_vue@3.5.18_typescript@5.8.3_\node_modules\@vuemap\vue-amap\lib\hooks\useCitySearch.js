'use strict';

function useCitySearch() {
  return new Promise((resolve) => {
    AMap.plugin("AMap.CitySearch", function() {
      const citySearch = new AMap.CitySearch();
      const getLocalCity = () => {
        return new Promise((resolve2, reject) => {
          citySearch.getLocalCity(function(status, result) {
            if (status === "complete" && result.info === "OK") {
              resolve2(result);
            } else {
              reject({
                status,
                result
              });
            }
          });
        });
      };
      resolve({
        getLocalCity
      });
    });
  });
}

exports.useCitySearch = useCitySearch;
//# sourceMappingURL=useCitySearch.js.map
