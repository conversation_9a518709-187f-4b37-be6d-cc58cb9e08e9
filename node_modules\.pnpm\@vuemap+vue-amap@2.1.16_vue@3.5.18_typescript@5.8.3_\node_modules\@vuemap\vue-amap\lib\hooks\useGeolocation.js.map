{"version": 3, "file": "useGeolocation.js", "sources": ["../../../hooks/useGeolocation.ts"], "sourcesContent": ["export interface GeolocationPromise {\r\n    /**\r\n     * 获取 用户的精确位置，有失败几率\r\n     * @return {Promise<CurrentPositionResult>}\r\n     */\r\n    getCurrentPosition: () => Promise<AMap.CurrentPositionResult>;\r\n\r\n    /**\r\n     * 根据用户 IP 获取 用户所在城市信息\r\n     * @return {Promise<CurrentPositionResult>}\r\n     */\r\n    getCityInfo: () => Promise<AMap.CurrentPositionResult>\r\n}\r\n\r\n/**\r\n * Geolocation插件getCurrentPosition方法返回的正确数据\r\n */\r\n\r\n\r\n/**\r\n * 定位插件hook，提供常用的基于浏览器定位的方法，和基于城市定位的方法\r\n * @param {GeolocationOptions} [options] 定位插件参数\r\n */\r\nexport function useGeolocation (options?: AMap.GeolocationOptions) {\r\n    return new Promise<GeolocationPromise>((resolve) => {\r\n        AMap.plugin('AMap.Geolocation', function () {\r\n            const geolocation = new AMap.Geolocation(options);\r\n            const getCurrentPosition = () => {\r\n                return new Promise<AMap.CurrentPositionResult>((resolve, reject) => {\r\n                    geolocation.getCurrentPosition(function (status, result) {\r\n                        if (status === 'complete' && result.info === 'SUCCESS') {\r\n                            resolve(result);\r\n                        } else {\r\n                            reject({\r\n                                status,\r\n                                result\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n            };\r\n            const getCityInfo = () => {\r\n                return new Promise<AMap.CurrentPositionResult>((resolve, reject) => {\r\n                    geolocation.getCityInfo(function (status, result) {\r\n                        if (status === 'complete' && result.info === 'SUCCESS') {\r\n                            resolve(result);\r\n                        } else {\r\n                            reject({\r\n                                status,\r\n                                result\r\n                            });\r\n                        }\r\n                    });\r\n                });\r\n            };\r\n            resolve({\r\n                getCurrentPosition,\r\n                getCityInfo\r\n            });\r\n        });\r\n    });\r\n}"], "names": ["resolve"], "mappings": ";;AAuBO,SAAS,eAAgB,OAAmC,EAAA;AAC/D,EAAO,OAAA,IAAI,OAA4B,CAAA,CAAC,OAAY,KAAA;AAChD,IAAK,IAAA,CAAA,MAAA,CAAO,oBAAoB,WAAY;AACxC,MAAA,MAAM,WAAc,GAAA,IAAI,IAAK,CAAA,WAAA,CAAY,OAAO,CAAA,CAAA;AAChD,MAAA,MAAM,qBAAqB,MAAM;AAC7B,QAAA,OAAO,IAAI,OAAA,CAAoC,CAACA,QAAAA,EAAS,MAAW,KAAA;AAChE,UAAY,WAAA,CAAA,kBAAA,CAAmB,SAAU,MAAA,EAAQ,MAAQ,EAAA;AACrD,YAAA,IAAI,MAAW,KAAA,UAAA,IAAc,MAAO,CAAA,IAAA,KAAS,SAAW,EAAA;AACpD,cAAAA,SAAQ,MAAM,CAAA,CAAA;AAAA,aACX,MAAA;AACH,cAAO,MAAA,CAAA;AAAA,gBACH,MAAA;AAAA,gBACA,MAAA;AAAA,eACH,CAAA,CAAA;AAAA,aACL;AAAA,WACH,CAAA,CAAA;AAAA,SACJ,CAAA,CAAA;AAAA,OACL,CAAA;AACA,MAAA,MAAM,cAAc,MAAM;AACtB,QAAA,OAAO,IAAI,OAAA,CAAoC,CAACA,QAAAA,EAAS,MAAW,KAAA;AAChE,UAAY,WAAA,CAAA,WAAA,CAAY,SAAU,MAAA,EAAQ,MAAQ,EAAA;AAC9C,YAAA,IAAI,MAAW,KAAA,UAAA,IAAc,MAAO,CAAA,IAAA,KAAS,SAAW,EAAA;AACpD,cAAAA,SAAQ,MAAM,CAAA,CAAA;AAAA,aACX,MAAA;AACH,cAAO,MAAA,CAAA;AAAA,gBACH,MAAA;AAAA,gBACA,MAAA;AAAA,eACH,CAAA,CAAA;AAAA,aACL;AAAA,WACH,CAAA,CAAA;AAAA,SACJ,CAAA,CAAA;AAAA,OACL,CAAA;AACA,MAAQ,OAAA,CAAA;AAAA,QACJ,kBAAA;AAAA,QACA,WAAA;AAAA,OACH,CAAA,CAAA;AAAA,KACJ,CAAA,CAAA;AAAA,GACJ,CAAA,CAAA;AACL;;;;"}