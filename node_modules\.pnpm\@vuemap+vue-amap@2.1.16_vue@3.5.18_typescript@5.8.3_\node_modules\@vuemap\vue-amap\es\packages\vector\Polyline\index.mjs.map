{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/vector/Polyline/index.ts"], "sourcesContent": ["import Polyline from './Polyline.vue';\r\nimport type {Plugin, App} from \"vue\";\r\nPolyline.install = (app: App) => {\r\n  app.component(Polyline.name, Polyline);\r\n  return app;\r\n};\r\nexport const ElAmapPolyline = Polyline as typeof Polyline & Plugin;\r\nexport default ElAmapPolyline;\r\n\r\nexport type ElAmapPolylineInstance = InstanceType<typeof Polyline>\r\n"], "names": ["Polyline"], "mappings": ";;;AAEAA,MAAS,CAAA,OAAA,GAAU,CAAC,GAAa,KAAA;AAC/B,EAAI,GAAA,CAAA,SAAA,CAAUA,MAAS,CAAA,IAAA,EAAMA,MAAQ,CAAA,CAAA;AACrC,EAAO,OAAA,GAAA,CAAA;AACT,CAAA,CAAA;AACO,MAAM,cAAiB,GAAAA;;;;"}