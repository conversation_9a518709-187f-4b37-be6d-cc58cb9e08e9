export declare type ResultLngLat = {
    lng: number;
    lat: number;
};
export declare function lonLatToTileNumbers(lon_deg: number, lat_deg: number, zoom: number): [number, number];
export declare function tileNumbersToLonLat(xtile: number, ytile: number, zoom: number): [number, number];
/**百度转84*/
export declare function bd09_To_gps84(lng: number, lat: number): ResultLngLat;
/**84转百度*/
export declare function gps84_To_bd09(lng: number, lat: number): ResultLngLat;
/**84转火星*/
export declare function gps84_To_gcj02(lng: number, lat: number): ResultLngLat;
/**火星转84*/
export declare function gcj02_To_gps84(lng: number, lat: number): ResultLngLat;
/**火星转百度*/
export declare function gcj02_To_bd09(x: number, y: number): ResultLngLat;
/**百度转火星*/
export declare function bd09_To_gcj02(bd_lng: number, bd_lat: number): ResultLngLat;
