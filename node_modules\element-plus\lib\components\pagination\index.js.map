{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/pagination/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Pagination from './src/pagination'\n\nexport const ElPagination = withInstall(Pagination)\nexport default ElPagination\n\nexport * from './src/pagination'\nexport * from './src/constants'\n"], "names": ["withInstall", "Pagination"], "mappings": ";;;;;;;;;AAEY,MAAC,YAAY,GAAGA,mBAAW,CAACC,qBAAU;;;;;;;;"}