export type ClipboardItems = ClipboardItemInterface[];
export interface ClipboardWithoutEventTarget {
    read(): Promise<ClipboardItems>;
    readText(): Promise<string>;
    write(data: ClipboardItems): Promise<void>;
    writeText(data: string): Promise<void>;
}
export interface ClipboardEventTarget extends EventTarget, ClipboardWithoutEventTarget {
}
export type ClipboardItemDataType = string | Blob;
export type ClipboardItemData = Promise<ClipboardItemDataType>;
export type ClipboardItemDelayedCallback = () => ClipboardItemDelayedCallback;
export interface ClipboardItemConstructor {
    new (items: {
        [type: string]: ClipboardItemDataType;
    }, options?: ClipboardItemOptions): ClipboardItemInterface;
    createDelayed?(items: {
        [type: string]: () => ClipboardItemDelayedCallback;
    }, options?: ClipboardItemOptions): ClipboardItemInterface;
}
export interface ClipboardItemInterface {
    readonly presentationStyle?: PresentationStyle;
    readonly lastModified?: number;
    readonly delayed?: boolean;
    readonly types: ReadonlyArray<string>;
    getType(type: string): Promise<Blob>;
}
export type PresentationStyle = "unspecified" | "inline" | "attachment";
export interface ClipboardItemOptions {
    presentationStyle?: PresentationStyle;
}
