{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/empty/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Empty from './src/empty.vue'\n\nexport const ElEmpty = withInstall(Empty)\nexport default ElEmpty\n\nexport * from './src/empty'\nexport type { EmptyInstance } from './src/instance'\n"], "names": ["withInstall", "Empty"], "mappings": ";;;;;;;;;AAEY,MAAC,OAAO,GAAGA,mBAAW,CAACC,kBAAK;;;;;;"}