{"version": 3, "file": "useLoca.mjs", "sources": ["../../../mixins/useLoca.ts"], "sourcesContent": ["import {nextTick} from \"vue\";\r\nimport type {IProvideType} from '@vuemap/vue-amap';\r\n\r\n\r\ninterface IUseWatchFnType {\r\n  setSource: () => void\r\n  $amapComponent: () => any\r\n  props: any\r\n}\r\n\r\nexport interface IUseLocaTypes {\r\n  $$getInstance: () => any\r\n  parentInstance: IProvideType\r\n}\r\n\r\nexport function useWatchFn (options: IUseWatchFnType){\r\n  return {\r\n    __layerStyle (style: any) {\r\n      nextTick(() => {\r\n        if (options.$amapComponent()?.setStyle) {\r\n          options.$amapComponent().setStyle(style);\r\n        }\r\n      }).then();\r\n    },\r\n    __sourceUrl (){\r\n      nextTick(() => {\r\n        options.setSource();\r\n      }).then();\r\n    },\r\n    __sourceData (){\r\n      nextTick(() => {\r\n        options.setSource();\r\n      }).then();\r\n    },\r\n    __geoBufferSource (){\r\n      nextTick(() => {\r\n        options.setSource();\r\n      }).then();\r\n    },\r\n    __visible (flag: boolean) {\r\n      const $amapComponent = options.$amapComponent();\r\n      if ($amapComponent?.show && $amapComponent?.hide) {\r\n        !flag ? $amapComponent.hide(options.props.visibleDuration) : $amapComponent.show(options.props.visibleDuration);\r\n      }\r\n    }\r\n  };\r\n}\r\n\r\nexport function useLocaEvents (options: {\r\n  parentInstance?: IProvideType\r\n  $amapComponent: any\r\n  emits: any,\r\n  props: any\r\n  setStyle: () => void\r\n}){\r\n  let isDragging = false;\r\n  let isRotating = false;\r\n  let source: any;\r\n  const {parentInstance, $amapComponent, emits, props, setStyle}  = options;\r\n  \r\n  const setSource = () => {\r\n    if (source) {\r\n      source.destroy();\r\n      source = null;\r\n    }\r\n    if (props.geoBufferSource) {\r\n      if(typeof props.geoBufferSource === 'string'){\r\n        source = new Loca.GeoBufferSource({\r\n          url: props.geoBufferSource\r\n        });\r\n      }else{\r\n        source = new Loca.GeoBufferSource({\r\n          data: props.geoBufferSource\r\n        });\r\n      }\r\n    }else if (props.sourceUrl) {\r\n      source = new Loca.GeoJSONSource({\r\n        url: props.sourceUrl\r\n      });\r\n    } else if (props.sourceData) {\r\n      source = new Loca.GeoJSONSource({\r\n        data: props.sourceData\r\n      });\r\n    } else {\r\n      source = new Loca.GeoJSONSource({\r\n      });\r\n    }\r\n    $amapComponent.setSource(source);\r\n  };\r\n  \r\n  const initComplete = () => {\r\n    if (props.initEvents) {\r\n      bindEvents();\r\n    }\r\n  };\r\n  const bindEvents = () => {\r\n    if(parentInstance){\r\n      const map = parentInstance.getMap();\r\n      if(props.onClick !== null){\r\n        map.on('click', clickMap);\r\n      }\r\n      if(props.onMousemove !== null){\r\n        map.on('mousemove', mouseMoveMap);\r\n        map.on('dragstart', dragStart);\r\n        map.on('dragend', dragEnd);\r\n        map.on('rotatestart', rotateStart);\r\n        map.on('rotateend', rotateEnd);\r\n        map.on('mouseout', mouseoutMap);\r\n      }\r\n      if(props.onRightclick !== null){\r\n        map.on('rightclick', rightclickMap);\r\n      }\r\n    }\r\n  };\r\n  const clickMap = (e: any) => {\r\n    const feature = _getFeature(e);\r\n    emits('click', feature, e);\r\n  };\r\n  const rightclickMap = (e: any) => {\r\n    const feature = _getFeature(e);\r\n    emits('rightclick', feature, e);\r\n  };\r\n  const mouseMoveMap = (e:any) => {\r\n    if(isDragging || isRotating){\r\n      return;\r\n    }\r\n    const feature = _getFeature(e);\r\n    emits('mousemove', feature, e);\r\n  };\r\n  const _getFeature = (e:any) => {\r\n    return $amapComponent.queryFeature(e.pixel.toArray());\r\n  };\r\n  const dragStart =  () => {\r\n    isDragging = true;\r\n  };\r\n  const dragEnd  = () => {\r\n    isDragging = false;\r\n  };\r\n  const mouseoutMap =  () => {\r\n    isDragging = false;\r\n    isRotating = false;\r\n  };\r\n  const rotateStart  = () => {\r\n    isRotating = true;\r\n  };\r\n  const rotateEnd  = () => {\r\n    isRotating = false;\r\n  };\r\n  const unBindEvents =  ()  => {\r\n    if(parentInstance){\r\n      const map = parentInstance.getMap();\r\n      map.off('click', clickMap);\r\n      map.off('rightclick', rightclickMap);\r\n      map.off('mousemove', mouseMoveMap);\r\n      map.off('dragstart', dragStart);\r\n      map.off('dragend', dragEnd);\r\n      map.off('rotatestart', rotateStart);\r\n      map.off('rotateend', rotateEnd);\r\n      map.off('mouseout', mouseoutMap);\r\n    }\r\n  };\r\n  \r\n  setSource();\r\n  setStyle();\r\n  parentInstance?.$amapComponent.add($amapComponent);\r\n  initComplete();\r\n  \r\n  const _destroyComponent = () => {\r\n    unBindEvents();\r\n    if(!parentInstance?.isDestroy){\r\n      parentInstance?.$amapComponent.remove($amapComponent);\r\n      $amapComponent.destroy();\r\n    }\r\n    if (source) {\r\n      source.destroy();\r\n      source = null;\r\n    }\r\n  };\r\n  \r\n  return {\r\n    _destroyComponent,\r\n    setSource\r\n  };\r\n}"], "names": [], "mappings": ";;AAeO,SAAS,WAAY,OAAyB,EAAA;AACnD,EAAO,OAAA;AAAA,IACL,aAAc,KAAY,EAAA;AACxB,MAAA,QAAA,CAAS,MAAM;AAlBrB,QAAA,IAAA,EAAA,CAAA;AAmBQ,QAAA,IAAA,CAAI,EAAQ,GAAA,OAAA,CAAA,cAAA,EAAR,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAA0B,QAAU,EAAA;AACtC,UAAQ,OAAA,CAAA,cAAA,EAAiB,CAAA,QAAA,CAAS,KAAK,CAAA,CAAA;AAAA,SACzC;AAAA,OACD,EAAE,IAAK,EAAA,CAAA;AAAA,KACV;AAAA,IACA,WAAc,GAAA;AACZ,MAAA,QAAA,CAAS,MAAM;AACb,QAAA,OAAA,CAAQ,SAAU,EAAA,CAAA;AAAA,OACnB,EAAE,IAAK,EAAA,CAAA;AAAA,KACV;AAAA,IACA,YAAe,GAAA;AACb,MAAA,QAAA,CAAS,MAAM;AACb,QAAA,OAAA,CAAQ,SAAU,EAAA,CAAA;AAAA,OACnB,EAAE,IAAK,EAAA,CAAA;AAAA,KACV;AAAA,IACA,iBAAoB,GAAA;AAClB,MAAA,QAAA,CAAS,MAAM;AACb,QAAA,OAAA,CAAQ,SAAU,EAAA,CAAA;AAAA,OACnB,EAAE,IAAK,EAAA,CAAA;AAAA,KACV;AAAA,IACA,UAAW,IAAe,EAAA;AACxB,MAAM,MAAA,cAAA,GAAiB,QAAQ,cAAe,EAAA,CAAA;AAC9C,MAAI,IAAA,CAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAgB,IAAQ,MAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAgB,IAAM,CAAA,EAAA;AAChD,QAAC,CAAA,IAAA,GAAO,cAAe,CAAA,IAAA,CAAK,OAAQ,CAAA,KAAA,CAAM,eAAe,CAAA,GAAI,cAAe,CAAA,IAAA,CAAK,OAAQ,CAAA,KAAA,CAAM,eAAe,CAAA,CAAA;AAAA,OAChH;AAAA,KACF;AAAA,GACF,CAAA;AACF,CAAA;AAEO,SAAS,cAAe,OAM7B,EAAA;AACA,EAAA,IAAI,UAAa,GAAA,KAAA,CAAA;AACjB,EAAA,IAAI,UAAa,GAAA,KAAA,CAAA;AACjB,EAAI,IAAA,MAAA,CAAA;AACJ,EAAA,MAAM,EAAC,cAAgB,EAAA,cAAA,EAAgB,KAAO,EAAA,KAAA,EAAO,UAAa,GAAA,OAAA,CAAA;AAElE,EAAA,MAAM,YAAY,MAAM;AACtB,IAAA,IAAI,MAAQ,EAAA;AACV,MAAA,MAAA,CAAO,OAAQ,EAAA,CAAA;AACf,MAAS,MAAA,GAAA,IAAA,CAAA;AAAA,KACX;AACA,IAAA,IAAI,MAAM,eAAiB,EAAA;AACzB,MAAG,IAAA,OAAO,KAAM,CAAA,eAAA,KAAoB,QAAS,EAAA;AAC3C,QAAS,MAAA,GAAA,IAAI,KAAK,eAAgB,CAAA;AAAA,UAChC,KAAK,KAAM,CAAA,eAAA;AAAA,SACZ,CAAA,CAAA;AAAA,OACE,MAAA;AACH,QAAS,MAAA,GAAA,IAAI,KAAK,eAAgB,CAAA;AAAA,UAChC,MAAM,KAAM,CAAA,eAAA;AAAA,SACb,CAAA,CAAA;AAAA,OACH;AAAA,KACF,MAAA,IAAU,MAAM,SAAW,EAAA;AACzB,MAAS,MAAA,GAAA,IAAI,KAAK,aAAc,CAAA;AAAA,QAC9B,KAAK,KAAM,CAAA,SAAA;AAAA,OACZ,CAAA,CAAA;AAAA,KACH,MAAA,IAAW,MAAM,UAAY,EAAA;AAC3B,MAAS,MAAA,GAAA,IAAI,KAAK,aAAc,CAAA;AAAA,QAC9B,MAAM,KAAM,CAAA,UAAA;AAAA,OACb,CAAA,CAAA;AAAA,KACI,MAAA;AACL,MAAA,MAAA,GAAS,IAAI,IAAA,CAAK,aAAc,CAAA,EAC/B,CAAA,CAAA;AAAA,KACH;AACA,IAAA,cAAA,CAAe,UAAU,MAAM,CAAA,CAAA;AAAA,GACjC,CAAA;AAEA,EAAA,MAAM,eAAe,MAAM;AACzB,IAAA,IAAI,MAAM,UAAY,EAAA;AACpB,MAAW,UAAA,EAAA,CAAA;AAAA,KACb;AAAA,GACF,CAAA;AACA,EAAA,MAAM,aAAa,MAAM;AACvB,IAAA,IAAG,cAAe,EAAA;AAChB,MAAM,MAAA,GAAA,GAAM,eAAe,MAAO,EAAA,CAAA;AAClC,MAAG,IAAA,KAAA,CAAM,YAAY,IAAK,EAAA;AACxB,QAAI,GAAA,CAAA,EAAA,CAAG,SAAS,QAAQ,CAAA,CAAA;AAAA,OAC1B;AACA,MAAG,IAAA,KAAA,CAAM,gBAAgB,IAAK,EAAA;AAC5B,QAAI,GAAA,CAAA,EAAA,CAAG,aAAa,YAAY,CAAA,CAAA;AAChC,QAAI,GAAA,CAAA,EAAA,CAAG,aAAa,SAAS,CAAA,CAAA;AAC7B,QAAI,GAAA,CAAA,EAAA,CAAG,WAAW,OAAO,CAAA,CAAA;AACzB,QAAI,GAAA,CAAA,EAAA,CAAG,eAAe,WAAW,CAAA,CAAA;AACjC,QAAI,GAAA,CAAA,EAAA,CAAG,aAAa,SAAS,CAAA,CAAA;AAC7B,QAAI,GAAA,CAAA,EAAA,CAAG,YAAY,WAAW,CAAA,CAAA;AAAA,OAChC;AACA,MAAG,IAAA,KAAA,CAAM,iBAAiB,IAAK,EAAA;AAC7B,QAAI,GAAA,CAAA,EAAA,CAAG,cAAc,aAAa,CAAA,CAAA;AAAA,OACpC;AAAA,KACF;AAAA,GACF,CAAA;AACA,EAAM,MAAA,QAAA,GAAW,CAAC,CAAW,KAAA;AAC3B,IAAM,MAAA,OAAA,GAAU,YAAY,CAAC,CAAA,CAAA;AAC7B,IAAM,KAAA,CAAA,OAAA,EAAS,SAAS,CAAC,CAAA,CAAA;AAAA,GAC3B,CAAA;AACA,EAAM,MAAA,aAAA,GAAgB,CAAC,CAAW,KAAA;AAChC,IAAM,MAAA,OAAA,GAAU,YAAY,CAAC,CAAA,CAAA;AAC7B,IAAM,KAAA,CAAA,YAAA,EAAc,SAAS,CAAC,CAAA,CAAA;AAAA,GAChC,CAAA;AACA,EAAM,MAAA,YAAA,GAAe,CAAC,CAAU,KAAA;AAC9B,IAAA,IAAG,cAAc,UAAW,EAAA;AAC1B,MAAA,OAAA;AAAA,KACF;AACA,IAAM,MAAA,OAAA,GAAU,YAAY,CAAC,CAAA,CAAA;AAC7B,IAAM,KAAA,CAAA,WAAA,EAAa,SAAS,CAAC,CAAA,CAAA;AAAA,GAC/B,CAAA;AACA,EAAM,MAAA,WAAA,GAAc,CAAC,CAAU,KAAA;AAC7B,IAAA,OAAO,cAAe,CAAA,YAAA,CAAa,CAAE,CAAA,KAAA,CAAM,SAAS,CAAA,CAAA;AAAA,GACtD,CAAA;AACA,EAAA,MAAM,YAAa,MAAM;AACvB,IAAa,UAAA,GAAA,IAAA,CAAA;AAAA,GACf,CAAA;AACA,EAAA,MAAM,UAAW,MAAM;AACrB,IAAa,UAAA,GAAA,KAAA,CAAA;AAAA,GACf,CAAA;AACA,EAAA,MAAM,cAAe,MAAM;AACzB,IAAa,UAAA,GAAA,KAAA,CAAA;AACb,IAAa,UAAA,GAAA,KAAA,CAAA;AAAA,GACf,CAAA;AACA,EAAA,MAAM,cAAe,MAAM;AACzB,IAAa,UAAA,GAAA,IAAA,CAAA;AAAA,GACf,CAAA;AACA,EAAA,MAAM,YAAa,MAAM;AACvB,IAAa,UAAA,GAAA,KAAA,CAAA;AAAA,GACf,CAAA;AACA,EAAA,MAAM,eAAgB,MAAO;AAC3B,IAAA,IAAG,cAAe,EAAA;AAChB,MAAM,MAAA,GAAA,GAAM,eAAe,MAAO,EAAA,CAAA;AAClC,MAAI,GAAA,CAAA,GAAA,CAAI,SAAS,QAAQ,CAAA,CAAA;AACzB,MAAI,GAAA,CAAA,GAAA,CAAI,cAAc,aAAa,CAAA,CAAA;AACnC,MAAI,GAAA,CAAA,GAAA,CAAI,aAAa,YAAY,CAAA,CAAA;AACjC,MAAI,GAAA,CAAA,GAAA,CAAI,aAAa,SAAS,CAAA,CAAA;AAC9B,MAAI,GAAA,CAAA,GAAA,CAAI,WAAW,OAAO,CAAA,CAAA;AAC1B,MAAI,GAAA,CAAA,GAAA,CAAI,eAAe,WAAW,CAAA,CAAA;AAClC,MAAI,GAAA,CAAA,GAAA,CAAI,aAAa,SAAS,CAAA,CAAA;AAC9B,MAAI,GAAA,CAAA,GAAA,CAAI,YAAY,WAAW,CAAA,CAAA;AAAA,KACjC;AAAA,GACF,CAAA;AAEA,EAAU,SAAA,EAAA,CAAA;AACV,EAAS,QAAA,EAAA,CAAA;AACT,EAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAgB,eAAe,GAAI,CAAA,cAAA,CAAA,CAAA;AACnC,EAAa,YAAA,EAAA,CAAA;AAEb,EAAA,MAAM,oBAAoB,MAAM;AAC9B,IAAa,YAAA,EAAA,CAAA;AACb,IAAG,IAAA,EAAC,iDAAgB,SAAU,CAAA,EAAA;AAC5B,MAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAgB,eAAe,MAAO,CAAA,cAAA,CAAA,CAAA;AACtC,MAAA,cAAA,CAAe,OAAQ,EAAA,CAAA;AAAA,KACzB;AACA,IAAA,IAAI,MAAQ,EAAA;AACV,MAAA,MAAA,CAAO,OAAQ,EAAA,CAAA;AACf,MAAS,MAAA,GAAA,IAAA,CAAA;AAAA,KACX;AAAA,GACF,CAAA;AAEA,EAAO,OAAA;AAAA,IACL,iBAAA;AAAA,IACA,SAAA;AAAA,GACF,CAAA;AACF;;;;"}