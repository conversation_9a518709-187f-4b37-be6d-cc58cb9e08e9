import installer from './defaults.mjs';
import './services/index.mjs';
import './packages/index.mjs';
import './mixins/index.mjs';
import './hooks/index.mjs';
import './utils/index.mjs';
export { initAMapApiLoader, lazyAMapApiLoaderInstance } from './services/injected-amap-api-instance.mjs';
export { resetJsApi } from './services/amap-api-loader.mjs';
export { ElAmap } from './packages/amap/index.mjs';
export { ElAmapControlControlBar } from './packages/control/ControlBar/index.mjs';
export { ElAmapControlHawkEye } from './packages/control/HawkEye/index.mjs';
export { ElAmapControlMapType } from './packages/control/MapType/index.mjs';
export { ElAmapControlScale } from './packages/control/Scale/index.mjs';
export { ElAmapControlToolBar } from './packages/control/ToolBar/index.mjs';
export { ElAmapSearchBox } from './packages/control/SearchBox/index.mjs';
export { ElAmapInfoWindow } from './packages/infoWindow/InfoWindow/index.mjs';
export { ElAmapLayerCanvas } from './packages/layer/data/Canvas/index.mjs';
export { ElAmapLayerCustom } from './packages/layer/data/Custom/index.mjs';
export { ElAmapLayerFlexible } from './packages/layer/data/Flexible/index.mjs';
export { ElAmapLayerGlCustom } from './packages/layer/data/GLCustom/index.mjs';
export { ElAmapLayerHeatMap } from './packages/layer/data/HeatMap/index.mjs';
export { ElAmapLayerImage } from './packages/layer/data/Image/index.mjs';
export { ElAmapLayerLabels } from './packages/layer/data/Labels/index.mjs';
export { ElAmapLayerVector } from './packages/layer/data/Vector/index.mjs';
export { ElAmapLayerDistrictCluster } from './packages/layer/data/DistrictCluster/index.mjs';
export { ElAmapLayerBuildings } from './packages/layer/official/Buildings/index.mjs';
export { ElAmapLayerDefault } from './packages/layer/official/DefaultLayer/index.mjs';
export { ElAmapLayerDistrict } from './packages/layer/official/DistrictLayer/index.mjs';
export { ElAmapLayerIndoorMap } from './packages/layer/official/IndoorMap/index.mjs';
export { ElAmapLayerRoadNet } from './packages/layer/official/RoadNet/index.mjs';
export { ElAmapLayerSatellite } from './packages/layer/official/Satellite/index.mjs';
export { ElAmapLayerTile } from './packages/layer/official/TileLayer/index.mjs';
export { ElAmapLayerTraffic } from './packages/layer/official/Traffic/index.mjs';
export { ElAmapLayerMapboxVectorTile } from './packages/layer/standard/MapboxVectorTileLayer/index.mjs';
export { ElAmapLayerWms } from './packages/layer/standard/WMS/index.mjs';
export { ElAmapLayerWmts } from './packages/layer/standard/WMTS/index.mjs';
export { ElAmapElasticMarker } from './packages/marker/ElasticMarker/index.mjs';
export { ElAmapLabelMarker } from './packages/marker/LabelMarker/index.mjs';
export { ElAmapMarker } from './packages/marker/Marker/index.mjs';
export { ElAmapMarkerCluster } from './packages/marker/MarkerCluster/index.mjs';
export { ElAmapMassMarks } from './packages/marker/MassMarks/index.mjs';
export { ElAmapText } from './packages/marker/Text/index.mjs';
export { ElAmapBezierCurve } from './packages/vector/BezierCurve/index.mjs';
export { ElAmapCircle } from './packages/vector/Circle/index.mjs';
export { ElAmapEllipse } from './packages/vector/Ellipse/index.mjs';
export { ElAmapGeojson } from './packages/vector/GeoJSON/index.mjs';
export { ElAmapPolygon } from './packages/vector/Polygon/index.mjs';
export { ElAmapPolyline } from './packages/vector/Polyline/index.mjs';
export { ElAmapRectangle } from './packages/vector/Rectangle/index.mjs';
export { ElAmapLayerTiles3d } from './packages/layer/data/Tiles3D/index.mjs';
export { ElAmapControlGeolocation } from './packages/control/Geolocation/index.mjs';
export { ElAmapCircleMarker } from './packages/marker/CircleMarker/index.mjs';
export { ElAmapLayerVideo } from './packages/layer/data/Video/index.mjs';
export { ElAmapMouseTool } from './packages/util/MouseTool/index.mjs';
export { ElAmapLayerCustomXyz } from './packages/layer/data/CustomXyz/index.mjs';
export { default as registerMixin } from './mixins/register-component.mjs';
export { provideKey, useRegister } from './mixins/useRegister.mjs';
export { useCitySearch } from './hooks/useCitySearch.mjs';
export { useWeather } from './hooks/useWeather.mjs';
export { useGeolocation } from './hooks/useGeolocation.mjs';
export { default as guid } from './utils/guid.mjs';
export { convertEventToLowerCase, convertLnglat, eventReg, isIndoorMapInstance, isLabelsLayerInstance, isMapInstance, isOverlayGroupInstance, isVectorLayerInstance, loadScript, upperCamelCase } from './utils/util.mjs';
export { bindInstanceEvent, removeInstanceEvent } from './utils/eventHelper.mjs';
export { lngLatTo, pixelTo, toBounds, toLngLat, toPixel, toSize } from './utils/convert-helper.mjs';
export { bd09_To_gcj02, bd09_To_gps84, gcj02_To_bd09, gcj02_To_gps84, gps84_To_bd09, gps84_To_gcj02, lonLatToTileNumbers, tileNumbersToLonLat } from './utils/GPSUtil.mjs';
export { buildProps, commonProps } from './utils/buildHelper.mjs';
export { makeInstaller } from './utils/make-installer.mjs';

const install = installer.install;

export { installer as default, install };
//# sourceMappingURL=index.mjs.map
