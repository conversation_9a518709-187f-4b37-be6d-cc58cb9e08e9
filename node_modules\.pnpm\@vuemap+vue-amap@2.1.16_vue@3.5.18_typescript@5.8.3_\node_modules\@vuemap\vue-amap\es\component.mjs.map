{"version": 3, "file": "component.mjs", "sources": ["../../component.ts"], "sourcesContent": ["import { ElAmap } from './packages/amap';\r\nimport {ElAmapControlControlBar} from './packages/control/ControlBar';\r\nimport {ElAmapControlHawkEye} from './packages/control/HawkEye';\r\nimport {ElAmapControlMapType} from './packages/control/MapType';\r\nimport {ElAmapControlScale} from './packages/control/Scale';\r\nimport {ElAmapControlToolBar} from './packages/control/ToolBar';\r\nimport {ElAmapSearchBox} from './packages/control/SearchBox';\r\nimport {ElAmapInfoWindow} from './packages/infoWindow/InfoWindow';\r\nimport {ElAmapLayerCanvas} from './packages/layer/data/Canvas';\r\nimport {ElAmapLayerCustom} from './packages/layer/data/Custom';\r\nimport {ElAmapLayerFlexible} from './packages/layer/data/Flexible';\r\nimport {ElAmapLayerGlCustom} from './packages/layer/data/GLCustom';\r\nimport {ElAmapLayerHeatMap} from './packages/layer/data/HeatMap';\r\nimport {ElAmapLayerImage} from './packages/layer/data/Image';\r\nimport {ElAmapLayerLabels} from './packages/layer/data/Labels';\r\nimport {ElAmapLayerVector} from './packages/layer/data/Vector';\r\nimport {ElAmapLayerBuildings} from './packages/layer/official/Buildings';\r\nimport {ElAmapLayerDefault} from './packages/layer/official/DefaultLayer';\r\nimport {ElAmapLayerDistrict} from './packages/layer/official/DistrictLayer';\r\nimport {ElAmapLayerIndoorMap} from './packages/layer/official/IndoorMap';\r\nimport {ElAmapLayerRoadNet} from './packages/layer/official/RoadNet';\r\nimport {ElAmapLayerSatellite} from './packages/layer/official/Satellite';\r\nimport {ElAmapLayerTile} from './packages/layer/official/TileLayer';\r\nimport {ElAmapLayerTraffic} from './packages/layer/official/Traffic';\r\nimport {ElAmapLayerMapboxVectorTile} from './packages/layer/standard/MapboxVectorTileLayer';\r\nimport {ElAmapLayerWms} from './packages/layer/standard/WMS';\r\nimport {ElAmapLayerWmts} from './packages/layer/standard/WMTS';\r\nimport {ElAmapElasticMarker} from './packages/marker/ElasticMarker';\r\nimport {ElAmapLabelMarker} from './packages/marker/LabelMarker';\r\nimport {ElAmapMarker} from './packages/marker/Marker';\r\nimport {ElAmapMarkerCluster} from './packages/marker/MarkerCluster';\r\nimport {ElAmapMassMarks} from './packages/marker/MassMarks';\r\nimport {ElAmapText} from './packages/marker/Text';\r\nimport {ElAmapBezierCurve} from './packages/vector/BezierCurve';\r\nimport {ElAmapCircle} from './packages/vector/Circle';\r\nimport {ElAmapEllipse} from './packages/vector/Ellipse';\r\nimport {ElAmapGeojson} from './packages/vector/GeoJSON';\r\nimport {ElAmapPolygon} from './packages/vector/Polygon';\r\nimport {ElAmapPolyline} from './packages/vector/Polyline';\r\nimport {ElAmapRectangle} from './packages/vector/Rectangle';\r\nimport {ElAmapLayerTiles3d} from './packages/layer/data/Tiles3D';\r\nimport {ElAmapControlGeolocation} from './packages/control/Geolocation';\r\nimport {ElAmapCircleMarker} from './packages/marker/CircleMarker';\r\nimport {ElAmapLayerVideo} from \"./packages/layer/data/Video\";\r\nimport {ElAmapMouseTool} from './packages/util/MouseTool';\r\nimport {ElAmapLayerDistrictCluster} from './packages/layer/data/DistrictCluster';\r\nimport {ElAmapLayerCustomXyz} from './packages/layer/data/CustomXyz';\r\n\r\nimport type { Plugin } from 'vue';\r\n\r\nexport default [\r\n  ElAmap,\r\n  ElAmapControlControlBar,\r\n  ElAmapControlHawkEye,\r\n  ElAmapControlMapType,\r\n  ElAmapControlScale,\r\n  ElAmapControlToolBar,\r\n  ElAmapSearchBox,\r\n  ElAmapInfoWindow,\r\n  ElAmapLayerCanvas,\r\n  ElAmapLayerCustom,\r\n  ElAmapLayerFlexible,\r\n  ElAmapLayerGlCustom,\r\n  ElAmapLayerHeatMap,\r\n  ElAmapLayerImage,\r\n  ElAmapLayerLabels,\r\n  ElAmapLayerVector,\r\n  ElAmapLayerBuildings,\r\n  ElAmapLayerDefault,\r\n  ElAmapLayerDistrict,\r\n  ElAmapLayerIndoorMap,\r\n  ElAmapLayerRoadNet,\r\n  ElAmapLayerSatellite,\r\n  ElAmapLayerTile,\r\n  ElAmapLayerTraffic,\r\n  ElAmapLayerMapboxVectorTile,\r\n  ElAmapLayerWms,\r\n  ElAmapLayerWmts,\r\n  ElAmapElasticMarker,\r\n  ElAmapLabelMarker,\r\n  ElAmapMarker,\r\n  ElAmapMarkerCluster,\r\n  ElAmapMassMarks,\r\n  ElAmapText,\r\n  ElAmapBezierCurve,\r\n  ElAmapCircle,\r\n  ElAmapEllipse,\r\n  ElAmapGeojson,\r\n  ElAmapPolygon,\r\n  ElAmapPolyline,\r\n  ElAmapRectangle,\r\n  ElAmapLayerTiles3d,\r\n  ElAmapControlGeolocation,\r\n  ElAmapCircleMarker,\r\n  ElAmapLayerVideo,\r\n  ElAmapMouseTool,\r\n  ElAmapLayerDistrictCluster,\r\n  ElAmapLayerCustomXyz\r\n] as Plugin[];\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDA,iBAAe;AAAA,EACb,MAAA;AAAA,EACA,uBAAA;AAAA,EACA,oBAAA;AAAA,EACA,oBAAA;AAAA,EACA,kBAAA;AAAA,EACA,oBAAA;AAAA,EACA,eAAA;AAAA,EACA,gBAAA;AAAA,EACA,iBAAA;AAAA,EACA,iBAAA;AAAA,EACA,mBAAA;AAAA,EACA,mBAAA;AAAA,EACA,kBAAA;AAAA,EACA,gBAAA;AAAA,EACA,iBAAA;AAAA,EACA,iBAAA;AAAA,EACA,oBAAA;AAAA,EACA,kBAAA;AAAA,EACA,mBAAA;AAAA,EACA,oBAAA;AAAA,EACA,kBAAA;AAAA,EACA,oBAAA;AAAA,EACA,eAAA;AAAA,EACA,kBAAA;AAAA,EACA,2BAAA;AAAA,EACA,cAAA;AAAA,EACA,eAAA;AAAA,EACA,mBAAA;AAAA,EACA,iBAAA;AAAA,EACA,YAAA;AAAA,EACA,mBAAA;AAAA,EACA,eAAA;AAAA,EACA,UAAA;AAAA,EACA,iBAAA;AAAA,EACA,YAAA;AAAA,EACA,aAAA;AAAA,EACA,aAAA;AAAA,EACA,aAAA;AAAA,EACA,cAAA;AAAA,EACA,eAAA;AAAA,EACA,kBAAA;AAAA,EACA,wBAAA;AAAA,EACA,kBAAA;AAAA,EACA,gBAAA;AAAA,EACA,eAAA;AAAA,EACA,0BAAA;AAAA,EACA,oBAAA;AACF,CAAA;;;;"}