'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var vue = require('vue');
require('../../mixins/index.js');
var guid = require('../../utils/guid.js');
require('../../services/index.js');
var props = require('./props.js');
var useRegister = require('../../mixins/useRegister.js');
var injectedAmapApiInstance = require('../../services/injected-amap-api-instance.js');

const _hoisted_1 = { class: "el-vue-amap-container" };
const _hoisted_2 = ["id"];
var script = /* @__PURE__ */ vue.defineComponent({
  ...{
    name: "ElAmap",
    inheritAttrs: false
  },
  __name: "amap",
  props: props.propsType,
  emits: ["init", "update:zoom", "update:center", "update:rotation", "update:pitch"],
  setup(__props, { expose: __expose, emit: __emit }) {
    const needInitComponents = [];
    const provideData = {
      $amapComponent: void 0,
      addChildComponent(cb) {
        needInitComponents.push(cb);
      },
      isDestroy: false
    };
    vue.provide(useRegister.provideKey, provideData);
    const props = __props;
    const emits = __emit;
    const mapDomId = vue.ref(props.vid || guid.default());
    let $amapComponent;
    const { $$getInstance } = useRegister.useRegister((options) => {
      return new Promise((resolve, reject) => {
        if (!injectedAmapApiInstance.lazyAMapApiLoaderInstance) {
          reject(new Error("\u8BF7\u521D\u59CB\u5316initAMapApiLoader"));
          return;
        }
        injectedAmapApiInstance.lazyAMapApiLoaderInstance.then(() => {
          vue.nextTick(() => {
            $amapComponent = new AMap.Map(mapDomId.value, options);
            provideData.$amapComponent = $amapComponent;
            bindModelEvents();
            resolve($amapComponent);
          });
        }).catch((e) => {
          reject(e);
        });
      });
    }, {
      isRoot: true,
      emits,
      needInitComponents,
      provideData,
      watchRedirectFn: {
        __dragEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ dragEnable: flag });
          }
        },
        __zoomEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ zoomEnable: flag });
          }
        },
        __jogEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ jogEnable: flag });
          }
        },
        __keyboardEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ keyboardEnable: flag });
          }
        },
        __doubleClickZoom(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ doubleClickZoom: flag });
          }
        },
        __scrollWheel(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ scrollWheel: flag });
          }
        },
        __rotateEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ rotateEnable: flag });
          }
        },
        __pitchEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ pitchEnable: flag });
          }
        },
        __resizeEnable(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ resizeEnable: flag });
          }
        },
        __showIndoorMap(flag) {
          if ($amapComponent) {
            $amapComponent.setStatus({ showIndoorMap: flag });
          }
        }
      }
    });
    const bindModelEvents = () => {
      $amapComponent.on("zoomchange", () => {
        emits("update:zoom", $amapComponent.getZoom());
      });
      $amapComponent.on("rotateend", () => {
        emits("update:rotation", $amapComponent.getRotation());
      });
      $amapComponent.on("dragging", () => {
        emits("update:center", getCenter());
      });
      $amapComponent.on("dragend", () => {
        emits("update:pitch", $amapComponent.getPitch());
      });
      $amapComponent.on("touchmove", () => {
        emits("update:center", getCenter());
      });
    };
    const getCenter = () => {
      const center = $amapComponent.getCenter();
      return [center.lng, center.lat];
    };
    vue.onBeforeUnmount(() => {
      if ($amapComponent) {
        $amapComponent.clearEvents();
        $amapComponent.destroy();
        $amapComponent = null;
      }
    });
    __expose({
      $$getInstance
    });
    return (_ctx, _cache) => {
      return vue.openBlock(), vue.createElementBlock("div", _hoisted_1, [
        vue.createElementVNode("div", {
          id: mapDomId.value,
          class: "el-vue-amap"
        }, null, 8, _hoisted_2),
        vue.renderSlot(_ctx.$slots, "default")
      ]);
    };
  }
});

exports.default = script;
//# sourceMappingURL=amap.vue2.js.map
