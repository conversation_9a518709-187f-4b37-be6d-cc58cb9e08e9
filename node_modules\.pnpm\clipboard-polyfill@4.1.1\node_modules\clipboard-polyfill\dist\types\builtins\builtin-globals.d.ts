import type { PromiseConstructor } from "../promise/es6-promise";
import { ClipboardItemConstructor, ClipboardItems } from "../ClipboardItem/spec";
export declare var originalNavigatorClipboardRead: (() => Promise<ClipboardItems>) | undefined;
export declare var originalNavigatorClipboardReadText: (() => Promise<string>) | undefined;
export declare var originalNavigatorClipboardWrite: ((data: ClipboardItems) => Promise<void>) | undefined;
export declare var originalNavigatorClipboardWriteText: ((data: string) => Promise<void>) | undefined;
export declare var originalWindowClipboardItem: ClipboardItemConstructor | undefined;
export declare var promiseConstructor: PromiseConstructor;
