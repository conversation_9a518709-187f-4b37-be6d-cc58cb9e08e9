'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

require('./HawkEye.vue.js');
var HawkEye_vue_vue_type_script_setup_true_lang = require('./HawkEye.vue2.js');

HawkEye_vue_vue_type_script_setup_true_lang.default.install = (app) => {
  app.component(HawkEye_vue_vue_type_script_setup_true_lang.default.name, HawkEye_vue_vue_type_script_setup_true_lang.default);
  return app;
};
const ElAmapControlHawkEye = HawkEye_vue_vue_type_script_setup_true_lang.default;

exports.ElAmapControlHawkEye = ElAmapControlHawkEye;
exports.default = ElAmapControlHawkEye;
//# sourceMappingURL=index.js.map
