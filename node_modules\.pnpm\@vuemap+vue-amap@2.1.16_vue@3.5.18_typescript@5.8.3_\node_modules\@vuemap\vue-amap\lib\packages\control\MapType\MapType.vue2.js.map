{"version": 3, "file": "MapType.vue2.js", "sources": ["../../../../../packages/control/MapType/MapType.vue"], "sourcesContent": ["<template>\r\n  <div />\r\n</template>\r\n<script setup lang=\"ts\">\r\nimport {defineOptions} from 'vue';\r\nimport {useRegister} from \"../../../mixins\";\r\nimport {buildProps} from \"../../../utils/buildHelper\";\r\n\r\ndefineOptions({\r\n  name: 'ElAmapControlMapType',\r\n  inheritAttrs: false\r\n});\r\n\r\ndefineProps(buildProps({\r\n  defaultType: {\r\n    type: Number\r\n  }, // 初始化默认图层类型。 取值为0：默认底图 取值为1：卫星图 默认值：0\r\n  showTraffic: {\r\n    type: Boolean,\r\n    default: false\r\n  }, // 叠加实时交通图层 默认值：false\r\n  showRoad: {\r\n    type: Boolean,\r\n    default: false\r\n  }// 叠加路网图层 默认值：false\r\n}));\r\nconst emits = defineEmits(['init']);\r\n\r\nlet $amapComponent: AMap.MapType;\r\n\r\nconst {$$getInstance, parentInstance} = useRegister<AMap.MapType, AMap.Map>((options, parentComponent) => {\r\n  return new Promise<AMap.MapType>((resolve) => {\r\n    parentComponent.plugin(['AMap.MapType'], () => {\r\n      $amapComponent = new AMap.MapType(options);\r\n      parentComponent.addControl($amapComponent);\r\n      resolve($amapComponent);\r\n    });\r\n  });\r\n\r\n}, {\r\n  emits,\r\n  destroyComponent () {\r\n    if ($amapComponent && parentInstance?.$amapComponent) {\r\n      if(!parentInstance?.isDestroy){\r\n        parentInstance?.$amapComponent.removeControl($amapComponent);\r\n      }\r\n      $amapComponent = null as any;\r\n    }\r\n  },\r\n});\r\n\r\ndefineExpose({\r\n  $$getInstance\r\n});\r\n</script>\r\n"], "names": ["useRegister"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BA,IAAA,MAAM,KAAQ,GAAA,MAAA,CAAA;AAEd,IAAI,IAAA,cAAA,CAAA;AAEJ,IAAA,MAAM,EAAC,aAAe,EAAA,cAAA,KAAkBA,uBAAoC,CAAA,CAAC,SAAS,eAAoB,KAAA;AACxG,MAAO,OAAA,IAAI,OAAsB,CAAA,CAAC,OAAY,KAAA;AAC5C,QAAA,eAAA,CAAgB,MAAO,CAAA,CAAC,cAAc,CAAA,EAAG,MAAM;AAC7C,UAAiB,cAAA,GAAA,IAAI,IAAK,CAAA,OAAA,CAAQ,OAAO,CAAA,CAAA;AACzC,UAAA,eAAA,CAAgB,WAAW,cAAc,CAAA,CAAA;AACzC,UAAA,OAAA,CAAQ,cAAc,CAAA,CAAA;AAAA,SACvB,CAAA,CAAA;AAAA,OACF,CAAA,CAAA;AAAA,KAEA,EAAA;AAAA,MACD,KAAA;AAAA,MACA,gBAAoB,GAAA;AAClB,QAAI,IAAA,cAAA,KAAkB,iDAAgB,cAAgB,CAAA,EAAA;AACpD,UAAG,IAAA,EAAC,iDAAgB,SAAU,CAAA,EAAA;AAC5B,YAAA,cAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,cAAA,CAAgB,eAAe,aAAc,CAAA,cAAA,CAAA,CAAA;AAAA,WAC/C;AACA,UAAiB,cAAA,GAAA,IAAA,CAAA;AAAA,SACnB;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AAED,IAAa,QAAA,CAAA;AAAA,MACX,aAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;"}