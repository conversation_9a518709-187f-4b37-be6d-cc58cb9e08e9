{"$schema": "https://raw.githubusercontent.com/JetBrains/web-types/master/schema/web-types.json", "framework": "vue", "name": "@vuemap/vue-amap", "version": "2.1.16", "contributions": {"html": {"types-syntax": "typescript", "description-markup": "markdown", "tags": [{"name": "el-amap", "source": {"symbol": "ElAmap"}, "description": "地图容器", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#amap", "attributes": [{"name": "view-mode", "description": "地图视图模式, 默认为‘2D’，可选’3D’，选择‘3D’会显示 3D 地图效果。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "show-label", "description": "是否展示地图文字和 POI 信息。默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "default-cursor", "description": "地图默认鼠标样式。参数defaultCursor应符合CSS的cursor属性规范。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "is-hotspot", "description": "是否开启地图热点和标注的 hover 效果。PC端默认是true, 移动端默认是 false。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "wall-color", "description": "地图楼块的侧面颜色，示例：'#ffffff' 或者 [255, 0, 0, 1]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "string|array", "kind": "expression"}}, {"name": "roof-color", "description": "地图楼块的顶面颜色，示例：'#ffffff' 或者 [255, 0, 0, 1]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "string|array", "kind": "expression"}}, {"name": "show-building-block", "description": "是否展示地图 3D 楼块，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "sky-color", "description": "天空颜色，3D 模式下带有俯仰角时会显示，示例：'#ffffff' 或者 [255, 0, 0, 1]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "string|array", "kind": "expression"}}, {"name": "web-g-l-params", "description": "额外配置的WebGL参数 eg: preserveDrawingBuffer。默认 {}", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "touch-zoom", "description": "地图在移动终端上是否可通过多点触控缩放浏览地图，默认为true。关闭手势缩放地图，请设置为false。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "touch-zoom-center", "description": "可缺省，当touchZoomCenter=1的时候，手机端双指缩放的以地图中心为中心，否则默认以双指中间点为中心。默认：1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "show-label", "description": "是否展示地图文字和 POI 信息。默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "center", "description": "初始中心经纬度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "zoom", "description": "地图显示的缩放级别，可以设置为浮点数；若center与level未赋值，地图初始化默认显示用户所在城市范围。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "zooms", "description": "图显示的缩放级别范围, 默认为 [2, 20] ，取值范围 [2 ~ 30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "rotation", "description": "地图顺时针旋转角度，取值范围 [0-360] ，默认值：0", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "pitch", "description": "俯仰角度，默认 0，最大值根据地图当前 zoom 级别不断增大，2D地图下无效 。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "features", "description": "设置地图上显示的元素种类, 支持'bg'（地图背景）、'point'（POI点）、'road'（道路）、'building'（建筑物），默认值：['bg','point','road','building']", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "layers", "description": "地图图层数组，数组可以是图层 中的一个或多个，默认为普通二维地图。 当叠加多个 图层 时，普通二维地图需通过实例化一个TileLayer类实现。 如果你希望创建一个默认底图图层，使用 AMap.createDefaultLayer()", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "resize-enable", "description": "是否监控地图容器尺寸变化，默认值为false。此属性可被 setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "drag-enable", "description": "地图是否可通过鼠标拖拽平移, 默认为 true。此属性可被 setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "zoom-enable", "description": "地图是否可缩放，默认值为 true。此属性可被 setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "jog-enable", "description": "地图是否使用缓动效果，默认值为true。此属性可被setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "pitch-enable", "description": "是否允许设置俯仰角度, 3D 视图下为 true, 2D 视图下无效。。此属性可被setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "rotate-enable", "description": "地图是否可旋转, 图默认为true。此属性可被setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "animate-enable", "description": "地图平移过程中是否使用动画（如调用panBy、panTo、setCenter、setZoomAndCenter等函数, 将对地图产生平移操作, 是否使用动画平移的效果）, 默认为true, 即使用动画。此属性可被 setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "keyboard-enable", "description": "地图是否可通过键盘控制, 默认为true, 方向键控制地图平移，\"+\"和\"-\"可以控制地图的缩放, Ctrl+“→”顺时针旋转，Ctrl+“←”逆时针旋转。此属性可被setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "double-click-zoom", "description": "地图是否可通过双击鼠标放大地图, 默认为true。此属性可被setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "scroll-wheel", "description": "地图是否可通过鼠标滚轮缩放浏览，默认为true。此属性可被setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-indoor-map", "description": "是否自动展示室内地图，默认是 false。此属性可被setStatus/getStatus 方法控制", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "map-style", "description": "设置地图显示样式，目前支持normal（默认样式）、dark（深色样式）、light（浅色样式）、fresh(osm清新风格样式)四种", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "label-reject-mask", "description": "文字是否拒绝掩模图层进行掩模", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "mask", "description": "为 Map 实例指定掩模的路径，各图层将只显示路径范围内图像，3D视图下有效。 格式为一个经纬度的一维、二维或三维数组。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "terrain", "description": "是否开启地形，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "resize", "description": "地图容器大小改变事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "complete", "description": "地图图块加载完成后触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "mapmove", "description": "地图平移时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "hotspotclick", "description": "鼠标点击热点时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "hotspotover", "description": "鼠标滑过热点时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "hotspotout", "description": "鼠标移出热点时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "movestart", "description": "地图平移开始时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "moveend", "description": "地图平移结束后触发。如地图有拖拽缓动效果，则在缓动结束后触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "zoomchange", "description": "地图缩放级别更改后触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "zoomstart", "description": "缩放开始时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "zoomend", "description": "缩放停止时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "mousemove", "description": "鼠标在地图上移动时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "mousewheel", "description": "鼠标滚轮开始缩放地图时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "mouseover", "description": "鼠标移入地图容器内时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "mouseout", "description": "鼠标移出地图容器时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "mouseup", "description": "鼠标在地图上单击抬起时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "mousedown", "description": "鼠标在地图上单击按下时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "rightclick", "description": "鼠标右键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "dragstart", "description": "开始拖拽地图时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "dragging", "description": "拖拽地图过程中触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "dragend", "description": "停止拖拽地图时触发。如地图有拖拽缓动效果，则在拽停止，缓动开始前触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/amap.html#events"}]}, {"name": "el-amap-control-control-bar", "source": {"symbol": "ElAmapControlControlBar"}, "description": "组合了旋转、倾斜、复位在内的地图控件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#amapcontrolcontrolbar", "attributes": [{"name": "position", "description": "控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes", "value": {"type": "string|object", "kind": "expression"}}, {"name": "offset", "description": "地图默认鼠标样式。参数defaultCursor应符合CSS的cursor属性规范。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "show-control-button", "description": "是否显示倾斜、旋转按钮。默认为 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "show", "description": "显示时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#events"}, {"name": "hide", "description": "隐藏时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/control-bar.html#events"}]}, {"name": "el-amap-control-geolocation", "source": {"symbol": "ElAmapControlGeolocation"}, "description": "AMap.Geolocation 定位服务插件。融合了浏览器定位、高精度IP定位、安卓定位sdk辅助定位等多种手段，提供了获取当前准确位置、获取当前城市信息、持续定位", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#amapcontrolgeolocation", "attributes": [{"name": "position", "description": "控件停靠位置,默认为\"RB\". 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "offset", "description": "缩略图距离悬停位置的像素距离，如 [2,2]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "border-color", "description": "按钮边框颜色值，同CSS，如'silver'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "border-radius", "description": "按钮圆角边框值，同CSS，如'5px'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "button-size", "description": "箭头按钮的像素尺寸，同CSS，如'12px'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "convert", "description": "是否将定位结果转换为高德坐标", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "enable-high-accuracy", "description": "进行浏览器原生定位的时候是否尝试获取较高精度，可能影响定位效率，默认为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "timeout", "description": "定位的超时时间，毫秒", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "maximum-age", "description": "浏览器原生定位的缓存时间，毫秒", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "show-button", "description": "是否显示定位按钮，默认为true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-circle", "description": "是否显示定位精度圆，默认为true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-marker", "description": "是否显示定位点，默认为true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "marker-options", "description": "定位点的样式", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "markeroptions", "kind": "expression"}}, {"name": "circle-options", "description": "定位圆的样式", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "circleoptions", "kind": "expression"}}, {"name": "pan-to-location", "description": "定位成功后是否自动移动到响应位置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "zoom-to-accuracy", "description": "定位成功后是否自动调整级别", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "geo-location-first", "description": "优先使用H5定位，默认移动端为true，PC端为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "no-ip-locate", "description": "是否禁用IP精确定位，默认为0，0:都用 1:手机上不用 2:PC上不用 3:都不用", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "no-geo-location", "description": "是否禁用浏览器原生定位，默认为0，0:都用 1:手机上不用 2:PC上不用 3:都不用", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "use-native", "description": "是否与高德定位SDK能力结合，需要同时使用安卓版高德定位sdk，否则无效", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "get-city-when-fail", "description": "定位失败之后是否返回基本城市定位信息", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "need-address", "description": "是否需要将定位结果进行逆地理编码操作", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extensions", "description": "是否需要详细的逆地理编码信息，默认为'base'只返回基本信息，可选'all'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "complete", "description": "定位结束后触发的事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/geolocation.html#events"}]}, {"name": "el-amap-control-hawk-eye", "source": {"symbol": "ElAmapControlHawkEye"}, "description": "鹰眼控件，用于显示缩略地图，显示于地图右下角，可以随主图的视口变化而变化，也可以配置成固定位置实现类似于南海附图的效果。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#amapcontrolhawkeye", "attributes": [{"name": "auto-move", "description": "是否随主图视口变化移动", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-rectangle", "description": "是否显示视口矩形", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-button", "description": "是否显示打开关闭的按钮", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "map-style", "description": "缩略图要显示的地图自定义样式，如'amap://styles/dark'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "layers", "description": "缩略图要显示的图层类型，默认为普通矢量地图", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "width", "description": "缩略图的宽度，同CSS，如'200px'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "height", "description": "缩略图的高度，同CSS，如'200px'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "offset", "description": "缩略图距离地图右下角的像素距离，如 [2,2]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "border-style", "description": "缩略图的边框样式，同CSS，如\"double solid solid double\"", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "border-color", "description": "缩略图的边框颜色，同CSS，如'silver'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "border-radius", "description": "缩略图的边框角度，同CSS，如'5px'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "border-width", "description": "缩略图的边框宽度，同CSS，如'2px'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "button-size", "description": "箭头按钮的像素尺寸，同CSS，如'12px'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "is-open", "description": "默认是否展开", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "show", "description": "显示时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#events"}, {"name": "hide", "description": "隐藏时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/hawk-eye.html#events"}]}, {"name": "el-amap-control-map-type", "source": {"symbol": "ElAmapControlMapType"}, "description": "地图类型切换插件。用户通过该插件进行地图切换。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#amapcontrolmaptype", "attributes": [{"name": "default-type", "description": "初始化默认图层类型。 取值为0：默认底图 取值为1：卫星图 默认值：0", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "show-traffic", "description": "叠加实时交通图层 默认值：false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-road", "description": "叠加路网图层 默认值：false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "show", "description": "显示时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#events"}, {"name": "hide", "description": "隐藏时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/map-type.html#events"}]}, {"name": "el-amap-control-scale", "source": {"symbol": "ElAmapControlScale"}, "description": "位于地图右下角，用户可控制其显示与隐藏。继承自 AMap.Control", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#amapcontrolscale", "attributes": [{"name": "position", "description": "控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes", "value": {"type": "string|object", "kind": "expression"}}, {"name": "offset", "description": "地图默认鼠标样式。参数defaultCursor应符合CSS的cursor属性规范。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "animate-enable", "description": "相对于地图容器左上角的偏移量，正数代表向右下偏移。默认为AMap.Pixel(10,10)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "show", "description": "显示时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#events"}, {"name": "hide", "description": "隐藏时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/scale.html#events"}]}, {"name": "el-amap-search-box", "source": {"symbol": "ElAmapSearchBox"}, "description": "用于进行 POI 搜索联想与数据查询的相关类型", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#amapsearchbox", "attributes": [{"name": "datatype", "description": "返回的数据类型。可选值：all-返回所有数据类型、poi-返回POI数据类型、bus-返回公交站点数据类型、busline-返回公交线路数据类型目前暂时不支持多种类型", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "input", "description": "可选参数，用来指定一个input输入框，设定之后，在input输入文字将自动生成下拉选择列表。输入框DOM对象的id值", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "output", "description": "可选参数，指定一个现有的div的id或者元素，作为展示提示结果的容器，当指定了input的时候有效，缺省的时候将自动创建一个显示结果面板", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "out-put-dir-auto", "description": "默认为true，表示是否在input位于页面较下方的时候自动将输入面板显示在input上方以避免被遮挡", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-result-on-scroll", "description": "页面滚动时关闭搜索结果列表，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "lang", "description": "设置检索语言类型，默认中文 'zh_cn'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "type", "description": "输入提示时限定POI类型，多个类型用“", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "city", "description": "输入提示时限定城市。可选值：城市名（中文或中文全拼）、citycode、adcode；默认值：“全国”", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "citylimit", "description": "是否强制限制在设置的城市内搜索,默认值为：false，true：强制限制设定城市，false：不强制限制设定城市", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "input-custom", "description": "是否自定义input，自定义的时候将使用用户的inputId，默认 false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "placeholder", "description": "默认输入框的placeholder属性", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "debounce", "description": "手动加上防抖功能，默认100毫秒", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "select", "description": "鼠标点击或者回车选中某个POI信息时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#events"}, {"name": "choose", "description": "鼠标或者键盘上下键选择POI信息时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/search-box.html#events"}]}, {"name": "el-amap-control-tool-bar", "source": {"symbol": "ElAmapControlToolBar"}, "description": "地图操作工具条插件。可支持方向导航、位置定位、视野级别缩放、视野级别选择等操作。继承自 AMap.Control", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#amapcontroltoolbar", "attributes": [{"name": "position", "description": "控件停靠位置 { top: 5; left: 5; right: 5; bottom: 5 } 或者 'LT': 左上角, 'RT': 右上角, 'LB': 左下角, 'RB': 右下角", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#attributes", "value": {"type": "string|object", "kind": "expression"}}, {"name": "offset", "description": "地图默认鼠标样式。参数defaultCursor应符合CSS的cursor属性规范。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "show", "description": "显示时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#events"}, {"name": "hide", "description": "隐藏时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/control/tool-bar.html#events"}]}, {"name": "el-amap-info-window", "source": {"symbol": "ElAmapInfoWindow"}, "description": "用于在地图上展示复杂的说明性信息的类型。<br/>信息窗体，地图仅可同时展示一个信息窗体，推荐为信息窗体通过样式显示设置尺寸", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#amapinfowindow", "attributes": [{"name": "is-custom", "description": "是否自定义窗体。设为true时，信息窗体外框及内容完全按照content所设的值添加（默认为false，即在系统默认的信息窗体外框中显示content内容）", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "auto-move", "description": "是否自动调整窗体到视野内（当信息窗体超出视野范围时，通过该属性设置是否自动平移地图，使信息窗体完全显示）", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "avoid", "description": "autoMove 为 true 时，自动平移到视野内后的上右下左的避让宽度。默认值： [20, 20, 20, 20]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "close-when-click-map", "description": "控制是否在鼠标点击地图后关闭信息窗体，默认false，鼠标点击地图后不关闭信息窗体", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "offset", "description": "信息窗体显示位置偏移量。默认基准点为信息窗体的底部中心。默认值: [0, 0]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否可见，默认 true。支持sync", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "content", "description": "显示内容，可以是HTML要素字符串或者HTMLElement对象。也可以根据示例中的方式使用slot实现", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "string|htmlelement", "kind": "expression"}}, {"name": "size", "description": "信息窗体尺寸（isCustom为true时，该属性无效）", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "anchor", "description": "信息窗体锚点。默认值：'bottom-center'。可选值：'top-left'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "position", "description": "信息窗体显示基点位置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "组件实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#events"}, {"name": "open", "description": "信息窗体打开之后触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#events"}, {"name": "close", "description": "信息窗体关闭之后触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/infoWindow/info-window.html#events"}]}, {"name": "el-amap-circle-marker", "source": {"symbol": "ElAmapCircleMarker"}, "description": "构造圆形对象，通过CircleOptions指定多边形样式", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#amapcirclemarker", "attributes": [{"name": "bubble", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上（自v1.3 新增）默认值：false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "center", "description": "圆心位置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "radius", "description": "圆半径，单位:px 最大值64", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "cursor", "description": "指定鼠标悬停时的鼠标样式，自定义cursor，IE仅支持cur/ani/ico格式，Opera不支持自定义cursor", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "visible", "description": "是否可见", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "多边形覆盖物的叠加顺序。地图上存在多个多边形覆盖物叠加时，通过该属性使级别较高的多边形覆盖物在上层显示默认zIndex：10", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "stroke-color", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-opacity", "description": "轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "float", "kind": "expression"}}, {"name": "stroke-weight", "description": "轮廓线宽度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "fill-color", "description": "多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "fill-opacity", "description": "多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.5", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "float", "kind": "expression"}}, {"name": "draggable", "description": "设置多边形是否可拖拽移动，默认为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "组件实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "rightclick", "description": "右键单击", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "hide", "description": "隐藏", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "show", "description": "显示", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "mousedown", "description": "鼠标按下", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "mouseup", "description": "鼠标抬起", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "mouseover", "description": "鼠标经过", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "mouseout", "description": "鼠标移出", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/circle-marker.html#events"}]}, {"name": "el-amap-elastic-marker", "source": {"symbol": "ElAmapElasticMarker"}, "description": "灵活点标记", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#amapelasticmarker", "attributes": [{"name": "top-when-click", "description": "鼠标点击时marker是否置顶，默认false ，不置顶", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bubble", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上, 默认值：false。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "zooms", "description": "点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "点标记是否可见，默认为true。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "点标记的叠加顺序。地图上存在多个点标记叠加时，通过该属性使级别较高的点标记在上层显示", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "position", "description": "点标记在地图上显示的位置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "offset", "description": "点标记显示位置偏移量，默认值为 [0,0] 。Marker指定position后，默认以marker左上角位置为基准点（若设置了anchor，则以anchor设置位置为基准点），对准所给定的position位置，若需使marker指定位置对准在position处，需根据marker的尺寸设置一定的偏移量。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "draggable", "description": "设置点标记是否可拖拽移动，默认为false。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "cursor", "description": "指定鼠标悬停时的鼠，默认值：'pointer'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "title", "description": "鼠标滑过点标记时的文字提示。不设置则鼠标滑过点标无文字提示。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "clickable", "description": "点标记是否可点击，默认值: true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "zoom-style-mapping", "description": "表示地图级别与styles中样式的映射，{14:0,15:0,16:1,17:1,}表示14到15级使用styles中的第0个样式，16-17级使用第二个样式", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "styles", "description": "多个不同样式的数组。每个style对象有用两个参数 icon 和 label", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属 ，支持JavaScript API任意数据类型，如 Marker的id等。可将自定义数据保存在该属性上，方便后续操作使用。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "`AMap.ElasticMarker`实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "rightclick", "description": "鼠标右键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "mousemove", "description": "鼠标移动", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "mouseover", "description": "鼠标移近点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "mouseout", "description": "鼠标移出点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "mousedown", "description": "鼠标在点标记上按下时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "mouseup", "description": "鼠标在点标记上按下后抬起时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "dragstart", "description": "开始拖拽点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "dragging", "description": "鼠标拖拽移动点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "dragend", "description": "点标记拖拽移动结束触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/elastic-marker.html#events"}]}, {"name": "el-amap-label-marker", "source": {"symbol": "ElAmapLabelMarker"}, "description": "标注类", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#amaplabelmarker", "attributes": [{"name": "name", "description": "标注名称，作为标注标识，并非最终在地图上显示的文字内容，显示文字内容请设置 opts.text.content", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "rank", "description": "避让优先级，获取标注的优先级，该优先级用于 labelsLayer 支持避让时，rank 值大的标注会避让掉 rank 值低的标注。默认值：1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "visible", "description": "标注是否可见，默认为true。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "标注的叠加顺序。地图上存在多个点标记叠加时，通过该属性使级别较高的点标记在上层显示，默认zIndex：1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "position", "description": "标注在地图上显示的位置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "zooms", "description": "标注显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "icon", "description": "标注图标设置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "text", "description": "标注文本设置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属 ，支持JavaScript API任意数据类型，如 Marker的id等。可将自定义数据保存在该属性上，方便后续操作使用。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "rotation", "description": "旋转角度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#attributes", "value": {"type": "number", "kind": "expression"}}], "events": [{"name": "init", "description": "`AMap.LabelMarker`实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events"}, {"name": "mousemove", "description": "鼠标移动", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events"}, {"name": "mouseover", "description": "鼠标移近点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events"}, {"name": "mouseout", "description": "鼠标移出点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events"}, {"name": "mousedown", "description": "鼠标在点标记上按下时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events"}, {"name": "mouseup", "description": "鼠标在点标记上按下后抬起时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/label-marker.html#events"}]}, {"name": "el-amap-marker-cluster", "source": {"symbol": "ElAmapMarkerCluster"}, "description": "用于展示大量点标记，将点标记按照距离进行聚合，以提高绘制性能。点聚合支持用户自定义样式，以插件形式调用。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#amapmarkercluster", "attributes": [{"name": "render-cluster-marker", "description": "该方法用来实现聚合点的自定义绘制，由开发者自己实现，API 将在绘制每个聚合点的时候调用这个方法，可以实现聚合点样式的灵活设定，指定了 renderClusterMarker 后 styles 无效。<br/>该函数的入参为一个Object，包含如下属性：<br/>1. count: 当前聚合点下聚合的 Marker 的数量<br/>2. marker: 当前聚合点显示的 Marker", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes", "value": {"type": "function", "kind": "expression"}}, {"name": "render-marker", "description": "该方法用来实现非聚合点的自定义绘制，由开发者自己实现，API 将在绘制每个非聚合点的时候调用这个方法<br/>该函数的入参为一个Object，包含如下属性：<br/>marker: 非聚合点 Marker 对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes", "value": {"type": "function", "kind": "expression"}}, {"name": "cluster-by-zoom-change", "description": "地图缩放过程中是否聚合。默认值 false。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "points", "description": "数据格式为一组含有经纬度信息的数组，如下所示。其中【经纬度】lnglat 为必填字段，【权重】weight 为可选字段,以权重高的点为中心进行聚合。示例: [{\"lnglat\":[\"113.864691\",\"22.942327\"], weight: 1},{\"lnglat\":[\"113.370643\",\"22.938827\"], weight: 8}]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "grid-size", "description": "聚合计算时网格的像素大小，默认60", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "max-zoom", "description": "最大的聚合级别，大于该级别就不进行相应的聚合。默认值为 18，即小于 18 级的级别均进行聚合，18 及以上级别不进行聚合", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "average-center", "description": "聚合点的图标位置是否是所有聚合内点的中心点。默认为 true。数据中如果含有权重值，以权重高的点为中心进行聚合", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "styles", "description": "指定聚合后的点标记的图标样式，[查看示例](https://lbs.amap.com/demo/jsapi-v2/example/mass-markers/markerclusterer)，可缺省，缺省时为默认样式<br/>数据元素分别对应聚合量在1-10,11-100,101-1000…的聚合点的样式；<br/>当用户设置聚合样式少于实际叠加的点数，未设置部分按照系统默认样式显示；<br/>单个图标样式包括以下几个属性：<br/>1. {string} url：图标显示图片的url地址（必选）<br/>2. {AMap.Size} size：图标显示图片的大小（必选）<br/>3. {AMap.Pixel} offset：图标定位在地图上的位置相对于图标左上角的偏移值。默认为(0,0),不偏移（可选）<br/>4. {AMap.Pixel} imageOffset：图片相对于可视区域的偏移值，此功能的作用等同CSS中的background-position属性。默认为(0,0)，不偏移（可选）<br/>5. {String} textColor：文字的颜色，默认为\"#000000\"（可选）<br/>6. {Number} textSize：文字的大小，默认为10（可选）", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "`AMap.MarkerCluster`实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker-cluster.html#events"}]}, {"name": "el-amap-marker", "source": {"symbol": "ElAmapMarker"}, "description": "", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#amapmarker", "attributes": [{"name": "top-when-click", "description": "鼠标点击时marker是否置顶，默认false ，不置顶", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bubble", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上, 默认值：false。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "zooms", "description": "点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "anchor", "description": "设置点标记锚点，可选值：'top-left','top-center','top-right', 'middle-left', 'center', 'middle-right', 'bottom-left', 'bottom-center', 'bottom-right'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "string|array", "kind": "expression"}}, {"name": "visible", "description": "点标记是否可见，默认为true。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "点标记的叠加顺序。地图上存在多个点标记叠加时，通过该属性使级别较高的点标记在上层显示，默认zIndex：12", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "position", "description": "点标记在地图上显示的位置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "offset", "description": "点标记显示位置偏移量，默认值为 [0,0] 。Marker指定position后，默认以marker左上角位置为基准点（若设置了anchor，则以anchor设置位置为基准点），对准所给定的position位置，若需使marker指定位置对准在position处，需根据marker的尺寸设置一定的偏移量。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "icon", "description": "在点标记中显示的图标。可以传一个图标地址，也可以传Icon对象。有合法的content内容设置时，此属性无效。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "string amap.icon", "kind": "expression"}}, {"name": "content", "description": "点标记显示内容。可以是HTML要素字符串或者HTML DOM对象。content有效时，icon属性将被覆盖。 支持slot", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "string htmlelement", "kind": "expression"}}, {"name": "draggable", "description": "设置点标记是否可拖拽移动，默认为false。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "cursor", "description": "指定鼠标悬停时的鼠，默认值：'pointer'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "angle", "description": "点标记的旋转角度，，广泛用于改变车辆行驶方向。默认值：0", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "title", "description": "鼠标滑过点标记时的文字提示。不设置则鼠标滑过点标无文字提示。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "clickable", "description": "点标记是否可点击，默认值: true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "label", "description": "添加文本标注，content为文本标注的内容，offset为偏移量，为偏移量,如设置了 direction，以 direction 方位为基准点进行偏移。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "{content|offset: [x|y]}", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属 ，支持JavaScript API任意数据类型，如 Marker的id等。可将自定义数据保存在该属性上，方便后续操作使用。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "move-options", "description": "marker修改位置时是否使用moveTo方法，使用moveTo可以动画移动，参数: {duration?: number,easing?: (passedTime: number) => number,autoRotation?: boolean}", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "`AMap.Marker`实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "rightclick", "description": "鼠标右键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "mousemove", "description": "鼠标移动", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "mouseover", "description": "鼠标移近点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "mouseout", "description": "鼠标移出点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "mousedown", "description": "鼠标在点标记上按下时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "mouseup", "description": "鼠标在点标记上按下后抬起时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "dragstart", "description": "开始拖拽点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "dragging", "description": "鼠标拖拽移动点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "dragend", "description": "点标记拖拽移动结束触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "moving", "description": "点标记在执行moveTo，moveAlong动画时触发事件，Object对象的格式是{passedPath:Array.}。其中passedPath为对象在moveAlong或者moveTo过程中走过的路径。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "moveend", "description": "点标记执行moveTo动画结束时触发事件，也可以由moveAlong方法触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "movealong", "description": "点标记执行moveAlong动画一次后触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/marker.html#events"}]}, {"name": "el-amap-mass-marks", "source": {"symbol": "ElAmapMassMarks"}, "description": "海量点类", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#amapmassmarks", "attributes": [{"name": "data", "description": "海量点数据", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "点标记是否可见，默认为true。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "点标记的叠加顺序。地图上存在多个点标记叠加时，通过该属性使级别较高的点标记在上层显示，默认zIndex：12", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "zooms", "description": "点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "cursor", "description": "指定鼠标悬停时的鼠，默认值：'pointer'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "styles", "description": "标号样式，可以是Object代表所有点样式一样，也可以是Array，根据各个点的设定来判断选择哪个样式", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes", "value": {"type": "array|object", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "`AMap.MassMarks`实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}, {"name": "complete", "description": "海量点加载完成事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}, {"name": "mousemove", "description": "鼠标移动", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}, {"name": "mouseover", "description": "鼠标移近点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}, {"name": "mouseout", "description": "鼠标移出点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}, {"name": "mousedown", "description": "鼠标在点标记上按下时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}, {"name": "mouseup", "description": "鼠标在点标记上按下后抬起时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/mass-marks.html#events"}]}, {"name": "el-amap-text", "source": {"symbol": "ElAmapText"}, "description": "文本标记", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#amaptext", "attributes": [{"name": "top-when-click", "description": "鼠标点击时marker是否置顶，默认false ，不置顶", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "bubble", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上, 默认值：false。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "zooms", "description": "点标记显示的层级范围，超过范围不显示。默认值：zooms: [2, 20]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "点标记是否可见，默认为true。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "点标记的叠加顺序。地图上存在多个点标记叠加时，通过该属性使级别较高的点标记在上层显示，默认zIndex：12", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "position", "description": "点标记在地图上显示的位置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "offset", "description": "点标记显示位置偏移量，默认值为 [0,0] 。Marker指定position后，默认以marker左上角位置为基准点（若设置了anchor，则以anchor设置位置为基准点），对准所给定的position位置，若需使marker指定位置对准在position处，需根据marker的尺寸设置一定的偏移量。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "text", "description": "标记显示的文本内容", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "draggable", "description": "设置点标记是否可拖拽移动，默认为false。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "cursor", "description": "指定鼠标悬停时的鼠，默认值：'pointer'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "angle", "description": "点标记的旋转角度，，广泛用于改变车辆行驶方向。默认值：0", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "title", "description": "鼠标滑过点标记时的文字提示。不设置则鼠标滑过点标无文字提示。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "clickable", "description": "点标记是否可点击，默认值: true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "text-style", "description": "设置文本样式，Object同css样式表，如:{'background-color':'red'}", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属 ，支持JavaScript API任意数据类型，如 Marker的id等。可将自定义数据保存在该属性上，方便后续操作使用。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "rightclick", "description": "鼠标右键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "mousemove", "description": "鼠标移动", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "mouseover", "description": "鼠标移近点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "mouseout", "description": "鼠标移出点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "mousedown", "description": "鼠标在点标记上按下时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "mouseup", "description": "鼠标在点标记上按下后抬起时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "dragstart", "description": "开始拖拽点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "dragging", "description": "鼠标拖拽移动点标记时触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "dragend", "description": "点标记拖拽移动结束触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "moving", "description": "点标记在执行moveTo，moveAlong动画时触发事件，Object对象的格式是{passedPath:Array.}。其中passedPath为对象在moveAlong或者moveTo过程中走过的路径。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "moveend", "description": "点标记执行moveTo动画结束时触发事件，也可以由moveAlong方法触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "movealong", "description": "点标记执行moveAlong动画一次后触发事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/marker/text.html#events"}]}, {"name": "el-amap-mouse-tool", "source": {"symbol": "ElAmapMouseTool"}, "description": "鼠标工具插件。通过该插件，可进行鼠标画标记点、线、多边形、矩形、圆、距离量测、面积量测、拉框放大、拉框缩小等功能", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#amapmousetool", "attributes": [{"name": "type", "description": "类型，默认 marker，可选值'marker', 'circle', 'rectangle', 'polyline', 'polygon', 'measureArea', 'rule', 'rectZoomIn', 'rectZoomOut'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "draw-options", "description": "设置绘制的图形的属性，可以实时更改，在切换时会生效，默认 null", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "auto-clear", "description": "是否自动清除地图上的绘制的图形，默认true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-tooltip", "description": "是否显示提示信息，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "tooltip-text-map", "description": "是否可见", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "text-options", "description": "提示信息的text的属性", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "组件实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#events"}, {"name": "draw", "description": "绘制事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/util/mouse-tool.html#events"}]}, {"name": "el-amap-bezier-curve", "source": {"symbol": "ElAmapBezierCurve"}, "description": "贝塞尔曲线", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#amapbeziercurve", "attributes": [{"name": "bubble", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上 默认 false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "edit-options", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polylineeditor)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "visible", "description": "是否可见", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "editable", "description": "折线当前是否可编辑", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "path", "description": "贝瑟尔曲线的路径。描述为一个二维数组规则如下：第一个元素是起点， 之后的元素同时描述控制点和途经点，之后每个元素可以有0个到2个控制点 控制点在前，途经点在最后 [ [lng,lat] ,//起点0 [lng,lat,lng,lat,lng,lat] ,//控制点、控制点、途经点2 [lng,lat,lng,lat] //控制点、途经点3 ] 或者 [ [ [lng,lat] ],//起点0 [ [lng,lat] , [lng,lat] ],//控制点、途经点1 [ [lng,lat] , [lng,lat] , [lng,lat] ],//控制点、控制点、途经点2 [ [lng,lat] , [lng,lat] ]//控制点、途经点3 ]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "z-index", "description": "折线覆盖物的叠加顺序。默认叠加顺序，先添加的线在底层，后添加的线在上层。通过该属性可调整叠加顺序，使级别较高的折线覆盖物在上层显示。默认zIndex：10", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "stroke-color", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#006600", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-opacity", "description": "线条透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "stroke-weight", "description": "线条宽度，单位：像素", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "border-weight", "description": "描边线宽度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "is-outline", "description": "线条是否带描边，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "outline-color", "description": "线条描边颜色，此项仅在isOutline为true时有效，默认：#000000", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "draggable", "description": "设置多边形是否可拖拽移动，默认为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "stroke-style", "description": "线样式，实线:solid，虚线:dashed", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-dasharray", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "line-join", "description": "折线拐点的绘制样式，默认值为'miter'尖角，其他可选值：'round'圆角、'bevel'斜角", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "line-cap", "description": "折线两端线帽的绘制样式，默认值为'butt'无头，其他可选值：'round'圆头、'square'方头", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "geodesic", "description": "是否绘制大地线，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-dir", "description": "是否延路径显示白色方向箭头,默认false。建议折线宽度大于6时使用", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "高德组件实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "rightclick", "description": "鼠标右键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "hide", "description": "隐藏", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "show", "description": "显示", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "mousedown", "description": "鼠标按下", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "mouseup", "description": "鼠标抬起", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "mouseover", "description": "鼠标经过", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "mouseout", "description": "鼠标移出", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "addnode", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "removenode", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "adjust", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "add", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}, {"name": "end", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/bezier-curve.html#events"}]}, {"name": "el-amap-circle", "source": {"symbol": "ElAmapCircle"}, "description": "构造圆形对象，通过CircleOptions指定多边形样式", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#amapcircle", "attributes": [{"name": "bubble", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上（自v1.3 新增）默认值：false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "edit-options", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polygoneditor)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "center", "description": "圆心位置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "radius", "description": "圆半径，单位:米", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "visible", "description": "是否可见", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "editable", "description": "多边形当前是否可编辑 (启动编辑时需要关闭 draggable，不然会导致图形被移走，但操作点还在原位)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "多边形覆盖物的叠加顺序。地图上存在多个多边形覆盖物叠加时，通过该属性使级别较高的多边形覆盖物在上层显示默认zIndex：10", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "stroke-color", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-opacity", "description": "轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "float", "kind": "expression"}}, {"name": "stroke-weight", "description": "轮廓线宽度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "fill-color", "description": "多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "fill-opacity", "description": "多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "float", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "stroke-style", "description": "轮廓线样式，实线:solid，虚线:dashed", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-dasharray", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "draggable", "description": "设置多边形是否可拖拽移动，默认为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "组件实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "rightclick", "description": "右键单击", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "hide", "description": "隐藏", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "show", "description": "显示", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "mousedown", "description": "鼠标按下", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "mouseup", "description": "鼠标抬起", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "mouseover", "description": "鼠标经过", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "mouseout", "description": "鼠标移出", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "addnode", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "removenode", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "move", "description": "移动覆盖物时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "adjust", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "add", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}, {"name": "end", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/circle.html#events"}]}, {"name": "el-amap-ellipse", "source": {"symbol": "ElAmapEllipse"}, "description": "构造椭圆对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#amapellipse", "attributes": [{"name": "bubble", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上（自v1.3 新增）默认值：false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "edit-options", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polygoneditor)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "center", "description": "圆心位置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "radius", "description": "椭圆的半径，用2个元素的数组表示，单位：米 如： radius: [1000, 2000] 表示横向半径是1000，纵向的半径是2000 默认值： [1000, 1000]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否可见", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "editable", "description": "多边形当前是否可编辑 (启动编辑时需要关闭 draggable，不然会导致图形被移走，但操作点还在原位)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "多边形覆盖物的叠加顺序。地图上存在多个多边形覆盖物叠加时，通过该属性使级别较高的多边形覆盖物在上层显示默认zIndex：10", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "stroke-color", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-opacity", "description": "轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "float", "kind": "expression"}}, {"name": "stroke-weight", "description": "轮廓线宽度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "fill-color", "description": "多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "fill-opacity", "description": "多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "float", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "stroke-style", "description": "轮廓线样式，实线:solid，虚线:dashed", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-dasharray", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "draggable", "description": "设置多边形是否可拖拽移动，默认为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "组件实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "rightclick", "description": "右键单击", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "hide", "description": "隐藏", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "show", "description": "显示", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "mousedown", "description": "鼠标按下", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "mouseup", "description": "鼠标抬起", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "mouseover", "description": "鼠标经过", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "mouseout", "description": "鼠标移出", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "addnode", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "removenode", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "move", "description": "移动覆盖物时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "adjust", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "add", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}, {"name": "end", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/ellipse.html#events"}]}, {"name": "<PERSON>-amap-g<PERSON><PERSON><PERSON>", "source": {"symbol": "ElAmap<PERSON><PERSON><PERSON><PERSON>"}, "description": "GeoJSON类，继承自OverLayGroup，可实现GeoJSON对象与OverlayGroup的相互转换", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#amapgeojson", "attributes": [{"name": "marker-options", "description": "marker的默认样式。存在getMarker参数时，该属性失效。该属性里的参数会全部带入marker中，但会被geojson的properties中的属性给覆盖。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "get-marker", "description": "指定点要素的绘制方式，缺省时为Marker的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes", "value": {"type": "funtion ", "kind": "expression"}}, {"name": "polyline-options", "description": "polyline的默认样式。存在getPolyline参数时，该属性失效。该属性里的参数会全部带入polyline中，但会被geojson的properties中的属性给覆盖。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "get-polyline", "description": "指定线要素的绘制方式，缺省时为Polyline的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes", "value": {"type": "funtion ", "kind": "expression"}}, {"name": "polygon-options", "description": "polygon的默认样式。存在getPolygon参数时，该属性失效。该属性里的参数会全部带入polygon中，但会被geojson的properties中的属性给覆盖。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "get-polygon", "description": "指定线要素的绘制方式，缺省时为Polygon的默认样式。geojson为当前要素对应的GeoJSON对象，lnglats为对应的线的路径", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes", "value": {"type": "funtion ", "kind": "expression"}}, {"name": "geo", "description": "要加载的标准GeoJSON对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "visible", "description": "是否可见", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "组件实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}, {"name": "rightclick", "description": "右键单击", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}, {"name": "mousedown", "description": "鼠标按下", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}, {"name": "mouseup", "description": "鼠标抬起", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}, {"name": "mouseover", "description": "鼠标经过", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}, {"name": "mouseout", "description": "鼠标移出", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/geojson.html#events"}]}, {"name": "el-amap-polygon", "source": {"symbol": "ElAmapPolygon"}, "description": "构造多边形对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#amappolygon", "attributes": [{"name": "bubble", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上（自v1.3 新增）默认值：false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "edit-options", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polygoneditor)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "path", "description": "多边形轮廓线的节点坐标数组。 支持 单个普通多边形(`{Array }`)，单个带孔多边形(`{Array<Array >}`)，多个带孔多边形(`{Array<Array<Array >>}`)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否可见", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "editable", "description": "多边形当前是否可编辑 (启动编辑时需要关闭 draggable，不然会导致图形被移走，但操作点还在原位)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "多边形覆盖物的叠加顺序。地图上存在多个多边形覆盖物叠加时，通过该属性使级别较高的多边形覆盖物在上层显示默认zIndex：10", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "stroke-color", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-opacity", "description": "轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "float", "kind": "expression"}}, {"name": "stroke-weight", "description": "轮廓线宽度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "fill-color", "description": "多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "fill-opacity", "description": "多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "float", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "stroke-style", "description": "轮廓线样式，实线:solid，虚线:dashed", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-dasharray", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "draggable", "description": "设置多边形是否可拖拽移动，默认为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "组件实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "rightclick", "description": "右键单击", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "hide", "description": "隐藏", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "show", "description": "显示", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "mousedown", "description": "鼠标按下", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "mouseup", "description": "鼠标抬起", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "mouseover", "description": "鼠标经过", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "mouseout", "description": "鼠标移出", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "addnode", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "removenode", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "move", "description": "移动覆盖物时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "adjust", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "add", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}, {"name": "end", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polygon.html#events"}]}, {"name": "el-amap-polyline", "source": {"symbol": "ElAmapPolyline"}, "description": "构造折线对象，支持 lineString 和 MultiLineString", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#amappolyline", "attributes": [{"name": "bubble", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上 默认 false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "edit-options", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polylineeditor)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "visible", "description": "是否可见", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "editable", "description": "折线当前是否可编辑", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "path", "description": "polyline 路径，支持 lineString 和 MultiLineString", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "z-index", "description": "折线覆盖物的叠加顺序。默认叠加顺序，先添加的线在底层，后添加的线在上层。通过该属性可调整叠加顺序，使级别较高的折线覆盖物在上层显示。默认zIndex：10", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "stroke-color", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#006600", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-opacity", "description": "线条透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "stroke-weight", "description": "线条宽度，单位：像素", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "border-weight", "description": "描边线宽度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "is-outline", "description": "线条是否带描边，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "outline-color", "description": "线条描边颜色，此项仅在isOutline为true时有效，默认：#000000", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "draggable", "description": "设置多边形是否可拖拽移动，默认为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "stroke-style", "description": "线样式，实线:solid，虚线:dashed", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-dasharray", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "line-join", "description": "折线拐点的绘制样式，默认值为'miter'尖角，其他可选值：'round'圆角、'bevel'斜角", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "line-cap", "description": "折线两端线帽的绘制样式，默认值为'butt'无头，其他可选值：'round'圆头、'square'方头", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "geodesic", "description": "是否绘制大地线，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "show-dir", "description": "是否延路径显示白色方向箭头,默认false。建议折线宽度大于6时使用", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "高德组件实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "rightclick", "description": "鼠标右键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "hide", "description": "隐藏", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "show", "description": "显示", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "mousedown", "description": "鼠标按下", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "mouseup", "description": "鼠标抬起", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "mouseover", "description": "鼠标经过", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "mouseout", "description": "鼠标移出", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "addnode", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "removenode", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "adjust", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "add", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}, {"name": "end", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/polyline.html#events"}]}, {"name": "el-amap-rectangle", "source": {"symbol": "ElAmapRectangle"}, "description": "构造矩形对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#amaprectangle", "attributes": [{"name": "bubble", "description": "是否将覆盖物的鼠标或touch等事件冒泡到地图上（自v1.3 新增）默认值：false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "edit-options", "description": "[设置编辑参数参数](https://a.amap.com/jsapi/static/doc/20210906/index.html?v=2#polygoneditor)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "bounds", "description": "矩形的范围, Array[Array[lng, lat]]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否可见", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "editable", "description": "多边形当前是否可编辑 (启动编辑时需要关闭 draggable，不然会导致图形被移走，但操作点还在原位)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "多边形覆盖物的叠加顺序。地图上存在多个多边形覆盖物叠加时，通过该属性使级别较高的多边形覆盖物在上层显示默认zIndex：10", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "stroke-color", "description": "线条颜色，使用16进制颜色代码赋值。默认值为#00D3FC", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-opacity", "description": "轮廓线透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "float", "kind": "expression"}}, {"name": "stroke-weight", "description": "轮廓线宽度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "fill-color", "description": "多边形填充颜色，使用16进制颜色代码赋值，如：#00B2D5", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "fill-opacity", "description": "多边形填充透明度，取值范围[0,1]，0表示完全透明，1表示不透明。默认为0.9", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "float", "kind": "expression"}}, {"name": "ext-data", "description": "用户自定义属性，支持JavaScript API任意数据类型，如Polygon的id等", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "any", "kind": "expression"}}, {"name": "stroke-style", "description": "轮廓线样式，实线:solid，虚线:dashed", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "stroke-dasharray", "description": "勾勒形状轮廓的虚线和间隙的样式，此属性在strokeStyle 为dashed 时有效， 此属性在ie9+浏览器有效 取值： 实线： [0,0,0] 虚线： [10,10] ， [10,10] 表示10个像素的实线和10个像素的空白（如此反复）组成的虚线 点画线： [10,2,10] ， [10,2,10] 表示10个像素的实线和2个像素的空白 + 10个像素的实线和10个像素的空白 （如此反复）组成的虚线", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "draggable", "description": "设置多边形是否可拖拽移动，默认为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "组件实例", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "click", "description": "鼠标左键单击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "dblclick", "description": "鼠标左键双击事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "rightclick", "description": "右键单击", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "hide", "description": "隐藏", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "show", "description": "显示", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "mousedown", "description": "鼠标按下", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "mouseup", "description": "鼠标抬起", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "mouseover", "description": "鼠标经过", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "mouseout", "description": "鼠标移出", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "touchstart", "description": "触摸开始时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "touchmove", "description": "触摸移动进行中时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "touchend", "description": "触摸结束时触发事件，仅适用移动设备", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "addnode", "description": "编辑状态下，通过鼠标在折线上增加一个节点或在多边形上增加一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "removenode", "description": "编辑状态下，通过鼠标在折线上删除一个节点或在多边形上删除一个顶点时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "move", "description": "移动覆盖物时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "adjust", "description": "编辑状态下，鼠标调整折线上某个节点或多边形上某个顶点的位置时触发此事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "add", "description": "创建一个覆盖物之后触发该事件，target即为创建对象。当editor编辑对象为空时，调用open接口，再点击一次屏幕就会创建新的覆盖物对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}, {"name": "end", "description": "关闭编辑状态，触发该事件，target即为编辑后的折线/多边形对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/vector/rectangle.html#events"}]}, {"name": "el-amap-layer-canvas", "source": {"symbol": "ElAmapLayerCanvas"}, "description": "Canvas图层类，用户可以将一个 Canvas 作为图层添加在地图上，Canvas图层会随缩放级别而自适应缩放。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#amaplayercanvas", "attributes": [{"name": "canvas", "description": "Canvas DOM 对象", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes", "value": {"type": "htmlcanvaselement", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "bounds", "description": "图片的范围大小经纬度，如果传递数字数组类型: [minlng,minlat,maxlng,maxlat]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes", "value": {"type": "array|amap.bounds", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：6", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#events"}, {"name": "complete", "description": "加载完成事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/canvas.html#events"}]}, {"name": "el-amap-layer-custom-xyz", "source": {"symbol": "ElAmapLayerCustomXyz"}, "description": "自定义瓦片纠偏图层是基于GLCustomLayer实现的瓦片加载图层，支持瓦片坐标系纠偏，支持`wgs84` `gcj02` `bd09`三种坐标系。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#amaplayercustomxyz", "attributes": [{"name": "url", "description": "瓦片地址，支持 {s} {x} {y} {z}，示例：`http://webst0{s}.is.autonavi.com/appmaptile?style=6&x={x}&y={y}&z={z}`", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "subdomains", "description": "子域名数组，当url中设置{s}后，该属性必填", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "string[]", "kind": "expression"}}, {"name": "tile-type", "description": "瓦片分割类型，默认是`xyz`，xyz代表瓦片是编号是从左上角开始，百度瓦片是由中间开始，所以需要区分普通瓦片还是百度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "'xyz' | 'bd09'", "kind": "expression"}}, {"name": "proj", "description": "瓦片使用的坐标系，默认是`gcj02`", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "'wgs84' | 'gcj02' | 'bd09'", "kind": "expression"}}, {"name": "zooms", "description": "图层缩放等级范围，默认 [2, 18]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "[number|number]", "kind": "expression"}}, {"name": "opacity", "description": "图层透明度，默认为 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "visible", "description": "图层是否可见，默认为 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层的层级，默认为 120", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "debug", "description": "开启debug后瓦片上将显示瓦片编号", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "mask", "description": "瓦片掩膜，数据结构与AMap.Map的mask参数一致", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "number[][] | number[][][]   | number[][][][]", "kind": "expression"}}, {"name": "cache-size", "description": "瓦片缓存数量，默认512，不限制缓存瓦片数", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "tile-max-zoom", "description": "瓦片在服务器的最大层级，当地图zoom超过该层级后直接使用该层级作为做大层级瓦片，默认18", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "altitude", "description": "加载的瓦片海拔，设置该值后，在3D模式下瓦片将浮空，默认：0", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#attributes", "value": {"type": "number", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom-xyz.html#events"}]}, {"name": "el-amap-layer-custom", "source": {"symbol": "ElAmapLayerCustom"}, "description": "自定义图层是一种完全由开发者来指定绘制方法的图层", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#amaplayercustom", "attributes": [{"name": "canvas", "description": "canvas 对象,必填", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes", "value": {"type": "htmlelement", "kind": "expression"}}, {"name": "render", "description": "绘制函数，初始化完成时候，开发者需要给该图层设定render方法，该方法需要实现图层的绘制，API会在合适的时机自动调用该方法", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes", "value": {"type": "function", "kind": "expression"}}, {"name": "always-render", "description": "是否主动，默认 false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-20]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：120", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/custom.html#events"}]}, {"name": "el-amap-layer-district-cluster", "source": {"symbol": "ElAmapLayerDistrictCluster"}, "description": "高德地图的区划聚合图层，图层基于AMapUI的区划插件改造实现", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#amaplayerdistrictcluster", "attributes": [{"name": "z-index", "description": "图层的层级，默认为 10", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "visible", "description": "图层是否可见，默认为 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "data", "description": "数据源数组，每个元素即为点相关的信息", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "get-position", "description": "返回数据项中的经纬度信息", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes", "value": {"type": "function", "kind": "expression"}}, {"name": "auto-set-fit-view", "description": "是否在绘制后自动调整地图视野以适合全部点，默认true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "top-adcodes", "description": "顶层区划的adcode列表。默认为[100000]，即全国范围.假如仅需要展示河北和北京，可以设置为[130000, 110000],", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes", "value": {"type": "number[]", "kind": "expression"}}, {"name": "excluded-adcodes", "description": "需要排除的区划的adcode列表", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes", "value": {"type": "number[]", "kind": "expression"}}, {"name": "render-options", "description": "绘制的引擎的参数", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes", "value": {"type": "renderoptions", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#events"}, {"name": "featureClick", "description": "鼠标点击feature对应的区域时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#events"}, {"name": "featureMouseover", "description": "鼠标移入feature对应的区域时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#events"}, {"name": "featureMouseout", "description": "鼠标移出feature对应的区域时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#events"}, {"name": "clusterMarkerClick", "description": "鼠标点击聚合标注时触发", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/district-cluster.html#events"}]}, {"name": "el-amap-layer-flexible", "source": {"symbol": "ElAmapLayerFlexible"}, "description": "灵活切片图层，继承自AMap.TileLayer，开发者可通过构造时传入给其传入createTile字段来指定每一个切片的内容<br/>", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#amaplayerflexible", "attributes": [{"name": "cache-size", "description": "缓存瓦片数量", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "create-tile", "description": "由开发者实现，由API自动调用，xyz分别为切片横向纵向编号和层级，切片大小 256。假设每次创建的贴片为A(支持img或者canvas)，当创建或者获取成功时请回调success(A)，不需要显示或者失败时请回调fail()", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes", "value": {"type": "function", "kind": "expression"}}, {"name": "tile-size", "description": "切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#events"}, {"name": "complete", "description": "图块切片加载完成事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/flexible.html#events"}]}, {"name": "el-amap-layer-gl-custom", "source": {"symbol": "ElAmapLayerGlCustom"}, "description": "3d 自定义图层", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#amaplayerglcustom", "attributes": [{"name": "init", "description": "初始化的时候，开发者可以在这个函数参数里面获取 gl 上下文，进行一些初始化的操作。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes", "value": {"type": "function", "kind": "expression"}}, {"name": "render", "description": "绘制函数，初始化完成时候，开发者需要给该图层设定render方法，该方法需要实现图层的绘制，API会在合适的时机自动调用该方法", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes", "value": {"type": "function", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-20]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：120", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/gl-custom.html#events"}]}, {"name": "el-amap-layer-heat-map", "source": {"symbol": "ElAmapLayerHeatMap"}, "description": "热力图，基于第三方heatmap.js实现，以特殊高亮的形式显示数据密集程度。根据密集程度的不同，图上会呈现不同的颜色，以直观的形式展现数据密度。API引用了heatmap.js最新版本v2.0，v2.0基于新的渲染模型，具有更高的渲染效率和更强的性能。支持chrome、firefox、safari、ie9及以上浏览器。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#amaplayerheatmap", "attributes": [{"name": "radius", "description": "热力图中单个点的半径，默认：30，单位：pixel", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "gradient", "description": "热力图的渐变区间，热力图按照设置的颜色及间隔显示热力图，例{0.4:'rgb(0, 255, 255)',0.85:'rgb(100, 0, 255)',},其中 key 表示间隔位置，取值范围： [0,1] ，value 为颜色值。默认：heatmap.js标准配色方案", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "config", "description": "3d参数在组件中被重命名为config，参数内容与3d一致。3D热力图属性", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [3-20]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：130", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "data-set", "description": "热力图数据集 {data: points, max: 100}", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/heat-map.html#events"}]}, {"name": "el-amap-layer-image", "source": {"symbol": "ElAmapLayerImage"}, "description": "图片图层类，用户可以将一张静态图片作为图层添加在地图上，图片图层会随缩放级别而自适应缩放。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#amaplayerimage", "attributes": [{"name": "url", "description": "图片地址链接", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "bounds", "description": "图片的范围大小经纬度，如果传递数字数组类型: [minlng,minlat,maxlng,maxlat]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes", "value": {"type": "array|amap.bounds", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：6", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#events"}, {"name": "complete", "description": "加载完成事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/image.html#events"}]}, {"name": "el-amap-layer-labels", "source": {"symbol": "ElAmapLayerLabels"}, "description": "标注层。<br/>", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#amaplayerlabels", "attributes": [{"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：120", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "collision", "description": "标注层内的标注是否避让", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "allow-collision", "description": "标注层内的标注是否允许其它标注层对它避让", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/labels.html#events"}]}, {"name": "el-amap-layer-tiles3d", "source": {"symbol": "ElAmapLayerTiles3d"}, "description": "使用 AMap.3DTilesLayer 图层加载渲染标准 3D Tiles 数据，可支持 i3dm、b3dm、pnts 格式", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#amaplayertiles3d", "attributes": [{"name": "url", "description": "3d Tiles 入口文件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "three-script-url", "description": "ThreeJS的文件加载地址", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "three-gltf-loader", "description": "threeJS的GltfLoader文件加载地址", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "layer-style", "description": "图层样式", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/tiles3d.html#events"}]}, {"name": "el-amap-layer-vector", "source": {"symbol": "ElAmapLayerVector"}, "description": "矢量标记图层。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#amaplayervector", "attributes": [{"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：120", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/vector.html#events"}]}, {"name": "el-amap-layer-video", "source": {"symbol": "ElAmapLayerVideo"}, "description": "Video图层类，用户可以将一个 Video 作为图层添加在地图上，Video图层会随缩放级别而自适应缩放，Video图层基于Canvas实现。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#amaplayervideo", "attributes": [{"name": "url", "description": "视频地址", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "bounds", "description": "图片的范围大小经纬度，如果传递数字数组类型: [minlng,minlat,maxlng,maxlat]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes", "value": {"type": "array|amap.bounds", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：6", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/data/video.html#events"}]}, {"name": "el-amap-layer-buildings", "source": {"symbol": "ElAmapLayerBuildings"}, "description": "建筑楼块 3D 图层。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#amaplayerbuildings", "attributes": [{"name": "wall-color", "description": "楼块侧面颜色，支持 rgba、rgb、十六进制等", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes", "value": {"type": "array<string>|string", "kind": "expression"}}, {"name": "roof-color", "description": "楼块顶面颜色，支持 rgba、rgb、十六进制等", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes", "value": {"type": "array<string>|string", "kind": "expression"}}, {"name": "height-factor", "description": "楼块的高度系数因子，默认为 1，也就是正常高度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "style-opts", "description": "楼块的围栏和样式设置", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "zooms", "description": "图层缩放等级范围，默认 [2, 20]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#events"}, {"name": "complete", "description": "图块切片加载完成事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/buildings.html#events"}]}, {"name": "el-amap-layer-default", "source": {"symbol": "ElAmapLayerDefault"}, "description": "", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#amaplayerdefault", "attributes": [{"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否可见，默认为true。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "默认zIndex：0", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/default-layer.html#events"}]}, {"name": "el-amap-layer-district", "source": {"symbol": "ElAmapLayerDistrict"}, "description": "简易行政区划图。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#amaplayerdistrict", "attributes": [{"name": "type", "description": "类型，可选值：World 世界, Country 国家, Province 省市", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "depth", "description": "设定数据的层级深度，depth为0的时候只显示国家面，depth为1的时候显示省级， 当国家为中国时设置depth为2的可以显示市一级", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "adcode", "description": "行政区的编码，可从链接下载[adcode与省市行政区对照表](https://a.amap.com/lbs/static/file/AMap_adcode_citycode.xlsx.zip)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "s-o-c", "description": "设定显示的国家 [SOC 国家代码、名称、Bounds对照表下载](https://a.amap.com/jsapi_demos/static/demo-center/js/soc-list.json)", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "styles", "description": "为简易行政区图设定各面的填充颜色和描边颜色。 styles各字段的值可以是颜色值，也可以是一个返回颜色值* 的回调函数function。支持的颜色格式有：<br/>1. #RRGGBB，如：'#FFFFFF' <br/>2. rgba()，如：'rgba(255,255,255,1)'<br/> 3. rgb()，如：'rgb(255,255,255)'<br/>4. [r,g,b,a] ，如： [1,1,1,1]<br/>5. ''，代表不赋予颜色", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/district-layer.html#events"}]}, {"name": "el-amap-layer-indoor-map", "source": {"symbol": "ElAmapLayerIndoorMap"}, "description": "室内图层，用于在适当级别展示室内地图，并提供显示商铺tip、切换楼层等功能。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#amaplayerindoormap", "attributes": [{"name": "cursor", "description": "指定鼠标悬停到店铺面时的鼠标样式", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "hide-floor-bar", "description": "是否隐藏楼层切换控件，默认值：false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/indoor-map.html#events"}]}, {"name": "el-amap-layer-road-net", "source": {"symbol": "ElAmapLayerRoadNet"}, "description": "路网图层，展示道路信息。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#amaplayerroadnet", "attributes": [{"name": "tile-size", "description": "切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#events"}, {"name": "complete", "description": "图块切片加载完成事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/roadNet.html#events"}]}, {"name": "el-amap-layer-satellite", "source": {"symbol": "ElAmapLayerSatellite"}, "description": "卫星图层类，继承自TileLayer。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#amaplayersatellite", "attributes": [{"name": "tile-size", "description": "切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#events"}, {"name": "complete", "description": "图块切片加载完成事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/satellite.html#events"}]}, {"name": "el-amap-layer-tile", "source": {"symbol": "ElAmapLayerTile"}, "description": "切片图层类，该类为基础类。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#amaplayertile", "attributes": [{"name": "data-zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "tile-size", "description": "切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "tile-url", "description": "切片取图地址 如：' https://abc{0,1,2,3}.amap.com/tile?x=[x]&y=[y]&z=[z] ' [x] 、 [y] 、 [z] 分别替代切片的xyz。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#events"}, {"name": "complete", "description": "图块切片加载完成事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/tile-layer.html#events"}]}, {"name": "el-amap-layer-traffic", "source": {"symbol": "ElAmapLayer<PERSON><PERSON>ff<PERSON>"}, "description": "实时交通图层类，继承自TileLayer。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#amaplayertraffic", "attributes": [{"name": "auto-refresh", "description": "是否自动更新数据，默认开启", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "tile-size", "description": "切片大小，取值： 256，表示切片大小为256 256， 128，表示切片大小为128 128， 64，表示切片大小为64*64。默认值为256", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "interval", "description": "自动更新数据的间隔毫秒数，默认 180ms", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#events"}, {"name": "complete", "description": "图块切片加载完成事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/official/traffic.html#events"}]}, {"name": "el-amap-layer-mapbox-vector-tile", "source": {"symbol": "ElAmapLayerMapboxVectorTile"}, "description": "为了满足基于矢量瓦片块的数据可视化、矢量瓦片边界展示等开发需求，通过 AMap.MapboxVectorTileLayer 插件提供了简易矢量瓦片图层", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#amaplayermapboxvectortile", "attributes": [{"name": "url", "description": "MVT 数据的链接地址", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "data-zooms", "description": "瓦片数据等级范围，超过范围会使用最大/最小等级的数据，默认范围 [2,18]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2,22]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "styles", "description": "样式", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/mapbox-vector-tile-layer.html#events"}]}, {"name": "el-amap-layer-wms", "source": {"symbol": "ElAmapLayerWms"}, "description": "用于加载OGC标准的WMS地图服务的一种图层类，仅支持EPSG3857坐标系统的WMS图层。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#amaplayerwms", "attributes": [{"name": "blend", "description": "地图级别切换时，不同级别的图片是否进行混合，如图层的图像内容为部分透明请设置为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "url", "description": "wmts服务的url地址，如：' https://services.arcgisonline.com/arcgis/rest/services/'+ 'Demographics/USA_Population_Density/MapServer/WMTS/'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "params", "description": "OGC标准的WMS地图服务的GetMap接口的参数，包括VERSION、LAYERS、STYLES、FORMAT、TRANSPARENT等，<br/>CRS、BBOX、REQUEST、WIDTH、HEIGHT等参数请勿添加，例如：<br/>{ </br/>  LAYERS: 'topp:states',<br/>  VERSION:'1.3.0',<br/>  FORMAT:'image/png'<br/>  }", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#events"}, {"name": "complete", "description": "图块切片加载完成事件", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wms.html#events"}]}, {"name": "el-amap-layer-wmts", "source": {"symbol": "ElAmapLayerWmts"}, "description": "用于加载OGC标准的WMS地图服务的一种图层类，仅支持EPSG3857坐标系统的WMTS图层。", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#amaplayerwmts", "attributes": [{"name": "blend", "description": "地图级别切换时，不同级别的图片是否进行混合，如图层的图像内容为部分透明请设置为false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "url", "description": "wmts服务的url地址，如：' https://services.arcgisonline.com/arcgis/rest/services/'+ 'Demographics/USA_Population_Density/MapServer/WMTS/'", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes", "value": {"type": "string", "kind": "expression"}}, {"name": "params", "description": "OGC标准的WMS地图服务的GetMap接口的参数，包括VERSION、LAYERS、STYLES、FORMAT、TRANSPARENT等，<br/>CRS、BBOX、REQUEST、WIDTH、HEIGHT等参数请勿添加，例如：<br/>{ </br/>  LAYERS: 'topp:states',<br/>  VERSION:'1.3.0',<br/>  FORMAT:'image/png'<br/>  }", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes", "value": {"type": "object", "kind": "expression"}}, {"name": "zooms", "description": "支持的缩放级别范围，默认范围 [2-30]", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes", "value": {"type": "array", "kind": "expression"}}, {"name": "visible", "description": "是否显示，默认 true", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "z-index", "description": "图层叠加的顺序值，1 表示最底层。默认 zIndex：4", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "opacity", "description": "透明度，默认 1", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes", "value": {"type": "number", "kind": "expression"}}, {"name": "re-event-when-update", "description": "是否在组件更新时重新注册事件，主要用于数组更新时，解决绑定了事件但事件的对象不会更新问题，默认false", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes", "value": {"type": "boolean", "kind": "expression"}}, {"name": "extra-options", "description": "额外扩展属性，会直接将属性拷贝到初始化的options中，当key与props内的一样时会被props覆盖", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#attributes", "value": {"type": "object", "kind": "expression"}}], "events": [{"name": "init", "description": "实例初始化结束", "doc-url": "https://vue-amap.guyixi.cn/zh-cn/component/vue-amap/layer/standard/wmts.html#events"}]}]}}}